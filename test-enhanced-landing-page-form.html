<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Landing Page Form Test</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            direction: rtl;
            background: #f8fafc;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; font-weight: bold; }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .feature-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 6px;
            border-right: 4px solid #667eea;
        }
        .feature-icon {
            margin-left: 12px;
            font-size: 1.2rem;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: right;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
        }
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .demo-block {
            border: 2px dashed #667eea;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: #f8f9fa;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>🚀 Enhanced Landing Page Form - Test & Documentation</h1>
    <p>This page demonstrates the improvements made to the landing page creation form in the admin panel.</p>

    <div class="test-section">
        <h2>✨ New Features Implemented</h2>
        <div class="feature-list">
            <div class="feature-item">
                <span class="feature-icon">📏</span>
                <div>
                    <strong>Larger Modal Size:</strong> Modal now uses 95% of screen width (max 1400px) and 95% height for comfortable editing
                </div>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🧱</span>
                <div>
                    <strong>Dynamic Content Blocks:</strong> Add unlimited text and image blocks with drag-and-drop reordering
                </div>
            </div>
            <div class="feature-item">
                <span class="feature-icon">📝</span>
                <div>
                    <strong>Rich Text Editing:</strong> Each text block has its own TinyMCE editor with Arabic RTL support
                </div>
            </div>
        </div>
    </div>

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
        }

        h1 {
            color: #333;
            text-align: center;
        }

        .test-section {
            margin: 20px auto;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 800px;
        }

        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-icon {
            font-size: 24px;
            color: #4caf50;
        }

        strong {
            color: #555;
        }
    </style>
</body>
</html>
