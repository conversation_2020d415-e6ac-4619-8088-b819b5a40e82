<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Console Error Fixes</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test Console Error Fixes</h1>
        <p>اختبار إصلاحات أخطاء وحدة التحكم JavaScript</p>

        <div class="test-section">
            <h2>اختبارات الإصلاحات</h2>
            <button class="test-button" onclick="testTinyMCEEditorsFix()">
                1. اختبار إصلاح TinyMCE Editors
            </button>
            <button class="test-button" onclick="testSelectionErrorHandling()">
                2. اختبار معالجة أخطاء Selection
            </button>
            <button class="test-button" onclick="testNotificationSound()">
                3. اختبار صوت التنبيه المحسن
            </button>
            <button class="test-button" onclick="testGlobalErrorHandlers()">
                4. اختبار معالجات الأخطاء العامة
            </button>
        </div>

        <div class="test-section">
            <h2>اختبار شامل</h2>
            <button class="test-button" onclick="runAllTests()">
                🚀 تشغيل جميع الاختبارات
            </button>
            <button class="test-button" onclick="clearResults()">
                🧹 مسح النتائج
            </button>
        </div>

        <div id="test-results"></div>
        <div id="console-output"></div>
    </div>

    <!-- Include TinyMCE for testing -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>

    <script>
        let errorCount = 0;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        // Capture console errors
        console.error = function(...args) {
            errorCount++;
            originalConsoleError.apply(console, args);
            logToConsole('ERROR: ' + args.join(' '));
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToConsole('WARN: ' + args.join(' '));
        };

        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function logToConsole(message) {
            const output = document.getElementById('console-output');
            output.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        function testTinyMCEEditorsFix() {
            addTestResult('🧪 اختبار إصلاح TinyMCE Editors...', 'info');
            logToConsole('Testing TinyMCE editors fix...');
            
            if (typeof tinymce !== 'undefined') {
                try {
                    // Simulate the fixed code
                    const editors = tinymce.editors;
                    const editorInstances = editors ? Object.values(editors) : [];
                    
                    logToConsole(`Found ${editorInstances.length} editor instances`);
                    
                    for (const editor of editorInstances) {
                        if (editor) {
                            logToConsole(`Editor: ${editor.id || 'unnamed'}`);
                        }
                    }
                    
                    addTestResult('✅ TinyMCE editors iteration fix works', 'success');
                } catch (error) {
                    addTestResult('❌ TinyMCE editors fix failed: ' + error.message, 'error');
                    logToConsole(`TinyMCE test error: ${error.message}`);
                }
            } else {
                addTestResult('⚠️ TinyMCE not available for testing', 'warning');
            }
        }

        function testSelectionErrorHandling() {
            addTestResult('🧪 اختبار معالجة أخطاء Selection...', 'info');
            logToConsole('Testing selection error handling...');
            
            try {
                // Test the fixed selection handling
                if (window.getSelection) {
                    const selection = window.getSelection();
                    if (selection && selection.rangeCount !== undefined) {
                        logToConsole(`Selection rangeCount: ${selection.rangeCount}`);
                        addTestResult('✅ Selection API accessible', 'success');
                    } else {
                        logToConsole('Selection object exists but rangeCount not accessible');
                        addTestResult('⚠️ Selection rangeCount not accessible', 'warning');
                    }
                } else {
                    addTestResult('⚠️ getSelection not available', 'warning');
                }
                
                // Test error handling
                try {
                    // This might trigger the error we're trying to handle
                    const fakeSelection = null;
                    const rangeCount = fakeSelection.rangeCount; // Should trigger error
                } catch (error) {
                    if (error.message.includes('rangeCount')) {
                        addTestResult('✅ Selection error properly caught', 'success');
                        logToConsole('Selection error handled: ' + error.message);
                    }
                }
                
            } catch (error) {
                addTestResult('❌ Selection test failed: ' + error.message, 'error');
                logToConsole(`Selection test error: ${error.message}`);
            }
        }

        function testNotificationSound() {
            addTestResult('🧪 اختبار صوت التنبيه المحسن...', 'info');
            logToConsole('Testing enhanced notification sound...');
            
            // Test Web Audio API fallback
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
                
                addTestResult('✅ Web Audio API notification works', 'success');
                logToConsole('Web Audio notification test successful');
                
            } catch (error) {
                addTestResult('❌ Web Audio API test failed: ' + error.message, 'error');
                logToConsole(`Web Audio test error: ${error.message}`);
            }
            
            // Test MP3 fallback handling
            try {
                const audio = new Audio('assets/notification.mp3');
                audio.volume = 0.1;
                audio.preload = 'none';
                
                audio.addEventListener('error', () => {
                    addTestResult('✅ MP3 error handling works (expected)', 'success');
                    logToConsole('MP3 error handled as expected');
                });
                
                audio.addEventListener('canplaythrough', () => {
                    addTestResult('✅ MP3 file loads successfully', 'success');
                    logToConsole('MP3 file loaded successfully');
                });
                
                // Try to load
                audio.load();
                
            } catch (error) {
                addTestResult('✅ MP3 creation error handled: ' + error.message, 'success');
                logToConsole(`MP3 creation error handled: ${error.message}`);
            }
        }

        function testGlobalErrorHandlers() {
            addTestResult('🧪 اختبار معالجات الأخطاء العامة...', 'info');
            logToConsole('Testing global error handlers...');
            
            const initialErrorCount = errorCount;
            
            // Test selection error handling
            try {
                // Trigger a selection error
                const fakeEvent = {
                    error: {
                        message: 'can\'t access property "rangeCount", selection is null'
                    }
                };
                
                // Simulate the error handler
                if (fakeEvent.error && fakeEvent.error.message && (
                    fakeEvent.error.message.includes('rangeCount') || 
                    fakeEvent.error.message.includes('selection is null')
                )) {
                    addTestResult('✅ Global error handler detects selection errors', 'success');
                    logToConsole('Selection error pattern detected correctly');
                }
                
            } catch (error) {
                addTestResult('❌ Global error handler test failed: ' + error.message, 'error');
            }
            
            // Check if any new errors occurred during testing
            const newErrors = errorCount - initialErrorCount;
            if (newErrors === 0) {
                addTestResult('✅ No new console errors during testing', 'success');
            } else {
                addTestResult(`⚠️ ${newErrors} new console errors detected`, 'warning');
            }
        }

        async function runAllTests() {
            clearResults();
            addTestResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            logToConsole('Starting all console error fix tests...');
            
            const initialErrorCount = errorCount;
            
            testTinyMCEEditorsFix();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testSelectionErrorHandling();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testNotificationSound();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testGlobalErrorHandlers();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            const totalNewErrors = errorCount - initialErrorCount;
            
            addTestResult('🎉 انتهاء جميع الاختبارات', 'info');
            addTestResult(`📊 إجمالي الأخطاء الجديدة: ${totalNewErrors}`, totalNewErrors === 0 ? 'success' : 'warning');
            logToConsole('All tests completed');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('console-output').textContent = '';
            errorCount = 0;
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('📋 تشغيل اختبار أساسي تلقائياً...', 'info');
                testTinyMCEEditorsFix();
            }, 1000);
        });
    </script>
</body>
</html>
