<?php
/**
 * Test User Auth API directly
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: text/plain');

echo "🔧 Test API User Auth Direct\n";
echo "============================\n\n";

// Test 1: Check if the API file exists and is readable
echo "📁 Test fichier API :\n";
$apiFile = 'php/api/user-auth.php';
if (file_exists($apiFile)) {
    echo "✅ Fichier {$apiFile} existe\n";
    if (is_readable($apiFile)) {
        echo "✅ Fichier lisible\n";
    } else {
        echo "❌ Fichier non lisible\n";
    }
} else {
    echo "❌ Fichier {$apiFile} n'existe pas\n";
}

// Test 2: Check config.php path resolution
echo "\n📋 Test résolution config.php :\n";
$configPath = 'php/config.php';
if (file_exists($configPath)) {
    echo "✅ Config {$configPath} existe\n";
} else {
    echo "❌ Config {$configPath} n'existe pas\n";
}

// Test 3: Try to include and parse the API file
echo "\n🔍 Test inclusion API :\n";
try {
    // Capture any output from the API file
    ob_start();
    
    // Set up minimal environment for the API
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_GET['action'] = 'check';
    
    // Include the API file
    include $apiFile;
    
    $output = ob_get_clean();
    
    echo "✅ API incluse sans erreur fatale\n";
    echo "Sortie de l'API :\n";
    echo "================\n";
    echo $output;
    echo "\n================\n";
    
} catch (ParseError $e) {
    ob_end_clean();
    echo "❌ Erreur de syntaxe PHP : " . $e->getMessage() . "\n";
    echo "Ligne : " . $e->getLine() . "\n";
} catch (Error $e) {
    ob_end_clean();
    echo "❌ Erreur fatale : " . $e->getMessage() . "\n";
    echo "Ligne : " . $e->getLine() . "\n";
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception : " . $e->getMessage() . "\n";
    echo "Trace : " . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Test terminé\n";
?>
