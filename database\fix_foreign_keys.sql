-- Fix Foreign Key Constraints to reference produits instead of livres
-- This script updates all foreign key constraints to use the consolidated produits table

SET FOREIGN_KEY_CHECKS = 0;

-- Fix details_commande foreign key
ALTER TABLE details_commande DROP FOREIGN KEY IF EXISTS details_commande_ibfk_2;
ALTER TABLE details_commande ADD CONSTRAINT details_commande_ibfk_2 
FOREIGN KEY (livre_id) REFERENCES produits(id);

-- Fix panier foreign key  
ALTER TABLE panier DROP FOREIGN KEY IF EXISTS panier_ibfk_1;
ALTER TABLE panier ADD CONSTRAINT panier_ibfk_1 
FOREIGN KEY (livre_id) REFERENCES produits(id) ON DELETE CASCADE;

-- Fix product_content_blocks foreign key
ALTER TABLE product_content_blocks DROP FOREIGN KEY IF EXISTS product_content_blocks_ibfk_1;
ALTER TABLE product_content_blocks ADD CONSTRAINT product_content_blocks_ibfk_1 
FOREIG<PERSON> KEY (product_id) REFERENCES produits(id) ON DELETE CASCADE;

SET FOREIGN_KEY_CHECKS = 1;
