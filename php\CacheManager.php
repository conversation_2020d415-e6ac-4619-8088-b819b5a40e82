<?php
/**
 * Simple File-Based Cache Manager for Mossaab Landing Page
 * Provides fast caching for API responses with Arabic text support
 */

class CacheManager
{
    private $cacheDir;
    private $defaultTtl;
    private $enabled;

    public function __construct($cacheDir = 'cache/', $defaultTtl = 3600, $enabled = true)
    {
        $this->cacheDir = rtrim($cacheDir, '/') . '/';
        $this->defaultTtl = $defaultTtl;
        $this->enabled = $enabled;

        // Create cache directory if it doesn't exist
        if ($this->enabled && !is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }

        // Create .htaccess to protect cache directory
        $htaccessPath = $this->cacheDir . '.htaccess';
        if ($this->enabled && !file_exists($htaccessPath)) {
            file_put_contents($htaccessPath, "Deny from all\n");
        }
    }

    /**
     * Get cached data
     */
    public function get($key)
    {
        if (!$this->enabled) {
            return null;
        }

        $filename = $this->getFilename($key);
        
        if (!file_exists($filename)) {
            return null;
        }

        $data = file_get_contents($filename);
        if ($data === false) {
            return null;
        }

        $cached = json_decode($data, true);
        if (!$cached || !isset($cached['expires']) || !isset($cached['data'])) {
            // Invalid cache format, delete it
            unlink($filename);
            return null;
        }

        // Check if cache has expired
        if (time() > $cached['expires']) {
            unlink($filename);
            return null;
        }

        return $cached['data'];
    }

    /**
     * Store data in cache
     */
    public function set($key, $data, $ttl = null)
    {
        if (!$this->enabled) {
            return false;
        }

        $ttl = $ttl ?? $this->defaultTtl;
        $filename = $this->getFilename($key);

        $cached = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time(),
            'key' => $key
        ];

        $json = json_encode($cached, JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
        
        return file_put_contents($filename, $json, LOCK_EX) !== false;
    }

    /**
     * Delete cached data
     */
    public function delete($key)
    {
        if (!$this->enabled) {
            return false;
        }

        $filename = $this->getFilename($key);
        
        if (file_exists($filename)) {
            return unlink($filename);
        }

        return true;
    }

    /**
     * Clear all cache
     */
    public function clear()
    {
        if (!$this->enabled) {
            return false;
        }

        $files = glob($this->cacheDir . '*.cache');
        $deleted = 0;

        foreach ($files as $file) {
            if (unlink($file)) {
                $deleted++;
            }
        }

        return $deleted;
    }

    /**
     * Get cache statistics
     */
    public function getStats()
    {
        if (!$this->enabled) {
            return [
                'enabled' => false,
                'total_files' => 0,
                'total_size' => 0,
                'expired_files' => 0
            ];
        }

        $files = glob($this->cacheDir . '*.cache');
        $totalSize = 0;
        $expiredFiles = 0;
        $validFiles = 0;

        foreach ($files as $file) {
            $size = filesize($file);
            $totalSize += $size;

            // Check if file is expired
            $data = file_get_contents($file);
            $cached = json_decode($data, true);
            
            if ($cached && isset($cached['expires'])) {
                if (time() > $cached['expires']) {
                    $expiredFiles++;
                } else {
                    $validFiles++;
                }
            }
        }

        return [
            'enabled' => true,
            'total_files' => count($files),
            'valid_files' => $validFiles,
            'expired_files' => $expiredFiles,
            'total_size' => $totalSize,
            'total_size_mb' => round($totalSize / 1024 / 1024, 2),
            'cache_dir' => $this->cacheDir
        ];
    }

    /**
     * Clean expired cache files
     */
    public function cleanExpired()
    {
        if (!$this->enabled) {
            return 0;
        }

        $files = glob($this->cacheDir . '*.cache');
        $deleted = 0;

        foreach ($files as $file) {
            $data = file_get_contents($file);
            $cached = json_decode($data, true);
            
            if (!$cached || !isset($cached['expires']) || time() > $cached['expires']) {
                if (unlink($file)) {
                    $deleted++;
                }
            }
        }

        return $deleted;
    }

    /**
     * Check if cache key exists and is valid
     */
    public function exists($key)
    {
        return $this->get($key) !== null;
    }

    /**
     * Get or set cache with callback
     */
    public function remember($key, $callback, $ttl = null)
    {
        $data = $this->get($key);
        
        if ($data !== null) {
            return $data;
        }

        // Cache miss, execute callback
        $data = $callback();
        
        if ($data !== null) {
            $this->set($key, $data, $ttl);
        }

        return $data;
    }

    /**
     * Generate cache filename from key
     */
    private function getFilename($key)
    {
        $hash = md5($key);
        return $this->cacheDir . $hash . '.cache';
    }

    /**
     * Generate cache key for API requests
     */
    public static function generateApiKey($endpoint, $params = [])
    {
        ksort($params); // Sort parameters for consistent keys
        $paramString = http_build_query($params);
        return 'api_' . md5($endpoint . '_' . $paramString);
    }

    /**
     * Cache API response with automatic invalidation
     */
    public function cacheApiResponse($endpoint, $params, $callback, $ttl = null)
    {
        $key = self::generateApiKey($endpoint, $params);
        
        return $this->remember($key, $callback, $ttl);
    }

    /**
     * Invalidate cache for specific patterns
     */
    public function invalidatePattern($pattern)
    {
        if (!$this->enabled) {
            return 0;
        }

        $files = glob($this->cacheDir . '*.cache');
        $deleted = 0;

        foreach ($files as $file) {
            $data = file_get_contents($file);
            $cached = json_decode($data, true);
            
            if ($cached && isset($cached['key']) && fnmatch($pattern, $cached['key'])) {
                if (unlink($file)) {
                    $deleted++;
                }
            }
        }

        return $deleted;
    }

    /**
     * Set cache headers for HTTP responses
     */
    public static function setCacheHeaders($maxAge = 3600, $public = true)
    {
        $cacheControl = $public ? 'public' : 'private';
        $cacheControl .= ", max-age=$maxAge";
        
        header("Cache-Control: $cacheControl");
        header("Expires: " . gmdate('D, d M Y H:i:s', time() + $maxAge) . ' GMT');
        header("Last-Modified: " . gmdate('D, d M Y H:i:s') . ' GMT');
        
        // Add ETag for better caching
        $etag = md5($_SERVER['REQUEST_URI'] . filemtime(__FILE__));
        header("ETag: \"$etag\"");
        
        // Check if client has cached version
        if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && $_SERVER['HTTP_IF_NONE_MATCH'] === "\"$etag\"") {
            http_response_code(304);
            exit;
        }
    }

    /**
     * Enable or disable caching
     */
    public function setEnabled($enabled)
    {
        $this->enabled = $enabled;
    }

    /**
     * Check if caching is enabled
     */
    public function isEnabled()
    {
        return $this->enabled;
    }
}

/**
 * Global cache instance
 */
class Cache
{
    private static $instance = null;

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new CacheManager();
        }
        return self::$instance;
    }

    public static function get($key)
    {
        return self::getInstance()->get($key);
    }

    public static function set($key, $data, $ttl = null)
    {
        return self::getInstance()->set($key, $data, $ttl);
    }

    public static function delete($key)
    {
        return self::getInstance()->delete($key);
    }

    public static function clear()
    {
        return self::getInstance()->clear();
    }

    public static function remember($key, $callback, $ttl = null)
    {
        return self::getInstance()->remember($key, $callback, $ttl);
    }
}
?>
