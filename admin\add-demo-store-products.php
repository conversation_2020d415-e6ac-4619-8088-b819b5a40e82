<?php
require_once __DIR__ . '/../php/config.php';

try {
    $pdo = getPDOConnection();
    
    echo "🛍️ ADDING 10 DIVERSE PRODUCTS TO DEMO STORE\n";
    echo "=" . str_repeat("=", 50) . "\n\n";
    
    // Demo store details
    $storeId = 1; // mossaab-store
    $userId = 1;  // <EMAIL>
    
    // 10 diverse products for the demo store
    $products = [
        // Books (2 products)
        [
            'store_id' => $storeId,
            'type' => 'book',
            'titre' => 'كتاب الطبخ الجزائري الأصيل',
            'description' => '<h3>كتاب الطبخ الجزائري الأصيل</h3><p>مجموعة شاملة من الوصفات التقليدية الجزائرية</p><ul><li>📚 أكثر من 200 وصفة تقليدية</li><li>🍽️ أطباق رئيسية وحلويات</li><li>📖 تعليمات مفصلة خطوة بخطوة</li><li>🎨 صور ملونة عالية الجودة</li><li>👩‍🍳 نصائح من الطباخين المحترفين</li></ul>',
            'prix' => 2500.00,
            'stock' => 50,
            'image_url' => 'images/products/cookbook-algerian.jpg',
            'actif' => 1
        ],
        [
            'store_id' => $storeId,
            'type' => 'book',
            'titre' => 'تاريخ الجزائر المعاصر',
            'description' => '<h3>تاريخ الجزائر المعاصر</h3><p>دراسة شاملة لتاريخ الجزائر من الاستقلال حتى اليوم</p><ul><li>📚 تحليل تاريخي معمق</li><li>🏛️ الأحداث السياسية المهمة</li><li>👥 شخصيات تاريخية مؤثرة</li><li>📊 إحصائيات ووثائق</li><li>🎓 مرجع أكاديمي موثوق</li></ul>',
            'prix' => 3200.00,
            'stock' => 30,
            'image_url' => 'images/products/history-algeria.jpg',
            'actif' => 1
        ],
        
        // Electronics (2 products)
        [
            'store_id' => $storeId,
            'type' => 'smartphone',
            'titre' => 'هاتف Samsung Galaxy A54 5G',
            'description' => '<h3>Samsung Galaxy A54 5G</h3><p>هاتف ذكي متطور بتقنية 5G وكاميرا احترافية</p><ul><li>📱 شاشة Super AMOLED 6.4 بوصة</li><li>📸 كاميرا ثلاثية 50MP + 12MP + 5MP</li><li>🔋 بطارية 5000mAh مع شحن سريع</li><li>💾 ذاكرة 128GB قابلة للتوسيع</li><li>🌐 دعم شبكات 5G</li><li>🎨 متوفر بألوان متعددة</li></ul>',
            'prix' => 45000.00,
            'stock' => 15,
            'image_url' => 'images/products/samsung-a54.jpg',
            'actif' => 1
        ],
        [
            'store_id' => $storeId,
            'type' => 'laptop',
            'titre' => 'لابتوب HP Pavilion 15 للطلاب',
            'description' => '<h3>HP Pavilion 15</h3><p>لابتوب مثالي للطلاب والاستخدام اليومي</p><ul><li>💻 معالج Intel Core i5 الجيل 12</li><li>🧠 ذاكرة عشوائية 8GB DDR4</li><li>💾 قرص صلب SSD 512GB</li><li>🖥️ شاشة 15.6 بوصة Full HD</li><li>⚡ بطارية تدوم حتى 8 ساعات</li><li>🎒 تصميم نحيف وخفيف الوزن</li></ul>',
            'prix' => 75000.00,
            'stock' => 8,
            'image_url' => 'images/products/hp-pavilion-15.jpg',
            'actif' => 1
        ],
        
        // Bags (2 products)
        [
            'store_id' => $storeId,
            'type' => 'bag',
            'titre' => 'حقيبة ظهر جلدية فاخرة للرجال',
            'description' => '<h3>حقيبة ظهر جلدية فاخرة</h3><p>حقيبة أنيقة مصنوعة من الجلد الطبيعي عالي الجودة</p><ul><li>👜 جلد طبيعي 100% مقاوم للماء</li><li>💼 مقصورات متعددة منظمة</li><li>💻 جيب مبطن للابتوب حتى 15.6 بوصة</li><li>🔒 سحابات معدنية قوية</li><li>👨‍💼 تصميم عصري وأنيق</li><li>📏 أبعاد: 45×30×15 سم</li></ul>',
            'prix' => 8500.00,
            'stock' => 25,
            'image_url' => 'images/products/leather-backpack-men.jpg',
            'actif' => 1
        ],
        [
            'store_id' => $storeId,
            'type' => 'bag',
            'titre' => 'حقيبة يد نسائية عصرية',
            'description' => '<h3>حقيبة يد نسائية عصرية</h3><p>حقيبة أنيقة تناسب جميع المناسبات</p><ul><li>👛 تصميم عصري وأنيق</li><li>🎨 متوفرة بألوان متعددة</li><li>👜 مقصورات داخلية منظمة</li><li>📱 جيب خاص للهاتف</li><li>✨ إكسسوارات معدنية ذهبية</li><li>🌟 مناسبة للعمل والمناسبات</li></ul>',
            'prix' => 4200.00,
            'stock' => 40,
            'image_url' => 'images/products/women-handbag.jpg',
            'actif' => 1
        ],
        
        // Clothing (2 products)
        [
            'store_id' => $storeId,
            'type' => 'clothing',
            'titre' => 'قندورة رجالية تقليدية جزائرية',
            'description' => '<h3>قندورة رجالية تقليدية</h3><p>قندورة أصيلة مصنوعة من أجود الأقمشة</p><ul><li>👘 تصميم تقليدي أصيل</li><li>🧵 قماش قطني عالي الجودة</li><li>🎨 تطريز يدوي فاخر</li><li>📏 مقاسات متعددة (S-XXL)</li><li>🌙 مناسبة للمناسبات الدينية</li><li>🇩🇿 صناعة جزائرية 100%</li></ul>',
            'prix' => 6800.00,
            'stock' => 20,
            'image_url' => 'images/products/qandoura-men.jpg',
            'actif' => 1
        ],
        [
            'store_id' => $storeId,
            'type' => 'clothing',
            'titre' => 'فستان نسائي أنيق للمناسبات',
            'description' => '<h3>فستان نسائي أنيق</h3><p>فستان عصري مناسب للمناسبات الخاصة</p><ul><li>👗 تصميم عصري وأنيق</li><li>✨ قماش شيفون عالي الجودة</li><li>🎨 ألوان زاهية وجذابة</li><li>📏 مقاسات من S إلى XL</li><li>💃 مناسب للحفلات والمناسبات</li><li>🌟 تفاصيل أنيقة ومميزة</li></ul>',
            'prix' => 5500.00,
            'stock' => 35,
            'image_url' => 'images/products/elegant-dress.jpg',
            'actif' => 1
        ],
        
        // Home items (2 products)
        [
            'store_id' => $storeId,
            'type' => 'home',
            'titre' => 'طقم أواني طبخ من الستانلس ستيل',
            'description' => '<h3>طقم أواني طبخ احترافي</h3><p>طقم كامل من أواني الطبخ عالية الجودة</p><ul><li>🍳 12 قطعة متنوعة الأحجام</li><li>⚡ ستانلس ستيل مقاوم للصدأ</li><li>🔥 مناسب لجميع أنواع المواقد</li><li>🧽 سهل التنظيف والصيانة</li><li>👨‍🍳 تصميم احترافي</li><li>📦 يأتي في علبة هدايا أنيقة</li></ul>',
            'prix' => 12000.00,
            'stock' => 18,
            'image_url' => 'images/products/cookware-set.jpg',
            'actif' => 1
        ],
        [
            'store_id' => $storeId,
            'type' => 'home',
            'titre' => 'مصباح LED ذكي متعدد الألوان',
            'description' => '<h3>مصباح LED ذكي</h3><p>إضاءة ذكية قابلة للتحكم عبر الهاتف</p><ul><li>💡 تقنية LED موفرة للطاقة</li><li>🌈 16 مليون لون مختلف</li><li>📱 تحكم عبر تطبيق الهاتف</li><li>🎵 مزامنة مع الموسيقى</li><li>⏰ مؤقت ذكي قابل للبرمجة</li><li>🏠 مناسب لجميع غرف المنزل</li></ul>',
            'prix' => 3800.00,
            'stock' => 45,
            'image_url' => 'images/products/smart-led-bulb.jpg',
            'actif' => 1
        ]
    ];
    
    echo "📦 Adding products to store ID: {$storeId}\n";
    echo "👤 Store owner: <EMAIL> (User ID: {$userId})\n\n";
    
    $insertStmt = $pdo->prepare("
        INSERT INTO produits (
            store_id, type, titre, description, prix, stock, 
            image_url, actif, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    ");
    
    $successCount = 0;
    
    foreach ($products as $index => $product) {
        try {
            $insertStmt->execute([
                $product['store_id'],
                $product['type'],
                $product['titre'],
                $product['description'],
                $product['prix'],
                $product['stock'],
                $product['image_url'],
                $product['actif']
            ]);
            
            $productId = $pdo->lastInsertId();
            $successCount++;
            
            echo "✅ Product " . ($index + 1) . " added successfully:\n";
            echo "   ID: {$productId}\n";
            echo "   Name: {$product['titre']}\n";
            echo "   Type: {$product['type']}\n";
            echo "   Price: {$product['prix']} DZD\n";
            echo "   Stock: {$product['stock']}\n\n";
            
        } catch (Exception $e) {
            echo "❌ Failed to add product " . ($index + 1) . ": " . $e->getMessage() . "\n\n";
        }
    }
    
    echo "🎉 PRODUCT ADDITION COMPLETE!\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo "✅ Successfully added: {$successCount}/10 products\n";
    echo "🏪 Store: متجر مصعب (mossaab-store)\n";
    echo "🔗 Store URL: http://localhost:8000/store/mossaab-store\n\n";
    
    // Verify products were added
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM produits 
        WHERE store_id = ? AND actif = 1
    ");
    $stmt->execute([$storeId]);
    $newCount = $stmt->fetchColumn();
    
    echo "📊 Verification:\n";
    echo "   Products now in demo store: {$newCount}\n";
    echo "   Ready for testing!\n\n";
    
    echo "🔍 NEXT STEPS:\n";
    echo "1. Visit: http://localhost:8000/store/mossaab-store\n";
    echo "2. Verify all 10 products are displayed\n";
    echo "3. Test product ordering functionality\n";
    echo "4. Proceed with user panel implementation\n";
    
} catch (Exception $e) {
    echo "❌ Critical Error: " . $e->getMessage() . "\n";
}
?>
