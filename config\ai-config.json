{"providers": {"openai": {"enabled": false, "api_key": "", "model": "gpt-4", "max_tokens": 1000, "temperature": 0.7}, "anthropic": {"enabled": false, "api_key": "", "model": "claude-3-sonnet-20240229", "max_tokens": 1000, "temperature": 0.7}, "gemini": {"enabled": false, "api_key": "", "model": "gemini-pro", "max_tokens": 1000, "temperature": 0.7}}, "default_provider": "openai", "language": "ar", "prompts": {"product_description": {"ar": "اكتب وصفاً تسويقياً جذاباً للمنتج التالي باللغة العربية. يجب أن يكون الوصف مقنعاً ومفصلاً ويبرز فوائد المنتج:", "en": "Write an attractive marketing description for the following product in Arabic. The description should be persuasive, detailed, and highlight the product benefits:"}, "landing_page_title": {"ar": "اقترح عنواناً جذاباً لصفحة هبوط للمنتج التالي باللغة العربية:", "en": "Suggest an attractive title for a landing page for the following product in Arabic:"}, "landing_page_content": {"ar": "اكتب محتوى تسويقي مقنع لصفحة هبوط للمنتج التالي باللغة العربية. يجب أن يتضمن عنواناً رئيسياً، وصفاً للمنتج، والفوائد الرئيسية:", "en": "Write persuasive marketing content for a landing page for the following product in Arabic. It should include a main title, product description, and key benefits:"}, "meta_description": {"ar": "اكتب وصف ميتا SEO للمنتج التالي باللغة العربية (أقل من 160 حرف):", "en": "Write an SEO meta description for the following product in Arabic (less than 160 characters):"}}, "features": {"auto_generate_descriptions": true, "auto_generate_titles": true, "auto_generate_meta": true, "suggest_improvements": true, "arabic_optimization": true}}