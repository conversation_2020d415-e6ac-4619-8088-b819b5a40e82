<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Order Form System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.warning {
            background: #ffc107;
            color: #333;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .test-result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        #console-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 14px;
        }
        .product-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .product-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        .product-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-bottom: 15px;
        }
        .product-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }
        .product-price {
            font-size: 1.2em;
            color: #28a745;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .buy-now-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            transition: background 0.3s ease;
        }
        .buy-now-btn:hover {
            background: #218838;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🛒 Test Order Form System</h1>
        <p>اختبار شامل لنظام نموذج الطلب مع التكامل الذكي للشحن</p>

        <!-- Database Tests -->
        <div class="test-section">
            <h3>🗄️ Database & API Tests</h3>
            <button class="test-button" onclick="testDatabaseTables()">
                1. اختبار جداول قاعدة البيانات
            </button>
            <button class="test-button" onclick="testGeographicAPI()">
                2. اختبار API البيانات الجغرافية
            </button>
            <button class="test-button" onclick="testShippingIntegration()">
                3. اختبار تكامل الشحن
            </button>
            <button class="test-button" onclick="testShippingManagement()">
                4. اختبار إدارة الشحن
            </button>
        </div>

        <!-- Order Form Demo -->
        <div class="test-section">
            <h3>🛍️ Order Form Demo</h3>
            <p>اختبر نموذج الطلب مع منتجات تجريبية:</p>
            
            <div class="product-demo">
                <div class="product-card">
                    <div class="product-image">📱 هاتف ذكي</div>
                    <div class="product-title">هاتف ذكي متطور</div>
                    <div class="product-price">45,000 دج</div>
                    <button class="buy-now-btn" onclick="openOrderForm('phone', 'هاتف ذكي متطور', 45000, 'https://via.placeholder.com/300x200?text=Phone')">
                        شراء الآن
                    </button>
                </div>
                
                <div class="product-card">
                    <div class="product-image">💻 لابتوب</div>
                    <div class="product-title">لابتوب للألعاب</div>
                    <div class="product-price">120,000 دج</div>
                    <button class="buy-now-btn" onclick="openOrderForm('laptop', 'لابتوب للألعاب', 120000, 'https://via.placeholder.com/300x200?text=Laptop')">
                        شراء الآن
                    </button>
                </div>
                
                <div class="product-card">
                    <div class="product-image">⌚ ساعة ذكية</div>
                    <div class="product-title">ساعة ذكية رياضية</div>
                    <div class="product-price">15,000 دج</div>
                    <button class="buy-now-btn" onclick="openOrderForm('watch', 'ساعة ذكية رياضية', 15000, 'https://via.placeholder.com/300x200?text=Watch')">
                        شراء الآن
                    </button>
                </div>
            </div>
        </div>

        <!-- Geographic Selection Test -->
        <div class="test-section">
            <h3>🗺️ Geographic Selection Test</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="testWilayaSelect">اختبار اختيار الولاية:</label>
                    <select id="testWilayaSelect">
                        <option value="">-- اختر الولاية --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="testCommuneSelect">اختبار اختيار البلدية:</label>
                    <select id="testCommuneSelect" disabled>
                        <option value="">-- اختر البلدية --</option>
                    </select>
                </div>
            </div>
            
            <button class="test-button" onclick="testCascadingDropdowns()">
                اختبار القوائم المتتالية
            </button>
            <button class="test-button" onclick="testShippingCalculation()">
                اختبار حساب الشحن
            </button>
            
            <div id="shippingTestResult" style="display: none; margin-top: 15px; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                <!-- Shipping calculation results will appear here -->
            </div>
        </div>

        <!-- Admin Management Test -->
        <div class="test-section">
            <h3>⚙️ Admin Management Test</h3>
            <button class="test-button warning" onclick="testCustomRates()">
                اختبار الرسوم المخصصة
            </button>
            <button class="test-button warning" onclick="testZoneManagement()">
                اختبار إدارة المناطق
            </button>
            <button class="test-button warning" onclick="testFreeShipping()">
                اختبار الشحن المجاني
            </button>
            <button class="test-button danger" onclick="openAdminPanel()">
                فتح لوحة الإدارة
            </button>
        </div>

        <!-- Comprehensive Tests -->
        <div class="test-section">
            <h3>🚀 Comprehensive Tests</h3>
            <button class="test-button success" onclick="runAllTests()">
                تشغيل جميع الاختبارات
            </button>
            <button class="test-button" onclick="clearResults()">
                مسح النتائج
            </button>
            <button class="test-button" onclick="generateTestReport()">
                تقرير الاختبار
            </button>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="testsRun">0</div>
                <div class="stat-label">اختبارات تم تشغيلها</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testsPass">0</div>
                <div class="stat-label">اختبارات نجحت</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testsFail">0</div>
                <div class="stat-label">اختبارات فشلت</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="apiCalls">0</div>
                <div class="stat-label">استدعاءات API</div>
            </div>
        </div>

        <!-- Results -->
        <div id="test-results"></div>
        <div id="console-output"></div>
    </div>

    <script src="js/test-order-form.js"></script>
</body>
</html>
