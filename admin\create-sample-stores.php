<?php

/**
 * Create Sample Stores Script
 * Creates sample store data for testing the store management system
 */

require_once '../php/config.php';

try {
    $pdo = getPDOConnection();

    echo "🌱 إنشاء بيانات تجريبية للمتاجر...\n\n";

    // First, check if stores table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'stores'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        echo "❌ جدول المتاجر غير موجود. يرجى تشغيل ترحيل قاعدة البيانات أولاً.\n";
        exit(1);
    }

    // Check if users table exists and has data
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();

    if ($userCount == 0) {
        echo "📝 إنشاء مستخدمين تجريبيين...\n";

        // Create sample users
        $sampleUsers = [
            [
                'username' => 'ahmed_mohamed',
                'first_name' => 'أحمد',
                'last_name' => 'محمد',
                'email' => '<EMAIL>',
                'phone' => '0555123456',
                'password' => password_hash('123456', PASSWORD_DEFAULT)
            ],
            [
                'username' => 'fatima_ali',
                'first_name' => 'فاطمة',
                'last_name' => 'علي',
                'email' => '<EMAIL>',
                'phone' => '0666789012',
                'password' => password_hash('123456', PASSWORD_DEFAULT)
            ],
            [
                'username' => 'mohamed_amine',
                'first_name' => 'محمد',
                'last_name' => 'الأمين',
                'email' => '<EMAIL>',
                'phone' => '0777345678',
                'password' => password_hash('123456', PASSWORD_DEFAULT)
            ]
        ];

        foreach ($sampleUsers as $user) {
            $stmt = $pdo->prepare("
                INSERT INTO users (username, first_name, last_name, email, phone, password, role_id, subscription_id, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $user['username'],
                $user['first_name'],
                $user['last_name'],
                $user['email'],
                $user['phone'],
                $user['password'],
                4, // customer role
                1, // basic subscription
                'active'
            ]);
            echo "✅ تم إنشاء المستخدم: {$user['first_name']} {$user['last_name']}\n";
        }
    }

    // Get user IDs
    $stmt = $pdo->query("SELECT id, CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')) as full_name FROM users LIMIT 10");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($users)) {
        echo "❌ لا توجد مستخدمين في قاعدة البيانات.\n";
        exit(1);
    }

    echo "\n🏪 إنشاء متاجر تجريبية...\n";

    // Sample stores data
    $sampleStores = [
        [
            'store_name' => 'متجر الكتب الإلكترونية',
            'store_slug' => 'ebooks-store',
            'description' => 'متجر متخصص في بيع الكتب الإلكترونية والمواد التعليمية',
            'status' => 'active',
            'theme' => 'modern',
            'settings' => [
                'currency' => 'DZD',
                'language' => 'ar',
                'timezone' => 'Africa/Algiers',
                'theme_color' => '#667eea',
                'allow_reviews' => true
            ]
        ],
        [
            'store_name' => 'متجر الإلكترونيات',
            'store_slug' => 'electronics-store',
            'description' => 'أحدث الأجهزة الإلكترونية والهواتف الذكية',
            'status' => 'active',
            'theme' => 'tech',
            'settings' => [
                'currency' => 'DZD',
                'language' => 'ar',
                'timezone' => 'Africa/Algiers',
                'theme_color' => '#4f46e5',
                'allow_reviews' => true
            ]
        ],
        [
            'store_name' => 'متجر الأزياء النسائية',
            'store_slug' => 'fashion-store',
            'description' => 'أجمل الأزياء والإكسسوارات النسائية',
            'status' => 'pending',
            'theme' => 'fashion',
            'settings' => [
                'currency' => 'DZD',
                'language' => 'ar',
                'timezone' => 'Africa/Algiers',
                'theme_color' => '#ec4899',
                'allow_reviews' => true
            ]
        ],
        [
            'store_name' => 'متجر المنتجات الطبيعية',
            'store_slug' => 'natural-products',
            'description' => 'منتجات طبيعية وعضوية للصحة والجمال',
            'status' => 'suspended',
            'theme' => 'natural',
            'settings' => [
                'currency' => 'DZD',
                'language' => 'ar',
                'timezone' => 'Africa/Algiers',
                'theme_color' => '#10b981',
                'allow_reviews' => true
            ]
        ],
        [
            'store_name' => 'متجر الرياضة واللياقة',
            'store_slug' => 'sports-store',
            'description' => 'معدات رياضية ومكملات غذائية للرياضيين',
            'status' => 'active',
            'theme' => 'sports',
            'settings' => [
                'currency' => 'DZD',
                'language' => 'ar',
                'timezone' => 'Africa/Algiers',
                'theme_color' => '#f59e0b',
                'allow_reviews' => true
            ]
        ]
    ];

    // Clear existing sample stores
    $stmt = $pdo->prepare("DELETE FROM stores WHERE store_slug IN (?, ?, ?, ?, ?)");
    $stmt->execute(['ebooks-store', 'electronics-store', 'fashion-store', 'natural-products', 'sports-store']);

    // Create sample stores
    foreach ($sampleStores as $index => $store) {
        $userId = $users[$index % count($users)]['id'];
        $userName = $users[$index % count($users)]['full_name'];

        $stmt = $pdo->prepare("
            INSERT INTO stores (
                user_id, store_name, store_slug, description, status, theme, settings,
                total_products, total_orders, total_revenue, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");

        $stmt->execute([
            $userId,
            $store['store_name'],
            $store['store_slug'],
            $store['description'],
            $store['status'],
            $store['theme'],
            json_encode($store['settings']),
            rand(5, 50), // total_products
            rand(0, 100), // total_orders
            rand(1000, 50000) / 100 // total_revenue
        ]);

        echo "✅ تم إنشاء المتجر: {$store['store_name']} (المالك: {$userName})\n";
    }

    // Get final count
    $stmt = $pdo->query("SELECT COUNT(*) FROM stores");
    $storeCount = $stmt->fetchColumn();

    echo "\n📊 إحصائيات البيانات التجريبية:\n";
    echo "👥 عدد المستخدمين: " . count($users) . "\n";
    echo "🏪 عدد المتاجر: {$storeCount}\n";

    // Show stores by status
    $stmt = $pdo->query("
        SELECT status, COUNT(*) as count
        FROM stores
        GROUP BY status
    ");
    $statusCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "\n📈 المتاجر حسب الحالة:\n";
    foreach ($statusCounts as $status) {
        echo "   {$status['status']}: {$status['count']}\n";
    }

    echo "\n🎉 تم إنشاء البيانات التجريبية بنجاح!\n";
    echo "\n💡 يمكنك الآن اختبار نظام إدارة المتاجر في لوحة التحكم.\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء البيانات التجريبية: " . $e->getMessage() . "\n";
    echo "\n💡 تأكد من:\n";
    echo "   - وجود جدول المتاجر (تشغيل الترحيل)\n";
    echo "   - صحة الاتصال بقاعدة البيانات\n";
    echo "   - صلاحيات الكتابة في قاعدة البيانات\n";
}
