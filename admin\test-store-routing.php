<?php
require_once __DIR__ . '/../php/config.php';

echo "🔍 TESTING STORE ROUTING ISSUES\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    $pdo = getPDOConnection();
    
    // Test 1: Check if store.php file exists and is accessible
    echo "📁 Test 1: Store File Accessibility\n";
    echo "-" . str_repeat("-", 30) . "\n";
    
    if (file_exists('../store.php')) {
        echo "✅ store.php file exists\n";
        
        // Test direct access to store.php
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/store.php?store=mossaab-store');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "📊 Direct store.php access: HTTP {$httpCode}\n";
        
        if ($httpCode === 200) {
            if (strpos($response, 'متجر مصعب') !== false) {
                echo "✅ Store page content: CORRECT (contains store name)\n";
            } else {
                echo "❌ Store page content: INCORRECT (missing store name)\n";
                echo "   Response preview: " . substr(strip_tags($response), 0, 100) . "...\n";
            }
        } else {
            echo "❌ Store page not accessible\n";
        }
    } else {
        echo "❌ store.php file not found\n";
    }
    echo "\n";
    
    // Test 2: Check URL rewriting
    echo "🔄 Test 2: URL Rewriting\n";
    echo "-" . str_repeat("-", 20) . "\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/store/mossaab-store');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    
    $rewriteResponse = curl_exec($ch);
    $rewriteHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "📊 URL rewrite access: HTTP {$rewriteHttpCode}\n";
    
    if ($rewriteHttpCode === 200) {
        if (strpos($rewriteResponse, 'متجر مصعب') !== false) {
            echo "✅ URL rewriting: WORKING (contains store name)\n";
        } else {
            echo "❌ URL rewriting: NOT WORKING (wrong content)\n";
            
            // Check what content is being served
            if (strpos($rewriteResponse, 'متجر الكتب') !== false) {
                echo "   ⚠️ Serving main landing page instead of store page\n";
            } else {
                echo "   ⚠️ Serving unknown content\n";
            }
            
            echo "   Response preview: " . substr(strip_tags($rewriteResponse), 0, 150) . "...\n";
        }
    } else {
        echo "❌ URL rewriting not working (HTTP {$rewriteHttpCode})\n";
    }
    echo "\n";
    
    // Test 3: Check .htaccess rules
    echo "📋 Test 3: .htaccess Configuration\n";
    echo "-" . str_repeat("-", 30) . "\n";
    
    if (file_exists('../.htaccess')) {
        echo "✅ .htaccess file exists\n";
        
        $htaccessContent = file_get_contents('../.htaccess');
        if (strpos($htaccessContent, 'store/([a-zA-Z0-9\-]+)') !== false) {
            echo "✅ Store rewrite rule found in .htaccess\n";
        } else {
            echo "❌ Store rewrite rule missing from .htaccess\n";
        }
        
        if (strpos($htaccessContent, 'RewriteEngine On') !== false) {
            echo "✅ RewriteEngine is enabled\n";
        } else {
            echo "❌ RewriteEngine not enabled\n";
        }
    } else {
        echo "❌ .htaccess file not found\n";
    }
    echo "\n";
    
    // Test 4: Check demo store in database
    echo "🗄️ Test 4: Demo Store Database\n";
    echo "-" . str_repeat("-", 30) . "\n";
    
    $stmt = $pdo->prepare("SELECT * FROM stores WHERE store_slug = 'mossaab-store'");
    $stmt->execute();
    $store = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($store) {
        echo "✅ Demo store found in database\n";
        echo "   Store ID: {$store['id']}\n";
        echo "   Store Name: {$store['store_name']}\n";
        echo "   Store Slug: {$store['store_slug']}\n";
        echo "   Status: {$store['status']}\n";
    } else {
        echo "❌ Demo store not found in database\n";
    }
    echo "\n";
    
    // Test 5: Check store products
    echo "📦 Test 5: Store Products\n";
    echo "-" . str_repeat("-", 20) . "\n";
    
    if ($store) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM produits 
            WHERE (store_id = ? OR store_id IS NULL) AND actif = 1
        ");
        $stmt->execute([$store['id']]);
        $productCount = $stmt->fetchColumn();
        
        echo "✅ Products available to store: {$productCount}\n";
        
        // Get sample products
        $stmt = $pdo->prepare("
            SELECT titre, type, prix 
            FROM produits 
            WHERE store_id = ? AND actif = 1 
            LIMIT 3
        ");
        $stmt->execute([$store['id']]);
        $sampleProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($sampleProducts)) {
            echo "📋 Sample store products:\n";
            foreach ($sampleProducts as $product) {
                echo "   • {$product['titre']} ({$product['type']}) - {$product['prix']} DZD\n";
            }
        }
    }
    echo "\n";
    
    // Summary and recommendations
    echo "🎯 DIAGNOSIS SUMMARY\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    if ($rewriteHttpCode === 200 && strpos($rewriteResponse, 'متجر مصعب') !== false) {
        echo "✅ Store routing is working correctly!\n";
    } else {
        echo "❌ Store routing issues detected:\n";
        
        if (!file_exists('../store.php')) {
            echo "   • store.php file missing\n";
        }
        
        if ($rewriteHttpCode !== 200) {
            echo "   • URL rewriting not working (HTTP {$rewriteHttpCode})\n";
        }
        
        if ($rewriteHttpCode === 200 && strpos($rewriteResponse, 'متجر الكتب') !== false) {
            echo "   • .htaccess rules not being applied (serving main page)\n";
        }
        
        if (!$store) {
            echo "   • Demo store missing from database\n";
        }
        
        echo "\n📋 RECOMMENDED FIXES:\n";
        echo "   1. Check Apache mod_rewrite is enabled\n";
        echo "   2. Verify .htaccess file permissions\n";
        echo "   3. Check store.php file exists and is readable\n";
        echo "   4. Ensure demo store exists in database\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
