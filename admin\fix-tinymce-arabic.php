<?php
/**
 * Fix TinyMCE Arabic Language Loading Issues
 * Addresses language file loading, MIME types, and configuration
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل TinyMCE العربية</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .fix-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .fix-button:hover {
            background: #0056b3;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 إصلاح مشاكل TinyMCE العربية</h1>
        <p>هذا السكريبت يحل مشاكل تحميل ملف اللغة العربية في TinyMCE ويضمن عمل الواجهة العربية بشكل صحيح.</p>

        <?php
        $allIssuesFixed = true;
        $fixedIssues = [];
        $remainingIssues = [];

        // Fix 1: Check Arabic Language File
        echo '<div class="fix-section">';
        echo '<h3>📁 إصلاح 1: فحص ملف اللغة العربية</h3>';
        
        $langFile = 'js/langs/ar.js';
        $langDir = 'js/langs';
        
        // Create langs directory if it doesn't exist
        if (!is_dir($langDir)) {
            if (mkdir($langDir, 0755, true)) {
                echo '<div class="result pass">✅ تم إنشاء مجلد js/langs</div>';
            } else {
                echo '<div class="result fail">❌ فشل في إنشاء مجلد js/langs</div>';
                $allIssuesFixed = false;
            }
        } else {
            echo '<div class="result pass">✅ مجلد js/langs موجود</div>';
        }
        
        if (file_exists($langFile)) {
            echo '<div class="result pass">✅ ملف اللغة العربية موجود</div>';
            
            // Check file size
            $fileSize = filesize($langFile);
            echo '<div class="result info">📊 حجم الملف: ' . $fileSize . ' بايت</div>';
            
            // Check file permissions
            if (is_readable($langFile)) {
                echo '<div class="result pass">✅ الملف قابل للقراءة</div>';
            } else {
                echo '<div class="result fail">❌ الملف غير قابل للقراءة</div>';
                $remainingIssues[] = 'صلاحيات ملف اللغة';
                $allIssuesFixed = false;
            }
            
            // Check file content
            $content = file_get_contents($langFile);
            if (strpos($content, "tinymce.addI18n('ar'") !== false) {
                echo '<div class="result pass">✅ محتوى الملف صحيح</div>';
                $fixedIssues[] = 'ملف اللغة العربية صحيح';
                
                // Count translations
                $translations = substr_count($content, ':');
                echo '<div class="result info">📊 عدد الترجمات: ' . $translations . '</div>';
            } else {
                echo '<div class="result fail">❌ محتوى الملف غير صحيح</div>';
                $remainingIssues[] = 'محتوى ملف اللغة خاطئ';
                $allIssuesFixed = false;
            }
            
        } else {
            echo '<div class="result fail">❌ ملف اللغة العربية غير موجود</div>';
            $remainingIssues[] = 'ملف اللغة العربية مفقود';
            $allIssuesFixed = false;
        }
        echo '</div>';

        // Fix 2: Create .htaccess for proper MIME types
        echo '<div class="fix-section">';
        echo '<h3>⚙️ إصلاح 2: إعداد MIME Types</h3>';
        
        $htaccessFile = '.htaccess';
        $htaccessContent = '# MIME Types for JavaScript files
AddType application/javascript .js
AddType text/javascript .js

# Enable compression for JavaScript files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE text/javascript
</IfModule>

# Cache control for JavaScript files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
';
        
        if (!file_exists($htaccessFile)) {
            if (file_put_contents($htaccessFile, $htaccessContent)) {
                echo '<div class="result pass">✅ تم إنشاء ملف .htaccess</div>';
                $fixedIssues[] = 'إعداد MIME types';
            } else {
                echo '<div class="result fail">❌ فشل في إنشاء ملف .htaccess</div>';
                $remainingIssues[] = 'إعداد MIME types';
                $allIssuesFixed = false;
            }
        } else {
            echo '<div class="result pass">✅ ملف .htaccess موجود</div>';
            
            $existingContent = file_get_contents($htaccessFile);
            if (strpos($existingContent, 'AddType application/javascript .js') === false) {
                // Append MIME type configuration
                if (file_put_contents($htaccessFile, "\n" . $htaccessContent, FILE_APPEND)) {
                    echo '<div class="result pass">✅ تم تحديث ملف .htaccess</div>';
                    $fixedIssues[] = 'تحديث MIME types';
                } else {
                    echo '<div class="result fail">❌ فشل في تحديث ملف .htaccess</div>';
                    $remainingIssues[] = 'تحديث MIME types';
                    $allIssuesFixed = false;
                }
            } else {
                echo '<div class="result pass">✅ إعدادات MIME types موجودة</div>';
            }
        }
        echo '</div>';

        // Fix 3: Check TinyMCE Configuration
        echo '<div class="fix-section">';
        echo '<h3>🔧 إصلاح 3: فحص تكوين TinyMCE</h3>';
        
        $tinymceConfigFile = 'js/tinymce-config.js';
        if (file_exists($tinymceConfigFile)) {
            echo '<div class="result pass">✅ ملف تكوين TinyMCE موجود</div>';
            
            $configContent = file_get_contents($tinymceConfigFile);
            
            // Check for Arabic language configuration
            if (strpos($configContent, "language: 'ar'") !== false) {
                echo '<div class="result pass">✅ تكوين اللغة العربية موجود</div>';
                $fixedIssues[] = 'تكوين TinyMCE للعربية';
            } else {
                echo '<div class="result warning">⚠️ تكوين اللغة العربية قد يحتاج تحديث</div>';
            }
            
            // Check for RTL configuration
            if (strpos($configContent, "directionality: 'rtl'") !== false) {
                echo '<div class="result pass">✅ تكوين RTL موجود</div>';
            } else {
                echo '<div class="result warning">⚠️ تكوين RTL قد يحتاج تحديث</div>';
            }
            
        } else {
            echo '<div class="result fail">❌ ملف تكوين TinyMCE غير موجود</div>';
            $remainingIssues[] = 'ملف تكوين TinyMCE مفقود';
            $allIssuesFixed = false;
        }
        echo '</div>';

        // Fix 4: Create Enhanced TinyMCE Configuration
        echo '<div class="fix-section">';
        echo '<h3>⚡ إصلاح 4: إنشاء تكوين TinyMCE محسن</h3>';
        
        $enhancedConfigFile = 'js/tinymce-arabic-config.js';
        $enhancedConfigContent = '/**
 * Enhanced TinyMCE Configuration for Arabic RTL Support
 * Fixes language loading and RTL issues
 */

// Enhanced TinyMCE initialization with better error handling
function initTinyMCEArabic(selector, options = {}) {
    const defaultConfig = {
        selector: selector,
        language: "ar",
        directionality: "rtl",
        plugins: "advlist autolink lists link image charmap print preview anchor searchreplace visualblocks code fullscreen insertdatetime media table paste code help wordcount",
        toolbar: "undo redo | formatselect | bold italic backcolor | alignright aligncenter alignleft alignjustify | bullist numlist outdent indent | removeformat | help",
        content_style: "body { font-family: \'Noto Sans Arabic\', sans-serif; direction: rtl; text-align: right; }",
        height: 400,
        menubar: false,
        branding: false,
        promotion: false,
        
        // Enhanced language loading with fallback
        language_url: "/admin/js/langs/ar.js",
        
        // Better error handling
        init_instance_callback: function (editor) {
            console.log("TinyMCE initialized successfully for:", editor.id);
        },
        
        setup: function (editor) {
            editor.on("init", function () {
                console.log("TinyMCE editor ready:", editor.id);
                
                // Ensure RTL direction
                editor.getBody().style.direction = "rtl";
                editor.getBody().style.textAlign = "right";
            });
            
            editor.on("LoadContent", function () {
                console.log("Content loaded in TinyMCE:", editor.id);
            });
        },
        
        // Fallback for language loading errors
        language_load_error_callback: function() {
            console.warn("Failed to load Arabic language file, using default");
            // Continue with English if Arabic fails
            return true;
        }
    };
    
    // Merge with custom options
    const config = Object.assign(defaultConfig, options);
    
    // Initialize TinyMCE with error handling
    try {
        tinymce.init(config);
        console.log("TinyMCE initialization started for:", selector);
    } catch (error) {
        console.error("TinyMCE initialization error:", error);
        
        // Fallback initialization without language
        const fallbackConfig = Object.assign({}, config);
        delete fallbackConfig.language;
        delete fallbackConfig.language_url;
        
        try {
            tinymce.init(fallbackConfig);
            console.log("TinyMCE fallback initialization successful");
        } catch (fallbackError) {
            console.error("TinyMCE fallback initialization failed:", fallbackError);
        }
    }
}

// Test Arabic language file loading
function testArabicLanguageFile() {
    return fetch("/admin/js/langs/ar.js")
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(content => {
            if (content.includes("tinymce.addI18n(\'ar\'")) {
                console.log("✅ Arabic language file loaded successfully");
                return true;
            } else {
                throw new Error("Invalid Arabic language file format");
            }
        })
        .catch(error => {
            console.error("❌ Arabic language file loading failed:", error);
            return false;
        });
}

// Auto-test language file when script loads
document.addEventListener("DOMContentLoaded", function() {
    testArabicLanguageFile().then(success => {
        if (success) {
            console.log("Arabic language file is ready for TinyMCE");
        } else {
            console.warn("Arabic language file has issues - TinyMCE will use fallback");
        }
    });
});
';
        
        if (file_put_contents($enhancedConfigFile, $enhancedConfigContent)) {
            echo '<div class="result pass">✅ تم إنشاء تكوين TinyMCE محسن</div>';
            $fixedIssues[] = 'تكوين TinyMCE محسن';
        } else {
            echo '<div class="result fail">❌ فشل في إنشاء تكوين TinyMCE محسن</div>';
            $remainingIssues[] = 'تكوين TinyMCE محسن';
            $allIssuesFixed = false;
        }
        
        echo '<div class="result info">💡 استخدم الدالة initTinyMCEArabic() بدلاً من tinymce.init() للحصول على دعم أفضل للعربية</div>';
        echo '</div>';

        // Fix 5: Test Language File Loading
        echo '<div class="fix-section">';
        echo '<h3>🧪 إصلاح 5: اختبار تحميل ملف اللغة</h3>';
        
        echo '<div id="languageTestResult" class="result info">جاري اختبار تحميل ملف اللغة...</div>';
        echo '</div>';

        // Summary
        echo '<div class="fix-section">';
        echo '<h3>📊 ملخص الإصلاحات</h3>';
        
        if ($allIssuesFixed) {
            echo '<div class="result pass">🎉 تم إصلاح جميع مشاكل TinyMCE العربية!</div>';
            echo '<div class="result pass">✅ TinyMCE جاهز للاستخدام مع دعم كامل للعربية</div>';
        } else {
            echo '<div class="result warning">⚠️ تم إصلاح معظم المشاكل، بعض المشاكل تحتاج تدخل يدوي</div>';
        }
        
        if (!empty($fixedIssues)) {
            echo '<h4>✅ المشاكل المُصلحة:</h4>';
            echo '<ul>';
            foreach ($fixedIssues as $issue) {
                echo '<li>' . $issue . '</li>';
            }
            echo '</ul>';
        }
        
        if (!empty($remainingIssues)) {
            echo '<h4>⚠️ المشاكل المتبقية:</h4>';
            echo '<ul>';
            foreach ($remainingIssues as $issue) {
                echo '<li>' . $issue . '</li>';
            }
            echo '</ul>';
        }
        
        echo '<h4>📋 تعليمات الاستخدام:</h4>';
        echo '<div class="code-block">';
        echo '// استخدم هذا الكود في صفحاتك:<br>';
        echo '&lt;script src="js/tinymce-arabic-config.js"&gt;&lt;/script&gt;<br>';
        echo '&lt;script&gt;<br>';
        echo '&nbsp;&nbsp;initTinyMCEArabic("#myTextarea");<br>';
        echo '&lt;/script&gt;';
        echo '</div>';
        
        echo '<h4>🧪 اختبار الوظائف:</h4>';
        echo '<p><a href="landing-pages-management.html" class="fix-button">اختبار TinyMCE في إدارة الصفحات</a></p>';
        echo '<p><a href="fix-authentication-errors.php" class="fix-button">إصلاح أخطاء المصادقة</a></p>';
        echo '<p><a href="test-all-fixes.php" class="fix-button">اختبار شامل</a></p>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        // Test Arabic language file loading via JavaScript
        async function testLanguageFileLoading() {
            const resultDiv = document.getElementById('languageTestResult');
            
            try {
                console.log('Testing Arabic language file loading...');
                const response = await fetch('js/langs/ar.js');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const content = await response.text();
                console.log('Language file content length:', content.length);
                
                if (content.includes("tinymce.addI18n('ar'")) {
                    resultDiv.className = 'result pass';
                    resultDiv.innerHTML = '✅ JavaScript: ملف اللغة العربية يتم تحميله بنجاح';
                    console.log('✅ Arabic language file loaded successfully');
                } else {
                    resultDiv.className = 'result fail';
                    resultDiv.innerHTML = '❌ JavaScript: ملف اللغة العربية تنسيق خاطئ';
                    console.error('❌ Invalid Arabic language file format');
                }
                
            } catch (error) {
                resultDiv.className = 'result fail';
                resultDiv.innerHTML = '❌ JavaScript: فشل في تحميل ملف اللغة - ' + error.message;
                console.error('❌ Language file loading failed:', error);
            }
        }
        
        // Test TinyMCE availability
        function testTinyMCEAvailability() {
            if (typeof tinymce !== 'undefined') {
                console.log('✅ TinyMCE is available');
                
                // Test Arabic language loading
                if (tinymce.util && tinymce.util.I18n) {
                    console.log('✅ TinyMCE I18n is available');
                } else {
                    console.warn('⚠️ TinyMCE I18n not available');
                }
            } else {
                console.warn('⚠️ TinyMCE is not loaded');
            }
        }
        
        // Run tests after page loads
        document.addEventListener('DOMContentLoaded', function() {
            testLanguageFileLoading();
            testTinyMCEAvailability();
        });
    </script>
</body>
</html>
