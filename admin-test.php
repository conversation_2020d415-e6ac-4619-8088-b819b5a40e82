<?php
/**
 * Admin Panel Asset and API Test
 * Verifies that all admin panel assets load correctly and APIs are functional
 */

// Define security check constant BEFORE including security.php
define('SECURITY_CHECK', true);

require_once 'php/config.php';

echo "<h1>Admin Panel Test Results</h1>";

// Test 1: Admin Panel Asset Files
echo "<h2>1. Admin Panel Asset Files Test</h2>";

$adminAssets = [
    'CSS Files' => [
        'admin/css/admin.css',
        'admin/css/landing-pages.css',
        'admin/css/login.css',
        'admin/css/product-landing.css'
    ],
    'JavaScript Files' => [
        'admin/js/admin.js',
        'admin/js/landing-pages.js',
        'admin/js/categories-management.js',
        'admin/js/payment-settings.js',
        'admin/js/product-landing.js',
        'admin/js/products.js'
    ],
    'HTML Files' => [
        'admin/index.html',
        'admin/login.html',
        'admin/categories_management.html',
        'admin/payment_settings.html'
    ]
];

foreach ($adminAssets as $category => $files) {
    echo "<h3>$category:</h3>";
    foreach ($files as $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            echo "✅ $file - Size: " . number_format($size) . " bytes<br>";
        } else {
            echo "❌ $file - File not found<br>";
        }
    }
}

// Test 2: API Endpoints Functionality
echo "<h2>2. API Endpoints Test</h2>";

$apiTests = [
    'Landing Pages API' => 'php/api/landing-pages.php',
    'Products API' => 'php/api/products.php',
    'Categories API' => 'php/api/categories.php',
    'Templates API' => 'php/api/templates.php'
];

foreach ($apiTests as $name => $endpoint) {
    echo "<h3>$name:</h3>";
    
    if (file_exists($endpoint)) {
        echo "✅ File exists<br>";
        
        // Check for security constant placement
        $content = file_get_contents($endpoint);
        $definePos = strpos($content, "define('SECURITY_CHECK', true);");
        $includePos = strpos($content, "require_once '../security.php';");
        
        if ($definePos !== false && $includePos !== false && $definePos < $includePos) {
            echo "✅ Security check properly ordered<br>";
        } elseif ($definePos !== false) {
            echo "✅ Security check found<br>";
        } else {
            echo "⚠️ No security check found (may be normal for some APIs)<br>";
        }
        
        // Test URL accessibility
        echo "🔗 Test URL: <a href='$endpoint' target='_blank'>$endpoint</a><br>";
    } else {
        echo "❌ File not found<br>";
    }
}

// Test 3: Database Connection for Admin Functions
echo "<h2>3. Database Tables Test</h2>";

$adminTables = [
    'produits' => 'Products table',
    'landing_pages' => 'Landing pages table',
    'landing_page_images' => 'Landing page images table'
];

foreach ($adminTables as $table => $description) {
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM $table");
        $stmt->execute();
        $result = $stmt->fetch();
        echo "✅ $description - {$result['count']} records<br>";
    } catch (Exception $e) {
        echo "❌ $description - Error: " . $e->getMessage() . "<br>";
    }
}

// Test 4: Admin Panel URLs
echo "<h2>4. Admin Panel Access URLs</h2>";
echo "<p>Test these URLs in your browser:</p>";
echo "<ul>";
echo "<li><a href='admin/index.html' target='_blank'>Main Admin Panel</a></li>";
echo "<li><a href='admin/login.html' target='_blank'>Admin Login</a></li>";
echo "<li><a href='admin/categories_management.html' target='_blank'>Categories Management</a></li>";
echo "<li><a href='admin/payment_settings.html' target='_blank'>Payment Settings</a></li>";
echo "</ul>";

// Test 5: JavaScript Console Test
echo "<h2>5. JavaScript Console Test</h2>";
echo "<p>Open browser developer tools (F12) and check for:</p>";
echo "<ul>";
echo "<li>✅ No 404 errors for CSS/JS files</li>";
echo "<li>✅ No 'Direct access not allowed' errors in console</li>";
echo "<li>✅ Landing pages load successfully</li>";
echo "<li>✅ API calls return JSON data</li>";
echo "</ul>";

// Test 6: MIME Type Configuration
echo "<h2>6. MIME Type Test</h2>";
echo "<p>The .htaccess file should be configured for proper MIME types:</p>";

if (file_exists('.htaccess')) {
    $htaccess = file_get_contents('.htaccess');
    if (strpos($htaccess, 'AddType application/javascript .js') !== false) {
        echo "✅ JavaScript MIME type configured<br>";
    } else {
        echo "⚠️ JavaScript MIME type not found in .htaccess<br>";
    }
    
    if (strpos($htaccess, 'AddType text/css .css') !== false) {
        echo "✅ CSS MIME type configured<br>";
    } else {
        echo "⚠️ CSS MIME type not found in .htaccess<br>";
    }
} else {
    echo "⚠️ .htaccess file not found<br>";
}

echo "<h2>7. Next Steps</h2>";
echo "<ol>";
echo "<li>Open the <a href='admin/index.html' target='_blank'>Admin Panel</a></li>";
echo "<li>Check browser console (F12) for any errors</li>";
echo "<li>Test creating a new landing page</li>";
echo "<li>Verify that API calls work without 'Direct access not allowed' errors</li>";
echo "</ol>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
h1 { border-bottom: 3px solid #007cba; padding-bottom: 10px; }
h2 { border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
ul, ol { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
.success { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
</style>
