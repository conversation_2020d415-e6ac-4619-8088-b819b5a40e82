<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Landing Page System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            direction: rtl;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; font-weight: bold; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
        }
        button:hover { background: #005a8b; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .log {
            background: #fff;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background: #e8f4fd;
            border-left: 4px solid #007cba;
            border-radius: 4px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: right;
        }
        th {
            background: #f5f5f5;
            font-weight: bold;
        }
        .status-ok { color: green; }
        .status-error { color: red; }
        .status-warning { color: orange; }
    </style>
</head>
<body>
    <h1>🧪 Complete Landing Page System Test</h1>
    <p>This comprehensive test verifies that the landing page routing system is working correctly after the fixes.</p>

    <div class="test-section">
        <h2>1. System Status Check</h2>
        <button onclick="checkSystemStatus()">Check System Status</button>
        <div id="systemStatus" class="log"></div>
    </div>

    <div class="test-section">
        <h2>2. API Functionality Test</h2>
        <button onclick="testAPIFunctionality()">Test Landing Pages API</button>
        <div id="apiResults" class="log"></div>
    </div>

    <div class="test-section">
        <h2>3. URL Routing Test</h2>
        <button onclick="testURLRouting()">Test URL Routing</button>
        <div id="routingResults" class="log"></div>
    </div>

    <div class="test-section">
        <h2>4. Create Test Landing Page</h2>
        <div class="step">
            <p><strong>Step 1:</strong> Create a test landing page through the API</p>
            <button onclick="createTestLandingPage()">Create Test Landing Page</button>
        </div>
        <div class="step">
            <p><strong>Step 2:</strong> Test the generated URL</p>
            <button onclick="testGeneratedURL()" id="testUrlBtn" disabled>Test Generated URL</button>
        </div>
        <div id="createResults" class="log"></div>
    </div>

    <div class="test-section">
        <h2>5. Admin Panel Integration Test</h2>
        <p>Test the admin panel functionality:</p>
        <ul>
            <li><a href="admin/index.html" target="_blank">Open Admin Panel</a></li>
            <li>Navigate to Landing Pages section</li>
            <li>Verify that "View" links work correctly</li>
            <li>Test creating, editing, and cloning landing pages</li>
        </ul>
        <div class="step">
            <p><strong>Expected Results:</strong></p>
            <ul>
                <li>✅ Landing pages list loads without errors</li>
                <li>✅ "View" buttons open landing pages correctly</li>
                <li>✅ URLs are in format: /landing-page-template.php?id=123</li>
                <li>✅ No "Direct access not allowed" errors</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>6. Test Results Summary</h2>
        <div id="testSummary" class="log">
            <p>Run the tests above to see the summary here.</p>
        </div>
    </div>

    <script>
        let testResults = {
            systemStatus: false,
            apiTest: false,
            routingTest: false,
            createTest: false,
            generatedUrl: null
        };

        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'status-error' : type === 'success' ? 'status-ok' : type === 'warning' ? 'status-warning' : '';
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        async function safeApiCall(url, options = {}) {
            try {
                console.log('🔄 API Call:', url);
                const response = await fetch(url, options);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const text = await response.text();
                
                if (!text.trim()) {
                    return { success: false, message: 'Empty response' };
                }

                try {
                    return JSON.parse(text);
                } catch (parseError) {
                    console.error('JSON Parse Error:', parseError);
                    console.error('Response text:', text);
                    throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                }
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        async function checkSystemStatus() {
            clearLog('systemStatus');
            logResult('systemStatus', 'Checking system status...', 'info');

            try {
                // Test database connection
                const dbTest = await safeApiCall('php/api/products.php');
                if (dbTest.success || Array.isArray(dbTest)) {
                    logResult('systemStatus', '✅ Database connection working', 'success');
                } else {
                    logResult('systemStatus', '❌ Database connection failed', 'error');
                }

                // Test landing pages API
                const lpTest = await safeApiCall('php/api/landing-pages.php');
                if (lpTest.success || Array.isArray(lpTest)) {
                    logResult('systemStatus', '✅ Landing Pages API working', 'success');
                    testResults.systemStatus = true;
                } else {
                    logResult('systemStatus', '❌ Landing Pages API failed', 'error');
                }

                // Test template file
                const templateTest = await fetch('landing-page-template.php?id=1');
                if (templateTest.status === 200 || templateTest.status === 404) {
                    logResult('systemStatus', '✅ Landing page template accessible', 'success');
                } else {
                    logResult('systemStatus', '❌ Landing page template not accessible', 'error');
                }

                updateTestSummary();

            } catch (error) {
                logResult('systemStatus', `❌ System check failed: ${error.message}`, 'error');
            }
        }

        async function testAPIFunctionality() {
            clearLog('apiResults');
            logResult('apiResults', 'Testing API functionality...', 'info');

            try {
                // Test GET request
                const getResponse = await safeApiCall('php/api/landing-pages.php');
                logResult('apiResults', '✅ GET request successful', 'success');
                
                if (Array.isArray(getResponse)) {
                    logResult('apiResults', `📊 Found ${getResponse.length} landing pages`, 'info');
                } else if (getResponse.success && getResponse.data) {
                    logResult('apiResults', `📊 Found ${getResponse.data.length} landing pages`, 'info');
                }

                testResults.apiTest = true;
                updateTestSummary();

            } catch (error) {
                logResult('apiResults', `❌ API test failed: ${error.message}`, 'error');
            }
        }

        async function testURLRouting() {
            clearLog('routingResults');
            logResult('routingResults', 'Testing URL routing...', 'info');

            try {
                // Test direct template access
                const directTest = await fetch('landing-page-template.php?id=999');
                if (directTest.status === 404) {
                    logResult('routingResults', '✅ Direct template access working (404 for non-existent ID is expected)', 'success');
                } else if (directTest.status === 200) {
                    logResult('routingResults', '✅ Direct template access working', 'success');
                } else {
                    logResult('routingResults', `⚠️ Direct template returned status: ${directTest.status}`, 'warning');
                }

                testResults.routingTest = true;
                updateTestSummary();

            } catch (error) {
                logResult('routingResults', `❌ Routing test failed: ${error.message}`, 'error');
            }
        }

        async function createTestLandingPage() {
            clearLog('createResults');
            logResult('createResults', 'Creating test landing page...', 'info');

            try {
                // First, get a product to use
                const products = await safeApiCall('php/api/products.php');
                let productId = null;

                if (Array.isArray(products) && products.length > 0) {
                    productId = products[0].id;
                } else if (products.success && products.data && products.data.length > 0) {
                    productId = products.data[0].id;
                }

                if (!productId) {
                    logResult('createResults', '❌ No products found. Please add a product first.', 'error');
                    return;
                }

                logResult('createResults', `📦 Using product ID: ${productId}`, 'info');

                // Create test landing page
                const formData = new FormData();
                formData.append('produit_id', productId);
                formData.append('titre', `Test Landing Page - ${new Date().toLocaleString()}`);
                formData.append('contenu_droit', 'This is test content for the right side.');
                formData.append('contenu_gauche', 'This is test content for the left side.');
                formData.append('template_id', 'custom');

                const createResponse = await safeApiCall('php/api/landing-pages.php', {
                    method: 'POST',
                    body: formData
                });

                if (createResponse.success) {
                    logResult('createResults', '✅ Test landing page created successfully!', 'success');
                    logResult('createResults', `🆔 Landing Page ID: ${createResponse.landing_page_id}`, 'info');
                    logResult('createResults', `🔗 URL: ${createResponse.url}`, 'info');
                    
                    testResults.createTest = true;
                    testResults.generatedUrl = createResponse.url;
                    
                    document.getElementById('testUrlBtn').disabled = false;
                    updateTestSummary();
                } else {
                    logResult('createResults', `❌ Failed to create landing page: ${createResponse.message}`, 'error');
                }

            } catch (error) {
                logResult('createResults', `❌ Create test failed: ${error.message}`, 'error');
            }
        }

        async function testGeneratedURL() {
            if (!testResults.generatedUrl) {
                logResult('createResults', '❌ No generated URL to test', 'error');
                return;
            }

            logResult('createResults', `🧪 Testing generated URL: ${testResults.generatedUrl}`, 'info');

            try {
                const response = await fetch(testResults.generatedUrl);
                
                if (response.status === 200) {
                    logResult('createResults', '✅ Generated URL works perfectly!', 'success');
                    logResult('createResults', `🌐 Test the URL: ${testResults.generatedUrl}`, 'info');
                    
                    // Create a clickable link
                    const linkElement = document.createElement('div');
                    linkElement.innerHTML = `<a href="${testResults.generatedUrl}" target="_blank" style="color: #007cba; font-weight: bold;">🔗 Click here to view the test landing page</a>`;
                    document.getElementById('createResults').appendChild(linkElement);
                    
                } else {
                    logResult('createResults', `❌ Generated URL returned status: ${response.status}`, 'error');
                }

            } catch (error) {
                logResult('createResults', `❌ URL test failed: ${error.message}`, 'error');
            }
        }

        function updateTestSummary() {
            const summary = document.getElementById('testSummary');
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            const totalTests = Object.keys(testResults).length - 1; // Exclude generatedUrl

            summary.innerHTML = `
                <h3>Test Results Summary</h3>
                <p><strong>Tests Passed:</strong> ${passedTests}/${totalTests}</p>
                <ul>
                    <li class="${testResults.systemStatus ? 'status-ok' : 'status-error'}">
                        ${testResults.systemStatus ? '✅' : '❌'} System Status Check
                    </li>
                    <li class="${testResults.apiTest ? 'status-ok' : 'status-error'}">
                        ${testResults.apiTest ? '✅' : '❌'} API Functionality Test
                    </li>
                    <li class="${testResults.routingTest ? 'status-ok' : 'status-error'}">
                        ${testResults.routingTest ? '✅' : '❌'} URL Routing Test
                    </li>
                    <li class="${testResults.createTest ? 'status-ok' : 'status-error'}">
                        ${testResults.createTest ? '✅' : '❌'} Create Test Landing Page
                    </li>
                </ul>
                ${passedTests === totalTests ? 
                    '<p class="status-ok"><strong>🎉 All tests passed! The landing page system is working correctly.</strong></p>' :
                    '<p class="status-error"><strong>⚠️ Some tests failed. Please check the individual test results above.</strong></p>'
                }
            `;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 Landing Page System Test Loaded');
            updateTestSummary();
        });
    </script>
</body>
</html>
