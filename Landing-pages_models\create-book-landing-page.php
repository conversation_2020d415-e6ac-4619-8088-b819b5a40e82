<?php
require_once __DIR__ . '/../php/config.php';

try {
    // Fetch a book product to associate the landing page with
    $product_stmt = $conn->query("SELECT id, titre FROM produits WHERE type = 'book' AND actif = 1 LIMIT 1");
    $product = $product_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        echo "❌ Aucun produit de type 'livre' actif n'a été trouvé pour créer une landing page.";
        exit;
    }

    $product_id = $product['id'];
    $product_title = $product['titre'];

    // Create a landing page for the book
    $stmt = $conn->prepare("
        INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $contenu_droit = '
    <h3>📖 لماذا هذا الكتاب هو خيارك الأفضل؟</h3>
    <ul>
        <li><strong>محتوى غني وعملي:</strong> يقدم خطوات واضحة ومباشرة لتطبيق المفاهيم.</li>
        <li><strong>أسلوب شيق ومبسط:</strong> يجعل أعقد المواضيع سهلة الفهم والاستيعاب.</li>
        <li><strong>أمثلة من واقع الحياة:</strong> تساعد على ربط النظريات بالتطبيقات العملية.</li>
        <li><strong>تصميم جذاب:</strong> طباعة عالية الجودة وتنسيق مريح للقراءة.</li>
    </ul>
    
    <h3>📚 ماذا ستتعلم من هذا الكتاب:</h3>
    <p>• اكتساب مهارات جديدة ومطلوبة في سوق العمل.<br>
    • تطوير طريقة تفكيرك وحل المشكلات.<br>
    • فهم عميق للموضوع من خلال دراسات حالة.<br>
    • نصائح الخبراء لتعزيز مسارك المهني.</p>
    ';
    
    $contenu_gauche = '
    <h3>✍️ عن المؤلف</h3>
    <p>خبير في مجاله يتمتع بسنوات من الخبرة العملية والأكاديمية، وقد ساهم في إثراء المحتوى العربي بالعديد من المؤلفات القيمة.</p>
    
    <h3>🌟 آراء القراء</h3>
    <blockquote style="border-right: 3px solid #667eea; padding-right: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">
        "كتاب رائع غير حياتي! أنصح به كل من يبحث عن التطور والنجاح."
        <cite style="display: block; margin-top: 10px; font-weight: bold;">- أحمد، قارئ شغوف</cite>
    </blockquote>
    
    <h3>🎁 عرض خاص لفترة محدودة</h3>
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
        <h4 style="margin: 0 0 10px 0;">خصم 30% للقراء الجدد!</h4>
        <p style="font-size: 1.5em; font-weight: bold; margin: 0;">احصل عليه الآن بسعر لا يصدق!</p>
    </div>
    ';
    
    $stmt->execute([
        $product_id,
        'صفحة هبوط لكتاب: ' . $product_title,
        $contenu_droit,
        $contenu_gauche,
        '/landing-page-template.php?id='
    ]);
    
    $landing_page_id = $conn->lastInsertId();
    
    // Add sample images for the book landing page
    $images = [
        'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=800&h=600&fit=crop', // Open book
        'https://images.unsplash.com/photo-1589998059171-988d887df646?w=800&h=600&fit=crop', // Books on shelf
        'https://images.unsplash.com/photo-1532012197267-da84d127e765?w=800&h=600&fit=crop'  // Person reading
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO landing_page_images (landing_page_id, image_url, ordre) 
        VALUES (?, ?, ?)
    ");
    
    foreach ($images as $index => $image_url) {
        $stmt->execute([$landing_page_id, $image_url, $index]);
    }
    
    // Update the lien_url with the actual landing page ID
    $stmt = $conn->prepare("UPDATE landing_pages SET lien_url = ? WHERE id = ?");
    $stmt->execute(['/landing-page-template.php?id=' . $landing_page_id, $landing_page_id]);
    
    echo "✅ Landing page pour le livre '{$product_title}' créée avec succès!<br>";
    echo "ID de la landing page: $landing_page_id<br>";
    echo "URL: <a href='/landing-page-template.php?id=$landing_page_id' target='_blank' style='color: #667eea; font-weight: bold;'>🚀 Voir la landing page</a><br>";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors de la création de la landing page pour le livre: " . $e->getMessage();
}
?>