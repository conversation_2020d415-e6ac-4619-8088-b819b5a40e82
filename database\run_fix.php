<?php
require_once __DIR__ . '/../php/config.php';

try {
    $pdo = getPDOConnection();
    $sql = file_get_contents(__DIR__ . '/fix_foreign_keys.sql');
    
    $statements = explode(';', $sql);
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            $pdo->exec($statement);
        }
    }
    
    echo "Foreign keys fixed successfully!\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
