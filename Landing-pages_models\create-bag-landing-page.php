<?php
require_once __DIR__ . '/../php/config.php';

try {
    // Fetch a bag product to associate the landing page with
    $product_stmt = $conn->query("SELECT id, titre FROM produits WHERE type = 'bag' AND actif = 1 LIMIT 1");
    $product = $product_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        echo "❌ Aucun produit de type 'sac' actif n'a été trouvé pour créer une landing page.";
        exit;
    }

    $product_id = $product['id'];
    $product_title = $product['titre'];

    // Create a landing page for the bag
    $stmt = $conn->prepare("
        INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $contenu_droit = '
    <h3>🎒 لماذا هذه الحقيبة هي رفيقك المثالي؟</h3>
    <ul>
        <li><strong>تصميم عصري وأنيق:</strong> تناسب جميع مناسباتك، من العمل إلى الرحلات.</li>
        <li><strong>مواد عالية الجودة:</strong> مصنوعة من قماش مقاوم للماء والتمزق لتدوم طويلاً.</li>
        <li><strong>سعة تخزين ذكية:</strong> جيوب متعددة ومنظمة لحفظ جميع أغراضك بأمان.</li>
        <li><strong>مريحة للحمل:</strong> أحزمة كتف مبطنة وقابلة للتعديل لراحة قصوى.</li>
    </ul>
    
    <h3>✨ مواصفات الحقيبة:</h3>
    <p>• <strong>المادة:</strong> نايلون باليستي متين.<br>
    • <strong>السعة:</strong> 25 لتر، مثالية لجهاز لابتوب 15 بوصة ومستلزمات أخرى.<br>
    • <strong>الوزن:</strong> خفيفة الوزن لا تتجاوز 0.8 كغ.<br>
    • <strong>الألوان:</strong> متوفرة بالأسود، الرمادي، والأزرق الداكن.</p>
    ';
    
    $contenu_gauche = '
    <h3>🌍 مثالية لـ:</h3>
    <p>الاستخدام اليومي، طلاب الجامعات، الموظفين، ومحبي السفر. تصميمها المرن يجعلها مناسبة لكل الظروف.</p>
    
    <h3>🌟 آراء العملاء</h3>
    <blockquote style="border-right: 3px solid #667eea; padding-right: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">
        "حقيبة عملية جداً وأنيقة. أستخدمها كل يوم للعمل ولا أستغني عنها!"
        <cite style="display: block; margin-top: 10px; font-weight: bold;">- فاطمة، مصممة جرافيك</cite>
    </blockquote>
    
    <h3>🎁 احصل على خصم 20% اليوم!</h3>
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
        <h4 style="margin: 0 0 10px 0;">عرض خاص للمتسوقين الأوائل!</h4>
        <p style="font-size: 1.5em; font-weight: bold; margin: 0;">اطلبها الآن واستفد من التوصيل المجاني!</p>
    </div>
    ';
    
    $stmt->execute([
        $product_id,
        'صفحة هبوط لحقيبة: ' . $product_title,
        $contenu_droit,
        $contenu_gauche,
        '/landing-page-template.php?id='
    ]);
    
    $landing_page_id = $conn->lastInsertId();
    
    // Add sample images for the bag landing page
    $images = [
        'https://images.unsplash.com/photo-1553062407-98eeb68c6a62?w=800&h=600&fit=crop', // Stylish backpack
        'https://images.unsplash.com/photo-1566150905458-1bf1b6949ddL?w=800&h=600&fit=crop', // Backpack details
        'https://images.unsplash.com/photo-1598033126220-e7a3a4749115?w=800&h=600&fit=crop'  // Person wearing backpack
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO landing_page_images (landing_page_id, image_url, ordre) 
        VALUES (?, ?, ?)
    ");
    
    foreach ($images as $index => $image_url) {
        $stmt->execute([$landing_page_id, $image_url, $index]);
    }
    
    // Update the lien_url with the actual landing page ID
    $stmt = $conn->prepare("UPDATE landing_pages SET lien_url = ? WHERE id = ?");
    $stmt->execute(['/landing-page-template.php?id=' . $landing_page_id, $landing_page_id]);
    
    echo "✅ Landing page pour le sac '{$product_title}' créée avec succès!<br>";
    echo "ID de la landing page: $landing_page_id<br>";
    echo "URL: <a href='/landing-page-template.php?id=$landing_page_id' target='_blank' style='color: #667eea; font-weight: bold;'>🚀 Voir la landing page</a><br>";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors de la création de la landing page pour le sac: " . $e->getMessage();
}
?>