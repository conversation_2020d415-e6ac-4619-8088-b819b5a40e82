<?php
/**
 * Test Critical Admin Panel Fixes
 * This script tests all the critical fixes applied to the admin panel
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../php/config.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات الحرجة - لوحة التحكم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .test-result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار الإصلاحات الحرجة - لوحة التحكم</h1>
        <p>هذا السكريبت يختبر جميع الإصلاحات الحرجة المطبقة على لوحة التحكم.</p>

        <?php
        $testResults = [];
        $totalTests = 0;
        $passedTests = 0;

        try {
            $pdo = getPDOConnection();
            if (!$pdo) {
                throw new Exception('فشل في الاتصال بقاعدة البيانات');
            }

            // Test 1: Product Edit Modal API Fix
            echo '<div class="test-section">';
            echo '<h3>🛠️ اختبار إصلاح Product Edit Modal</h3>';
            
            // Test if products API returns correct format
            $testProductId = 1; // Assuming there's at least one product
            $stmt = $pdo->query("SELECT id FROM produits LIMIT 1");
            $product = $stmt->fetch();
            
            if ($product) {
                $testProductId = $product['id'];
                
                // Simulate API call
                ob_start();
                $_GET['id'] = $testProductId;
                $_SERVER['REQUEST_METHOD'] = 'GET';
                include '../php/api/products.php';
                $apiOutput = ob_get_clean();
                
                $apiData = json_decode($apiOutput, true);
                
                if ($apiData && isset($apiData['success']) && $apiData['success'] && isset($apiData['data'])) {
                    echo '<div class="test-result pass">✅ Products API يُرجع التنسيق الصحيح مع success و data</div>';
                    $passedTests++;
                } else {
                    echo '<div class="test-result fail">❌ Products API لا يُرجع التنسيق الصحيح</div>';
                    echo '<pre>' . htmlspecialchars($apiOutput) . '</pre>';
                }
                $totalTests++;
            } else {
                echo '<div class="test-result warning">⚠️ لا توجد منتجات للاختبار</div>';
                $totalTests++;
            }
            
            // Test AI Magic Wand script existence
            if (file_exists('js/ai-magic-wand.js')) {
                echo '<div class="test-result pass">✅ AI Magic Wand script موجود</div>';
                $passedTests++;
            } else {
                echo '<div class="test-result fail">❌ AI Magic Wand script غير موجود</div>';
            }
            $totalTests++;
            
            echo '</div>';

            // Test 2: Categories Management
            echo '<div class="test-section">';
            echo '<h3>📋 اختبار إدارة الفئات</h3>';
            
            // Test categories API
            ob_start();
            $_SERVER['REQUEST_METHOD'] = 'GET';
            include '../php/api/categories.php';
            $categoriesOutput = ob_get_clean();
            
            $categoriesData = json_decode($categoriesOutput, true);
            
            if ($categoriesData && isset($categoriesData['success']) && $categoriesData['success']) {
                echo '<div class="test-result pass">✅ Categories API يعمل بنجاح</div>';
                echo '<div class="test-result pass">📊 عدد الفئات: ' . count($categoriesData['categories']) . '</div>';
                $passedTests++;
            } else {
                echo '<div class="test-result fail">❌ Categories API فشل</div>';
                echo '<pre>' . htmlspecialchars($categoriesOutput) . '</pre>';
            }
            $totalTests++;
            
            // Test categories table structure
            $stmt = $pdo->query("DESCRIBE categories");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $requiredColumns = ['id', 'nom_ar', 'actif', 'ordre'];
            $missingColumns = [];
            $existingColumns = array_column($columns, 'Field');
            
            foreach ($requiredColumns as $col) {
                if (!in_array($col, $existingColumns)) {
                    $missingColumns[] = $col;
                }
            }
            
            if (empty($missingColumns)) {
                echo '<div class="test-result pass">✅ جدول الفئات يحتوي على جميع الأعمدة المطلوبة</div>';
                $passedTests++;
            } else {
                echo '<div class="test-result fail">❌ أعمدة مفقودة في جدول الفئات: ' . implode(', ', $missingColumns) . '</div>';
            }
            $totalTests++;
            
            echo '</div>';

            // Test 3: Payment Settings
            echo '<div class="test-section">';
            echo '<h3>💳 اختبار إعدادات الدفع</h3>';
            
            // Test payment settings API
            ob_start();
            $_SERVER['REQUEST_METHOD'] = 'GET';
            include '../php/api/payment-settings.php';
            $paymentOutput = ob_get_clean();
            
            $paymentData = json_decode($paymentOutput, true);
            
            if ($paymentData && isset($paymentData['success']) && $paymentData['success']) {
                echo '<div class="test-result pass">✅ Payment Settings API يعمل بنجاح</div>';
                $passedTests++;
            } else {
                echo '<div class="test-result fail">❌ Payment Settings API فشل</div>';
                echo '<pre>' . htmlspecialchars($paymentOutput) . '</pre>';
            }
            $totalTests++;
            
            // Test payment_settings table
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM payment_settings");
                $settingsCount = $stmt->fetch()['count'];
                echo '<div class="test-result pass">✅ جدول payment_settings موجود مع ' . $settingsCount . ' إعداد</div>';
                $passedTests++;
            } catch (Exception $e) {
                echo '<div class="test-result fail">❌ جدول payment_settings غير موجود أو معطل</div>';
            }
            $totalTests++;
            
            // Test BaridiMob support in payment settings HTML
            $paymentHtml = file_get_contents('payment_settings.html');
            if (strpos($paymentHtml, 'baridimob') !== false) {
                echo '<div class="test-result pass">✅ دعم BaridiMob موجود في إعدادات الدفع</div>';
                $passedTests++;
            } else {
                echo '<div class="test-result fail">❌ دعم BaridiMob غير موجود في إعدادات الدفع</div>';
            }
            $totalTests++;
            
            echo '</div>';

            // Test 4: JavaScript Files Integration
            echo '<div class="test-section">';
            echo '<h3>📜 اختبار تكامل ملفات JavaScript</h3>';
            
            $jsFiles = [
                'js/admin.js' => ['editProduct', 'initializeAIMagicWand'],
                'js/categories-management.js' => ['loadCategories', 'initializeCategoriesManagement'],
                'js/payment-settings.js' => ['saveAllSettings', 'loadFromAPI']
            ];

            foreach ($jsFiles as $file => $functions) {
                $totalTests++;
                if (file_exists($file)) {
                    $content = file_get_contents($file);
                    $allFunctionsFound = true;
                    $missingFunctions = [];
                    
                    foreach ($functions as $function) {
                        if (strpos($content, $function) === false) {
                            $allFunctionsFound = false;
                            $missingFunctions[] = $function;
                        }
                    }
                    
                    if ($allFunctionsFound) {
                        echo '<div class="test-result pass">✅ ' . $file . ' - جميع الوظائف موجودة</div>';
                        $passedTests++;
                    } else {
                        echo '<div class="test-result warning">⚠️ ' . $file . ' - وظائف مفقودة: ' . implode(', ', $missingFunctions) . '</div>';
                    }
                } else {
                    echo '<div class="test-result fail">❌ ' . $file . ' - الملف غير موجود</div>';
                }
            }
            
            echo '</div>';

            // Summary
            echo '<div class="test-section">';
            echo '<h3>📊 ملخص النتائج</h3>';
            
            $successRate = ($totalTests > 0) ? round(($passedTests / $totalTests) * 100, 2) : 0;
            
            if ($successRate >= 90) {
                $statusClass = 'pass';
                $statusIcon = '🎉';
                $statusText = 'ممتاز - جميع الإصلاحات تعمل بنجاح';
            } elseif ($successRate >= 70) {
                $statusClass = 'warning';
                $statusIcon = '⚠️';
                $statusText = 'جيد مع بعض التحذيرات';
            } else {
                $statusClass = 'fail';
                $statusIcon = '❌';
                $statusText = 'يحتاج إصلاحات إضافية';
            }
            
            echo '<div class="test-result ' . $statusClass . '">';
            echo $statusIcon . ' <strong>النتيجة النهائية:</strong> ' . $passedTests . '/' . $totalTests . ' اختبار نجح (' . $successRate . '%) - ' . $statusText;
            echo '</div>';

            echo '<h4>🔧 الإصلاحات المطبقة:</h4>';
            echo '<ul>';
            echo '<li>✅ إصلاح Product Edit Modal - API يُرجع التنسيق الصحيح مع تكامل AI Magic Wand</li>';
            echo '<li>✅ إصلاح Categories Management - تحسين معالجة الأخطاء وتسجيل التفاصيل</li>';
            echo '<li>✅ تحسين Payment Settings - إضافة دعم BaridiMob وتكامل قاعدة البيانات</li>';
            echo '<li>✅ تحسين JavaScript - إضافة وظائف التهيئة المفقودة</li>';
            echo '</ul>';

            echo '<h4>🧪 أدوات الاختبار:</h4>';
            echo '<p><a href="index.html" class="test-button">العودة إلى لوحة التحكم</a></p>';
            echo '<p><a href="test-categories-api.php" class="test-button">اختبار API الفئات</a></p>';
            echo '<p><a href="fix-database.php" class="test-button">إصلاح قاعدة البيانات</a></p>';
            
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="test-result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
        }
        ?>

        <!-- JavaScript Tests -->
        <div class="test-section">
            <h3>🧪 اختبارات JavaScript المباشرة</h3>
            <div id="jsTestResults">جاري تحميل الاختبارات...</div>
            
            <script>
                async function runJavaScriptTests() {
                    const resultsDiv = document.getElementById('jsTestResults');
                    let results = '';
                    
                    // Test 1: Product Edit Modal API
                    try {
                        const response = await fetch('../php/api/products.php?id=1');
                        const data = await response.json();
                        
                        if (data.success && data.data) {
                            results += '<div class="test-result pass">✅ Product API يُرجع التنسيق الصحيح عبر JavaScript</div>';
                        } else {
                            results += '<div class="test-result fail">❌ Product API لا يُرجع التنسيق الصحيح</div>';
                        }
                    } catch (error) {
                        results += '<div class="test-result fail">❌ خطأ في Product API: ' + error.message + '</div>';
                    }
                    
                    // Test 2: Categories API
                    try {
                        const response = await fetch('../php/api/categories.php');
                        const data = await response.json();
                        
                        if (data.success) {
                            results += '<div class="test-result pass">✅ Categories API يعمل عبر JavaScript - عدد الفئات: ' + data.categories.length + '</div>';
                        } else {
                            results += '<div class="test-result fail">❌ Categories API فشل عبر JavaScript</div>';
                        }
                    } catch (error) {
                        results += '<div class="test-result fail">❌ خطأ في Categories API: ' + error.message + '</div>';
                    }
                    
                    // Test 3: Payment Settings API
                    try {
                        const response = await fetch('../php/api/payment-settings.php');
                        const data = await response.json();
                        
                        if (data.success) {
                            results += '<div class="test-result pass">✅ Payment Settings API يعمل عبر JavaScript</div>';
                        } else {
                            results += '<div class="test-result fail">❌ Payment Settings API فشل عبر JavaScript</div>';
                        }
                    } catch (error) {
                        results += '<div class="test-result fail">❌ خطأ في Payment Settings API: ' + error.message + '</div>';
                    }
                    
                    resultsDiv.innerHTML = results;
                }
                
                // Run tests after page loads
                document.addEventListener('DOMContentLoaded', runJavaScriptTests);
            </script>
        </div>

    </div>
</body>
</html>
