<?php
// Security check
if (!defined('SECURITY_CHECK')) {
    die('Direct access not allowed');
}

require_once __DIR__ . '/../config/ai.php';
require_once __DIR__ . '/../config/config.php';

// Legacy AIConfig class - maintains backward compatibility with existing code
class AIConfig
{
    private static function forwardToNew()
    {
        static $instance = null;
        if ($instance === null) {
            $instance = \AIConfig::getInstance();
        }
        return $instance;
    }

    public static function getInstance()
    {
        return self::forwardToNew();
    }

    public static function getProvider($provider)
    {
        $config = self::forwardToNew();
        return [
            'enabled' => $config->isProviderEnabled($provider),
            'api_key' => $config->getApiKey($provider),
            'status' => $config->getProviderStatus($provider)
        ];
    }

    public static function isAvailable()
    {
        $status = self::forwardToNew()->getProviderStatus();
        return !empty(array_filter($status, function ($s) {
            return $s['enabled'] ?? false;
        }));
    }

    public static function getEnabledProviders()
    {
        $status = self::forwardToNew()->getProviderStatus();
        return array_keys(array_filter($status, function ($s) {
            return $s['enabled'] ?? false;
        }));
    }

    public static function getDefaultProvider()
    {
        $config = self::forwardToNew();
        foreach (['openai', 'anthropic', 'gemini'] as $provider) {
            if ($config->isProviderEnabled($provider)) {
                return $provider;
            }
        }
        return null;
    }

    public static function getPrompt($type)
    {
        $templates = [
            'product_description' => "قم بإنشاء وصف مقنع للمنتج التالي:\n\n{product}\n\nContext: {context}",
            'landing_page_title' => "قم بإنشاء عنوان مقنع لصفحة هبوط المنتج التالي:\n\n{product}\n\nContext: {context}",
            'landing_page_content' => "قم بإنشاء محتوى جذاب لصفحة هبوط المنتج التالي:\n\n{product}\n\nContext: {context}",
            'meta_description' => "قم بإنشاء وصف تعريفي SEO للمنتج التالي:\n\n{product}\n\nContext: {context}"
        ];
        return $templates[$type] ?? '';
    }

    public static function refreshFromEnv()
    {
        Config::reload();
        return self::forwardToNew()->getProviderStatus();
    }

    public static function save()
    {
        return true;
    }
    public static function get($key, $default = null)
    {
        return Config::get('AI_' . strtoupper($key), $default);
    }
    public static function set($key, $value)
    {
        return false;
    }
}
