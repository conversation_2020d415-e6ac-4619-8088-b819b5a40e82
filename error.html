<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عذراً، حدث خطأ - متجر الكتب</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #3498db, #2c3e50);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            line-height: 1.6;
        }

        .error-container {
            text-align: center;
            padding: 40px;
            max-width: 600px;
            width: 90%;
        }

        .error-code {
            font-size: 8rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .error-message {
            font-size: 2rem;
            margin-bottom: 30px;
            color: #fff;
        }

        .error-description {
            font-size: 1.1rem;
            margin-bottom: 40px;
            color: rgba(255, 255, 255, 0.9);
        }

        .back-button {
            display: inline-block;
            padding: 15px 30px;
            background: #fff;
            color: #2c3e50;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 600;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 480px) {
            .error-code {
                font-size: 6rem;
            }

            .error-message {
                font-size: 1.5rem;
            }

            .error-description {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">404</div>
        <h1 class="error-message">عذراً، حدث خطأ</h1>
        <p class="error-description">الصفحة التي تبحث عنها غير موجودة أو تم نقلها أو حذفها.</p>
        <a href="index.html" class="back-button">العودة إلى الصفحة الرئيسية</a>
    </div>

    <script>
        // Détecter le type d'erreur et mettre à jour le contenu
        window.onload = function() {
            const errorCode = window.location.search.match(/[?&]code=(\d+)/) || [null, '404'];
            const code = errorCode[1];
            
            const errorMessages = {
                '400': 'طلب غير صالح',
                '401': 'غير مصرح',
                '403': 'ممنوع الوصول',
                '404': 'الصفحة غير موجودة',
                '500': 'خطأ في الخادم'
            };

            const errorDescriptions = {
                '400': 'لا يمكن معالجة الطلب بسبب صيغة غير صحيحة.',
                '401': 'يجب عليك تسجيل الدخول للوصول إلى هذه الصفحة.',
                '403': 'ليس لديك صلاحية للوصول إلى هذه الصفحة.',
                '404': 'الصفحة التي تبحث عنها غير موجودة أو تم نقلها أو حذفها.',
                '500': 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.'
            };

            document.querySelector('.error-code').textContent = code;
            document.querySelector('.error-message').textContent = errorMessages[code] || 'عذراً، حدث خطأ';
            document.querySelector('.error-description').textContent = errorDescriptions[code] || 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
        };
    </script>
</body>
</html>