<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفئات - لوحة التحكم</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .categories-container {
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .category-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }
        .category-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .category-icon {
            font-size: 2em;
            margin-left: 15px;
        }
        .category-info h3 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .category-info p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }
        .category-actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        .btn-edit {
            background: #ffc107;
            color: #333;
        }
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        .btn-toggle {
            background: #28a745;
            color: white;
        }
        .btn-toggle.inactive {
            background: #6c757d;
        }
        .add-category-form {
            background: #e8f4fd;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px dashed #007bff;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .color-picker {
            width: 60px !important;
            height: 40px;
            padding: 0;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .icon-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
            gap: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .icon-option {
            padding: 10px;
            text-align: center;
            border: 2px solid transparent;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .icon-option:hover,
        .icon-option.selected {
            border-color: #007bff;
            background: #e8f4fd;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <header class="admin-header">
            <h1>🗂️ إدارة الفئات</h1>
            <div class="header-actions">
                <button onclick="window.history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة
                </button>
                <button onclick="location.reload()" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> تحديث
                </button>
            </div>
        </header>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalCategories">0</div>
                <div class="stat-label">إجمالي الفئات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeCategories">0</div>
                <div class="stat-label">الفئات النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalProducts">0</div>
                <div class="stat-label">المنتجات المربوطة</div>
            </div>
        </div>

        <!-- Add New Category Form -->
        <div class="add-category-form">
            <h3>➕ إضافة فئة جديدة</h3>
            <form id="addCategoryForm">
                <div class="form-row">
                    <div class="form-group">
                        <label>الاسم بالعربية *</label>
                        <input type="text" id="nomAr" name="nom_ar" required placeholder="مثال: كتب">
                    </div>
                    <div class="form-group">
                        <label>الاسم بالإنجليزية *</label>
                        <input type="text" id="nomEn" name="nom_en" required placeholder="مثال: books">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>الوصف بالعربية</label>
                        <textarea id="descAr" name="description_ar" rows="3" placeholder="وصف الفئة بالعربية"></textarea>
                    </div>
                    <div class="form-group">
                        <label>الوصف بالإنجليزية</label>
                        <textarea id="descEn" name="description_en" rows="3" placeholder="Category description in English"></textarea>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>اللون</label>
                        <input type="color" id="couleur" name="couleur" value="#007bff" class="color-picker">
                    </div>
                    <div class="form-group">
                        <label>ترتيب العرض</label>
                        <input type="number" id="ordre" name="ordre_affichage" min="0" value="0">
                    </div>
                </div>

                <div class="form-group">
                    <label>اختر الأيقونة</label>
                    <input type="hidden" id="selectedIcon" name="icone" value="fas fa-box">
                    <div class="icon-selector" id="iconSelector">
                        <!-- Icons will be populated by JavaScript -->
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة الفئة
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </form>
        </div>

        <!-- Categories List -->
        <div class="categories-container">
            <h3>📋 قائمة الفئات الحالية</h3>
            <div id="categoriesList">
                <!-- Categories will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div id="editCategoryModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ تعديل الفئة</h3>
                <button type="button" class="close-modal" onclick="closeEditModal()">×</button>
            </div>
            <form id="editCategoryForm">
                <input type="hidden" id="editCategoryId">
                <!-- Same form fields as add form -->
                <div class="form-row">
                    <div class="form-group">
                        <label>الاسم بالعربية *</label>
                        <input type="text" id="editNomAr" required>
                    </div>
                    <div class="form-group">
                        <label>الاسم بالإنجليزية *</label>
                        <input type="text" id="editNomEn" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>الوصف بالعربية</label>
                        <textarea id="editDescAr" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>الوصف بالإنجليزية</label>
                        <textarea id="editDescEn" rows="3"></textarea>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>اللون</label>
                        <input type="color" id="editCouleur" class="color-picker">
                    </div>
                    <div class="form-group">
                        <label>ترتيب العرض</label>
                        <input type="number" id="editOrdre" min="0">
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeEditModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/categories-management.js"></script>
</body>
</html>
