<?php
/**
 * Landing Page Routing Test
 * Tests the fixed landing page URL routing system
 */

// Define security check constant BEFORE including security.php
define('SECURITY_CHECK', true);

require_once 'php/config.php';

echo "<h1>🔗 Landing Page Routing Test</h1>";

// Test 1: Check existing landing pages
echo "<h2>1. Existing Landing Pages</h2>";
try {
    $stmt = $conn->prepare("SELECT id, titre, lien_url, produit_id FROM landing_pages ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $landingPages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($landingPages) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Product ID</th><th>Current URL</th><th>Test Links</th></tr>";
        
        foreach ($landingPages as $page) {
            echo "<tr>";
            echo "<td>{$page['id']}</td>";
            echo "<td>" . htmlspecialchars($page['titre']) . "</td>";
            echo "<td>{$page['produit_id']}</td>";
            echo "<td>" . htmlspecialchars($page['lien_url']) . "</td>";
            echo "<td>";
            
            // Test direct ID access
            echo "<a href='landing-page-template.php?id={$page['id']}' target='_blank'>Direct ID</a> | ";
            
            // Test URL-based access if it's an old-style URL
            if (strpos($page['lien_url'], '/landing/product-') === 0) {
                $urlPart = str_replace('/landing/product-', '', $page['lien_url']);
                echo "<a href='landing/product-{$urlPart}' target='_blank'>URL Route</a>";
            } else {
                echo "<span style='color: green;'>✅ Uses direct template URL</span>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No landing pages found. Create one first using the admin panel.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 2: URL Rewrite Rules Test
echo "<h2>2. URL Rewrite Rules Test</h2>";
echo "<p>The .htaccess file should contain this rule:</p>";
echo "<code>RewriteRule ^landing/product-([0-9]+)-([a-zA-Z0-9]+)/?$ landing-page-template.php?url=$1-$2 [L,QSA]</code>";

if (file_exists('.htaccess')) {
    $htaccess = file_get_contents('.htaccess');
    if (strpos($htaccess, 'landing/product-') !== false) {
        echo "<p style='color: green;'>✅ Landing page rewrite rule found in .htaccess</p>";
    } else {
        echo "<p style='color: red;'>❌ Landing page rewrite rule NOT found in .htaccess</p>";
    }
} else {
    echo "<p style='color: red;'>❌ .htaccess file not found</p>";
}

// Test 3: Landing Page Template Test
echo "<h2>3. Landing Page Template Test</h2>";
echo "<p>Testing if landing-page-template.php can handle both URL formats:</p>";

if (file_exists('landing-page-template.php')) {
    echo "<p style='color: green;'>✅ landing-page-template.php exists</p>";
    
    // Check if it handles URL parameter
    $templateContent = file_get_contents('landing-page-template.php');
    if (strpos($templateContent, '$_GET[\'url\']') !== false) {
        echo "<p style='color: green;'>✅ Template supports URL parameter routing</p>";
    } else {
        echo "<p style='color: red;'>❌ Template does NOT support URL parameter routing</p>";
    }
    
    if (strpos($templateContent, '$_GET[\'id\']') !== false) {
        echo "<p style='color: green;'>✅ Template supports direct ID access</p>";
    } else {
        echo "<p style='color: red;'>❌ Template does NOT support direct ID access</p>";
    }
} else {
    echo "<p style='color: red;'>❌ landing-page-template.php not found</p>";
}

// Test 4: API URL Generation Test
echo "<h2>4. API URL Generation Test</h2>";
echo "<p>Testing the generateUniqueUrl function and URL update process:</p>";

// Include the API file to test the function
if (file_exists('php/api/landing-pages.php')) {
    $apiContent = file_get_contents('php/api/landing-pages.php');
    
    if (strpos($apiContent, 'function generateUniqueUrl') !== false) {
        echo "<p style='color: green;'>✅ generateUniqueUrl function found</p>";
    }
    
    if (strpos($apiContent, 'UPDATE landing_pages SET lien_url') !== false) {
        echo "<p style='color: green;'>✅ URL update logic found in API</p>";
    }
    
    if (strpos($apiContent, '/landing-page-template.php?id=') !== false) {
        echo "<p style='color: green;'>✅ API updates URLs to use template format</p>";
    }
}

// Test 5: Create Test Landing Page
echo "<h2>5. Create Test Landing Page</h2>";
echo "<p>Click the button below to create a test landing page and verify the routing:</p>";

?>

<form method="post" style="margin: 20px 0;">
    <input type="hidden" name="test_create" value="1">
    <button type="submit" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        Create Test Landing Page
    </button>
</form>

<?php

if (isset($_POST['test_create'])) {
    echo "<h3>Creating Test Landing Page...</h3>";
    
    try {
        // Get a product to use for the test
        $stmt = $conn->prepare("SELECT id, titre FROM produits LIMIT 1");
        $stmt->execute();
        $product = $stmt->fetch();
        
        if ($product) {
            // Create test landing page
            $title = "Test Landing Page - " . date('Y-m-d H:i:s');
            $content = "This is a test landing page created to verify routing.";
            $tempUrl = '/landing/product-' . $product['id'] . '-' . uniqid();
            
            $stmt = $conn->prepare(
                "INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url, template_id)
                 VALUES (?, ?, ?, ?, ?, ?)"
            );
            $stmt->execute([$product['id'], $title, $content, $content, $tempUrl, 'custom']);
            $landingPageId = $conn->lastInsertId();
            
            // Update with final URL
            $finalUrl = '/landing-page-template.php?id=' . $landingPageId;
            $stmt = $conn->prepare("UPDATE landing_pages SET lien_url = ? WHERE id = ?");
            $stmt->execute([$finalUrl, $landingPageId]);
            
            echo "<p style='color: green;'>✅ Test landing page created successfully!</p>";
            echo "<p><strong>Landing Page ID:</strong> $landingPageId</p>";
            echo "<p><strong>Product:</strong> " . htmlspecialchars($product['titre']) . "</p>";
            echo "<p><strong>Final URL:</strong> $finalUrl</p>";
            echo "<p><strong>Test Links:</strong></p>";
            echo "<ul>";
            echo "<li><a href='$finalUrl' target='_blank'>Direct Template Access</a></li>";
            echo "<li><a href='landing-page-template.php?id=$landingPageId' target='_blank'>Alternative Direct Access</a></li>";
            echo "</ul>";
            
        } else {
            echo "<p style='color: red;'>❌ No products found. Please add a product first.</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error creating test landing page: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>6. Next Steps</h2>";
echo "<ol>";
echo "<li>Test the links above to verify landing pages load correctly</li>";
echo "<li>Check the admin panel to ensure new landing pages get proper URLs</li>";
echo "<li>Verify that both old and new URL formats work</li>";
echo "<li>Test creating landing pages through the admin interface</li>";
echo "</ol>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { margin: 10px 0; }
th, td { padding: 8px 12px; text-align: left; }
th { background: #f5f5f5; }
code { background: #f5f5f5; padding: 2px 5px; border-radius: 3px; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
