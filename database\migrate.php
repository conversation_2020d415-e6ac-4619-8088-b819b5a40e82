<?php
require_once __DIR__ . '/../php/config.php';

try {
    // Initialiser la connexion PDO
    $pdo = getPDOConnection();

    // Lire le contenu du fichier de migration
    $migrationFile = __DIR__ . '/migrations/merge_livres_produits.sql';
    $sql = file_get_contents($migrationFile);

    if ($sql === false) {
        throw new Exception("Impossible de lire le fichier de migration");
    }

    // Diviser le fichier en requêtes individuelles
    $queries = array_filter(array_map('trim', explode(';', $sql)));

    // Exécuter chaque requête
    foreach ($queries as $query) {
        if (!empty($query)) {
            $pdo->exec($query);
        }
    }

    echo "Migration terminée avec succès!";

} catch (PDOException $e) {
    die("Erreur lors de la migration : " . $e->getMessage());
} catch (Exception $e) {
    die("Erreur : " . $e->getMessage());
}