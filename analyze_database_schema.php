<?php
/**
 * Database Schema Analysis for MariaDB 11.5.2
 * Analyzes actual table structure to fix script compatibility
 */

require_once 'php/config.php';

echo "<h1>🔍 Database Schema Analysis - MariaDB Port 3307</h1>\n";
echo "<style>
body{font-family:Arial;margin:20px;background:#f8f9fa;} 
.container{max-width:1200px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 4px 6px rgba(0,0,0,0.1);}
.success{color:#28a745;font-weight:bold;} 
.error{color:#dc3545;font-weight:bold;} 
.warning{color:#ffc107;font-weight:bold;} 
.info{color:#17a2b8;font-weight:bold;} 
pre{background:#f8f9fa;padding:15px;border-radius:5px;border-left:4px solid #007bff;overflow-x:auto;} 
table{border-collapse:collapse;width:100%;margin:15px 0;} 
th,td{border:1px solid #dee2e6;padding:12px;text-align:left;} 
th{background:#e9ecef;font-weight:bold;}
.section{background:#f0f8ff;padding:25px;margin:20px 0;border-radius:12px;border-left:5px solid #007bff;}
.highlight{background:#fff3cd;padding:15px;border-radius:8px;margin:15px 0;}
.code-block{background:#f8f9fa;border:1px solid #e9ecef;border-radius:5px;padding:15px;margin:10px 0;font-family:monospace;}
</style>\n";

echo "<div class='container'>\n";

try {
    $pdo = getPDOConnection();
    echo "<p class='success'>✅ Connected to MariaDB successfully</p>\n";
    
    // SECTION 1: Analyze produits table structure
    echo "<div class='section'>\n";
    echo "<h2>📋 Section 1: Produits Table Structure Analysis</h2>\n";
    
    $stmt = $pdo->query("DESCRIBE produits");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current Table Structure:</h3>\n";
    echo "<table>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    
    $columnNames = [];
    $columnTypes = [];
    foreach ($columns as $column) {
        $columnNames[] = $column['Field'];
        $columnTypes[$column['Field']] = $column['Type'];
        
        echo "<tr>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<h3>Available Columns:</h3>\n";
    echo "<div class='code-block'>" . implode(', ', $columnNames) . "</div>\n";
    echo "</div>\n";
    
    // SECTION 2: Column Mapping Analysis
    echo "<div class='section'>\n";
    echo "<h2>🔗 Section 2: Column Mapping Analysis</h2>\n";
    
    // Expected columns vs actual columns
    $expectedColumns = [
        'id' => 'Primary key',
        'category_id' => 'Foreign key to categories',
        'type' => 'Product type',
        'titre' => 'Product title',
        'description' => 'Product description',
        'prix' => 'Product price',
        'stock' => 'Stock quantity',
        'auteur' => 'Author (for books)',
        'materiel' => 'Material/specifications',
        'actif' => 'Active status',
        'date_creation' => 'Creation date',
        'date_modification' => 'Modification date'
    ];
    
    echo "<h3>Column Mapping Analysis:</h3>\n";
    echo "<table>\n";
    echo "<tr><th>Expected Column</th><th>Purpose</th><th>Status in Your DB</th><th>Recommendation</th></tr>\n";
    
    $mappingResults = [];
    foreach ($expectedColumns as $expected => $purpose) {
        $status = in_array($expected, $columnNames) ? '✅ EXISTS' : '❌ MISSING';
        $recommendation = '';
        
        if (in_array($expected, $columnNames)) {
            $mappingResults[$expected] = $expected; // Direct mapping
            $recommendation = 'Use as-is';
        } else {
            // Try to find alternative column names
            $alternatives = [];
            foreach ($columnNames as $actual) {
                if (stripos($actual, str_replace('_', '', $expected)) !== false || 
                    stripos($expected, str_replace('_', '', $actual)) !== false) {
                    $alternatives[] = $actual;
                }
            }
            
            if (!empty($alternatives)) {
                $mappingResults[$expected] = $alternatives[0];
                $recommendation = "Map to: " . $alternatives[0];
                $status = '⚠️ MAP TO: ' . $alternatives[0];
            } else {
                $mappingResults[$expected] = null;
                $recommendation = 'Skip or add column';
            }
        }
        
        echo "<tr>";
        echo "<td><strong>$expected</strong></td>";
        echo "<td>$purpose</td>";
        echo "<td>$status</td>";
        echo "<td>$recommendation</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    echo "</div>\n";
    
    // SECTION 3: Generate Fixed SQL
    echo "<div class='section'>\n";
    echo "<h2>🔧 Section 3: Fixed SQL Generation</h2>\n";
    
    // Build the corrected INSERT statement
    $validColumns = [];
    $placeholders = [];
    
    foreach ($mappingResults as $expected => $actual) {
        if ($actual && in_array($actual, $columnNames)) {
            $validColumns[] = $actual;
            $placeholders[] = ":$expected";
        }
    }
    
    $insertSQL = "INSERT INTO produits (" . implode(', ', $validColumns) . ") VALUES (" . implode(', ', $placeholders) . ")";
    
    echo "<h3>Corrected INSERT Statement:</h3>\n";
    echo "<div class='code-block'>$insertSQL</div>\n";
    
    echo "<h3>Column Mapping for PHP Script:</h3>\n";
    echo "<div class='code-block'>\n";
    echo "\$columnMapping = [\n";
    foreach ($mappingResults as $expected => $actual) {
        if ($actual) {
            echo "    '$expected' => '$actual',\n";
        } else {
            echo "    // '$expected' => null, // Column not found\n";
        }
    }
    echo "];\n";
    echo "</div>\n";
    echo "</div>\n";
    
    // SECTION 4: Check other critical tables
    echo "<div class='section'>\n";
    echo "<h2>📊 Section 4: Other Critical Tables Analysis</h2>\n";
    
    $criticalTables = ['categories', 'landing_pages', 'landing_page_images'];
    
    foreach ($criticalTables as $tableName) {
        echo "<h3>Table: $tableName</h3>\n";
        
        $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'>✅ Table exists</p>\n";
            
            $stmt = $pdo->query("DESCRIBE $tableName");
            $tableColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Columns:</strong> ";
            $colNames = array_column($tableColumns, 'Field');
            echo implode(', ', $colNames) . "</p>\n";
        } else {
            echo "<p class='error'>❌ Table missing - will be created</p>\n";
        }
    }
    echo "</div>\n";
    
    // SECTION 5: Generate Fixed Script
    echo "<div class='section'>\n";
    echo "<h2>🛠️ Section 5: Fixed Script Generation</h2>\n";
    
    echo "<h3>PHP Code for Fixed Product Creation:</h3>\n";
    echo "<div class='code-block'>\n";
    echo htmlspecialchars('
// Fixed product creation code
$columnMapping = [
');
    
    foreach ($mappingResults as $expected => $actual) {
        if ($actual) {
            echo htmlspecialchars("    '$expected' => '$actual',\n");
        }
    }
    
    echo htmlspecialchars('
];

// Build dynamic SQL
$columns = array_values($columnMapping);
$placeholders = array_map(function($col) { return ":$col"; }, array_keys($columnMapping));

$sql = "INSERT INTO produits (" . implode(", ", $columns) . ") VALUES (" . implode(", ", $placeholders) . ")";

$stmt = $pdo->prepare($sql);

// Execute with mapped parameters
$params = [];
foreach ($columnMapping as $expected => $actual) {
    if (isset($productData[$expected])) {
        $params[":$expected"] = $productData[$expected];
    }
}

$result = $stmt->execute($params);
');
    echo "</div>\n";
    
    echo "<div class='highlight'>\n";
    echo "<h3>🎯 Next Steps:</h3>\n";
    echo "<ol>\n";
    echo "<li>Copy the column mapping above</li>\n";
    echo "<li>Update complete_system_rebuild.php with the corrected code</li>\n";
    echo "<li>Test the fixed script</li>\n";
    echo "<li>Verify products are created successfully</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    // SECTION 6: Sample Data Test
    echo "<div class='section'>\n";
    echo "<h2>🧪 Section 6: Sample Data Test</h2>\n";
    
    echo "<p>Testing with a sample product to verify the mapping works:</p>\n";
    
    $testProduct = [
        'type' => 'test',
        'titre' => 'Test Product - تجربة',
        'description' => 'Test description - وصف تجريبي',
        'prix' => 1000.00,
        'stock' => 10,
        'auteur' => null,
        'materiel' => null,
        'actif' => 1
    ];
    
    try {
        // Build test SQL
        $testColumns = [];
        $testPlaceholders = [];
        $testParams = [];
        
        foreach ($mappingResults as $expected => $actual) {
            if ($actual && in_array($actual, $columnNames) && isset($testProduct[$expected])) {
                $testColumns[] = $actual;
                $testPlaceholders[] = ":$expected";
                $testParams[":$expected"] = $testProduct[$expected];
            }
        }
        
        if (!empty($testColumns)) {
            $testSQL = "INSERT INTO produits (" . implode(', ', $testColumns) . ") VALUES (" . implode(', ', $testPlaceholders) . ")";
            
            echo "<p><strong>Test SQL:</strong></p>\n";
            echo "<div class='code-block'>$testSQL</div>\n";
            
            echo "<p><strong>Test Parameters:</strong></p>\n";
            echo "<div class='code-block'>" . print_r($testParams, true) . "</div>\n";
            
            // Don't actually execute the test - just show what would happen
            echo "<p class='info'>ℹ️ Test SQL generated successfully - ready for execution</p>\n";
        } else {
            echo "<p class='error'>❌ No valid columns found for mapping</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Test failed: " . $e->getMessage() . "</p>\n";
    }
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='section'>\n";
    echo "<h2 class='error'>❌ Analysis Error</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    echo "</div>\n";
}

echo "</div>\n";
?>

<script>
console.log('🔍 Database schema analysis completed');
</script>
