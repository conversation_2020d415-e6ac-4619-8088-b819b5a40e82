/**
 * Error Handling and User-Friendly Arabic Messages
 * Provides toast notifications, contextual help, and error pages
 */

class ErrorHandler {
    constructor() {
        this.init();
    }

    init() {
        this.createToastContainer();
        this.createErrorStyles();
        this.setupGlobalErrorHandling();
        this.setupFormValidation();
        this.setupAPIErrorHandling();
        this.createErrorPages();
    }

    /**
     * Create toast notification container
     */
    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container';
        container.setAttribute('aria-live', 'polite');
        container.setAttribute('aria-atomic', 'true');
        document.body.appendChild(container);
    }

    /**
     * Create CSS styles for error handling
     */
    createErrorStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* Toast Notifications */
            .toast-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                direction: rtl;
            }
            
            .toast {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                margin-bottom: 10px;
                padding: 16px 20px;
                border-left: 4px solid;
                animation: slideInRight 0.3s ease-out;
                position: relative;
                min-height: 60px;
                display: flex;
                align-items: center;
            }
            
            .toast.success {
                border-left-color: #28a745;
                background: #d4edda;
                color: #155724;
            }
            
            .toast.error {
                border-left-color: #dc3545;
                background: #f8d7da;
                color: #721c24;
            }
            
            .toast.warning {
                border-left-color: #ffc107;
                background: #fff3cd;
                color: #856404;
            }
            
            .toast.info {
                border-left-color: #17a2b8;
                background: #d1ecf1;
                color: #0c5460;
            }
            
            .toast-icon {
                font-size: 1.5rem;
                margin-left: 12px;
                flex-shrink: 0;
            }
            
            .toast-content {
                flex: 1;
            }
            
            .toast-title {
                font-weight: 600;
                margin-bottom: 4px;
                font-size: 1rem;
            }
            
            .toast-message {
                font-size: 0.9rem;
                line-height: 1.4;
                margin: 0;
            }
            
            .toast-close {
                position: absolute;
                top: 8px;
                left: 8px;
                background: none;
                border: none;
                font-size: 1.2rem;
                cursor: pointer;
                color: inherit;
                opacity: 0.7;
                padding: 4px;
                border-radius: 4px;
            }
            
            .toast-close:hover {
                opacity: 1;
                background: rgba(0, 0, 0, 0.1);
            }
            
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
            
            .toast.removing {
                animation: slideOutRight 0.3s ease-in forwards;
            }
            
            /* Form Error Styles */
            .form-error {
                color: #dc3545;
                font-size: 0.875rem;
                margin-top: 4px;
                display: flex;
                align-items: center;
                gap: 6px;
            }
            
            .form-error-icon {
                font-size: 1rem;
                flex-shrink: 0;
            }
            
            .form-field-error {
                border-color: #dc3545 !important;
                box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
            }
            
            .form-field-success {
                border-color: #28a745 !important;
                box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2) !important;
            }
            
            /* Contextual Help */
            .help-text {
                font-size: 0.8rem;
                color: #6c757d;
                margin-top: 4px;
                line-height: 1.4;
            }
            
            .help-icon {
                display: inline-block;
                width: 16px;
                height: 16px;
                background: #6c757d;
                color: white;
                border-radius: 50%;
                text-align: center;
                line-height: 16px;
                font-size: 12px;
                cursor: help;
                margin-right: 4px;
            }
            
            /* Error Page Styles */
            .error-page {
                text-align: center;
                padding: 4rem 2rem;
                max-width: 600px;
                margin: 0 auto;
                direction: rtl;
            }
            
            .error-code {
                font-size: 6rem;
                font-weight: bold;
                color: #dc3545;
                margin-bottom: 1rem;
                line-height: 1;
            }
            
            .error-title {
                font-size: 2rem;
                color: #2c3e50;
                margin-bottom: 1rem;
            }
            
            .error-description {
                font-size: 1.1rem;
                color: #6c757d;
                margin-bottom: 2rem;
                line-height: 1.6;
            }
            
            .error-actions {
                display: flex;
                gap: 1rem;
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .error-btn {
                padding: 12px 24px;
                border-radius: 6px;
                text-decoration: none;
                font-weight: 600;
                transition: all 0.2s ease;
                border: 2px solid;
                min-width: 140px;
            }
            
            .error-btn-primary {
                background: #2c3e50;
                color: white;
                border-color: #2c3e50;
            }
            
            .error-btn-primary:hover {
                background: #1a252f;
                border-color: #1a252f;
            }
            
            .error-btn-secondary {
                background: transparent;
                color: #2c3e50;
                border-color: #2c3e50;
            }
            
            .error-btn-secondary:hover {
                background: #2c3e50;
                color: white;
            }
            
            /* Loading Error States */
            .loading-error {
                text-align: center;
                padding: 2rem;
                color: #dc3545;
            }
            
            .loading-error-icon {
                font-size: 3rem;
                margin-bottom: 1rem;
            }
            
            .loading-error-message {
                font-size: 1.1rem;
                margin-bottom: 1rem;
            }
            
            .retry-btn {
                background: #dc3545;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 600;
                transition: background 0.2s ease;
            }
            
            .retry-btn:hover {
                background: #c82333;
            }
            
            /* Mobile Responsive */
            @media (max-width: 768px) {
                .toast-container {
                    right: 10px;
                    left: 10px;
                    max-width: none;
                }
                
                .toast {
                    padding: 12px 16px;
                }
                
                .error-code {
                    font-size: 4rem;
                }
                
                .error-title {
                    font-size: 1.5rem;
                }
                
                .error-actions {
                    flex-direction: column;
                    align-items: center;
                }
                
                .error-btn {
                    width: 100%;
                    max-width: 280px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', title = null, duration = 5000) {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };
        
        const titles = {
            success: 'نجح',
            error: 'خطأ',
            warning: 'تحذير',
            info: 'معلومات'
        };
        
        toast.innerHTML = `
            <div class="toast-icon">${icons[type]}</div>
            <div class="toast-content">
                ${title ? `<div class="toast-title">${title}</div>` : `<div class="toast-title">${titles[type]}</div>`}
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" aria-label="إغلاق">&times;</button>
        `;
        
        // Add close functionality
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => {
            this.removeToast(toast);
        });
        
        container.appendChild(toast);
        
        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                this.removeToast(toast);
            }, duration);
        }
        
        return toast;
    }

    /**
     * Remove toast notification
     */
    removeToast(toast) {
        toast.classList.add('removing');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    /**
     * Setup global error handling
     */
    setupGlobalErrorHandling() {
        // Handle JavaScript errors
        window.addEventListener('error', (event) => {
            console.error('JavaScript Error:', event.error);
            this.showToast(
                'حدث خطأ غير متوقع. يرجى تحديث الصفحة والمحاولة مرة أخرى.',
                'error',
                'خطأ في النظام'
            );
        });
        
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled Promise Rejection:', event.reason);
            this.showToast(
                'فشل في تحميل البيانات. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.',
                'error',
                'خطأ في الاتصال'
            );
        });
    }

    /**
     * Setup form validation with Arabic messages
     */
    setupFormValidation() {
        document.addEventListener('invalid', (event) => {
            event.preventDefault();
            const input = event.target;
            this.showFieldError(input, this.getValidationMessage(input));
        }, true);
        
        document.addEventListener('input', (event) => {
            const input = event.target;
            if (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA' || input.tagName === 'SELECT') {
                this.clearFieldError(input);
                
                if (input.validity.valid) {
                    this.showFieldSuccess(input);
                }
            }
        });
    }

    /**
     * Get Arabic validation message for input
     */
    getValidationMessage(input) {
        const validity = input.validity;
        const fieldName = input.getAttribute('aria-label') || input.getAttribute('placeholder') || 'الحقل';
        
        if (validity.valueMissing) {
            return `${fieldName} مطلوب`;
        }
        
        if (validity.typeMismatch) {
            if (input.type === 'email') {
                return 'يرجى إدخال عنوان بريد إلكتروني صحيح';
            }
            if (input.type === 'url') {
                return 'يرجى إدخال رابط صحيح';
            }
            return 'تنسيق البيانات غير صحيح';
        }
        
        if (validity.patternMismatch) {
            return 'تنسيق البيانات المدخلة غير صحيح';
        }
        
        if (validity.tooShort) {
            return `${fieldName} قصير جداً (الحد الأدنى ${input.minLength} أحرف)`;
        }
        
        if (validity.tooLong) {
            return `${fieldName} طويل جداً (الحد الأقصى ${input.maxLength} حرف)`;
        }
        
        if (validity.rangeUnderflow) {
            return `القيمة صغيرة جداً (الحد الأدنى ${input.min})`;
        }
        
        if (validity.rangeOverflow) {
            return `القيمة كبيرة جداً (الحد الأقصى ${input.max})`;
        }
        
        if (validity.stepMismatch) {
            return 'القيمة غير صحيحة';
        }
        
        return 'البيانات المدخلة غير صحيحة';
    }

    /**
     * Show field error
     */
    showFieldError(input, message) {
        this.clearFieldError(input);
        
        input.classList.add('form-field-error');
        input.setAttribute('aria-invalid', 'true');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'form-error';
        errorDiv.innerHTML = `
            <span class="form-error-icon">⚠</span>
            <span>${message}</span>
        `;
        
        const errorId = 'error-' + Math.random().toString(36).substr(2, 9);
        errorDiv.id = errorId;
        input.setAttribute('aria-describedby', errorId);
        
        input.parentNode.insertBefore(errorDiv, input.nextSibling);
    }

    /**
     * Show field success
     */
    showFieldSuccess(input) {
        input.classList.remove('form-field-error');
        input.classList.add('form-field-success');
        input.setAttribute('aria-invalid', 'false');
    }

    /**
     * Clear field error
     */
    clearFieldError(input) {
        input.classList.remove('form-field-error', 'form-field-success');
        input.removeAttribute('aria-invalid');
        input.removeAttribute('aria-describedby');
        
        const existingError = input.parentNode.querySelector('.form-error');
        if (existingError) {
            existingError.remove();
        }
    }

    /**
     * Setup API error handling
     */
    setupAPIErrorHandling() {
        // Intercept fetch requests to handle errors
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch(...args);
                
                if (!response.ok) {
                    const errorMessage = this.getAPIErrorMessage(response.status);
                    this.showToast(errorMessage, 'error');
                }
                
                return response;
            } catch (error) {
                console.error('Fetch error:', error);
                this.showToast(
                    'فشل في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.',
                    'error',
                    'خطأ في الشبكة'
                );
                throw error;
            }
        };
    }

    /**
     * Get Arabic error message for HTTP status
     */
    getAPIErrorMessage(status) {
        const messages = {
            400: 'البيانات المرسلة غير صحيحة. يرجى التحقق من المعلومات المدخلة.',
            401: 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.',
            403: 'ليس لديك صلاحية للوصول إلى هذا المحتوى.',
            404: 'المحتوى المطلوب غير موجود.',
            408: 'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.',
            429: 'تم تجاوز الحد المسموح من الطلبات. يرجى الانتظار قليلاً.',
            500: 'خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.',
            502: 'الخادم غير متاح حالياً. يرجى المحاولة مرة أخرى لاحقاً.',
            503: 'الخدمة غير متاحة حالياً. يرجى المحاولة مرة أخرى لاحقاً.'
        };
        
        return messages[status] || 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
    }

    /**
     * Create error pages
     */
    createErrorPages() {
        // This would typically be done server-side, but we can provide client-side fallbacks
        this.createNetworkErrorPage();
        this.createLoadingErrorComponent();
    }

    /**
     * Create network error page
     */
    createNetworkErrorPage() {
        // This function can be called when network errors occur
        window.showNetworkError = () => {
            document.body.innerHTML = `
                <div class="error-page">
                    <div class="error-code">⚠</div>
                    <h1 class="error-title">مشكلة في الاتصال</h1>
                    <p class="error-description">
                        لا يمكن الاتصال بالخادم حالياً. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.
                    </p>
                    <div class="error-actions">
                        <button class="error-btn error-btn-primary" onclick="location.reload()">
                            إعادة المحاولة
                        </button>
                        <a href="/" class="error-btn error-btn-secondary">
                            العودة للرئيسية
                        </a>
                    </div>
                </div>
            `;
        };
    }

    /**
     * Create loading error component
     */
    createLoadingErrorComponent() {
        window.showLoadingError = (container, retryCallback) => {
            container.innerHTML = `
                <div class="loading-error">
                    <div class="loading-error-icon">⚠</div>
                    <div class="loading-error-message">فشل في تحميل البيانات</div>
                    <button class="retry-btn" onclick="(${retryCallback.toString()})()">
                        إعادة المحاولة
                    </button>
                </div>
            `;
        };
    }

    /**
     * Show success message
     */
    showSuccess(message, title = 'تم بنجاح') {
        return this.showToast(message, 'success', title);
    }

    /**
     * Show error message
     */
    showError(message, title = 'خطأ') {
        return this.showToast(message, 'error', title);
    }

    /**
     * Show warning message
     */
    showWarning(message, title = 'تحذير') {
        return this.showToast(message, 'warning', title);
    }

    /**
     * Show info message
     */
    showInfo(message, title = 'معلومات') {
        return this.showToast(message, 'info', title);
    }

    /**
     * Add contextual help to form field
     */
    addHelp(input, helpText) {
        const helpDiv = document.createElement('div');
        helpDiv.className = 'help-text';
        helpDiv.innerHTML = `<span class="help-icon">؟</span>${helpText}`;
        
        const helpId = 'help-' + Math.random().toString(36).substr(2, 9);
        helpDiv.id = helpId;
        
        const existingDescribedBy = input.getAttribute('aria-describedby');
        if (existingDescribedBy) {
            input.setAttribute('aria-describedby', `${existingDescribedBy} ${helpId}`);
        } else {
            input.setAttribute('aria-describedby', helpId);
        }
        
        input.parentNode.insertBefore(helpDiv, input.nextSibling);
    }
}

// Initialize error handler
const errorHandler = new ErrorHandler();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorHandler;
}

// Make available globally
window.ErrorHandler = ErrorHandler;
window.errorHandler = errorHandler;
