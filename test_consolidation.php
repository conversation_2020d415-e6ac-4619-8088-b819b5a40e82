<?php
require_once 'php/config.php';

echo "=== Testing Database Consolidation ===\n";

// Test 1: Check products count
try {
    $stmt = $conn->query('SELECT COUNT(*) as count FROM produits');
    $count = $stmt->fetch()['count'];
    echo "✓ Products count: {$count}\n";
} catch (Exception $e) {
    echo "✗ Error counting products: " . $e->getMessage() . "\n";
}

// Test 2: Check product types
try {
    $stmt = $conn->query('SELECT type, COUNT(*) as count FROM produits GROUP BY type');
    $types = $stmt->fetchAll();
    echo "✓ Product types:\n";
    foreach ($types as $type) {
        echo "  - {$type['type']}: {$type['count']}\n";
    }
} catch (Exception $e) {
    echo "✗ Error checking product types: " . $e->getMessage() . "\n";
}

// Test 3: Test ProductLanding class
try {
    require_once 'php/ProductLanding.php';
    $productLanding = new ProductLanding();
    
    // Test slug generation
    $slug = $productLanding->generateSlug('Test Product');
    echo "✓ Slug generation works: {$slug}\n";
    
} catch (Exception $e) {
    echo "✗ Error testing ProductLanding: " . $e->getMessage() . "\n";
}

// Test 4: Test Books class
try {
    require_once 'php/books.php';
    $books = new Books($conn);
    
    $allBooks = $books->getAllBooks();
    if (is_array($allBooks)) {
        echo "✓ Books class works: " . count($allBooks) . " books found\n";
    } else {
        echo "✗ Books class error: " . print_r($allBooks, true) . "\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error testing Books class: " . $e->getMessage() . "\n";
}

echo "\n=== Test Completed ===\n";
?>
