<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - لوحة التحكم</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- User Management Content -->
    <div class="user-management-content">
        <!-- Header Section -->
        <div class="user-management-header">
                            <div class="section-title-wrapper">
                                <div class="section-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="section-title-content">
                                    <h3 class="section-title">إدارة المستخدمين</h3>
                                    <p class="section-subtitle">إدارة حسابات المستخدمين والصلاحيات والأدوار</p>
                                </div>
                            </div>
                            <div class="users-summary">
                                <div class="summary-item">
                                    <span class="summary-label">إجمالي المستخدمين:</span>
                                    <span class="summary-value" id="totalUsers">0</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">المستخدمين النشطين:</span>
                                    <span class="summary-value" id="activeUsers">0</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">المديرين:</span>
                                    <span class="summary-value" id="adminUsers">0</span>
                                </div>
                            </div>
                        </div>

                        <!-- Users Management Tools -->
                        <div class="users-tools-container">
                            <div class="tools-header">
                                <h4 class="tools-title">
                                    <i class="fas fa-tools"></i>
                                    أدوات إدارة المستخدمين
                                </h4>
                                <div class="tools-actions">
                                    <button class="btn btn-primary enhanced-btn" onclick="showAddUserModal()">
                                        <i class="fas fa-user-plus"></i>
                                        إضافة مستخدم جديد
                                    </button>
                                    <button class="btn btn-secondary enhanced-btn" onclick="exportUsers()">
                                        <i class="fas fa-download"></i>
                                        تصدير المستخدمين
                                    </button>
                                </div>
                            </div>

                            <!-- Search and Filter -->
                            <div class="users-filters">
                                <div class="filter-group">
                                    <label for="userSearch" class="filter-label">
                                        <i class="fas fa-search"></i>
                                        البحث
                                    </label>
                                    <input type="text" id="userSearch" class="enhanced-input" placeholder="البحث بالاسم أو البريد الإلكتروني...">
                                </div>
                                <div class="filter-group">
                                    <label for="roleFilter" class="filter-label">
                                        <i class="fas fa-filter"></i>
                                        الدور
                                    </label>
                                    <select id="roleFilter" class="enhanced-select">
                                        <option value="">جميع الأدوار</option>
                                        <option value="admin">مدير</option>
                                        <option value="editor">محرر</option>
                                        <option value="customer">عميل</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="statusFilter" class="filter-label">
                                        <i class="fas fa-toggle-on"></i>
                                        الحالة
                                    </label>
                                    <select id="statusFilter" class="enhanced-select">
                                        <option value="">جميع الحالات</option>
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                        <option value="suspended">معلق</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Users Table -->
                        <div class="users-table-container">
                            <div class="table-header">
                                <h4 class="table-title">
                                    <i class="fas fa-list"></i>
                                    قائمة المستخدمين
                                </h4>
                                <div class="table-info">
                                    <span id="usersCount">0 مستخدم</span>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="enhanced-table" id="usersTable">
                                    <thead>
                                        <tr>
                                            <th>
                                                <input type="checkbox" id="selectAllUsers" onchange="toggleSelectAll()">
                                            </th>
                                            <th>الصورة</th>
                                            <th>الاسم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الدور</th>
                                            <th>الحالة</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>آخر دخول</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTableBody">
                                        <!-- Users will be loaded here -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="table-pagination">
                                <div class="pagination-info">
                                    <span id="paginationInfo">عرض 0 من 0</span>
                                </div>
                                <div class="pagination-controls">
                                    <button class="btn btn-sm" id="prevPage" onclick="changePage(-1)">
                                        <i class="fas fa-chevron-right"></i>
                                        السابق
                                    </button>
                                    <span class="page-numbers" id="pageNumbers"></span>
                                    <button class="btn btn-sm" id="nextPage" onclick="changePage(1)">
                                        التالي
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Bulk Actions -->
                        <div class="bulk-actions" id="bulkActions" style="display: none;">
                            <div class="bulk-actions-content">
                                <span class="bulk-count">تم تحديد <span id="selectedCount">0</span> مستخدم</span>
                                <div class="bulk-buttons">
                                    <button class="btn btn-warning" onclick="bulkAction('activate')">
                                        <i class="fas fa-check"></i>
                                        تفعيل
                                    </button>
                                    <button class="btn btn-secondary" onclick="bulkAction('deactivate')">
                                        <i class="fas fa-ban"></i>
                                        إلغاء تفعيل
                                    </button>
                                    <button class="btn btn-danger" onclick="bulkAction('delete')">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
        </div>
    </div>

    <!-- Add/Edit User Modal -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة مستخدم جديد</h3>
                <button class="modal-close" onclick="closeUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="userForm" class="enhanced-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userName" class="enhanced-label">
                                <i class="fas fa-user"></i>
                                الاسم الكامل
                            </label>
                            <input type="text" id="userName" class="enhanced-input" required>
                        </div>
                        <div class="form-group">
                            <label for="userEmail" class="enhanced-label">
                                <i class="fas fa-envelope"></i>
                                البريد الإلكتروني
                            </label>
                            <input type="email" id="userEmail" class="enhanced-input" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userRole" class="enhanced-label">
                                <i class="fas fa-user-tag"></i>
                                الدور
                            </label>
                            <select id="userRole" class="enhanced-select" required>
                                <option value="">اختر الدور</option>
                                <option value="admin">مدير</option>
                                <option value="editor">محرر</option>
                                <option value="customer">عميل</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="userStatus" class="enhanced-label">
                                <i class="fas fa-toggle-on"></i>
                                الحالة
                            </label>
                            <select id="userStatus" class="enhanced-select" required>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="suspended">معلق</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userPassword" class="enhanced-label">
                                <i class="fas fa-lock"></i>
                                كلمة المرور
                            </label>
                            <div class="input-group">
                                <input type="password" id="userPassword" class="enhanced-input">
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('userPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="input-hint">اتركها فارغة للاحتفاظ بكلمة المرور الحالية (في حالة التعديل)</div>
                        </div>
                        <div class="form-group">
                            <label for="userPhone" class="enhanced-label">
                                <i class="fas fa-phone"></i>
                                رقم الهاتف
                            </label>
                            <input type="tel" id="userPhone" class="enhanced-input">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="userNotes" class="enhanced-label">
                            <i class="fas fa-sticky-note"></i>
                            ملاحظات
                        </label>
                        <textarea id="userNotes" class="enhanced-textarea" rows="3" placeholder="ملاحظات إضافية عن المستخدم..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeUserModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveUser()">
                    <i class="fas fa-save"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>

    <!-- User Details Modal -->
    <div class="modal" id="userDetailsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تفاصيل المستخدم</h3>
                <button class="modal-close" onclick="closeUserDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- User details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeUserDetailsModal()">إغلاق</button>
            </div>
        </div>
    </div>

    <script src="js/user-management.js"></script>
</body>
</html>
