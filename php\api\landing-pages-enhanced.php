<?php

/**
 * Enhanced Landing Pages API - Version 2.0
 *
 * Handles CRUD operations for landing pages with comprehensive security,
 * error handling, and MariaDB compatibility.
 *
 * Endpoints:
 * - GET    /api/landing-pages-enhanced.php           - List all landing pages with pagination
 * - GET    /api/landing-pages-enhanced.php?id=X     - Get specific landing page
 * - POST   /api/landing-pages-enhanced.php          - Create new landing page
 * - PUT    /api/landing-pages-enhanced.php          - Update existing landing page
 * - DELETE /api/landing-pages-enhanced.php?id=X     - Delete landing page
 *
 * Security Features:
 * - Input validation and sanitization
 * - XSS protection for HTML content
 * - CSRF protection for state-changing operations
 * - Template validation against allowed values
 *
 * Database Compatibility:
 * - Dynamic column mapping for MariaDB compatibility
 * - Transaction support with rollback on errors
 * - Foreign key constraint validation
 *
 * <AUTHOR> Page System Enhanced
 * @version 2.0
 * @since 2024-01-01
 */

require_once '../config.php';

// Set JSON response headers with security headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-CSRF-Token');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * Allowed template IDs for validation
 */
const ALLOWED_TEMPLATES = ['modern', 'classic', 'minimal', 'elegant', 'professional'];

/**
 * Error codes for consistent error handling
 */
const ERROR_CODES = [
    'VALIDATION_ERROR' => 400,
    'NOT_FOUND' => 404,
    'DATABASE_ERROR' => 500,
    'INVALID_METHOD' => 405,
    'INVALID_TEMPLATE' => 400,
    'MISSING_REQUIRED_FIELD' => 400,
    'FOREIGN_KEY_VIOLATION' => 400,
    'IMAGE_UPLOAD_ERROR' => 400
];

/**
 * Send standardized JSON response
 *
 * @param bool $success Success status
 * @param mixed $data Response data
 * @param string $message Response message
 * @param string $errorCode Error code for failures
 * @param int $httpCode HTTP status code
 */
function sendResponse($success, $data = null, $message = '', $errorCode = '', $httpCode = 200)
{
    http_response_code($httpCode);

    $response = [
        'success' => $success,
        'timestamp' => date('c'),
        'method' => $_SERVER['REQUEST_METHOD']
    ];

    if ($success) {
        $response['data'] = $data;
        if ($message) $response['message'] = $message;
    } else {
        $response['error'] = [
            'message' => $message,
            'code' => $errorCode,
            'http_code' => $httpCode
        ];
        // Log error for debugging
        error_log("API Error: $errorCode - $message");
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Validate and sanitize input data
 *
 * @param array $data Input data to validate
 * @param array $rules Validation rules
 * @return array Validated and sanitized data
 * @throws Exception If validation fails
 */
function validateInput($data, $rules)
{
    $validated = [];

    foreach ($rules as $field => $rule) {
        $value = $data[$field] ?? null;

        // Check required fields
        if ($rule['required'] && (is_null($value) || $value === '')) {
            throw new Exception("Field '$field' is required", ERROR_CODES['MISSING_REQUIRED_FIELD']);
        }

        // Skip validation for optional empty fields
        if (!$rule['required'] && (is_null($value) || $value === '')) {
            $validated[$field] = null;
            continue;
        }

        // Type validation and sanitization
        switch ($rule['type']) {
            case 'int':
                if (!is_numeric($value)) {
                    throw new Exception("Field '$field' must be a number", ERROR_CODES['VALIDATION_ERROR']);
                }
                $validated[$field] = (int)$value;
                break;

            case 'string':
                $validated[$field] = trim(strip_tags($value));
                if (isset($rule['max_length']) && strlen($validated[$field]) > $rule['max_length']) {
                    throw new Exception("Field '$field' exceeds maximum length", ERROR_CODES['VALIDATION_ERROR']);
                }
                break;

            case 'html':
                // Allow specific HTML tags but sanitize
                $allowed_tags = '<p><br><strong><em><ul><li><ol><h3><h4><div><span>';
                $validated[$field] = strip_tags($value, $allowed_tags);
                // Additional XSS protection
                $validated[$field] = htmlspecialchars($validated[$field], ENT_QUOTES, 'UTF-8', false);
                break;

            case 'template':
                if (!in_array($value, ALLOWED_TEMPLATES)) {
                    throw new Exception("Invalid template ID. Allowed: " . implode(', ', ALLOWED_TEMPLATES), ERROR_CODES['INVALID_TEMPLATE']);
                }
                $validated[$field] = $value;
                break;

            case 'url':
                $validated[$field] = filter_var($value, FILTER_SANITIZE_URL);
                break;

            default:
                $validated[$field] = $value;
        }
    }

    return $validated;
}

/**
 * Validate that a product exists and is active
 *
 * @param PDO $pdo Database connection
 * @param int $productId Product ID to validate
 * @return bool True if product exists and is active
 */
function validateProductExists($pdo, $productId)
{
    try {
        $stmt = $pdo->prepare("SELECT id FROM produits WHERE id = ? AND actif = 1");
        $stmt->execute([$productId]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        error_log("Error validating product: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate unique URL for landing page
 *
 * @param int $productId Product ID
 * @return string Unique URL
 */
function generateUniqueUrl($productId)
{
    return '/landing-page.php?product=' . $productId;
}

/**
 * Handle image upload and validation
 *
 * @param array $file File data from $_FILES
 * @param string $uploadDir Upload directory
 * @return string|false Uploaded file path or false on failure
 */
function handleImageUpload($file, $uploadDir)
{
    // Validate file
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception("Invalid image type. Allowed: JPEG, PNG, GIF, WebP", ERROR_CODES['IMAGE_UPLOAD_ERROR']);
    }

    if ($file['size'] > $maxSize) {
        throw new Exception("Image too large. Maximum size: 5MB", ERROR_CODES['IMAGE_UPLOAD_ERROR']);
    }

    // Create upload directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid('landing_') . '.' . $extension;
    $filepath = $uploadDir . $filename;

    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception("Failed to upload image", ERROR_CODES['IMAGE_UPLOAD_ERROR']);
    }

    return '/uploads/products/landing/' . $filename;
}

/**
 * Get database column mapping for MariaDB compatibility
 *
 * @param PDO $pdo Database connection
 * @return array Column mapping
 */
function getDatabaseColumnMapping($pdo)
{
    static $mapping = null;

    if ($mapping === null) {
        try {
            $stmt = $pdo->query("DESCRIBE landing_pages");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Create mapping for expected vs actual columns
            $mapping = [];
            $expectedColumns = ['id', 'titre', 'produit_id', 'template_id', 'contenu_droit', 'contenu_gauche', 'lien_url', 'actif'];

            foreach ($expectedColumns as $expected) {
                if (in_array($expected, $columns)) {
                    $mapping[$expected] = $expected;
                } else {
                    // Try to find alternative column names
                    foreach ($columns as $actual) {
                        if (stripos($actual, str_replace('_', '', $expected)) !== false) {
                            $mapping[$expected] = $actual;
                            break;
                        }
                    }
                }
            }
        } catch (PDOException $e) {
            error_log("Error getting column mapping: " . $e->getMessage());
            // Fallback to standard mapping
            $mapping = [
                'id' => 'id',
                'titre' => 'titre',
                'produit_id' => 'produit_id',
                'template_id' => 'template_id',
                'contenu_droit' => 'contenu_droit',
                'contenu_gauche' => 'contenu_gauche',
                'lien_url' => 'lien_url',
                'actif' => 'actif'
            ];
        }
    }

    return $mapping;
}

/**
 * Handle GET requests - List landing pages or get specific landing page
 *
 * Supports pagination, filtering, and includes related data
 */
function handleGet()
{
    try {
        $pdo = getPDOConnection();
        $columnMapping = getDatabaseColumnMapping($pdo);

        // Get query parameters
        $id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
        $productId = filter_input(INPUT_GET, 'product_id', FILTER_VALIDATE_INT);
        $templateId = filter_input(INPUT_GET, 'template_id', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $active = filter_input(INPUT_GET, 'active', FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
        $page = filter_input(INPUT_GET, 'page', FILTER_VALIDATE_INT) ?: 1;
        $limit = filter_input(INPUT_GET, 'limit', FILTER_VALIDATE_INT) ?: 10;
        $offset = ($page - 1) * $limit;

        if ($id) {
            // Get specific landing page with related data
            $sql = "
                SELECT lp.{$columnMapping['id']} as id,
                       lp.{$columnMapping['titre']} as titre,
                       lp.{$columnMapping['produit_id']} as produit_id,
                       lp.{$columnMapping['template_id']} as template_id,
                       lp.{$columnMapping['contenu_droit']} as contenu_droit,
                       lp.{$columnMapping['contenu_gauche']} as contenu_gauche,
                       lp.{$columnMapping['lien_url']} as lien_url,
                       lp.{$columnMapping['actif']} as actif,
                       p.titre as product_title,
                       p.prix as product_price,
                       p.type as product_type,
                       c.nom_ar as category_name,
                       c.icone as category_icon,
                       c.couleur as category_color
                FROM landing_pages lp
                LEFT JOIN produits p ON lp.{$columnMapping['produit_id']} = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE lp.{$columnMapping['id']} = ?
            ";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            $landingPage = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$landingPage) {
                sendResponse(false, null, 'Landing page not found', 'NOT_FOUND', 404);
            }

            // Get images for this landing page
            $stmt = $pdo->prepare("SELECT image_url, ordre FROM landing_page_images WHERE landing_page_id = ? ORDER BY ordre");
            $stmt->execute([$id]);
            $landingPage['images'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

            sendResponse(true, $landingPage, 'Landing page retrieved successfully');
        } else {
            // Get multiple landing pages with filtering and pagination
            $whereConditions = [];
            $params = [];

            if ($productId) {
                $whereConditions[] = "lp.{$columnMapping['produit_id']} = ?";
                $params[] = $productId;
            }

            if ($templateId && in_array($templateId, ALLOWED_TEMPLATES)) {
                $whereConditions[] = "lp.{$columnMapping['template_id']} = ?";
                $params[] = $templateId;
            }

            if ($active !== null) {
                $whereConditions[] = "lp.{$columnMapping['actif']} = ?";
                $params[] = $active ? 1 : 0;
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // Get total count for pagination
            $countSql = "
                SELECT COUNT(*) as total
                FROM landing_pages lp
                LEFT JOIN produits p ON lp.{$columnMapping['produit_id']} = p.id
                $whereClause
            ";

            $stmt = $pdo->prepare($countSql);
            $stmt->execute($params);
            $totalCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

            // Get landing pages with pagination
            $sql = "
                SELECT lp.{$columnMapping['id']} as id,
                       lp.{$columnMapping['titre']} as titre,
                       lp.{$columnMapping['produit_id']} as produit_id,
                       lp.{$columnMapping['template_id']} as template_id,
                       lp.{$columnMapping['lien_url']} as lien_url,
                       lp.{$columnMapping['actif']} as actif,
                       p.titre as product_title,
                       p.prix as product_price,
                       p.type as product_type,
                       c.nom_ar as category_name
                FROM landing_pages lp
                LEFT JOIN produits p ON lp.{$columnMapping['produit_id']} = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                $whereClause
                ORDER BY lp.{$columnMapping['id']} DESC
                LIMIT ? OFFSET ?
            ";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $landingPages = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get images for each landing page
            foreach ($landingPages as &$landingPage) {
                $stmt = $pdo->prepare("SELECT image_url, ordre FROM landing_page_images WHERE landing_page_id = ? ORDER BY ordre");
                $stmt->execute([$landingPage['id']]);
                $landingPage['images'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }

            $response = [
                'landing_pages' => $landingPages,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $totalCount,
                    'total_pages' => ceil($totalCount / $limit)
                ],
                'filters' => [
                    'product_id' => $productId,
                    'template_id' => $templateId,
                    'active' => $active
                ]
            ];

            sendResponse(true, $response, 'Landing pages retrieved successfully');
        }
    } catch (PDOException $e) {
        error_log("Database error in handleGet: " . $e->getMessage());
        sendResponse(false, null, 'Database error occurred', 'DATABASE_ERROR', 500);
    } catch (Exception $e) {
        sendResponse(false, null, $e->getMessage(), 'VALIDATION_ERROR', $e->getCode() ?: 400);
    }
}

/**
 * Handle POST requests - Create new landing page
 *
 * Validates input, creates landing page, and handles image uploads
 */
function handlePost()
{
    try {
        $pdo = getPDOConnection();
        $columnMapping = getDatabaseColumnMapping($pdo);

        // Get input data
        $input = $_POST;

        // Validation rules
        $rules = [
            'produit_id' => ['required' => true, 'type' => 'int'],
            'titre' => ['required' => true, 'type' => 'string', 'max_length' => 255],
            'template_id' => ['required' => false, 'type' => 'template'],
            'contenu_droit' => ['required' => false, 'type' => 'html'],
            'contenu_gauche' => ['required' => false, 'type' => 'html'],
            'lien_url' => ['required' => false, 'type' => 'url'],
            'actif' => ['required' => false, 'type' => 'int']
        ];

        // Handle different input field names for backward compatibility
        if (isset($input['productSelect'])) $input['produit_id'] = $input['productSelect'];
        if (isset($input['landingPageTitle'])) $input['titre'] = $input['landingPageTitle'];
        if (isset($input['rightContent'])) $input['contenu_droit'] = $input['rightContent'];
        if (isset($input['leftContent'])) $input['contenu_gauche'] = $input['leftContent'];

        // Validate input
        $validated = validateInput($input, $rules);

        // Validate product exists
        if (!validateProductExists($pdo, $validated['produit_id'])) {
            sendResponse(false, null, 'Product not found or inactive', 'FOREIGN_KEY_VIOLATION', 400);
        }

        // Set defaults
        $validated['template_id'] = $validated['template_id'] ?: 'modern';
        $validated['lien_url'] = $validated['lien_url'] ?: generateUniqueUrl($validated['produit_id']);
        $validated['actif'] = $validated['actif'] ?? 1;

        // Start transaction
        $pdo->beginTransaction();

        try {
            // Insert landing page
            $sql = "
                INSERT INTO landing_pages (
                    {$columnMapping['produit_id']},
                    {$columnMapping['titre']},
                    {$columnMapping['template_id']},
                    {$columnMapping['contenu_droit']},
                    {$columnMapping['contenu_gauche']},
                    {$columnMapping['lien_url']},
                    {$columnMapping['actif']}
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                $validated['produit_id'],
                $validated['titre'],
                $validated['template_id'],
                $validated['contenu_droit'],
                $validated['contenu_gauche'],
                $validated['lien_url'],
                $validated['actif']
            ]);

            $landingPageId = $pdo->lastInsertId();

            // Handle image uploads
            $uploadedImages = [];
            if (!empty($_FILES['landingPageImages'])) {
                $uploadDir = '../../uploads/products/landing/';

                // Handle multiple files
                $files = $_FILES['landingPageImages'];
                $fileCount = is_array($files['name']) ? count($files['name']) : 1;

                for ($i = 0; $i < $fileCount; $i++) {
                    $file = [
                        'name' => is_array($files['name']) ? $files['name'][$i] : $files['name'],
                        'type' => is_array($files['type']) ? $files['type'][$i] : $files['type'],
                        'tmp_name' => is_array($files['tmp_name']) ? $files['tmp_name'][$i] : $files['tmp_name'],
                        'size' => is_array($files['size']) ? $files['size'][$i] : $files['size']
                    ];

                    if ($file['tmp_name'] && $file['size'] > 0) {
                        $imageUrl = handleImageUpload($file, $uploadDir);

                        // Insert image record
                        $stmt = $pdo->prepare("INSERT INTO landing_page_images (landing_page_id, image_url, ordre) VALUES (?, ?, ?)");
                        $stmt->execute([$landingPageId, $imageUrl, $i]);

                        $uploadedImages[] = $imageUrl;
                    }
                }
            }

            $pdo->commit();

            // Get the created landing page with related data
            $stmt = $pdo->prepare("
                SELECT lp.{$columnMapping['id']} as id,
                       lp.{$columnMapping['titre']} as titre,
                       lp.{$columnMapping['template_id']} as template_id,
                       lp.{$columnMapping['lien_url']} as lien_url,
                       p.titre as product_title
                FROM landing_pages lp
                LEFT JOIN produits p ON lp.{$columnMapping['produit_id']} = p.id
                WHERE lp.{$columnMapping['id']} = ?
            ");
            $stmt->execute([$landingPageId]);
            $createdPage = $stmt->fetch(PDO::FETCH_ASSOC);
            $createdPage['images'] = $uploadedImages;

            sendResponse(true, $createdPage, 'Landing page created successfully', '', 201);
        } catch (Exception $e) {
            $pdo->rollBack();
            throw $e;
        }
    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Database error in handlePost: " . $e->getMessage());
        sendResponse(false, null, 'Database error occurred', 'DATABASE_ERROR', 500);
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        sendResponse(false, null, $e->getMessage(), 'VALIDATION_ERROR', $e->getCode() ?: 400);
    }
}

/**
 * Handle PUT requests - Update existing landing page
 *
 * Validates input and updates landing page data
 */
function handlePut()
{
    try {
        $pdo = getPDOConnection();
        $columnMapping = getDatabaseColumnMapping($pdo);

        // Get JSON input for PUT requests
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            sendResponse(false, null, 'Invalid JSON input', 'VALIDATION_ERROR', 400);
        }

        // Validation rules
        $rules = [
            'id' => ['required' => true, 'type' => 'int'],
            'produit_id' => ['required' => false, 'type' => 'int'],
            'titre' => ['required' => false, 'type' => 'string', 'max_length' => 255],
            'template_id' => ['required' => false, 'type' => 'template'],
            'contenu_droit' => ['required' => false, 'type' => 'html'],
            'contenu_gauche' => ['required' => false, 'type' => 'html'],
            'lien_url' => ['required' => false, 'type' => 'url'],
            'actif' => ['required' => false, 'type' => 'int']
        ];

        // Validate input
        $validated = validateInput($input, $rules);

        // Check if landing page exists
        $stmt = $pdo->prepare("SELECT {$columnMapping['id']} FROM landing_pages WHERE {$columnMapping['id']} = ?");
        $stmt->execute([$validated['id']]);
        if (!$stmt->fetch()) {
            sendResponse(false, null, 'Landing page not found', 'NOT_FOUND', 404);
        }

        // Validate product exists if provided
        if (isset($validated['produit_id']) && !validateProductExists($pdo, $validated['produit_id'])) {
            sendResponse(false, null, 'Product not found or inactive', 'FOREIGN_KEY_VIOLATION', 400);
        }

        // Build update query dynamically
        $updateFields = [];
        $updateParams = [];

        foreach ($validated as $field => $value) {
            if ($field !== 'id' && isset($columnMapping[$field])) {
                $updateFields[] = "{$columnMapping[$field]} = ?";
                $updateParams[] = $value;
            }
        }

        if (empty($updateFields)) {
            sendResponse(false, null, 'No valid fields to update', 'VALIDATION_ERROR', 400);
        }

        // Add ID parameter for WHERE clause
        $updateParams[] = $validated['id'];

        $sql = "UPDATE landing_pages SET " . implode(', ', $updateFields) . " WHERE {$columnMapping['id']} = ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($updateParams);

        // Get updated landing page
        $stmt = $pdo->prepare("
            SELECT lp.{$columnMapping['id']} as id,
                   lp.{$columnMapping['titre']} as titre,
                   lp.{$columnMapping['template_id']} as template_id,
                   lp.{$columnMapping['lien_url']} as lien_url,
                   lp.{$columnMapping['actif']} as actif,
                   p.titre as product_title
            FROM landing_pages lp
            LEFT JOIN produits p ON lp.{$columnMapping['produit_id']} = p.id
            WHERE lp.{$columnMapping['id']} = ?
        ");
        $stmt->execute([$validated['id']]);
        $updatedPage = $stmt->fetch(PDO::FETCH_ASSOC);

        sendResponse(true, $updatedPage, 'Landing page updated successfully');
    } catch (PDOException $e) {
        error_log("Database error in handlePut: " . $e->getMessage());
        sendResponse(false, null, 'Database error occurred', 'DATABASE_ERROR', 500);
    } catch (Exception $e) {
        sendResponse(false, null, $e->getMessage(), 'VALIDATION_ERROR', $e->getCode() ?: 400);
    }
}

/**
 * Handle DELETE requests - Delete landing page
 *
 * Deletes landing page and associated images
 */
function handleDelete()
{
    try {
        $pdo = getPDOConnection();
        $columnMapping = getDatabaseColumnMapping($pdo);

        $id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

        if (!$id) {
            sendResponse(false, null, 'Landing page ID is required', 'MISSING_REQUIRED_FIELD', 400);
        }

        // Check if landing page exists
        $stmt = $pdo->prepare("SELECT {$columnMapping['id']} FROM landing_pages WHERE {$columnMapping['id']} = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendResponse(false, null, 'Landing page not found', 'NOT_FOUND', 404);
        }

        // Start transaction
        $pdo->beginTransaction();

        try {
            // Get images to delete files
            $stmt = $pdo->prepare("SELECT image_url FROM landing_page_images WHERE landing_page_id = ?");
            $stmt->execute([$id]);
            $images = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Delete image records
            $stmt = $pdo->prepare("DELETE FROM landing_page_images WHERE landing_page_id = ?");
            $stmt->execute([$id]);

            // Delete landing page
            $stmt = $pdo->prepare("DELETE FROM landing_pages WHERE {$columnMapping['id']} = ?");
            $stmt->execute([$id]);

            $pdo->commit();

            // Delete image files
            foreach ($images as $imageUrl) {
                $filePath = '../../' . ltrim($imageUrl, '/');
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }

            sendResponse(true, ['deleted_id' => $id, 'deleted_images' => count($images)], 'Landing page deleted successfully');
        } catch (Exception $e) {
            $pdo->rollBack();
            throw $e;
        }
    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Database error in handleDelete: " . $e->getMessage());
        sendResponse(false, null, 'Database error occurred', 'DATABASE_ERROR', 500);
    } catch (Exception $e) {
        sendResponse(false, null, $e->getMessage(), 'VALIDATION_ERROR', $e->getCode() ?: 400);
    }
}

// Main request handler
try {
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost();
            break;
        case 'PUT':
            handlePut();
            break;
        case 'DELETE':
            handleDelete();
            break;
        default:
            sendResponse(false, null, 'Method not allowed', 'INVALID_METHOD', 405);
    }
} catch (Exception $e) {
    error_log("Unexpected error: " . $e->getMessage());
    sendResponse(false, null, 'Internal server error', 'DATABASE_ERROR', 500);
}
