<?php
/**
 * Test User Authentication Debug
 */

require_once 'php/config.php';

header('Content-Type: text/plain');

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "🔧 Test Authentification Debug\n";
echo "==============================\n\n";

try {
    echo "📋 Test de la configuration :\n";
    $pdo = getPDOConnection();
    echo "✅ Connexion PDO réussie\n\n";
    
    // Test des utilisateurs
    echo "👥 Test de la table users :\n";
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "✅ Table users accessible, {$result['count']} utilisateurs trouvés\n";
    
    // Test utilisateur demo
    echo "\n🧪 Test utilisateur demo :\n";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "✅ Utilisateur demo trouvé :\n";
        echo "  - ID: {$user['id']}\n";
        echo "  - Username: {$user['username']}\n";
        echo "  - Email: {$user['email']}\n";
        echo "  - Nom: {$user['first_name']} {$user['last_name']}\n";
        echo "  - Status: {$user['status']}\n";
        echo "  - Role ID: {$user['role_id']}\n";
        
        // Test mot de passe
        if (password_verify('demo123', $user['password'])) {
            echo "✅ Vérification mot de passe réussie\n";
        } else {
            echo "❌ Vérification mot de passe échouée\n";
        }
    } else {
        echo "❌ Utilisateur demo non trouvé\n";
    }
    
    // Test direct de l'API d'authentification
    echo "\n🔌 Test API d'authentification :\n";
    
    // Simuler une requête de login
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_GET['action'] = 'login';
    
    // Simuler les données POST
    $loginData = json_encode([
        'email' => '<EMAIL>',
        'password' => 'demo123'
    ]);
    
    // Créer un fichier temporaire pour simuler php://input
    $tempFile = tempnam(sys_get_temp_dir(), 'login_test');
    file_put_contents($tempFile, $loginData);
    
    echo "Données de login préparées\n";
    echo "Email: <EMAIL>\n";
    echo "Password: demo123\n";
    
    echo "\n✅ Tous les tests de base réussis !\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Test terminé\n";
?>
