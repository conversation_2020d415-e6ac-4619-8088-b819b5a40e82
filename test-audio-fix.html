<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Audio Fix</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', <PERSON>l, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔊 Test Audio File Fix</h1>
        <p>اختبار ملف الصوت notification.mp3</p>

        <div class="test-section">
            <h2>اختبارات الصوت</h2>
            <button class="test-button" onclick="testAudioFile()">
                1. اختبار ملف notification.mp3
            </button>
            <button class="test-button" onclick="testAudioWithFallback()">
                2. اختبار مع fallback
            </button>
            <button class="test-button" onclick="createValidAudio()">
                3. إنشاء ملف صوت صالح
            </button>
        </div>

        <div id="test-results"></div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function testAudioFile() {
            addTestResult('🧪 اختبار ملف notification.mp3...', 'info');
            
            try {
                const audio = new Audio('assets/notification.mp3');
                audio.volume = 0.3;
                audio.preload = 'metadata';
                
                audio.addEventListener('loadedmetadata', () => {
                    addTestResult('✅ ملف الصوت تم تحميله بنجاح', 'success');
                    addTestResult(`مدة الملف: ${audio.duration} ثانية`, 'info');
                });
                
                audio.addEventListener('error', (e) => {
                    addTestResult('❌ خطأ في تحميل ملف الصوت: ' + e.message, 'error');
                    console.error('Audio error:', e);
                });
                
                audio.addEventListener('canplaythrough', () => {
                    addTestResult('✅ الملف جاهز للتشغيل', 'success');
                });
                
                // Try to load
                audio.load();
                
            } catch (error) {
                addTestResult('❌ خطأ في إنشاء عنصر الصوت: ' + error.message, 'error');
            }
        }

        function testAudioWithFallback() {
            addTestResult('🧪 اختبار مع fallback...', 'info');
            
            // Test with the same logic as admin.js
            try {
                const audio = new Audio('assets/notification.mp3');
                audio.volume = 0.3;
                audio.preload = 'none';

                if (document.hasFocus()) {
                    const playPromise = audio.play();
                    if (playPromise !== undefined) {
                        playPromise.then(() => {
                            addTestResult('✅ تم تشغيل الصوت بنجاح', 'success');
                        }).catch(error => {
                            addTestResult('⚠️ فشل تشغيل الصوت (متوقع): ' + error.message, 'info');
                        });
                    }
                } else {
                    addTestResult('⚠️ الصفحة غير مركزة، لا يمكن تشغيل الصوت', 'info');
                }
            } catch (error) {
                addTestResult('❌ خطأ في اختبار fallback: ' + error.message, 'error');
            }
        }

        function createValidAudio() {
            addTestResult('🧪 إنشاء ملف صوت صالح...', 'info');
            
            // Create a simple beep sound using Web Audio API
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 800; // 800 Hz
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
                
                addTestResult('✅ تم إنشاء صوت تنبيه بديل', 'success');
                
                // Show how to replace the notification sound
                addTestResult('💡 يمكن استخدام Web Audio API بدلاً من ملف MP3', 'info');
                
            } catch (error) {
                addTestResult('❌ خطأ في إنشاء صوت بديل: ' + error.message, 'error');
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('📋 تشغيل اختبار تلقائي...', 'info');
                testAudioFile();
            }, 1000);
        });
    </script>
</body>
</html>
