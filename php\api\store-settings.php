<?php

/**
 * Store Settings API
 * Handles store configuration including branding, social media, and business details
 */

require_once '../config.php';

// Set JSON header
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? 'get';

    switch ($action) {
        case 'get':
            getStoreSettings($conn);
            break;
        case 'update':
        default:
            updateStoreSettings($conn);
            break;
    }
} catch (Exception $e) {
    error_log("Store Settings API Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}

/**
 * Get store settings
 */
function getStoreSettings($conn)
{
    try {
        // Check if store_settings table exists
        $stmt = $conn->query("SHOW TABLES LIKE 'store_settings'");
        if ($stmt->rowCount() == 0) {
            // Create table if it doesn't exist
            createStoreSettingsTable($conn);
        }

        // Get all store settings
        $stmt = $conn->prepare("SELECT setting_key, setting_value FROM store_settings WHERE is_active = 1");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Convert to associative array
        $storeSettings = [];
        foreach ($settings as $setting) {
            $storeSettings[$setting['setting_key']] = $setting['setting_value'];
        }

        // Set default values if not exists
        $defaults = [
            'store_name' => 'متجر مصعب',
            'store_description' => '',
            'store_logo' => '',
            'store_phone' => '',
            'store_email' => '',
            'store_address' => '',
            'store_city' => '',
            'store_country' => 'الجزائر',
            'facebook_url' => '',
            'instagram_url' => '',
            'twitter_url' => '',
            'youtube_url' => '',
            'whatsapp_number' => '',
            'business_hours' => '',
            'currency' => 'دج',
            'tax_rate' => '0',
            'shipping_cost' => '0',
            'free_shipping_threshold' => '0'
        ];

        // Merge with defaults
        $storeSettings = array_merge($defaults, $storeSettings);

        echo json_encode([
            'success' => true,
            'data' => $storeSettings
        ]);
    } catch (Exception $e) {
        error_log("Error getting store settings: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في جلب إعدادات المتجر'
        ]);
    }
}

/**
 * Update store settings
 */
function updateStoreSettings($conn)
{
    try {
        // Check if store_settings table exists
        $stmt = $conn->query("SHOW TABLES LIKE 'store_settings'");
        if ($stmt->rowCount() == 0) {
            createStoreSettingsTable($conn);
        }

        $conn->beginTransaction();

        // Handle logo upload if present
        $logoPath = '';
        if (isset($_FILES['store_logo']) && $_FILES['store_logo']['error'] === UPLOAD_ERR_OK) {
            $logoPath = handleLogoUpload($_FILES['store_logo']);
            if ($logoPath) {
                $_POST['store_logo'] = $logoPath;
            }
        }

        // Settings to save
        $settingsToSave = [
            'store_name',
            'store_description',
            'store_logo',
            'store_phone',
            'store_email',
            'store_address',
            'store_city',
            'store_country',
            'facebook_url',
            'instagram_url',
            'twitter_url',
            'youtube_url',
            'whatsapp_number',
            'business_hours',
            'currency',
            'tax_rate',
            'shipping_cost',
            'free_shipping_threshold'
        ];

        // Prepare upsert statement
        $stmt = $conn->prepare("
            INSERT INTO store_settings (setting_key, setting_value, setting_type, description, is_active)
            VALUES (?, ?, 'string', ?, 1)
            ON DUPLICATE KEY UPDATE
            setting_value = VALUES(setting_value),
            updated_at = CURRENT_TIMESTAMP
        ");

        $updatedSettings = [];

        foreach ($settingsToSave as $key) {
            if (isset($_POST[$key])) {
                $value = sanitizeInput($_POST[$key]);
                $description = getSettingDescription($key);

                $stmt->execute([$key, $value, $description]);
                $updatedSettings[$key] = $value;
            }
        }

        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ إعدادات المتجر بنجاح',
            'data' => $updatedSettings
        ]);
    } catch (Exception $e) {
        $conn->rollBack();
        error_log("Error updating store settings: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في حفظ إعدادات المتجر: ' . $e->getMessage()
        ]);
    }
}

/**
 * Create store settings table
 */
function createStoreSettingsTable($conn)
{
    $sql = "
        CREATE TABLE store_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_type ENUM('string', 'integer', 'float', 'boolean', 'json') DEFAULT 'string',
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_setting_key (setting_key),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $conn->exec($sql);
}

/**
 * Handle logo upload
 */
function handleLogoUpload($file)
{
    try {
        // Validate file
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            throw new Exception('نوع الملف غير مدعوم');
        }

        // Check file size (max 2MB)
        if ($file['size'] > 2 * 1024 * 1024) {
            throw new Exception('حجم الملف كبير جداً');
        }

        // Create upload directory if it doesn't exist
        $uploadDir = '../uploads/store/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'logo_' . time() . '_' . uniqid() . '.' . $extension;
        $uploadPath = $uploadDir . $filename;

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            return '/uploads/store/' . $filename;
        } else {
            throw new Exception('فشل في رفع الملف');
        }
    } catch (Exception $e) {
        error_log("Logo upload error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get setting description
 */
function getSettingDescription($key)
{
    $descriptions = [
        'store_name' => 'اسم المتجر',
        'store_description' => 'وصف المتجر',
        'store_logo' => 'شعار المتجر',
        'store_phone' => 'رقم هاتف المتجر',
        'store_email' => 'بريد المتجر الإلكتروني',
        'store_address' => 'عنوان المتجر',
        'store_city' => 'مدينة المتجر',
        'store_country' => 'بلد المتجر',
        'facebook_url' => 'رابط صفحة فيسبوك',
        'instagram_url' => 'رابط حساب إنستغرام',
        'twitter_url' => 'رابط حساب تويتر',
        'youtube_url' => 'رابط قناة يوتيوب',
        'whatsapp_number' => 'رقم الواتساب',
        'business_hours' => 'ساعات العمل',
        'currency' => 'العملة المستخدمة',
        'tax_rate' => 'معدل الضريبة',
        'shipping_cost' => 'تكلفة الشحن الافتراضية',
        'free_shipping_threshold' => 'حد الشحن المجاني'
    ];

    return $descriptions[$key] ?? $key;
}

/**
 * Sanitize input
 */
function sanitizeInput($input)
{
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}
