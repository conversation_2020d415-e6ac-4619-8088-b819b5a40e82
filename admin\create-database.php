<?php
/**
 * Database Creation and Connection Test Script
 * Automatically creates database and tests connection
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء قاعدة البيانات واختبار الاتصال</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .fix-button:hover {
            background: #0056b3;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ إنشاء قاعدة البيانات واختبار الاتصال</h1>
        <p>هذا السكريبت ينشئ قاعدة البيانات تلقائياً ويختبر الاتصال.</p>

        <?php
        try {
            // Load environment variables
            $envFile = dirname(__DIR__) . '/.env';
            if (!file_exists($envFile)) {
                throw new Exception('ملف .env غير موجود');
            }

            $envLines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($envLines as $line) {
                if (strpos(trim($line), '#') === 0) continue;
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value);
                }
            }

            $host = $_ENV['DB_HOST'] ?? 'localhost';
            $port = $_ENV['DB_PORT'] ?? '3306';
            $database = $_ENV['DB_DATABASE'] ?? '';
            $username = $_ENV['DB_USERNAME'] ?? '';
            $password = $_ENV['DB_PASSWORD'] ?? '';
            $charset = $_ENV['DB_CHARSET'] ?? 'utf8mb4';

            echo '<div class="result info">📋 محاولة الاتصال بـ: ' . $host . ':' . $port . '</div>';
            echo '<div class="result info">📋 قاعدة البيانات المطلوبة: ' . $database . '</div>';

            // Step 1: Connect without specifying database
            try {
                $dsn = "mysql:host=$host;port=$port;charset=$charset";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                ]);

                echo '<div class="result pass">✅ اتصال ناجح بخادم MySQL</div>';

                // Step 2: Check if database exists
                $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
                if ($stmt->rowCount() > 0) {
                    echo '<div class="result pass">✅ قاعدة البيانات ' . $database . ' موجودة بالفعل</div>';
                } else {
                    echo '<div class="result warning">⚠️ قاعدة البيانات ' . $database . ' غير موجودة</div>';
                    
                    // Step 3: Create database
                    echo '<div class="result info">🔧 جاري إنشاء قاعدة البيانات...</div>';
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET $charset COLLATE {$charset}_unicode_ci");
                    echo '<div class="result pass">✅ تم إنشاء قاعدة البيانات ' . $database . ' بنجاح</div>';
                }

                // Step 4: Connect to the specific database
                $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=$charset";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                ]);

                echo '<div class="result pass">✅ اتصال ناجح بقاعدة البيانات ' . $database . '</div>';

                // Step 5: Check existing tables
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo '<div class="result info">📊 عدد الجداول الموجودة: ' . count($tables) . '</div>';

                if (count($tables) > 0) {
                    echo '<div class="result info">📋 الجداول الموجودة:</div>';
                    foreach ($tables as $table) {
                        echo '<div class="result info">  • ' . $table . '</div>';
                    }
                } else {
                    echo '<div class="result warning">⚠️ لا توجد جداول في قاعدة البيانات</div>';
                    echo '<div class="result info">💡 يمكن إنشاء الجداول باستخدام سكريبت الإصلاح</div>';
                }

                // Step 6: Test basic operations
                echo '<h3>🧪 اختبار العمليات الأساسية</h3>';
                
                // Test table creation
                try {
                    $pdo->exec("CREATE TABLE IF NOT EXISTS test_connection (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        test_message VARCHAR(255),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )");
                    echo '<div class="result pass">✅ إنشاء جدول اختبار ناجح</div>';

                    // Test insert
                    $stmt = $pdo->prepare("INSERT INTO test_connection (test_message) VALUES (?)");
                    $stmt->execute(['اختبار الاتصال - ' . date('Y-m-d H:i:s')]);
                    echo '<div class="result pass">✅ إدراج بيانات ناجح</div>';

                    // Test select
                    $stmt = $pdo->query("SELECT * FROM test_connection ORDER BY id DESC LIMIT 1");
                    $result = $stmt->fetch();
                    if ($result) {
                        echo '<div class="result pass">✅ استعلام البيانات ناجح: ' . $result['test_message'] . '</div>';
                    }

                    // Clean up test table
                    $pdo->exec("DROP TABLE test_connection");
                    echo '<div class="result pass">✅ حذف جدول الاختبار ناجح</div>';

                } catch (PDOException $e) {
                    echo '<div class="result fail">❌ فشل في اختبار العمليات: ' . $e->getMessage() . '</div>';
                }

                // Step 7: Create AI settings table if needed
                echo '<h3>🤖 إنشاء جدول إعدادات الذكاء الاصطناعي</h3>';
                
                try {
                    $aiTableSQL = "
                    CREATE TABLE IF NOT EXISTS ai_settings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        setting_key VARCHAR(100) NOT NULL UNIQUE,
                        setting_value TEXT,
                        provider VARCHAR(50),
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_setting_key (setting_key),
                        INDEX idx_provider (provider)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";
                    
                    $pdo->exec($aiTableSQL);
                    echo '<div class="result pass">✅ تم إنشاء جدول ai_settings بنجاح</div>';

                    // Insert default AI settings
                    $defaultSettings = [
                        ['openai_enabled', '0', 'openai'],
                        ['anthropic_enabled', '0', 'anthropic'],
                        ['gemini_enabled', '0', 'gemini'],
                        ['default_provider', 'openai', 'general'],
                        ['rate_limit', '60', 'general'],
                        ['rate_period', '60', 'general']
                    ];

                    $insertStmt = $pdo->prepare("
                        INSERT IGNORE INTO ai_settings (setting_key, setting_value, provider) 
                        VALUES (?, ?, ?)
                    ");

                    foreach ($defaultSettings as $setting) {
                        $insertStmt->execute($setting);
                    }
                    
                    echo '<div class="result pass">✅ تم إدراج الإعدادات الافتراضية للذكاء الاصطناعي</div>';

                } catch (PDOException $e) {
                    echo '<div class="result fail">❌ فشل في إنشاء جدول AI: ' . $e->getMessage() . '</div>';
                }

                // Step 8: Create categories table if needed
                echo '<h3>📋 إنشاء جدول الفئات</h3>';
                
                try {
                    $categoriesTableSQL = "
                    CREATE TABLE IF NOT EXISTS categories (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        nom_ar VARCHAR(255) NOT NULL,
                        nom_en VARCHAR(255),
                        description_ar TEXT,
                        description_en TEXT,
                        icone VARCHAR(100) DEFAULT 'fas fa-tag',
                        couleur VARCHAR(7) DEFAULT '#007bff',
                        ordre INT DEFAULT 0,
                        actif TINYINT(1) DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_ordre (ordre),
                        INDEX idx_actif (actif)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";
                    
                    $pdo->exec($categoriesTableSQL);
                    echo '<div class="result pass">✅ تم إنشاء جدول categories بنجاح</div>';

                } catch (PDOException $e) {
                    echo '<div class="result fail">❌ فشل في إنشاء جدول الفئات: ' . $e->getMessage() . '</div>';
                }

                echo '<h3>🎉 تم الانتهاء بنجاح!</h3>';
                echo '<div class="result pass">✅ قاعدة البيانات جاهزة للاستخدام</div>';
                echo '<div class="result pass">✅ يمكنك الآن تشغيل لوحة التحكم بدون مشاكل</div>';

            } catch (PDOException $e) {
                echo '<div class="result fail">❌ فشل في الاتصال بخادم MySQL: ' . $e->getMessage() . '</div>';
                
                // Provide specific error guidance
                $errorMessage = $e->getMessage();
                if (strpos($errorMessage, 'Connection refused') !== false) {
                    echo '<div class="result warning">💡 خدمة MySQL غير متاحة. تأكد من تشغيل MySQL/MariaDB</div>';
                    echo '<div class="result info">🔧 لتشغيل MySQL على Windows:</div>';
                    echo '<div class="result info">  • XAMPP: افتح XAMPP Control Panel وشغل MySQL</div>';
                    echo '<div class="result info">  • WAMP: افتح WAMP وشغل MySQL</div>';
                    echo '<div class="result info">  • خدمة Windows: net start MySQL (كمدير)</div>';
                } elseif (strpos($errorMessage, 'Access denied') !== false) {
                    echo '<div class="result warning">💡 مشكلة في اسم المستخدم أو كلمة المرور</div>';
                    echo '<div class="result info">🔧 تحقق من بيانات الاتصال في ملف .env</div>';
                }
            }

        } catch (Exception $e) {
            echo '<div class="result fail">❌ خطأ عام: ' . $e->getMessage() . '</div>';
        }
        ?>

        <h3>🛠️ الخطوات التالية</h3>
        <a href="diagnose-database.php" class="fix-button">تشخيص مشاكل قاعدة البيانات</a>
        <a href="fix-database.php" class="fix-button">تشغيل إصلاح قاعدة البيانات الكامل</a>
        <a href="test-ai-categories-fixes.php" class="fix-button">اختبار إعدادات الذكاء الاصطناعي</a>
        <a href="index.html" class="fix-button">العودة إلى لوحة التحكم</a>

    </div>
</body>
</html>
