<?php
/**
 * Test Config and APIs
 */

echo "Testing Config and APIs\n\n";

// Test 1: Check if config can be loaded
echo "1. Testing config loading:\n";
try {
    require_once 'php/config.php';
    echo "✅ Config loaded successfully\n";
} catch (Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Test database connection
echo "\n2. Testing database connection:\n";
try {
    $pdo = getPDOConnection();
    echo "✅ Database connection successful\n";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

// Test 3: Check API file existence and basic syntax
echo "\n3. Checking API files:\n";
$apiFiles = [
    'php/api/templates.php',
    'php/api/landing-pages.php',
    'php/api/users.php',
    'php/api/subscription-limits.php'
];

foreach ($apiFiles as $file) {
    $exists = file_exists($file);
    $status = $exists ? '✅' : '❌';
    echo "{$status} {$file}";
    
    if ($exists) {
        // Check for syntax errors
        $output = [];
        $return_var = 0;
        exec("php -l \"{$file}\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo " (syntax OK)";
        } else {
            echo " (syntax ERROR: " . implode(' ', $output) . ")";
        }
    }
    echo "\n";
}

// Test 4: Test templates API functionality
echo "\n4. Testing templates API functionality:\n";
try {
    // Create a simple template manager
    class SimpleTemplateManager {
        public function getTemplates() {
            return [
                'success' => true,
                'templates' => [
                    [
                        'id' => 'book',
                        'name' => 'قالب الكتب',
                        'description' => 'قالب مخصص للكتب والمنشورات',
                        'icon' => 'fas fa-book'
                    ],
                    [
                        'id' => 'laptop',
                        'name' => 'قالب الحاسوب المحمول',
                        'description' => 'قالب مخصص للأجهزة الإلكترونية',
                        'icon' => 'fas fa-laptop'
                    ]
                ]
            ];
        }
    }
    
    $templateManager = new SimpleTemplateManager();
    $templates = $templateManager->getTemplates();
    $json = json_encode($templates, JSON_UNESCAPED_UNICODE);
    
    echo "✅ Templates functionality working\n";
    echo "JSON length: " . strlen($json) . " characters\n";
    echo "Template count: " . count($templates['templates']) . "\n";
    
} catch (Exception $e) {
    echo "❌ Templates error: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
?>
