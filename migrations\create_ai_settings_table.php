<?php

/**
 * AI Settings Database Migration Script
 * Creates and updates the ai_settings table to store provider status and configuration
 */

require_once __DIR__ . '/../php/config/config.php';
require_once __DIR__ . '/../php/security.php';

try {
    // Get database configuration
    $config = Config::getInstance();
    $dbConfig = $config->getDatabaseConfig();

    // Connect to database
    $dsn = "mysql:host={$dbConfig['host']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbConfig['name']}` CHARACTER SET {$dbConfig['charset']} COLLATE {$dbConfig['charset']}_unicode_ci;");
    $pdo->exec("USE `{$dbConfig['name']}`;");

    // Create ai_settings table
    $sql = "CREATE TABLE IF NOT EXISTS `ai_settings` (
        `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        `provider` VARCHAR(50) NOT NULL,
        `enabled` BOOLEAN DEFAULT TRUE,
        `api_key` TEXT,
        `status_message` TEXT,
        `last_tested` TIMESTAMP NULL,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY `provider_unique` (`provider`)
    ) ENGINE=InnoDB DEFAULT CHARSET={$dbConfig['charset']};";

    $pdo->exec($sql);

    // Migrate existing API keys from .env if they exist
    $aiConfig = $config->getAIConfig();
    $security = Security::getInstance();

    $providers = [
        'openai' => $aiConfig['openai_key'],
        'anthropic' => $aiConfig['anthropic_key'],
        'gemini' => $aiConfig['gemini_key']
    ];

    $stmt = $pdo->prepare("
        INSERT INTO ai_settings (provider, enabled, api_key)
        VALUES (:provider, true, :api_key)
        ON DUPLICATE KEY UPDATE
        api_key = :api_key
    ");

    foreach ($providers as $provider => $key) {
        if (!empty($key)) {
            // Encrypt API key before storing
            $encryptedKey = $security->encrypt($key);

            $stmt->execute([
                'provider' => $provider,
                'api_key' => $encryptedKey
            ]);

            // Test connection and update status
            try {
                $success = $security->testProviderConnection($provider, $key);
                $statusMessage = $success ? 'Connection successful' : 'Connection failed';
            } catch (Exception $e) {
                $statusMessage = $e->getMessage();
                $success = false;
            }

            $pdo->prepare("
                UPDATE ai_settings
                SET status_message = :status,
                    enabled = :enabled,
                    last_tested = CURRENT_TIMESTAMP
                WHERE provider = :provider
            ")->execute([
                'status' => $statusMessage,
                'enabled' => $success,
                'provider' => $provider
            ]);
        }
    }

    echo "Migration completed successfully.\n";
    exit(0);
} catch (Exception $e) {
    error_log("Migration failed: " . $e->getMessage());
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
