<?php
require_once __DIR__ . '/bootstrap.php';

class Database
{
    private static $instance = null;
    private $pdo;

    private function __construct()
    {
        $this->connect();
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function connect()
    {
        try {
            $dbConfig = Config::getDbConfig();

            $dsn = sprintf(
                "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
                $dbConfig['host'],
                $dbConfig['port'],
                $dbConfig['database']
            );

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ];

            $this->pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);

            // Set timezone to match PHP's timezone
            $timezone = date('P');
            $this->pdo->exec("SET time_zone='$timezone';");
        } catch (PDOException $e) {
            error_log('Database Connection Error: ' . $e->getMessage());
            throw new Exception('Database connection failed. Please check your configuration.');
        }
    }

    public function getPDO()
    {
        return $this->pdo;
    }

    // Proxy PDO methods for convenience
    public function prepare($statement, $options = [])
    {
        return $this->pdo->prepare($statement, $options);
    }

    public function query($statement)
    {
        return $this->pdo->query($statement);
    }

    public function exec($statement)
    {
        return $this->pdo->exec($statement);
    }

    public function lastInsertId($name = null)
    {
        return $this->pdo->lastInsertId($name);
    }

    public function beginTransaction()
    {
        return $this->pdo->beginTransaction();
    }

    public function commit()
    {
        return $this->pdo->commit();
    }

    public function rollBack()
    {
        return $this->pdo->rollBack();
    }
}

// Create helper functions for backward compatibility
function getDatabaseConnection()
{
    return Database::getInstance()->getPDO();
}

// Initialize global database connection for legacy code
global $pdo;
try {
    $pdo = getDatabaseConnection();
} catch (Exception $e) {
    if (Config::get('APP_DEBUG', false)) {
        die('Database Error: ' . $e->getMessage());
    } else {
        die('A database error occurred. Please try again later.');
    }
}
