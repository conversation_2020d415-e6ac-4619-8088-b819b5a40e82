<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Notification Sound</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', <PERSON>l, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 Create Notification Sound</h1>
        <p>إنشاء ملف صوت تنبيه صالح لاستبدال notification.mp3</p>

        <div class="section">
            <h2>إنشاء أصوات التنبيه</h2>
            <button class="button" onclick="createSimpleBeep()">
                1. إنشاء صوت بسيط
            </button>
            <button class="button" onclick="createPleasantChime()">
                2. إنشاء صوت جميل
            </button>
            <button class="button" onclick="createNotificationTone()">
                3. إنشاء نغمة تنبيه
            </button>
            <button class="button" onclick="generateDataURL()">
                4. إنشاء Data URL للاستخدام
            </button>
        </div>

        <div id="results"></div>
        <div id="output"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function logOutput(message) {
            const output = document.getElementById('output');
            output.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        function createSimpleBeep() {
            addResult('🧪 إنشاء صوت بسيط...', 'info');
            logOutput('Creating simple beep sound...');
            
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 800; // 800 Hz
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
                
                addResult('✅ تم إنشاء صوت بسيط بنجاح', 'success');
                logOutput('Simple beep created and played');
                
            } catch (error) {
                addResult('❌ فشل في إنشاء الصوت: ' + error.message, 'error');
                logOutput(`Error: ${error.message}`);
            }
        }

        function createPleasantChime() {
            addResult('🧪 إنشاء صوت جميل...', 'info');
            logOutput('Creating pleasant chime sound...');
            
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                // Create multiple oscillators for a chord
                const frequencies = [523.25, 659.25, 783.99]; // C5, E5, G5
                const oscillators = [];
                const gainNode = audioContext.createGain();
                
                gainNode.connect(audioContext.destination);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.8);
                
                frequencies.forEach(freq => {
                    const osc = audioContext.createOscillator();
                    const oscGain = audioContext.createGain();
                    
                    osc.connect(oscGain);
                    oscGain.connect(gainNode);
                    
                    osc.frequency.value = freq;
                    osc.type = 'sine';
                    oscGain.gain.setValueAtTime(0.3, audioContext.currentTime);
                    
                    osc.start(audioContext.currentTime);
                    osc.stop(audioContext.currentTime + 0.8);
                    
                    oscillators.push(osc);
                });
                
                addResult('✅ تم إنشاء صوت جميل بنجاح', 'success');
                logOutput('Pleasant chime created and played');
                
            } catch (error) {
                addResult('❌ فشل في إنشاء الصوت: ' + error.message, 'error');
                logOutput(`Error: ${error.message}`);
            }
        }

        function createNotificationTone() {
            addResult('🧪 إنشاء نغمة تنبيه...', 'info');
            logOutput('Creating notification tone...');
            
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                // Create a two-tone notification
                const createTone = (frequency, startTime, duration) => {
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);
                    
                    oscillator.frequency.value = frequency;
                    oscillator.type = 'sine';
                    
                    gainNode.gain.setValueAtTime(0, startTime);
                    gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);
                    
                    oscillator.start(startTime);
                    oscillator.stop(startTime + duration);
                };
                
                // Two-tone notification: high-low
                createTone(880, audioContext.currentTime, 0.15); // A5
                createTone(660, audioContext.currentTime + 0.2, 0.15); // E5
                
                addResult('✅ تم إنشاء نغمة تنبيه بنجاح', 'success');
                logOutput('Notification tone created and played');
                
            } catch (error) {
                addResult('❌ فشل في إنشاء النغمة: ' + error.message, 'error');
                logOutput(`Error: ${error.message}`);
            }
        }

        function generateDataURL() {
            addResult('🧪 إنشاء Data URL...', 'info');
            logOutput('Generating data URL for notification sound...');
            
            // Create a simple WAV file data URL
            const sampleRate = 44100;
            const duration = 0.2; // 200ms
            const frequency = 800; // 800 Hz
            const samples = sampleRate * duration;
            
            // Create WAV file buffer
            const buffer = new ArrayBuffer(44 + samples * 2);
            const view = new DataView(buffer);
            
            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + samples * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, samples * 2, true);
            
            // Generate sine wave
            for (let i = 0; i < samples; i++) {
                const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate);
                const amplitude = 0.3 * Math.exp(-i / (sampleRate * 0.1)); // Fade out
                const value = Math.round(sample * amplitude * 32767);
                view.setInt16(44 + i * 2, value, true);
            }
            
            // Convert to base64
            const bytes = new Uint8Array(buffer);
            let binary = '';
            for (let i = 0; i < bytes.length; i++) {
                binary += String.fromCharCode(bytes[i]);
            }
            const base64 = btoa(binary);
            const dataURL = `data:audio/wav;base64,${base64}`;
            
            logOutput('Generated WAV data URL:');
            logOutput(dataURL.substring(0, 100) + '...');
            logOutput(`Total length: ${dataURL.length} characters`);
            
            // Test the generated audio
            try {
                const audio = new Audio(dataURL);
                audio.volume = 0.3;
                audio.play().then(() => {
                    addResult('✅ تم إنشاء Data URL وتشغيله بنجاح', 'success');
                    logOutput('Data URL audio played successfully');
                }).catch(error => {
                    addResult('⚠️ تم إنشاء Data URL لكن فشل التشغيل', 'info');
                    logOutput(`Playback failed: ${error.message}`);
                });
            } catch (error) {
                addResult('❌ فشل في إنشاء Data URL: ' + error.message, 'error');
                logOutput(`Error: ${error.message}`);
            }
            
            // Provide code for integration
            logOutput('\n--- Code for integration ---');
            logOutput('Replace the MP3 file with this data URL:');
            logOutput(`const audio = new Audio('${dataURL.substring(0, 50)}...');`);
        }

        // Auto-run simple test
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('📋 تشغيل اختبار تلقائي...', 'info');
                createSimpleBeep();
            }, 1000);
        });
    </script>
</body>
</html>
