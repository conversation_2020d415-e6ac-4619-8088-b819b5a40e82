// Global error handler for selection-related errors
window.addEventListener('error', function(event) {
    if (event.error && event.error.message) {
        if (event.error.message.includes('rangeCount') || event.error.message.includes('mozInputSource')) {
            event.preventDefault(); // Prevent the error from propagating
            console.warn('Selection error prevented:', event.error.message);
            return true;
        }
    }
});

// Enhanced console warning handler
const originalConsoleWarn = console.warn;
console.warn = function(message) {
    if (message && (message.includes('mozInputSource') || message.includes('rangeCount'))) {
        return; // Suppress known warnings
    }
    originalConsoleWarn.apply(console, arguments);
};

// TinyMCE Configuration - Self-hosted version without API key
window.addEventListener('load', function() {
    initTinyMCE();
});

function initTinyMCE() {
    console.log('TinyMCE config initialization started');
    
    // Check if TinyMCE is loaded with retry limit
    let retryCount = 0;
    const maxRetries = 5;
    
    function tryInit() {
        if (typeof tinymce === 'undefined') {
            if (retryCount < maxRetries) {
                console.warn(`TinyMCE not loaded, retry ${retryCount + 1}/${maxRetries} in 500ms...`);
                retryCount++;
                setTimeout(tryInit, 500);
                return;
            } else {
                console.error('TinyMCE failed to load after maximum retries');
                fallbackToTextareas();
                return;
            }
        }
        initTinyMCECore();
    }
    
    tryInit();
}

function fallbackToTextareas() {
    console.log('Falling back to regular textareas...');
    document.querySelectorAll('textarea.tinymce').forEach(textarea => {
        textarea.style.display = 'block';
        textarea.style.minHeight = '400px';
        textarea.style.direction = 'rtl';
        textarea.style.textAlign = 'right';
        textarea.style.fontFamily = 'Noto Sans Arabic, sans-serif';
        textarea.style.border = '1px solid #ccc';
        textarea.style.padding = '10px';
        textarea.style.margin = '10px 0';
        textarea.style.width = '100%';
        textarea.style.boxSizing = 'border-box';
    });
}

function initTinyMCECore() {

    // Remove any existing TinyMCE instances first
    tinymce.remove();

    console.log('Initializing TinyMCE with configuration...');
    // Remove any existing instances and clear the undo stack
    tinymce.remove();
    tinymce.EditorManager.execCommand('mceRemoveEditor', true);
    
    tinymce.init({
        selector: 'textarea.tinymce',
        // No license_key needed for self-hosted version
        language: 'ar',
        language_url: '/admin/js/langs/ar.js', // Use local language file
        directionality: 'rtl',
        content_css: [
            'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap'
        ],
        font_family_formats: 'Noto Sans Arabic=Noto Sans Arabic,sans-serif',
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
            'preview', 'anchor', 'searchreplace', 'visualblocks', 'code',
            'fullscreen', 'insertdatetime', 'media', 'table', 'wordcount'
        ],
        toolbar: 'undo redo | formatselect | bold italic | alignright aligncenter alignleft | bullist numlist outdent indent | link image | preview fullscreen',
        promotion: false,
        branding: false,
        min_height: 400,
        resize: true,
        paste_data_images: true,
        convert_urls: false,
        relative_urls: false,
        remove_script_host: false,
        readonly: false, // Explicitly set to false
        menubar: false,
        statusbar: true,
        content_style: 'body { font-family: "Noto Sans Arabic", sans-serif; direction: rtl; text-align: right; }',
        setup: function(editor) {
            // Enhanced error handling for selection operations
            editor.on('SelectionChange', function(e) {
                try {
                    const selection = editor.selection.getSel();
                    if (!selection) {
                        console.warn('No selection available');
                        return;
                    }
                    // Validate selection range
                    if (selection.rangeCount > 0) {
                        const range = selection.getRangeAt(0);
                        if (!range || !range.startContainer) {
                            console.warn('Invalid selection range');
                            return;
                        }
                    }
                } catch (error) {
                    console.warn('Selection change error:', error);
                    return;
                }
            });
            
            // Handle initialization errors
            editor.on('init', function(e) {
                try {

                    console.log('TinyMCE initialized for:', editor.id);
                    if (editor.getContainer()) {
                        editor.getContainer().style.direction = 'rtl';
                    } else {
                        console.warn('Editor container not found for:', editor.id);
                    }
                    
                    // Force editable mode with error handling
                    try {
                        editor.mode.set('design');
                    } catch (error) {
                        console.warn('Error setting editor mode:', error);
                    }
                    
                    // Refresh content with error handling
                    try {
                        const content = editor.getContent();
                        editor.setContent(content);
                    } catch (error) {
                        console.warn('Error refreshing editor content:', error);
                    }
                } catch (error) {
                    console.error('Editor initialization error:', error);
                    fallbackToTextareas();
                }
            });
        },
        init_instance_callback: function(editor) {
            try {
                console.log('TinyMCE ready for:', editor.id);
                
                // Ensure editor is fully editable with error handling
                try {
                    editor.mode.set('design');
                } catch (modeError) {
                    console.warn('Error setting editor mode:', modeError);
                }
                
                // Remove any readonly attributes with error handling
                try {
                    const editorBody = editor.getBody();
                    if (editorBody) {
                        editorBody.removeAttribute('readonly');
                        editorBody.contentEditable = true;
                        
                        // Additional initialization checks
                        if (!editorBody.isContentEditable) {
                            console.warn('Editor body not editable after initialization');
                            editorBody.contentEditable = true;
                        }
                    } else {
                        console.warn('Editor body not found for:', editor.id);
                    }
                } catch (bodyError) {
                    console.error('Error configuring editor body:', bodyError);
                }
                
                // Verify editor state
                if (!editor.initialized) {
                    console.warn('Editor not fully initialized:', editor.id);
                    editor.initialized = true;
                }
            } catch (error) {
                console.error('Editor instance callback error:', error);
                fallbackToTextareas();
            }
        }
    }).then(function() {
        console.log('TinyMCE initialization successful');
        
        // Verify all editors are properly initialized with error handling
        const textareas = document.querySelectorAll('textarea.tinymce');
        if (textareas.length === 0) {
            console.warn('No TinyMCE textareas found on page');
            return;
        }
        
        let initializationErrors = 0;
        textareas.forEach(function(textarea) {
            try {
                if (!textarea.id) {
                    console.warn('Textarea missing ID attribute');
                    return;
                }
                
                const editor = tinymce.get(textarea.id);
                if (editor && editor.initialized) {
                    console.log('Editor initialized successfully:', textarea.id);
                    
                    // Verify editor functionality
                    try {
                        editor.setContent(editor.getContent());
                        const body = editor.getBody();
                        if (body && body.contentEditable !== 'true') {
                            body.contentEditable = true;
                        }
                    } catch (verifyError) {
                        console.warn('Editor verification failed:', verifyError);
                        initializationErrors++;
                    }
                } else {
                    console.warn('Editor not properly initialized:', textarea.id);
                    initializationErrors++;
                    
                    // Try to reinitialize with error handling
                    try {
                        if (tinymce.activeEditor && tinymce.activeEditor.settings) {
                            tinymce.init({
                                selector: '#' + textarea.id,
                                ...tinymce.activeEditor.settings
                            }).catch(function(reinitError) {
                                console.error('Editor reinitialization failed:', reinitError);
                                fallbackToTextareas();
                            });
                        } else {
                            console.error('No active editor settings available for reinitialization');
                            fallbackToTextareas();
                        }
                    } catch (reinitError) {
                        console.error('Error during editor reinitialization:', reinitError);
                        fallbackToTextareas();
                    }
                }
            } catch (error) {
                console.error('Error checking editor initialization:', error);
                initializationErrors++;
            }
        });
        
        if (initializationErrors > 0) {
            console.warn(`${initializationErrors} editor(s) failed to initialize properly`);
        }
    }).catch(function(error) {
        console.error('TinyMCE initialization failed:', error);
        // Fallback to regular textareas if TinyMCE fails
        document.querySelectorAll('textarea.tinymce').forEach(textarea => {
            textarea.style.display = 'block';
            textarea.style.minHeight = '400px';
            textarea.style.border = '1px solid #ccc';
            textarea.style.padding = '10px';
        });
    });
}
