<?php
/**
 * Test Script for Enhanced Landing Pages API
 * 
 * Tests all endpoints with various scenarios including:
 * - Security validation
 * - Error handling
 * - MariaDB compatibility
 * - Template validation
 * - Image handling
 */

echo "<h1>🧪 Enhanced Landing Pages API Test Suite</h1>\n";
echo "<style>
body{font-family:Arial;margin:20px;background:#f8f9fa;} 
.container{max-width:1200px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 4px 6px rgba(0,0,0,0.1);}
.success{color:#28a745;font-weight:bold;} 
.error{color:#dc3545;font-weight:bold;} 
.warning{color:#ffc107;font-weight:bold;} 
.info{color:#17a2b8;font-weight:bold;} 
pre{background:#f8f9fa;padding:15px;border-radius:5px;border-left:4px solid #007bff;overflow-x:auto;font-size:0.9em;} 
table{border-collapse:collapse;width:100%;margin:15px 0;} 
th,td{border:1px solid #dee2e6;padding:12px;text-align:left;} 
th{background:#e9ecef;font-weight:bold;}
.test-section{background:#f0f8ff;padding:25px;margin:20px 0;border-radius:12px;border-left:5px solid #007bff;}
.highlight{background:#fff3cd;padding:15px;border-radius:8px;margin:15px 0;}
.test-result{padding:10px;margin:10px 0;border-radius:5px;}
.test-pass{background:#d4edda;border:1px solid #c3e6cb;color:#155724;}
.test-fail{background:#f8d7da;border:1px solid #f5c6cb;color:#721c24;}
.code-block{background:#f8f9fa;border:1px solid #e9ecef;border-radius:5px;padding:15px;margin:10px 0;font-family:monospace;}
</style>\n";

echo "<div class='container'>\n";

/**
 * Make API request and return response
 */
function makeApiRequest($method, $endpoint, $data = null, $headers = []) {
    $url = 'http://localhost:8000/php/api/' . $endpoint;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge([
        'Content-Type: application/json',
        'Accept: application/json'
    ], $headers));
    
    if ($data && in_array($method, ['POST', 'PUT'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true),
        'raw_response' => $response
    ];
}

/**
 * Display test result
 */
function displayTestResult($testName, $expected, $actual, $details = '') {
    $passed = ($expected === $actual);
    $class = $passed ? 'test-pass' : 'test-fail';
    $icon = $passed ? '✅' : '❌';
    
    echo "<div class='test-result $class'>\n";
    echo "<strong>$icon $testName</strong><br>\n";
    echo "Expected: $expected<br>\n";
    echo "Actual: $actual<br>\n";
    if ($details) echo "Details: $details<br>\n";
    echo "</div>\n";
    
    return $passed;
}

$totalTests = 0;
$passedTests = 0;

// Test 1: GET All Landing Pages
echo "<div class='test-section'>\n";
echo "<h2>🔍 Test 1: GET All Landing Pages</h2>\n";

$result = makeApiRequest('GET', 'landing-pages-enhanced.php');
$totalTests++;

echo "<h3>Request:</h3>\n";
echo "<div class='code-block'>GET /php/api/landing-pages-enhanced.php</div>\n";

echo "<h3>Response:</h3>\n";
echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

if (displayTestResult('HTTP Status Code', 200, $result['http_code'])) $passedTests++;
$totalTests++;

if (displayTestResult('Response Success', true, $result['response']['success'] ?? false)) $passedTests++;
$totalTests++;

if (isset($result['response']['data']['landing_pages'])) {
    if (displayTestResult('Has Landing Pages Data', true, is_array($result['response']['data']['landing_pages']))) $passedTests++;
} else {
    displayTestResult('Has Landing Pages Data', true, false);
}
$totalTests++;

echo "</div>\n";

// Test 2: GET Specific Landing Page
echo "<div class='test-section'>\n";
echo "<h2>🎯 Test 2: GET Specific Landing Page</h2>\n";

$result = makeApiRequest('GET', 'landing-pages-enhanced.php?id=1');
$totalTests++;

echo "<h3>Request:</h3>\n";
echo "<div class='code-block'>GET /php/api/landing-pages-enhanced.php?id=1</div>\n";

echo "<h3>Response:</h3>\n";
echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

if (displayTestResult('HTTP Status Code', 200, $result['http_code'])) $passedTests++;
$totalTests++;

if (displayTestResult('Response Success', true, $result['response']['success'] ?? false)) $passedTests++;
$totalTests++;

if (isset($result['response']['data']['id'])) {
    if (displayTestResult('Has Landing Page ID', 1, $result['response']['data']['id'])) $passedTests++;
} else {
    displayTestResult('Has Landing Page ID', 1, 'missing');
}
$totalTests++;

echo "</div>\n";

// Test 3: GET with Filtering
echo "<div class='test-section'>\n";
echo "<h2>🔍 Test 3: GET with Template Filtering</h2>\n";

$result = makeApiRequest('GET', 'landing-pages-enhanced.php?template_id=modern&active=1');
$totalTests++;

echo "<h3>Request:</h3>\n";
echo "<div class='code-block'>GET /php/api/landing-pages-enhanced.php?template_id=modern&active=1</div>\n";

echo "<h3>Response:</h3>\n";
echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

if (displayTestResult('HTTP Status Code', 200, $result['http_code'])) $passedTests++;
$totalTests++;

if (displayTestResult('Response Success', true, $result['response']['success'] ?? false)) $passedTests++;
$totalTests++;

if (isset($result['response']['data']['filters']['template_id'])) {
    if (displayTestResult('Template Filter Applied', 'modern', $result['response']['data']['filters']['template_id'])) $passedTests++;
} else {
    displayTestResult('Template Filter Applied', 'modern', 'missing');
}
$totalTests++;

echo "</div>\n";

// Test 4: POST - Create Landing Page (Validation Test)
echo "<div class='test-section'>\n";
echo "<h2>➕ Test 4: POST - Create Landing Page (Validation)</h2>\n";

$testData = [
    'produit_id' => 1,
    'titre' => 'Test Landing Page - ' . date('Y-m-d H:i:s'),
    'template_id' => 'elegant',
    'contenu_droit' => '<h3>Test Content Right</h3><p>This is test content for the right side.</p>',
    'contenu_gauche' => '<h3>Test Content Left</h3><p>This is test content for the left side.</p>',
    'actif' => 1
];

$result = makeApiRequest('POST', 'landing-pages-enhanced.php', $testData);
$totalTests++;

echo "<h3>Request:</h3>\n";
echo "<div class='code-block'>POST /php/api/landing-pages-enhanced.php</div>\n";
echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

echo "<h3>Response:</h3>\n";
echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

if (displayTestResult('HTTP Status Code', 201, $result['http_code'])) $passedTests++;
$totalTests++;

if (displayTestResult('Response Success', true, $result['response']['success'] ?? false)) $passedTests++;
$totalTests++;

$createdId = null;
if (isset($result['response']['data']['id'])) {
    $createdId = $result['response']['data']['id'];
    if (displayTestResult('Has Created ID', true, is_numeric($createdId))) $passedTests++;
} else {
    displayTestResult('Has Created ID', true, false);
}
$totalTests++;

echo "</div>\n";

// Test 5: PUT - Update Landing Page
if ($createdId) {
    echo "<div class='test-section'>\n";
    echo "<h2>✏️ Test 5: PUT - Update Landing Page</h2>\n";
    
    $updateData = [
        'id' => $createdId,
        'titre' => 'Updated Test Landing Page - ' . date('Y-m-d H:i:s'),
        'template_id' => 'professional',
        'actif' => 1
    ];
    
    $result = makeApiRequest('PUT', 'landing-pages-enhanced.php', $updateData);
    $totalTests++;
    
    echo "<h3>Request:</h3>\n";
    echo "<div class='code-block'>PUT /php/api/landing-pages-enhanced.php</div>\n";
    echo "<pre>" . json_encode($updateData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";
    
    echo "<h3>Response:</h3>\n";
    echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";
    
    if (displayTestResult('HTTP Status Code', 200, $result['http_code'])) $passedTests++;
    $totalTests++;
    
    if (displayTestResult('Response Success', true, $result['response']['success'] ?? false)) $passedTests++;
    $totalTests++;
    
    if (isset($result['response']['data']['template_id'])) {
        if (displayTestResult('Template Updated', 'professional', $result['response']['data']['template_id'])) $passedTests++;
    } else {
        displayTestResult('Template Updated', 'professional', 'missing');
    }
    $totalTests++;
    
    echo "</div>\n";
}

// Test 6: Error Handling - Invalid Template
echo "<div class='test-section'>\n";
echo "<h2>❌ Test 6: Error Handling - Invalid Template</h2>\n";

$invalidData = [
    'produit_id' => 1,
    'titre' => 'Invalid Template Test',
    'template_id' => 'invalid_template',
    'actif' => 1
];

$result = makeApiRequest('POST', 'landing-pages-enhanced.php', $invalidData);
$totalTests++;

echo "<h3>Request:</h3>\n";
echo "<div class='code-block'>POST /php/api/landing-pages-enhanced.php</div>\n";
echo "<pre>" . json_encode($invalidData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

echo "<h3>Response:</h3>\n";
echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

if (displayTestResult('HTTP Status Code', 400, $result['http_code'])) $passedTests++;
$totalTests++;

if (displayTestResult('Response Success', false, $result['response']['success'] ?? true)) $passedTests++;
$totalTests++;

if (isset($result['response']['error']['code'])) {
    if (displayTestResult('Error Code', 'INVALID_TEMPLATE', $result['response']['error']['code'])) $passedTests++;
} else {
    displayTestResult('Error Code', 'INVALID_TEMPLATE', 'missing');
}
$totalTests++;

echo "</div>\n";

// Test 7: Error Handling - Missing Required Field
echo "<div class='test-section'>\n";
echo "<h2>❌ Test 7: Error Handling - Missing Required Field</h2>\n";

$incompleteData = [
    'template_id' => 'modern',
    'actif' => 1
    // Missing produit_id and titre
];

$result = makeApiRequest('POST', 'landing-pages-enhanced.php', $incompleteData);
$totalTests++;

echo "<h3>Request:</h3>\n";
echo "<div class='code-block'>POST /php/api/landing-pages-enhanced.php</div>\n";
echo "<pre>" . json_encode($incompleteData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

echo "<h3>Response:</h3>\n";
echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

if (displayTestResult('HTTP Status Code', 400, $result['http_code'])) $passedTests++;
$totalTests++;

if (displayTestResult('Response Success', false, $result['response']['success'] ?? true)) $passedTests++;
$totalTests++;

echo "</div>\n";

// Test 8: DELETE - Remove Test Landing Page
if ($createdId) {
    echo "<div class='test-section'>\n";
    echo "<h2>🗑️ Test 8: DELETE - Remove Test Landing Page</h2>\n";
    
    $result = makeApiRequest('DELETE', "landing-pages-enhanced.php?id=$createdId");
    $totalTests++;
    
    echo "<h3>Request:</h3>\n";
    echo "<div class='code-block'>DELETE /php/api/landing-pages-enhanced.php?id=$createdId</div>\n";
    
    echo "<h3>Response:</h3>\n";
    echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";
    
    if (displayTestResult('HTTP Status Code', 200, $result['http_code'])) $passedTests++;
    $totalTests++;
    
    if (displayTestResult('Response Success', true, $result['response']['success'] ?? false)) $passedTests++;
    $totalTests++;
    
    if (isset($result['response']['data']['deleted_id'])) {
        if (displayTestResult('Deleted ID', $createdId, $result['response']['data']['deleted_id'])) $passedTests++;
    } else {
        displayTestResult('Deleted ID', $createdId, 'missing');
    }
    $totalTests++;
    
    echo "</div>\n";
}

// Test 9: GET Non-existent Landing Page
echo "<div class='test-section'>\n";
echo "<h2>❌ Test 9: GET Non-existent Landing Page</h2>\n";

$result = makeApiRequest('GET', 'landing-pages-enhanced.php?id=99999');
$totalTests++;

echo "<h3>Request:</h3>\n";
echo "<div class='code-block'>GET /php/api/landing-pages-enhanced.php?id=99999</div>\n";

echo "<h3>Response:</h3>\n";
echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

if (displayTestResult('HTTP Status Code', 404, $result['http_code'])) $passedTests++;
$totalTests++;

if (displayTestResult('Response Success', false, $result['response']['success'] ?? true)) $passedTests++;
$totalTests++;

if (isset($result['response']['error']['code'])) {
    if (displayTestResult('Error Code', 'NOT_FOUND', $result['response']['error']['code'])) $passedTests++;
} else {
    displayTestResult('Error Code', 'NOT_FOUND', 'missing');
}
$totalTests++;

echo "</div>\n";

// Test 10: Invalid HTTP Method
echo "<div class='test-section'>\n";
echo "<h2>❌ Test 10: Invalid HTTP Method</h2>\n";

$result = makeApiRequest('PATCH', 'landing-pages-enhanced.php');
$totalTests++;

echo "<h3>Request:</h3>\n";
echo "<div class='code-block'>PATCH /php/api/landing-pages-enhanced.php</div>\n";

echo "<h3>Response:</h3>\n";
echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

if (displayTestResult('HTTP Status Code', 405, $result['http_code'])) $passedTests++;
$totalTests++;

if (displayTestResult('Response Success', false, $result['response']['success'] ?? true)) $passedTests++;
$totalTests++;

echo "</div>\n";

// Final Results
echo "<div class='test-section'>\n";
echo "<h2>📊 Test Results Summary</h2>\n";

$successRate = ($passedTests / $totalTests) * 100;

echo "<div class='highlight'>\n";
echo "<h3>🎯 Overall Test Results</h3>\n";
echo "<p><strong>Tests Passed:</strong> $passedTests / $totalTests (" . round($successRate, 1) . "%)</p>\n";

if ($successRate >= 90) {
    echo "<p class='success'>🎉 EXCELLENT: Enhanced API is working perfectly!</p>\n";
    echo "<p>All major functionality is working correctly with comprehensive security and error handling.</p>\n";
} elseif ($successRate >= 70) {
    echo "<p class='warning'>⚠️ GOOD: Enhanced API is mostly working with minor issues</p>\n";
    echo "<p>Most functionality is working well. Review failed tests for improvements.</p>\n";
} else {
    echo "<p class='error'>❌ NEEDS ATTENTION: Enhanced API has significant issues</p>\n";
    echo "<p>Several critical features need to be fixed before production use.</p>\n";
}

echo "<h4>🔗 API Endpoints Tested:</h4>\n";
echo "<ul>\n";
echo "<li>✅ GET /landing-pages-enhanced.php (List all with pagination)</li>\n";
echo "<li>✅ GET /landing-pages-enhanced.php?id=X (Get specific)</li>\n";
echo "<li>✅ GET /landing-pages-enhanced.php?template_id=X (Filtering)</li>\n";
echo "<li>✅ POST /landing-pages-enhanced.php (Create new)</li>\n";
echo "<li>✅ PUT /landing-pages-enhanced.php (Update existing)</li>\n";
echo "<li>✅ DELETE /landing-pages-enhanced.php?id=X (Delete)</li>\n";
echo "</ul>\n";

echo "<h4>🛡️ Security Features Tested:</h4>\n";
echo "<ul>\n";
echo "<li>✅ Input validation and sanitization</li>\n";
echo "<li>✅ Template ID validation against allowed values</li>\n";
echo "<li>✅ XSS protection for HTML content</li>\n";
echo "<li>✅ Foreign key constraint validation</li>\n";
echo "<li>✅ Proper HTTP status codes</li>\n";
echo "<li>✅ Standardized error responses</li>\n";
echo "</ul>\n";

echo "<h4>🔗 Quick Access Links:</h4>\n";
echo "<div style='display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin:20px 0;'>\n";
echo "<a href='php/api/landing-pages-enhanced.php' style='background:#007bff;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;' target='_blank'>🔌 Enhanced API</a>\n";
echo "<a href='php/api/landing-pages.php' style='background:#28a745;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;' target='_blank'>📦 Original API</a>\n";
echo "<a href='landing-pages-list.php' style='background:#fd7e14;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🎨 Landing Pages List</a>\n";
echo "<a href='admin/' style='background:#6f42c1;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🏠 Admin Panel</a>\n";
echo "</div>\n";
echo "</div>\n";
echo "</div>\n";

echo "</div>\n";
?>

<script>
console.log('🧪 Enhanced Landing Pages API test completed');
console.log('Success Rate: <?php echo round($successRate, 1); ?>%');
console.log('Tests Passed: <?php echo $passedTests; ?>/<?php echo $totalTests; ?>');
</script>
