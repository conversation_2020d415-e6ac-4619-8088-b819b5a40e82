<?php
require_once 'php/config.php';

// Get landing page ID from URL
$url = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
preg_match('/product-(\d+)-([\w]+)/', $url, $matches);

if (empty($matches)) {
    header('Location: /error.html');
    exit;
}

$productId = $matches[1];

try {
    // Get landing page data
    $stmt = $conn->prepare(
        "SELECT lp.*, p.titre as product_title, p.prix, p.image_url as product_image 
         FROM landing_pages lp 
         JOIN produits p ON lp.produit_id = p.id 
         WHERE p.id = ? AND lp.lien_url LIKE ?"
    );
    $stmt->execute([$productId, '%' . $matches[0] . '%']);
    $page = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$page) {
        header('Location: /error.html');
        exit;
    }

    // Get landing page images
    $stmt = $conn->prepare(
        "SELECT image_url 
         FROM landing_page_images 
         WHERE landing_page_id = ? 
         ORDER BY ordre"
    );
    $stmt->execute([$page['id']]);
    $images = $stmt->fetchAll(PDO::FETCH_COLUMN);

} catch (PDOException $e) {
    header('Location: /error.html');
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page['titre']); ?></title>
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($page['titre']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars(strip_tags($page['contenu_droit'])); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($page['product_image']); ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars('https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>">
    
    <link rel="stylesheet" href="css/style.css">
    <style>
        .landing-page {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .product-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .product-title {
            font-size: 2.5rem;
            color: #1e293b;
            margin-bottom: 20px;
        }

        .product-price {
            font-size: 1.5rem;
            color: #3b82f6;
            font-weight: bold;
        }

        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .gallery img {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
        }

        .content-block {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: transform 0.2s;
            margin-top: 20px;
        }

        .cta-button:hover {
            transform: translateY(-2px);
        }

        .share-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 40px;
        }

        .share-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: transform 0.2s;
        }

        .share-button:hover {
            transform: translateY(-2px);
        }

        .facebook { background: #1877f2; }
        .twitter { background: #1da1f2; }
        .whatsapp { background: #25d366; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="landing-page">
        <div class="product-header">
            <h1 class="product-title"><?php echo htmlspecialchars($page['titre']); ?></h1>
            <div class="product-price"><?php echo number_format($page['prix'], 2); ?> دج</div>
        </div>

        <?php if (!empty($images)): ?>
        <div class="gallery">
            <?php foreach ($images as $image): ?>
            <img src="<?php echo htmlspecialchars($image); ?>" alt="<?php echo htmlspecialchars($page['titre']); ?>">
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <div class="content-grid">
            <div class="content-block right-content">
                <?php echo $page['contenu_droit']; ?>
            </div>
            <div class="content-block left-content">
                <?php echo $page['contenu_gauche']; ?>
            </div>
        </div>

        <div style="text-align: center;">
            <a href="/cart.html?product=<?php echo $productId; ?>" class="cta-button">أضف إلى السلة</a>
        </div>

        <div class="share-buttons">
            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode('https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>" 
               target="_blank" class="share-button facebook">
                <i class="fab fa-facebook-f"></i>
            </a>
            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode('https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>&text=<?php echo urlencode($page['titre']); ?>" 
               target="_blank" class="share-button twitter">
                <i class="fab fa-twitter"></i>
            </a>
            <a href="https://api.whatsapp.com/send?text=<?php echo urlencode($page['titre'] . ' - https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>" 
               target="_blank" class="share-button whatsapp">
                <i class="fab fa-whatsapp"></i>
            </a>
        </div>
    </div>

    <script>
        // Add smooth scrolling for gallery images
        document.querySelectorAll('.gallery img').forEach(img => {
            img.addEventListener('click', () => {
                img.scrollIntoView({ behavior: 'smooth', block: 'center' });
            });
        });
    </script>
</body>
</html>