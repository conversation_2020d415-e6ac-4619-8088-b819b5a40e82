<?php
require_once '../debug.php';
require_once '../config.php';
require_once '../security.php';

class AIConfig
{
    private static $config = null;
    private static $providers = ['openai', 'anthropic', 'gemini'];

    public static function load()
    {
        if (self::$config === null) {
            self::$config = [
                'openai' => [
                    'api_key' => getenv('OPENAI_API_KEY'),
                    'enabled' => true,
                    'models' => ['gpt-3.5-turbo', 'gpt-4']
                ],
                'anthropic' => [
                    'api_key' => getenv('ANTHROPIC_API_KEY'),
                    'enabled' => false,
                    'models' => ['claude-2', 'claude-instant']
                ],
                'gemini' => [
                    'api_key' => getenv('GEMINI_API_KEY'),
                    'enabled' => false,
                    'models' => ['gemini-pro']
                ]
            ];
        }
        return self::$config;
    }

    public static function isAvailable()
    {
        $config = self::load();
        foreach (self::$providers as $provider) {
            if (!empty($config[$provider]['api_key']) && $config[$provider]['enabled']) {
                return true;
            }
        }
        return false;
    }

    public static function validateApiKey($provider, $key)
    {
        switch ($provider) {
            case 'openai':
                return preg_match('/^sk-[a-zA-Z0-9]{32,}$/', $key);
            case 'anthropic':
                return preg_match('/^sk-ant-[a-zA-Z0-9]{32,}$/', $key);
            case 'gemini':
                return preg_match('/^[a-zA-Z0-9\-_]{39}$/', $key);
            default:
                return false;
        }
    }

    public static function getProvider($name)
    {
        $config = self::load();
        return $config[$name] ?? null;
    }

    public static function updateProvider($name, $settings)
    {
        if (!in_array($name, self::$providers)) {
            throw new Exception("Invalid provider: $name");
        }

        self::$config[$name] = array_merge(self::$config[$name] ?? [], $settings);
        return true;
    }
}
