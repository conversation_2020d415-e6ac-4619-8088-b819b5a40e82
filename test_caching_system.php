<?php
/**
 * Test Script for Caching System
 * Verifies API caching, cache invalidation, and performance improvements
 */

require_once 'php/config.php';
require_once 'php/CacheManager.php';

echo "<h1>💾 Caching System Test</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
    .success { color: #28a745; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .metric { background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .performance-comparison { background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
</style>\n";

try {
    // Test 1: Cache Manager Initialization
    echo "<div class='test-section'>\n";
    echo "<h2>🔧 Test 1: Cache Manager Initialization</h2>\n";
    
    $cache = new CacheManager('cache/', 3600);
    
    // Check if cache directory exists
    if (is_dir('cache/')) {
        echo "<p class='success'>✅ Cache directory exists</p>\n";
        echo "<p>Permissions: " . substr(sprintf('%o', fileperms('cache/')), -4) . "</p>\n";
        
        // Check .htaccess protection
        if (file_exists('cache/.htaccess')) {
            echo "<p class='success'>✅ Cache directory protected with .htaccess</p>\n";
        } else {
            echo "<p class='warning'>⚠️ .htaccess protection missing</p>\n";
        }
    } else {
        echo "<p class='warning'>⚠️ Cache directory will be created automatically</p>\n";
    }
    
    // Test cache functionality
    echo "<h3>Basic Cache Operations:</h3>\n";
    
    // Test SET operation
    $testData = ['test' => 'data', 'timestamp' => time(), 'arabic' => 'مرحبا بك'];
    $setResult = $cache->set('test_key', $testData, 60);
    echo "<p><strong>SET operation:</strong> " . ($setResult ? '<span class="success">Success</span>' : '<span class="error">Failed</span>') . "</p>\n";
    
    // Test GET operation
    $retrievedData = $cache->get('test_key');
    $getSuccess = ($retrievedData === $testData);
    echo "<p><strong>GET operation:</strong> " . ($getSuccess ? '<span class="success">Success</span>' : '<span class="error">Failed</span>') . "</p>\n";
    
    if (!$getSuccess) {
        echo "<p class='error'>Expected: " . json_encode($testData) . "</p>\n";
        echo "<p class='error'>Got: " . json_encode($retrievedData) . "</p>\n";
    }
    
    // Test EXISTS operation
    $existsResult = $cache->exists('test_key');
    echo "<p><strong>EXISTS operation:</strong> " . ($existsResult ? '<span class="success">True</span>' : '<span class="error">False</span>') . "</p>\n";
    
    // Test DELETE operation
    $deleteResult = $cache->delete('test_key');
    echo "<p><strong>DELETE operation:</strong> " . ($deleteResult ? '<span class="success">Success</span>' : '<span class="error">Failed</span>') . "</p>\n";
    
    // Verify deletion
    $afterDelete = $cache->exists('test_key');
    echo "<p><strong>After DELETE:</strong> " . (!$afterDelete ? '<span class="success">Correctly deleted</span>' : '<span class="error">Still exists</span>') . "</p>\n";
    
    echo "</div>\n";
    
    // Test 2: API Caching Performance
    echo "<div class='test-section'>\n";
    echo "<h2>🚀 Test 2: API Caching Performance</h2>\n";
    
    $iterations = 5;
    $timesWithoutCache = [];
    $timesWithCache = [];
    
    echo "<h3>Testing Database Query Performance:</h3>\n";
    
    // Test without cache
    echo "<h4>Without Cache:</h4>\n";
    for ($i = 0; $i < $iterations; $i++) {
        $start = microtime(true);
        
        $stmt = $conn->prepare("
            SELECT p.*,
                   CASE WHEN lp.id IS NOT NULL THEN true ELSE false END as has_landing_page,
                   lp.lien_url as landing_url
            FROM produits p
            LEFT JOIN landing_pages lp ON p.id = lp.produit_id
            LIMIT 20
        ");
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $end = microtime(true);
        $time = ($end - $start) * 1000;
        $timesWithoutCache[] = $time;
        
        echo "<p>Query " . ($i + 1) . ": " . number_format($time, 2) . " ms (" . count($products) . " products)</p>\n";
    }
    
    $avgWithoutCache = array_sum($timesWithoutCache) / count($timesWithoutCache);
    echo "<p class='metric'><strong>Average without cache: " . number_format($avgWithoutCache, 2) . " ms</strong></p>\n";
    
    // Test with cache
    echo "<h4>With Cache:</h4>\n";
    for ($i = 0; $i < $iterations; $i++) {
        $start = microtime(true);
        
        $cacheKey = 'products_test_performance_' . $i;
        $products = $cache->remember($cacheKey, function() use ($conn) {
            $stmt = $conn->prepare("
                SELECT p.*,
                       CASE WHEN lp.id IS NOT NULL THEN true ELSE false END as has_landing_page,
                       lp.lien_url as landing_url
                FROM produits p
                LEFT JOIN landing_pages lp ON p.id = lp.produit_id
                LIMIT 20
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }, 300);
        
        $end = microtime(true);
        $time = ($end - $start) * 1000;
        $timesWithCache[] = $time;
        
        $cacheStatus = ($i === 0) ? 'MISS' : 'HIT';
        echo "<p>Query " . ($i + 1) . ": " . number_format($time, 2) . " ms ($cacheStatus) (" . count($products) . " products)</p>\n";
    }
    
    $avgWithCache = array_sum($timesWithCache) / count($timesWithCache);
    echo "<p class='metric'><strong>Average with cache: " . number_format($avgWithCache, 2) . " ms</strong></p>\n";
    
    // Calculate improvement
    $improvement = (($avgWithoutCache - $avgWithCache) / $avgWithoutCache) * 100;
    echo "<div class='performance-comparison'>\n";
    echo "<h3>🎯 Performance Improvement: " . number_format($improvement, 1) . "%</h3>\n";
    echo "<p>Cache reduces query time by " . number_format($avgWithoutCache - $avgWithCache, 2) . " ms on average</p>\n";
    echo "<p>Speed increase: " . number_format($avgWithoutCache / $avgWithCache, 1) . "x faster</p>\n";
    echo "</div>\n";
    
    // Clean up test cache
    for ($i = 0; $i < $iterations; $i++) {
        $cache->delete('products_test_performance_' . $i);
    }
    
    echo "</div>\n";
    
    // Test 3: Cache Statistics and Management
    echo "<div class='test-section'>\n";
    echo "<h2>📊 Test 3: Cache Statistics and Management</h2>\n";
    
    // Create some test cache entries
    for ($i = 1; $i <= 5; $i++) {
        $cache->set("test_entry_$i", ['id' => $i, 'data' => "Test data $i"], 60);
    }
    
    $stats = $cache->getStats();
    
    echo "<h3>Cache Statistics:</h3>\n";
    echo "<table>\n";
    echo "<tr><th>Metric</th><th>Value</th></tr>\n";
    echo "<tr><td>Cache Enabled</td><td>" . ($stats['enabled'] ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . "</td></tr>\n";
    echo "<tr><td>Total Files</td><td>{$stats['total_files']}</td></tr>\n";
    echo "<tr><td>Valid Files</td><td>{$stats['valid_files']}</td></tr>\n";
    echo "<tr><td>Expired Files</td><td>{$stats['expired_files']}</td></tr>\n";
    echo "<tr><td>Total Size</td><td>{$stats['total_size_mb']} MB</td></tr>\n";
    echo "<tr><td>Cache Directory</td><td>{$stats['cache_dir']}</td></tr>\n";
    echo "</table>\n";
    
    // Test cache cleaning
    echo "<h3>Cache Management:</h3>\n";
    $cleanedFiles = $cache->cleanExpired();
    echo "<p><strong>Expired files cleaned:</strong> $cleanedFiles</p>\n";
    
    // Test pattern invalidation
    $invalidated = $cache->invalidatePattern('test_entry_*');
    echo "<p><strong>Pattern invalidation (test_entry_*):</strong> $invalidated files</p>\n";
    
    echo "</div>\n";
    
    // Test 4: API Integration Test
    echo "<div class='test-section'>\n";
    echo "<h2>🌐 Test 4: API Integration Test</h2>\n";
    
    $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/php/api/products.php';
    
    echo "<h3>Testing API Endpoint: $apiUrl</h3>\n";
    
    $apiTimes = [];
    $cacheHits = 0;
    
    for ($i = 0; $i < 3; $i++) {
        $start = microtime(true);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'header' => 'Cache-Control: no-cache'
            ]
        ]);
        
        $response = @file_get_contents($apiUrl, false, $context);
        $end = microtime(true);
        
        if ($response !== false) {
            $time = ($end - $start) * 1000;
            $apiTimes[] = $time;
            
            $data = json_decode($response, true);
            $productCount = 0;
            $cacheStatus = 'UNKNOWN';
            
            if ($data && isset($data['data'])) {
                $productCount = count($data['data']);
            }
            
            // Check response headers for cache indicators
            $headers = $http_response_header ?? [];
            foreach ($headers as $header) {
                if (strpos($header, 'Cache-Control') !== false) {
                    $cacheStatus = 'CACHED';
                    if ($i > 0) $cacheHits++;
                    break;
                }
            }
            
            echo "<p>API Call " . ($i + 1) . ": " . number_format($time, 2) . " ms ($productCount products) [$cacheStatus]</p>\n";
        } else {
            echo "<p class='error'>API Call " . ($i + 1) . ": Failed</p>\n";
        }
        
        // Small delay between requests
        usleep(100000); // 0.1 seconds
    }
    
    if (!empty($apiTimes)) {
        $avgApiTime = array_sum($apiTimes) / count($apiTimes);
        echo "<p class='metric'><strong>Average API Response Time: " . number_format($avgApiTime, 2) . " ms</strong></p>\n";
        echo "<p class='metric'><strong>Cache Hit Rate: " . number_format(($cacheHits / (count($apiTimes) - 1)) * 100, 1) . "%</strong></p>\n";
        
        if ($avgApiTime < 200) {
            echo "<p class='success'>✅ Excellent API performance (< 200ms)</p>\n";
        } elseif ($avgApiTime < 500) {
            echo "<p class='warning'>⚠ Good API performance (< 500ms)</p>\n";
        } else {
            echo "<p class='error'>❌ API performance needs improvement (> 500ms)</p>\n";
        }
    }
    
    echo "</div>\n";
    
    // Test 5: Cache Invalidation Test
    echo "<div class='test-section'>\n";
    echo "<h2>🔄 Test 5: Cache Invalidation Test</h2>\n";
    
    echo "<p>Testing automatic cache invalidation when data changes...</p>\n";
    
    // Simulate cache invalidation scenario
    $testKey = 'invalidation_test';
    $originalData = ['version' => 1, 'data' => 'original'];
    $updatedData = ['version' => 2, 'data' => 'updated'];
    
    // Set initial cache
    $cache->set($testKey, $originalData, 300);
    echo "<p>1. Set initial cache data</p>\n";
    
    // Verify cache exists
    $cached = $cache->get($testKey);
    if ($cached === $originalData) {
        echo "<p class='success'>2. ✅ Cache data retrieved correctly</p>\n";
    } else {
        echo "<p class='error'>2. ❌ Cache data mismatch</p>\n";
    }
    
    // Simulate data update and cache invalidation
    $cache->delete($testKey);
    echo "<p>3. Simulated data update - cache invalidated</p>\n";
    
    // Verify cache is gone
    $afterInvalidation = $cache->get($testKey);
    if ($afterInvalidation === null) {
        echo "<p class='success'>4. ✅ Cache successfully invalidated</p>\n";
    } else {
        echo "<p class='error'>4. ❌ Cache still exists after invalidation</p>\n";
    }
    
    // Set new cache with updated data
    $cache->set($testKey, $updatedData, 300);
    echo "<p>5. Set updated cache data</p>\n";
    
    // Verify new data
    $newCached = $cache->get($testKey);
    if ($newCached === $updatedData) {
        echo "<p class='success'>6. ✅ Updated cache data retrieved correctly</p>\n";
    } else {
        echo "<p class='error'>6. ❌ Updated cache data mismatch</p>\n";
    }
    
    // Clean up
    $cache->delete($testKey);
    
    echo "</div>\n";
    
    // Summary
    echo "<div class='test-section' style='border-left-color: #28a745;'>\n";
    echo "<h2>📈 Caching System Test Summary</h2>\n";
    
    $score = 0;
    $maxScore = 100;
    
    // Basic functionality (30 points)
    if ($setResult && $getSuccess && $deleteResult) {
        $score += 30;
        echo "<p class='success'>✅ Basic cache operations: 30/30 points</p>\n";
    } else {
        echo "<p class='error'>❌ Basic cache operations: 0/30 points</p>\n";
    }
    
    // Performance improvement (40 points)
    if (isset($improvement)) {
        if ($improvement > 50) {
            $perfScore = 40;
        } elseif ($improvement > 25) {
            $perfScore = 30;
        } elseif ($improvement > 10) {
            $perfScore = 20;
        } else {
            $perfScore = 10;
        }
        $score += $perfScore;
        echo "<p class='success'>✅ Performance improvement: $perfScore/40 points (" . number_format($improvement, 1) . "% faster)</p>\n";
    }
    
    // API integration (20 points)
    if (!empty($apiTimes) && $avgApiTime < 500) {
        $apiScore = $avgApiTime < 200 ? 20 : 15;
        $score += $apiScore;
        echo "<p class='success'>✅ API integration: $apiScore/20 points</p>\n";
    } else {
        echo "<p class='warning'>⚠ API integration: 5/20 points</p>\n";
        $score += 5;
    }
    
    // Cache management (10 points)
    if ($stats['enabled'] && $stats['total_files'] >= 0) {
        $score += 10;
        echo "<p class='success'>✅ Cache management: 10/10 points</p>\n";
    } else {
        echo "<p class='error'>❌ Cache management: 0/10 points</p>\n";
    }
    
    echo "<h3>Overall Score: $score/$maxScore</h3>\n";
    
    if ($score >= 80) {
        echo "<p class='success'>🎉 Excellent! Caching system is working perfectly!</p>\n";
    } elseif ($score >= 60) {
        echo "<p class='warning'>👍 Good! Caching system is working well with minor issues.</p>\n";
    } else {
        echo "<p class='error'>⚠ Caching system needs attention.</p>\n";
    }
    
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='test-section' style='border-left-color: #dc3545;'>\n";
    echo "<h2>❌ Error During Caching System Testing</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<p class='error'>Stack trace:</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    echo "</div>\n";
}
?>

<script>
console.log('💾 Caching system test completed');
</script>
