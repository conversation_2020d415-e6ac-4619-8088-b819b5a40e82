<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج الطلب - Order Form</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .form-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .form-section h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.4em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group select {
            cursor: pointer;
        }

        .form-group select:disabled {
            background: #f8f9fa;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .loading-indicator {
            display: none;
            text-align: center;
            padding: 10px;
            color: #667eea;
        }

        .loading-indicator.show {
            display: block;
        }

        .order-summary {
            background: #fff;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 25px;
            height: fit-content;
            position: sticky;
            top: 20px;
        }

        .order-summary h3 {
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.5em;
        }

        .product-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .product-info img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .product-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .product-price {
            font-size: 1.2em;
            color: #28a745;
            font-weight: bold;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .summary-item:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 1.2em;
            color: #667eea;
        }

        .shipping-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }

        .shipping-info.calculating {
            background: #fff3cd;
            border-left-color: #ffc107;
        }

        .shipping-info.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            font-weight: bold;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }

        .submit-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .error-message {
            color: #dc3545;
            font-size: 0.9em;
            margin-top: 5px;
            display: none;
        }

        .success-message {
            color: #28a745;
            font-size: 0.9em;
            margin-top: 5px;
            display: none;
        }

        @media (max-width: 768px) {
            .form-container {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .order-summary {
                position: static;
            }
        }

        .zone-badge {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-right: 5px;
        }

        .delivery-estimate {
            color: #6c757d;
            font-size: 0.9em;
            font-style: italic;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        @media (max-width: 576px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shopping-cart"></i> نموذج الطلب</h1>
            <p>أكمل بياناتك لإتمام عملية الشراء</p>
        </div>

        <div class="form-container">
            <!-- Order Form -->
            <div class="form-section">
                <h3><i class="fas fa-user"></i> معلومات الطلب</h3>
                
                <form id="orderForm">
                    <!-- Personal Information -->
                    <div class="form-row">
                        <div class="form-group">
                            <label for="fullName">الاسم الكامل *</label>
                            <input type="text" id="fullName" name="fullName" required>
                            <div class="error-message" id="fullNameError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">رقم الهاتف *</label>
                            <input type="tel" id="phone" name="phone" required placeholder="0555123456">
                            <div class="error-message" id="phoneError"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" placeholder="<EMAIL>">
                        <div class="error-message" id="emailError"></div>
                    </div>

                    <!-- Geographic Selection -->
                    <h3 style="margin-top: 30px;"><i class="fas fa-map-marker-alt"></i> عنوان التوصيل</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="wilayaSelect">الولاية *</label>
                            <select id="wilayaSelect" name="wilaya" required>
                                <option value="">-- اختر الولاية --</option>
                            </select>
                            <div class="loading-indicator" id="wilayaLoading">
                                <i class="fas fa-spinner fa-spin"></i> جاري تحميل الولايات...
                            </div>
                            <div class="error-message" id="wilayaError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="communeSelect">البلدية *</label>
                            <select id="communeSelect" name="commune" required disabled>
                                <option value="">-- اختر البلدية --</option>
                            </select>
                            <div class="loading-indicator" id="communeLoading">
                                <i class="fas fa-spinner fa-spin"></i> جاري تحميل البلديات...
                            </div>
                            <div class="error-message" id="communeError"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="address">العنوان التفصيلي *</label>
                        <textarea id="address" name="address" rows="3" required placeholder="الحي، الشارع، رقم المنزل..."></textarea>
                        <div class="error-message" id="addressError"></div>
                    </div>

                    <!-- Payment Method -->
                    <h3 style="margin-top: 30px;"><i class="fas fa-credit-card"></i> معلومات الدفع</h3>
                    
                    <div class="form-group">
                        <label for="paymentMethod">طريقة الدفع *</label>
                        <select id="paymentMethod" name="paymentMethod" required>
                            <option value="">-- اختر طريقة الدفع --</option>
                            <option value="cod">الدفع عند الاستلام</option>
                            <option value="ccp">الحساب الجاري البريدي (CCP)</option>
                            <option value="bank">التحويل البنكي</option>
                        </select>
                        <div class="error-message" id="paymentError"></div>
                    </div>

                    <div id="ccpInfo" class="form-group" style="display: none;">
                        <label for="ccpNumber">رقم الحساب الجاري البريدي (CCP)</label>
                        <input type="text" id="ccpNumber" name="ccpNumber" placeholder="**********">
                        <small style="color: #6c757d;">اتركه فارغاً إذا كنت ستدفع عند الاستلام</small>
                    </div>

                    <div class="form-group">
                        <label for="notes">ملاحظات إضافية</label>
                        <textarea id="notes" name="notes" rows="3" placeholder="أي ملاحظات خاصة بالطلب..."></textarea>
                    </div>
                </form>
            </div>

            <!-- Order Summary -->
            <div class="order-summary">
                <h3>ملخص الطلب</h3>
                
                <div class="product-info">
                    <img id="productImage" src="https://via.placeholder.com/300x150?text=Product+Image" alt="Product">
                    <div class="product-title" id="productTitle">اسم المنتج</div>
                    <div class="product-price" id="productPrice">0 دج</div>
                </div>

                <div class="summary-item">
                    <span>سعر المنتج:</span>
                    <span id="summaryProductPrice">0 دج</span>
                </div>

                <div class="summary-item">
                    <span>رسوم الشحن:</span>
                    <span id="summaryShippingCost">-- دج</span>
                </div>

                <div class="shipping-info" id="shippingInfo" style="display: none;">
                    <div id="shippingDetails"></div>
                </div>

                <div class="summary-item">
                    <span>المجموع الكلي:</span>
                    <span id="summaryTotal">0 دج</span>
                </div>

                <button type="submit" form="orderForm" class="submit-btn" id="submitBtn" disabled>
                    <i class="fas fa-check"></i> تأكيد الطلب
                </button>
            </div>
        </div>
    </div>

    <script src="js/order-form.js"></script>
</body>
</html>
