function copyToClipboard(text) {
    // Create a temporary input element
    const input = document.createElement('input');
    input.style.position = 'fixed';
    input.style.opacity = 0;
    input.value = text;
    document.body.appendChild(input);

    // Select and copy the text
    input.select();
    input.setSelectionRange(0, 99999);
    document.execCommand('copy');

    // Remove the temporary input
    document.body.removeChild(input);

    // Show copied message
    const button = document.querySelector('.copy-link');
    if (button) {
        const originalText = button.textContent;
        button.textContent = 'تم النسخ';
        button.classList.add('copied');

        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('copied');
        }, 2000);
    }
}

function shareOnFacebook(url) {
    const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(text, url) {
    const shareUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(text + ' ' + url)}`;
    window.open(shareUrl, '_blank');
}

function shareOnTwitter(text, url) {
    const shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

// Initialize share buttons
document.addEventListener('DOMContentLoaded', () => {
    // Copy link button
    const copyButtons = document.querySelectorAll('.copy-link');
    copyButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const url = button.getAttribute('data-url') || window.location.href;
            copyToClipboard(url);
        });
    });

    // Facebook share buttons
    const facebookButtons = document.querySelectorAll('.share-facebook');
    facebookButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const url = button.getAttribute('data-url') || window.location.href;
            shareOnFacebook(url);
        });
    });

    // WhatsApp share buttons
    const whatsappButtons = document.querySelectorAll('.share-whatsapp');
    whatsappButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const url = button.getAttribute('data-url') || window.location.href;
            const text = button.getAttribute('data-text') || document.title;
            shareOnWhatsApp(text, url);
        });
    });

    // Twitter share buttons
    const twitterButtons = document.querySelectorAll('.share-twitter');
    twitterButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const url = button.getAttribute('data-url') || window.location.href;
            const text = button.getAttribute('data-text') || document.title;
            shareOnTwitter(text, url);
        });
    });
});