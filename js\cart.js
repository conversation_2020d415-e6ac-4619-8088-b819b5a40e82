// Récupérer les éléments du panier depuis le localStorage
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Mettre à jour l'affichage du panier
function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');
    const emptyCart = document.getElementById('emptyCart');
    const cartContainer = document.querySelector('.cart-container');
    const cartCount = document.querySelector('.cart-count');

    // Mettre à jour le compteur du panier
    cartCount.textContent = cart.reduce((total, item) => total + item.quantity, 0);

    if (cart.length === 0) {
        cartContainer.style.display = 'none';
        emptyCart.style.display = 'block';
        return;
    }

    cartContainer.style.display = 'grid';
    emptyCart.style.display = 'none';
    
    // Vider le conteneur des articles
    cartItems.innerHTML = '';

    // Ajouter chaque article au panier
    cart.forEach((item, index) => {
        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item';
        cartItem.innerHTML = `
            <img src="${item.image}" alt="${item.title}">
            <div class="item-details">
                <h3>${item.title}</h3>
                <div class="item-price">${item.price} دج</div>
                <div class="quantity-controls">
                    <button onclick="updateQuantity(${index}, -1)">-</button>
                    <span>${item.quantity}</span>
                    <button onclick="updateQuantity(${index}, 1)">+</button>
                </div>
            </div>
            <i class="fas fa-trash remove-item" onclick="removeItem(${index})"></i>
        `;

        cartItems.appendChild(cartItem);
    });

    updateCartSummary();
}

// Mettre à jour la quantité d'un article
function updateQuantity(index, change) {
    const item = cart[index];
    const newQuantity = item.quantity + change;

    if (newQuantity > 0) {
        item.quantity = newQuantity;
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartDisplay();
    } else if (newQuantity === 0) {
        removeItem(index);
    }
}

// Supprimer un article du panier
function removeItem(index) {
    cart.splice(index, 1);
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartDisplay();
}

// Mettre à jour le résumé du panier
function updateCartSummary() {
    const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    const shipping = 500; // Frais de livraison fixes
    const total = subtotal + shipping;

    document.getElementById('subtotal').textContent = `${subtotal} دج`;
    document.getElementById('total').textContent = `${total} دج`;
}

// Gérer le processus de paiement
document.getElementById('checkoutButton').addEventListener('click', () => {
    // Rediriger vers la page de paiement
    window.location.href = 'checkout.html';
});

// Initialiser l'affichage du panier au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    updateCartDisplay();
});