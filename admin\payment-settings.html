<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الدفع - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Payment Settings Content -->
    <div class="payment-settings-content">
        <!-- Header Section -->
        <div class="payment-settings-header">
            <div class="section-title-wrapper">
                <div class="section-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="section-title-content">
                    <h3 class="section-title">إعدادات الدفع</h3>
                    <p class="section-subtitle">تكوين وإدارة طرق الدفع والمعاملات المالية</p>
                </div>
            </div>
            <div class="settings-summary">
                <div class="summary-item">
                    <span class="summary-label">طرق الدفع المفعلة:</span>
                    <span class="summary-value" id="activePaymentMethods">--</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">حالة النظام:</span>
                    <span class="summary-value status-active">متصل</span>
                </div>
            </div>
        </div>

        <!-- Payment Methods Configuration -->
        <div class="settings-section">
            <div class="section-header">
                <h4 class="section-title">
                    <i class="fas fa-money-check-alt"></i>
                    طرق الدفع
                </h4>
                <p class="section-description">تكوين طرق الدفع المختلفة المتاحة للعملاء</p>
            </div>

            <div class="settings-grid">
                <!-- Cash on Delivery Settings -->
                <div class="setting-card" data-method="cod">
                    <div class="setting-header">
                        <div class="setting-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="setting-info">
                            <h5>الدفع عند الاستلام</h5>
                            <p>الطريقة الأكثر شيوعاً في الجزائر</p>
                            <div id="codStats"></div>
                        </div>
                        <div class="setting-toggle">
                            <label class="switch">
                                <input type="checkbox" id="cod-enabled" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="setting-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="cod-fee" class="enhanced-label">
                                    <i class="fas fa-coins"></i>
                                    رسوم التوصيل (دج)
                                </label>
                                <input type="number" id="cod-fee" class="enhanced-input" min="0" placeholder="0">
                            </div>
                            <div class="form-group">
                                <label for="cod-min-order" class="enhanced-label">
                                    <i class="fas fa-shopping-cart"></i>
                                    الحد الأدنى للطلب (دج)
                                </label>
                                <input type="number" id="cod-min-order" class="enhanced-input" min="0" placeholder="1000">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="cod-areas" class="enhanced-label">
                                <i class="fas fa-map-marker-alt"></i>
                                المناطق المتاحة
                            </label>
                            <textarea id="cod-areas" class="enhanced-input" rows="2" placeholder="جميع ولايات الجزائر"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="cod-notes" class="enhanced-label">
                                <i class="fas fa-sticky-note"></i>
                                ملاحظات للعميل
                            </label>
                            <textarea id="cod-notes" class="enhanced-input" rows="3" placeholder="يرجى تحضير المبلغ المطلوب عند الاستلام"></textarea>
                        </div>
                        <button type="button" class="test-payment-btn action-button" data-method="cod">
                            <i class="fas fa-vial"></i>
                            اختبار الطريقة
                        </button>
                    </div>
                </div>

                <!-- CCP Settings -->
                <div class="setting-card" data-method="ccp">
                    <div class="setting-header">
                        <div class="setting-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="setting-info">
                            <h5>الحساب الجاري البريدي (CCP)</h5>
                            <p>بريد الجزائر - الطريقة التقليدية</p>
                        </div>
                        <div class="setting-toggle">
                            <label class="switch">
                                <input type="checkbox" id="ccp-enabled">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="setting-content" style="display: none;">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="ccp-number" class="enhanced-label">
                                    <i class="fas fa-hashtag"></i>
                                    رقم الحساب الجاري البريدي
                                </label>
                                <input type="text" id="ccp-number" class="enhanced-input" placeholder="1234567890" maxlength="10">
                            </div>
                            <div class="form-group">
                                <label for="ccp-key" class="enhanced-label">
                                    <i class="fas fa-key"></i>
                                    مفتاح الحساب
                                </label>
                                <input type="text" id="ccp-key" class="enhanced-input" placeholder="12" maxlength="2">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="ccp-holder" class="enhanced-label">
                                    <i class="fas fa-user"></i>
                                    اسم صاحب الحساب
                                </label>
                                <input type="text" id="ccp-holder" class="enhanced-input" placeholder="الاسم الكامل">
                            </div>
                            <div class="form-group">
                                <label for="ccp-wilaya" class="enhanced-label">
                                    <i class="fas fa-map-marker-alt"></i>
                                    الولاية
                                </label>
                                <input type="text" id="ccp-wilaya" class="enhanced-input" placeholder="الجزائر">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="ccp-instructions" class="enhanced-label">
                                <i class="fas fa-info-circle"></i>
                                تعليمات للعميل
                            </label>
                            <textarea id="ccp-instructions" class="enhanced-input" rows="3" placeholder="يرجى إرسال إيصال التحويل عبر الواتساب"></textarea>
                        </div>
                        <button type="button" class="test-payment-btn action-button" data-method="ccp">
                            <i class="fas fa-vial"></i>
                            اختبار الطريقة
                        </button>
                    </div>
                </div>

                <!-- BaridiMob Settings -->
                <div class="setting-card" data-method="baridimob">
                    <div class="setting-header">
                        <div class="setting-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="setting-info">
                            <h5>بريدي موب (BaridiMob)</h5>
                            <p>الدفع الإلكتروني عبر الهاتف</p>
                        </div>
                        <div class="setting-toggle">
                            <label class="switch">
                                <input type="checkbox" id="baridimob-enabled">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="setting-content" style="display: none;">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="baridimob-number" class="enhanced-label">
                                    <i class="fas fa-phone"></i>
                                    رقم الهاتف المرتبط
                                </label>
                                <input type="tel" id="baridimob-number" class="enhanced-input" placeholder="+213 5XX XXX XXX">
                            </div>
                            <div class="form-group">
                                <label for="baridimob-holder" class="enhanced-label">
                                    <i class="fas fa-user"></i>
                                    اسم صاحب الحساب
                                </label>
                                <input type="text" id="baridimob-holder" class="enhanced-input" placeholder="الاسم الكامل">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="baridimob-qr" class="enhanced-label">
                                <i class="fas fa-qrcode"></i>
                                رمز QR للدفع (اختياري)
                            </label>
                            <input type="file" id="baridimob-qr" class="enhanced-input" accept="image/*">
                            <small class="form-text">يمكنك رفع صورة رمز QR لتسهيل عملية الدفع</small>
                        </div>
                        <div class="form-group">
                            <label for="baridimob-instructions" class="enhanced-label">
                                <i class="fas fa-info-circle"></i>
                                تعليمات للعميل
                            </label>
                            <textarea id="baridimob-instructions" class="enhanced-input" rows="3" placeholder="يرجى إرسال لقطة شاشة من عملية الدفع"></textarea>
                        </div>
                        <button type="button" class="test-payment-btn action-button" data-method="baridimob">
                            <i class="fas fa-vial"></i>
                            اختبار الطريقة
                        </button>
                    </div>
                </div>

                <!-- Bank Transfer Settings -->
                <div class="setting-card" data-method="bank">
                    <div class="setting-header">
                        <div class="setting-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="setting-info">
                            <h5>التحويل المصرفي</h5>
                            <p>تحويل مباشر إلى الحساب المصرفي</p>
                        </div>
                        <div class="setting-toggle">
                            <label class="switch">
                                <input type="checkbox" id="paypalEnabled">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="setting-content">
                        <div class="form-group">
                            <label for="paypalClientId" class="enhanced-label">
                                <i class="fas fa-id-card"></i>
                                PayPal Client ID
                            </label>
                            <input type="text" id="paypalClientId" class="enhanced-input" placeholder="Client ID">
                        </div>
                        <div class="form-group">
                            <label for="paypalClientSecret" class="enhanced-label">
                                <i class="fas fa-lock"></i>
                                PayPal Client Secret
                            </label>
                            <div class="input-group">
                                <input type="password" id="paypalClientSecret" class="enhanced-input" placeholder="Client Secret">
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('paypalClientSecret')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bank Transfer Settings -->
                <div class="setting-card">
                    <div class="setting-header">
                        <div class="setting-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="setting-info">
                            <h5>التحويل البنكي</h5>
                            <p>دفع عبر التحويل البنكي المباشر</p>
                        </div>
                        <div class="setting-toggle">
                            <label class="switch">
                                <input type="checkbox" id="bankTransferEnabled">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="setting-content">
                        <div class="form-group">
                            <label for="bankName" class="enhanced-label">
                                <i class="fas fa-building"></i>
                                اسم البنك
                            </label>
                            <input type="text" id="bankName" class="enhanced-input" placeholder="اسم البنك">
                        </div>
                        <div class="form-group">
                            <label for="accountNumber" class="enhanced-label">
                                <i class="fas fa-hashtag"></i>
                                رقم الحساب
                            </label>
                            <input type="text" id="accountNumber" class="enhanced-input" placeholder="رقم الحساب">
                        </div>
                        <div class="form-group">
                            <label for="iban" class="enhanced-label">
                                <i class="fas fa-code"></i>
                                IBAN
                            </label>
                            <input type="text" id="iban" class="enhanced-input" placeholder="IBAN">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Currency Settings -->
        <div class="settings-section">
            <div class="section-header">
                <h4 class="section-title">
                    <i class="fas fa-coins"></i>
                    إعدادات العملة
                </h4>
                <p class="section-description">تكوين العملة الافتراضية وأسعار الصرف</p>
            </div>

            <div class="settings-grid">
                <div class="setting-card">
                    <div class="form-group">
                        <label for="defaultCurrency" class="enhanced-label">
                            <i class="fas fa-money-bill"></i>
                            العملة الافتراضية
                        </label>
                        <select id="defaultCurrency" class="enhanced-select">
                            <option value="DZD">دينار جزائري (DZD)</option>
                            <option value="USD">دولار أمريكي (USD)</option>
                            <option value="EUR">يورو (EUR)</option>
                            <option value="SAR">ريال سعودي (SAR)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="currencySymbol" class="enhanced-label">
                            <i class="fas fa-at"></i>
                            رمز العملة
                        </label>
                        <input type="text" id="currencySymbol" class="enhanced-input" value="د.ج" placeholder="د.ج">
                    </div>
                    <div class="form-group">
                        <label for="currencyPosition" class="enhanced-label">
                            <i class="fas fa-align-left"></i>
                            موضع رمز العملة
                        </label>
                        <select id="currencyPosition" class="enhanced-select">
                            <option value="before">قبل المبلغ</option>
                            <option value="after" selected>بعد المبلغ</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Actions -->
        <div class="save-actions">
            <button type="button" class="enhanced-save-btn" onclick="savePaymentSettings()">
                <i class="fas fa-save"></i>
                حفظ إعدادات الدفع
            </button>
            <button type="button" class="enhanced-test-btn" onclick="testPaymentConnection()">
                <i class="fas fa-vial"></i>
                اختبار الاتصال
            </button>
        </div>
    </div>

    <script src="js/payment-settings.js"></script>
</body>
</html>
