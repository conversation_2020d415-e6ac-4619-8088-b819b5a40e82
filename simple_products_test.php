<?php
// Simple test to check products API response format
require_once 'php/config.php';

try {
    // Simulate what the API should return
    $stmt = $conn->prepare(
        "SELECT p.*,
                CASE
                    WHEN lp.id IS NOT NULL THEN true
                    ELSE false
                END as has_landing_page,
                lp.lien_url as landing_url
         FROM produits p
         LEFT JOIN landing_pages lp ON p.id = lp.produit_id
         WHERE p.actif = 1"
    );
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format response like the API should
    $response = [
        'success' => true,
        'products' => $products,
        'total_count' => count($products)
    ];

    echo "✅ Products API simulation successful!\n";
    echo "Response format:\n";
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
