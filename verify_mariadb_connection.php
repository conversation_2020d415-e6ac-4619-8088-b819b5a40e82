<?php
/**
 * MariaDB Connection Verification for localhost:3307/mossab-landing-page
 * This script verifies connection to YOUR specific database and shows current state
 */

require_once 'php/config.php';

echo "<h1>🔍 MariaDB Database Verification - Port 3307</h1>\n";
echo "<style>
body{font-family:Arial;margin:20px;background:#f8f9fa;} 
.container{max-width:1200px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 4px 6px rgba(0,0,0,0.1);}
.success{color:#28a745;font-weight:bold;} 
.error{color:#dc3545;font-weight:bold;} 
.warning{color:#ffc107;font-weight:bold;} 
.info{color:#17a2b8;font-weight:bold;} 
pre{background:#f8f9fa;padding:15px;border-radius:5px;border-left:4px solid #007bff;} 
table{border-collapse:collapse;width:100%;margin:15px 0;} 
th,td{border:1px solid #dee2e6;padding:12px;text-align:left;} 
th{background:#e9ecef;font-weight:bold;}
.step{background:#f0f8ff;padding:20px;margin:15px 0;border-radius:8px;border-left:4px solid #007bff;}
.highlight{background:#fff3cd;padding:10px;border-radius:5px;margin:10px 0;}
</style>\n";

echo "<div class='container'>\n";

try {
    // STEP 1: Test Database Connection
    echo "<div class='step'>\n";
    echo "<h2>🔌 Step 1: Testing MariaDB Connection</h2>\n";
    
    $connectionTest = testDatabaseConnection();
    
    if ($connectionTest['success']) {
        echo "<p class='success'>✅ Successfully connected to MariaDB!</p>\n";
        echo "<div class='highlight'>\n";
        echo "<h3>📊 Connection Details:</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Database Version:</strong> {$connectionTest['version']}</li>\n";
        echo "<li><strong>Database Name:</strong> {$connectionTest['database']}</li>\n";
        echo "<li><strong>Host:</strong> {$connectionTest['host']}</li>\n";
        echo "<li><strong>Port:</strong> {$connectionTest['port']}</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
    } else {
        echo "<p class='error'>❌ Failed to connect to MariaDB!</p>\n";
        echo "<p class='error'>Error: {$connectionTest['error']}</p>\n";
        echo "<div class='highlight'>\n";
        echo "<h3>🔧 Troubleshooting Steps:</h3>\n";
        echo "<ol>\n";
        echo "<li>Verify MariaDB is running on port 3307</li>\n";
        echo "<li>Check if database 'mossab-landing-page' exists</li>\n";
        echo "<li>Verify root user has access permissions</li>\n";
        echo "<li>Test connection: <code>mysql -u root -h localhost -P 3307 mossab-landing-page</code></li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        throw new Exception("Database connection failed");
    }
    echo "</div>\n";
    
    // STEP 2: Analyze Current Database Structure
    echo "<div class='step'>\n";
    echo "<h2>🗂️ Step 2: Current Database Structure Analysis</h2>\n";
    
    $pdo = getPDOConnection();
    
    // Get all tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p class='info'>📋 Found " . count($tables) . " tables in database</p>\n";
    
    echo "<table>\n";
    echo "<tr><th>Table Name</th><th>Rows</th><th>Engine</th><th>Collation</th><th>Status</th></tr>\n";
    
    $criticalTables = ['produits', 'landing_pages', 'categories', 'landing_page_images'];
    $tableStats = [];
    
    foreach ($tables as $table) {
        // Get table info
        $stmt = $pdo->query("SHOW TABLE STATUS LIKE '$table'");
        $tableInfo = $stmt->fetch();
        
        // Get row count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
        $rowCount = $stmt->fetch()['count'];
        
        $status = '✅ OK';
        if (in_array($table, $criticalTables)) {
            if ($table === 'produits' && $rowCount < 5) $status = '⚠️ Missing Products';
            if ($table === 'landing_pages' && $rowCount === 0) $status = '⚠️ No Landing Pages';
            if ($table === 'categories' && $rowCount < 5) $status = '⚠️ Missing Categories';
        }
        
        $tableStats[$table] = [
            'rows' => $rowCount,
            'engine' => $tableInfo['Engine'],
            'collation' => $tableInfo['Collation'],
            'status' => $status
        ];
        
        echo "<tr>";
        echo "<td><strong>$table</strong></td>";
        echo "<td>$rowCount</td>";
        echo "<td>{$tableInfo['Engine']}</td>";
        echo "<td>{$tableInfo['Collation']}</td>";
        echo "<td>$status</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    echo "</div>\n";
    
    // STEP 3: Critical Tables Analysis
    echo "<div class='step'>\n";
    echo "<h2>🎯 Step 3: Critical Tables Analysis</h2>\n";
    
    // Products Analysis
    echo "<h3>📦 Products Table Analysis</h3>\n";
    if (in_array('produits', $tables)) {
        $stmt = $pdo->query("SELECT id, titre, type, prix, actif FROM produits ORDER BY id");
        $products = $stmt->fetchAll();
        
        echo "<p class='info'>Current products in database: " . count($products) . "</p>\n";
        
        if (count($products) > 0) {
            echo "<table>\n";
            echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>Price</th><th>Active</th></tr>\n";
            foreach ($products as $product) {
                $activeStatus = $product['actif'] ? '✅' : '❌';
                echo "<tr>";
                echo "<td>{$product['id']}</td>";
                echo "<td>" . htmlspecialchars($product['titre']) . "</td>";
                echo "<td>{$product['type']}</td>";
                echo "<td>{$product['prix']} DZD</td>";
                echo "<td>$activeStatus</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p class='warning'>⚠️ No products found in database</p>\n";
        }
    } else {
        echo "<p class='error'>❌ Products table does not exist!</p>\n";
    }
    
    // Landing Pages Analysis
    echo "<h3>🎨 Landing Pages Table Analysis</h3>\n";
    if (in_array('landing_pages', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM landing_pages");
        $landingPagesCount = $stmt->fetch()['count'];
        
        echo "<p class='info'>Current landing pages: $landingPagesCount</p>\n";
        
        if ($landingPagesCount > 0) {
            $stmt = $pdo->query("SELECT id, titre, produit_id, actif FROM landing_pages ORDER BY id");
            $pages = $stmt->fetchAll();
            
            echo "<table>\n";
            echo "<tr><th>ID</th><th>Title</th><th>Product ID</th><th>Active</th></tr>\n";
            foreach ($pages as $page) {
                $activeStatus = $page['actif'] ? '✅' : '❌';
                echo "<tr>";
                echo "<td>{$page['id']}</td>";
                echo "<td>" . htmlspecialchars($page['titre']) . "</td>";
                echo "<td>{$page['produit_id']}</td>";
                echo "<td>$activeStatus</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p class='warning'>⚠️ No landing pages found</p>\n";
        }
    } else {
        echo "<p class='error'>❌ Landing pages table does not exist!</p>\n";
    }
    
    // Categories Analysis
    echo "<h3>🗂️ Categories Table Analysis</h3>\n";
    if (in_array('categories', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
        $categoriesCount = $stmt->fetch()['count'];
        
        echo "<p class='info'>Current categories: $categoriesCount</p>\n";
        
        if ($categoriesCount > 0) {
            $stmt = $pdo->query("SELECT id, nom_ar, nom_en, actif FROM categories ORDER BY ordre_affichage");
            $categories = $stmt->fetchAll();
            
            echo "<table>\n";
            echo "<tr><th>ID</th><th>Arabic Name</th><th>English Name</th><th>Active</th></tr>\n";
            foreach ($categories as $category) {
                $activeStatus = $category['actif'] ? '✅' : '❌';
                echo "<tr>";
                echo "<td>{$category['id']}</td>";
                echo "<td>{$category['nom_ar']}</td>";
                echo "<td>{$category['nom_en']}</td>";
                echo "<td>$activeStatus</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p class='warning'>⚠️ No categories found</p>\n";
        }
    } else {
        echo "<p class='error'>❌ Categories table does not exist!</p>\n";
    }
    echo "</div>\n";
    
    // STEP 4: Requirements Assessment
    echo "<div class='step'>\n";
    echo "<h2>📋 Step 4: Requirements Assessment</h2>\n";
    
    $requirements = [
        'Database Connection' => $connectionTest['success'],
        'Products Table Exists' => in_array('produits', $tables),
        'Landing Pages Table Exists' => in_array('landing_pages', $tables),
        'Categories Table Exists' => in_array('categories', $tables),
        'Has 5 Products' => isset($tableStats['produits']) && $tableStats['produits']['rows'] >= 5,
        'Has Landing Pages' => isset($tableStats['landing_pages']) && $tableStats['landing_pages']['rows'] > 0,
        'Has Categories' => isset($tableStats['categories']) && $tableStats['categories']['rows'] >= 5
    ];
    
    echo "<table>\n";
    echo "<tr><th>Requirement</th><th>Status</th><th>Action Needed</th></tr>\n";
    
    foreach ($requirements as $requirement => $met) {
        $status = $met ? '<span class="success">✅ Met</span>' : '<span class="error">❌ Not Met</span>';
        $action = $met ? 'None' : 'Required';
        
        echo "<tr>";
        echo "<td>$requirement</td>";
        echo "<td>$status</td>";
        echo "<td>$action</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    $metRequirements = array_sum($requirements);
    $totalRequirements = count($requirements);
    
    echo "<div class='highlight'>\n";
    echo "<h3>📊 Overall Status: $metRequirements/$totalRequirements requirements met</h3>\n";
    
    if ($metRequirements === $totalRequirements) {
        echo "<p class='success'>🎉 All requirements are met! System is ready.</p>\n";
    } else {
        echo "<p class='warning'>⚠️ Some requirements need to be addressed.</p>\n";
        echo "<p><strong>Next Steps:</strong></p>\n";
        echo "<ol>\n";
        if (!$requirements['Has 5 Products']) {
            echo "<li>Create the 5 required products</li>\n";
        }
        if (!$requirements['Has Landing Pages']) {
            echo "<li>Create test landing pages</li>\n";
        }
        if (!$requirements['Has Categories']) {
            echo "<li>Set up categories system</li>\n";
        }
        echo "</ol>\n";
    }
    echo "</div>\n";
    echo "</div>\n";
    
    // STEP 5: Next Actions
    echo "<div class='step'>\n";
    echo "<h2>🚀 Step 5: Next Actions</h2>\n";
    echo "<p>Based on the analysis above, here are the recommended next steps:</p>\n";
    echo "<div style='display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:15px;'>\n";
    
    echo "<div style='background:#e8f5e9;padding:15px;border-radius:8px;'>\n";
    echo "<h4>✅ If All Requirements Met</h4>\n";
    echo "<p>Your database is ready! You can proceed with:</p>\n";
    echo "<ul><li>Testing the admin panel</li><li>Creating new landing pages</li><li>Managing products and categories</li></ul>\n";
    echo "</div>\n";
    
    echo "<div style='background:#fff3cd;padding:15px;border-radius:8px;'>\n";
    echo "<h4>🔧 If Requirements Missing</h4>\n";
    echo "<p>Run the complete rebuild script:</p>\n";
    echo "<ul><li><a href='complete_system_rebuild.php'>Complete System Rebuild</a></li><li>This will create all missing data</li><li>Set up proper database structure</li></ul>\n";
    echo "</div>\n";
    
    echo "</div>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='step'>\n";
    echo "<h2 class='error'>❌ Critical Error</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<p><strong>This usually means:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>MariaDB is not running on port 3307</li>\n";
    echo "<li>Database 'mossab-landing-page' doesn't exist</li>\n";
    echo "<li>Connection credentials are incorrect</li>\n";
    echo "<li>PHP PDO MySQL extension is not installed</li>\n";
    echo "</ul>\n";
    echo "<p><strong>To fix:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>Start MariaDB service</li>\n";
    echo "<li>Verify database exists: <code>SHOW DATABASES;</code></li>\n";
    echo "<li>Import your SQL file if needed</li>\n";
    echo "<li>Check PHP extensions: <code>php -m | grep pdo</code></li>\n";
    echo "</ol>\n";
    echo "</div>\n";
}

echo "</div>\n";
?>

<script>
console.log('🔍 MariaDB verification completed');
console.log('Database: mossab-landing-page on localhost:3307');
</script>
