.form-group input:invalid,
.form-group select:invalid,
.form-group textarea:invalid,
.tox-tinymce.invalid {
    border: 2px solid #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.form-group input:focus:invalid,
.form-group select:focus:invalid,
.form-group textarea:focus:invalid,
.tox-tinymce.invalid:focus-within {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid,
.tox-tinymce:not(.invalid) {
    border-color: #28a745 !important;
}

.form-group input:focus:valid,
.form-group select:focus:valid,
.form-group textarea:focus:valid,
.tox-tinymce:not(.invalid):focus-within {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Ensure TinyMCE editors are properly accessible */
.tox-tinymce iframe {
    min-height: 200px !important;
}

/* Improve focus visibility */
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus,
.tox-tinymce:focus-within {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}