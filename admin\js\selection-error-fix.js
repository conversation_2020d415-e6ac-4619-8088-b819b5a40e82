/**
 * Ultimate Selection Error Fix
 * Comprehensive solution for all selection-related errors
 */

(function() {
    "use strict";
    
    console.log("🛡️ Ultimate Selection Error Fix loading...");
    
    // Store original methods safely
    const originalGetSelection = window.getSelection;
    const originalCreateRange = document.createRange;
    
    // Create completely safe selection object
    function createUltimateSafeSelection() {
        return {
            rangeCount: 0,
            anchorNode: null,
            anchorOffset: 0,
            focusNode: null,
            focusOffset: 0,
            isCollapsed: true,
            type: "None",
            toString: function() { return ""; },
            removeAllRanges: function() { 
                try { 
                    if (originalGetSelection && originalGetSelection.call) {
                        const sel = originalGetSelection.call(window);
                        if (sel && sel.removeAllRanges) sel.removeAllRanges();
                    }
                } catch(e) { /* ignore */ }
            },
            addRange: function() { /* safe no-op */ },
            getRangeAt: function(index) { 
                if (index === 0) return createUltimateSafeRange();
                return null; 
            },
            collapse: function() { /* safe no-op */ },
            extend: function() { /* safe no-op */ },
            modify: function() { /* safe no-op */ },
            selectAllChildren: function() { /* safe no-op */ },
            deleteFromDocument: function() { /* safe no-op */ },
            containsNode: function() { return false; }
        };
    }
    
    // Create completely safe range object
    function createUltimateSafeRange() {
        const safeElement = document.body || document.documentElement || document.createElement("div");
        return {
            startContainer: safeElement,
            endContainer: safeElement,
            startOffset: 0,
            endOffset: 0,
            collapsed: true,
            commonAncestorContainer: safeElement,
            setStart: function() { /* safe no-op */ },
            setEnd: function() { /* safe no-op */ },
            setStartBefore: function() { /* safe no-op */ },
            setStartAfter: function() { /* safe no-op */ },
            setEndBefore: function() { /* safe no-op */ },
            setEndAfter: function() { /* safe no-op */ },
            selectNode: function() { /* safe no-op */ },
            selectNodeContents: function() { /* safe no-op */ },
            collapse: function() { /* safe no-op */ },
            cloneContents: function() { return document.createDocumentFragment(); },
            deleteContents: function() { /* safe no-op */ },
            extractContents: function() { return document.createDocumentFragment(); },
            insertNode: function() { /* safe no-op */ },
            surroundContents: function() { /* safe no-op */ },
            compareBoundaryPoints: function() { return 0; },
            cloneRange: function() { return createUltimateSafeRange(); },
            detach: function() { /* safe no-op */ },
            toString: function() { return ""; },
            getBoundingClientRect: function() { 
                return { top: 0, left: 0, bottom: 0, right: 0, width: 0, height: 0, x: 0, y: 0 }; 
            },
            getClientRects: function() { return []; }
        };
    }
    
    // Ultimate getSelection override
    window.getSelection = function() {
        try {
            if (!originalGetSelection) {
                return createUltimateSafeSelection();
            }
            
            const selection = originalGetSelection.call(window);
            
            if (!selection) {
                console.warn("Selection is null, returning safe selection");
                return createUltimateSafeSelection();
            }
            
            // Check if selection has required properties
            if (typeof selection.rangeCount === "undefined") {
                console.warn("Selection missing rangeCount, returning safe selection");
                return createUltimateSafeSelection();
            }
            
            // Wrap with safety
            const safeSelection = Object.create(selection);
            
            // Override critical methods with safety checks
            safeSelection.getRangeAt = function(index) {
                try {
                    if (selection && selection.rangeCount > index && index >= 0) {
                        return selection.getRangeAt(index);
                    }
                    return createUltimateSafeRange();
                } catch (error) {
                    console.warn("getRangeAt error:", error);
                    return createUltimateSafeRange();
                }
            };
            
            safeSelection.removeAllRanges = function() {
                try {
                    if (selection && selection.removeAllRanges) {
                        selection.removeAllRanges();
                    }
                } catch (error) {
                    console.warn("removeAllRanges error:", error);
                }
            };
            
            return safeSelection;
            
        } catch (error) {
            console.warn("getSelection error:", error);
            return createUltimateSafeSelection();
        }
    };
    
    // Ultimate createRange override
    document.createRange = function() {
        try {
            if (originalCreateRange) {
                return originalCreateRange.call(document);
            }
            return createUltimateSafeRange();
        } catch (error) {
            console.warn("createRange error:", error);
            return createUltimateSafeRange();
        }
    };
    
    // Global error handler for selection errors
    window.addEventListener("error", function(event) {
        if (event.error && event.error.message) {
            const message = event.error.message.toLowerCase();
            if (message.includes("selection") || 
                message.includes("rangecount") || 
                message.includes("getrangeat") ||
                message.includes("contextmenu")) {
                console.warn("Selection error intercepted and handled:", event.error.message);
                event.preventDefault();
                event.stopPropagation();
                return true;
            }
        }
    }, true);
    
    // Prevent context menu errors
    document.addEventListener("contextmenu", function(event) {
        try {
            // Allow normal context menu but catch any errors
            return true;
        } catch (error) {
            console.warn("Context menu error handled:", error);
            return true;
        }
    }, { passive: true });
    
    console.log("✅ Ultimate Selection Error Fix loaded successfully");
    
})();