<?php
require_once 'config.php';

class Orders {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    // Créer une nouvelle commande
    public function createOrder($data) {
        try {
            $this->pdo->beginTransaction();
            
            // Créer une instance de Notifications
            require_once 'notifications.php';
            $notifications = new Notifications($this->pdo);

            // Insérer la commande principale
            $stmt = $this->pdo->prepare(
                'INSERT INTO commandes (session_id, nom_client, telephone, adresse_livraison, montant_total, statut) 
                VALUES (?, ?, ?, ?, ?, ?)'
            );

            $stmt->execute([
                session_id(),
                sanitize($data['nom']),
                sanitize($data['telephone']),
                sanitize($data['adresse']),
                floatval($data['montant_total']),
                'en_attente'
            ]);

            $orderId = $this->pdo->lastInsertId();

            // Insérer les détails de la commande
            foreach ($data['items'] as $item) {
                $stmt = $this->pdo->prepare(
                    'INSERT INTO details_commande (commande_id, livre_id, quantite, prix_unitaire) 
                    VALUES (?, ?, ?, ?)'
                );

                $stmt->execute([
                    $orderId,
                    $item['id'],
                    $item['quantite'],
                    $item['prix']
                ]);

                // Mettre à jour le stock
                $stmt = $this->pdo->prepare(
                    'UPDATE livres SET stock = stock - ? WHERE id = ? AND stock >= ?'
                );
                $stmt->execute([$item['quantite'], $item['id'], $item['quantite']]);

                if ($stmt->rowCount() === 0) {
                    throw new Exception('Stock insuffisant pour le livre ID: ' . $item['id']);
                }
            }

            $this->pdo->commit();
            
            // Créer une notification pour la nouvelle commande
            $notifications->create(
                'new_order',
                sprintf('Nouvelle commande #%d de %s - %s دج', $orderId, $data['nom'], number_format($data['montant_total'], 2)),
                $orderId
            );
            
            return ['success' => true, 'order_id' => $orderId];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            return ['error' => $e->getMessage()];
        }
    }

    // Récupérer une commande par son ID
    public function getOrderById($id) {
        try {
            // Récupérer les informations de la commande
            $stmt = $this->pdo->prepare(
                'SELECT c.*, GROUP_CONCAT(l.titre) as livres 
                FROM commandes c 
                LEFT JOIN details_commande dc ON c.id = dc.commande_id 
                LEFT JOIN livres l ON dc.livre_id = l.id 
                WHERE c.id = ? 
                GROUP BY c.id'
            );
            $stmt->execute([$id]);
            $order = $stmt->fetch();

            if ($order) {
                // Récupérer les détails des livres
                $stmt = $this->pdo->prepare(
                    'SELECT l.titre, l.auteur, dc.quantite, dc.prix_unitaire 
                    FROM details_commande dc 
                    JOIN livres l ON dc.livre_id = l.id 
                    WHERE dc.commande_id = ?'
                );
                $stmt->execute([$id]);
                $order['details'] = $stmt->fetchAll();
            }

            return $order;
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Mettre à jour le statut d'une commande
    public function updateOrderStatus($id, $status) {
        try {
            $stmt = $this->pdo->prepare(
                'UPDATE commandes SET statut = ? WHERE id = ?'
            );
            $stmt->execute([$status, $id]);
            return ['success' => true, 'rows_affected' => $stmt->rowCount()];
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Enregistrer un reçu de paiement
    public function savePaymentReceipt($orderId, $receiptNumber, $receiptImage) {
        try {
            $stmt = $this->pdo->prepare(
                'INSERT INTO recus_paiement (commande_id, numero_recu, image_recu_url) 
                VALUES (?, ?, ?)'
            );
            $stmt->execute([
                $orderId,
                sanitize($receiptNumber),
                sanitize($receiptImage)
            ]);

            // Mettre à jour le statut de la commande
            $this->updateOrderStatus($orderId, 'payé');

            return ['success' => true, 'receipt_id' => $this->pdo->lastInsertId()];
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Récupérer toutes les commandes (pour l'admin)
    public function getAllOrders($filters = []) {
        try {
            $sql = 'SELECT c.*, COUNT(dc.id) as nombre_articles 
                   FROM commandes c 
                   LEFT JOIN details_commande dc ON c.id = dc.commande_id 
                   WHERE 1=1';
            $params = [];
            
            if (!empty($filters['status'])) {
                $sql .= ' AND c.statut = :status';
                $params[':status'] = $filters['status'];
            }
            
            if (!empty($filters['date'])) {
                switch($filters['date']) {
                    case 'today':
                        $sql .= ' AND DATE(c.date_commande) = CURDATE()';
                        break;
                    case 'week':
                        $sql .= ' AND YEARWEEK(c.date_commande) = YEARWEEK(CURDATE())';
                        break;
                    case 'month':
                        $sql .= ' AND YEAR(c.date_commande) = YEAR(CURDATE()) AND MONTH(c.date_commande) = MONTH(CURDATE())';
                        break;
                }
            }
            
            $sql .= ' GROUP BY c.id ORDER BY c.date_commande DESC';
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }
}

// Instancier la classe Orders avec une nouvelle connexion PDO
$pdo = getPDOConnection();
$orders = new Orders($pdo);

// Traitement des requêtes API
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Créer une nouvelle commande
    $data = json_decode(file_get_contents('php://input'), true);
    if ($data) {
        echo json_encode($orders->createOrder($data));
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (isset($_GET['id'])) {
        // Récupérer une commande spécifique
        echo json_encode($orders->getOrderById($_GET['id']));
    } elseif (isAdminLoggedIn()) {
        // Récupérer toutes les commandes (admin uniquement)
        $filters = [
            'status' => $_GET['status'] ?? '',
            'date' => $_GET['date'] ?? ''
        ];
        echo json_encode($orders->getAllOrders($filters));
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'PUT' && isAdminLoggedIn()) {
    // Mettre à jour le statut d'une commande
    $data = json_decode(file_get_contents('php://input'), true);
    if (isset($_GET['id']) && isset($data['status'])) {
        echo json_encode($orders->updateOrderStatus($_GET['id'], $data['status']));
    }
}

// Traitement du reçu de paiement
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'receipt') {
    if (isset($_POST['order_id']) && isset($_POST['receipt_number']) && isset($_FILES['receipt_image'])) {
        // Gérer l'upload de l'image du reçu
        $uploadDir = '../uploads/receipts/';
        $fileExtension = pathinfo($_FILES['receipt_image']['name'], PATHINFO_EXTENSION);
        $fileName = uniqid('receipt_') . '.' . $fileExtension;
        $uploadFile = $uploadDir . $fileName;

        if (move_uploaded_file($_FILES['receipt_image']['tmp_name'], $uploadFile)) {
            echo json_encode($orders->savePaymentReceipt(
                $_POST['order_id'],
                $_POST['receipt_number'],
                'uploads/receipts/' . $fileName
            ));
        } else {
            echo json_encode(['error' => 'Erreur lors de l\'upload du fichier']);
        }
    }
}