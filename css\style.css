/* Reset CSS */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Base Styles */
body {
  font-family: "Noto Sans Arabic", sans-serif;
  line-height: 1.6;
  background-color: #f8f9fa;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.logo h1 {
  font-size: 1.8rem;
  color: #2c3e50;
  font-weight: 700;
}

.cart-icon a {
  color: #2c3e50;
  text-decoration: none;
  font-size: 1.5rem;
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8px;
  left: -8px;
  background-color: #e74c3c;
  color: #fff;
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 50%;
}

/* Hero Section */
.hero {
  padding: 120px 0 80px;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero .container {
  position: relative;
  z-index: 1;
}

.hero h1 {
  font-size: 3.2rem;
  margin-bottom: 15px;
  font-weight: 800;
  animation: fadeInUp 0.8s ease;
}

.hero h2 {
  font-size: 2.2rem;
  margin-bottom: 15px;
  font-weight: 600;
  animation: fadeInUp 1s ease;
}

.hero p {
  font-size: 1.3rem;
  opacity: 0.9;
  max-width: 700px;
  margin: 0 auto 30px auto;
  line-height: 1.6;
  animation: fadeInUp 1.2s ease;
}

.hero-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
  animation: fadeInUp 1.4s ease;
  flex-wrap: wrap;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* CTA Buttons */
.cta-button {
  display: inline-block;
  padding: 15px 30px;
  border-radius: 50px;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 1.1rem;
  border: none;
  cursor: pointer;
}

.cta-button.primary {
  background: #ff6b6b;
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid white;
}

.cta-button.large {
  padding: 18px 36px;
  font-size: 1.3rem;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.cta-button.primary:hover {
  background: #ff5252;
  box-shadow: 0 10px 25px rgba(255, 82, 82, 0.4);
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Features Section */
.features {
  padding: 80px 0;
  background: #f8fafc;
}

.features h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 50px;
  color: #2d3748;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.feature-card {
  background: white;
  padding: 40px 30px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  display: block;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #2d3748;
}

.feature-card p {
  color: #718096;
  line-height: 1.6;
}

/* How It Works Section */
.how-it-works {
  padding: 80px 0;
  background: white;
}

.how-it-works h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 50px;
  color: #2d3748;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.step {
  text-align: center;
  position: relative;
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 20px auto;
}

.step h3 {
  font-size: 1.4rem;
  margin-bottom: 15px;
  color: #2d3748;
}

.step p {
  color: #718096;
  line-height: 1.6;
}

/* Pricing Section */
.pricing {
  padding: 80px 0;
  background: #f8fafc;
}

.pricing h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 50px;
  color: #2d3748;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.pricing-card {
  background: white;
  padding: 40px 30px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  position: relative;
}

.pricing-card.featured {
  border: 2px solid #667eea;
  transform: scale(1.05);
}

.pricing-card.featured::before {
  content: 'الأكثر شعبية';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: #667eea;
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.pricing-card.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.pricing-card h3 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #2d3748;
}

.price {
  font-size: 2.5rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 30px;
}

.price span {
  font-size: 1rem;
  color: #718096;
  font-weight: normal;
}

.pricing-card ul {
  list-style: none;
  padding: 0;
  margin: 0 0 30px 0;
}

.pricing-card li {
  padding: 10px 0;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
}

.pricing-card li:last-child {
  border-bottom: none;
}

.pricing-button {
  display: inline-block;
  background: #667eea;
  color: white;
  padding: 12px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: bold;
  transition: all 0.3s ease;
}

.pricing-button:hover {
  background: #5a67d8;
  transform: translateY(-2px);
}

/* Final CTA Section */
.final-cta {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.final-cta h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.final-cta p {
  font-size: 1.3rem;
  opacity: 0.9;
  margin-bottom: 30px;
}

/* Products Section */
.books {
  padding: 60px 0;
}

.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  padding: 20px 0;
}

.book-card {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.book-card:hover {
  transform: translateY(-5px);
}

.book-card img {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.book-info {
  padding: 20px;
}

.book-info h3 {
  font-size: 1.4rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.author {
  color: #7f8c8d;
  margin-bottom: 0.5rem;
}

.price {
  font-size: 1.2rem;
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 1rem;
}

/* Product Type Specific Styles */
.book-card[data-type="book"] {
  border-top: 4px solid #3498db;
}

.book-card[data-type="bag"] {
  border-top: 4px solid #e67e22;
}

.book-card[data-type="laptop"] {
  border-top: 4px solid #9b59b6;
}

/* Product Details */
.product-details {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.product-details p {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.buttons {
  display: flex;
  gap: 10px;
}

.add-to-cart,
.buy-now {
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-family: "Noto Sans Arabic", sans-serif;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.add-to-cart {
  background-color: #3498db;
  color: #fff;
  flex: 1;
}

.add-to-cart:hover {
  background-color: #2980b9;
}

.buy-now {
  background-color: #2ecc71;
  color: #fff;
  flex: 1;
}

.buy-now:hover {
  background-color: #27ae60;
}

/* Footer */
.footer {
  background-color: #2c3e50;
  color: #fff;
  padding: 20px 0;
  text-align: center;
}

/* Enhanced Responsive Design */

/* Mobile Small (320px and up) */
@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .nav {
    padding: 0.8rem 0;
    flex-wrap: wrap;
  }

  .logo h1 {
    font-size: 1.5rem;
  }

  .cart-icon a {
    font-size: 1.3rem;
    padding: 12px;
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: rgba(44, 62, 80, 0.1);
  }

  .hero {
    padding: 90px 0 30px;
  }

  .hero h2 {
    font-size: 1.8rem;
    line-height: 1.3;
  }

  .hero p {
    font-size: 1rem;
    padding: 0 10px;
  }

  .books-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px 0;
  }

  .book-card {
    margin: 0 auto;
    max-width: 300px;
  }

  .book-card img {
    height: 200px;
  }

  .book-card h3 {
    font-size: 1.1rem;
    padding: 0 10px;
  }

  .book-card p {
    font-size: 0.9rem;
    padding: 0 10px;
  }

  .buttons {
    flex-direction: column;
    gap: 10px;
    padding: 15px;
  }

  .btn {
    width: 100%;
    min-height: 44px;
    font-size: 1rem;
    padding: 12px 20px;
  }
}

/* Mobile Medium (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .cart-icon a {
    padding: 10px;
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
  }

  .hero {
    padding: 100px 0 40px;
  }

  .hero h2 {
    font-size: 2rem;
  }

  .books-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .book-card img {
    height: 250px;
  }

  .buttons {
    flex-direction: column;
    gap: 12px;
  }

  .btn {
    min-height: 44px;
    padding: 12px 24px;
  }
}

/* Tablet (769px to 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .books-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
  }

  .hero h2 {
    font-size: 2.2rem;
  }
}

/* Touch-friendly improvements for all mobile devices */
@media (max-width: 1024px) {
  /* Ensure all interactive elements are touch-friendly */
  button,
  .btn,
  a[role="button"],
  input[type="submit"],
  input[type="button"] {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  /* Improve form elements for touch */
  input,
  textarea,
  select {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
  }

  /* Better spacing for touch navigation */
  .nav a,
  .nav button {
    padding: 12px 16px;
    margin: 4px;
  }

  /* Improve readability */
  body {
    font-size: 16px;
    line-height: 1.6;
  }

  /* Better contrast for mobile */
  .book-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .book-card:hover {
    transform: translateY(-2px);
  }
}

/* ========================================
   ACCESSIBILITY ENHANCEMENTS
   ======================================== */

/* Screen reader only content */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Enhanced focus indicators for accessibility */
*:focus {
  outline: 3px solid #0066cc !important;
  outline-offset: 2px !important;
}

/* Skip links for keyboard navigation */
.skip-links {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10000;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px 12px;
  text-decoration: none;
  border-radius: 4px;
  font-weight: bold;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 6px;
  outline: 2px solid #fff;
}

/* High contrast mode support */
.high-contrast {
  filter: contrast(150%);
}

.high-contrast * {
  background-color: #000 !important;
  color: #fff !important;
  border-color: #fff !important;
}

.high-contrast *:focus {
  outline: 4px solid #ffff00 !important;
  outline-offset: 3px !important;
}

.high-contrast .btn-primary {
  background-color: #0066cc !important;
  color: #fff !important;
}

.high-contrast img {
  filter: contrast(120%) brightness(120%);
}

/* Improved color contrast for better readability */
.book-card h3 {
  color: #1a1a1a;
  font-weight: 600;
}

.book-card p {
  color: #333;
  line-height: 1.5;
}

.price {
  color: #2c3e50;
  font-weight: bold;
  font-size: 1.1em;
}

/* Enhanced button accessibility */
.btn {
  font-weight: 600;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.btn:focus {
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3);
}

.btn-primary {
  background-color: #2c3e50;
  color: #fff;
  border-color: #2c3e50;
}

.btn-primary:hover, .btn-primary:focus {
  background-color: #1a252f;
  border-color: #1a252f;
}

.btn-secondary {
  background-color: transparent;
  color: #2c3e50;
  border-color: #2c3e50;
}

.btn-secondary:hover, .btn-secondary:focus {
  background-color: #2c3e50;
  color: #fff;
}

/* Form accessibility improvements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
  border: 2px solid #ddd;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 16px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #0066cc;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
  outline: none;
}

.form-group input[aria-invalid="true"],
.form-group textarea[aria-invalid="true"],
.form-group select[aria-invalid="true"] {
  border-color: #d32f2f;
}

.error-message {
  color: #d32f2f;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Enhanced navigation accessibility */
.nav a {
  color: #2c3e50;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.nav a:hover,
.nav a:focus {
  background-color: rgba(44, 62, 80, 0.1);
  text-decoration: underline;
}

/* Cart accessibility enhancements */
.cart-icon {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #e74c3c;
  color: #fff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}
