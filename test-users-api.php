<?php
/**
 * Test script for Users API
 */

require_once 'php/config.php';

echo "<h1>Testing Users API</h1>";

try {
    echo "<h2>1. Testing Database Connection</h2>";
    $pdo = getPDOConnection();
    echo "✅ Database connection successful<br>";
    
    echo "<h2>2. Testing Users Table</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Users table exists<br>";
        
        // Check users count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $count = $stmt->fetch()['count'];
        echo "📊 Total users in database: $count<br>";
        
        // Show all users
        echo "<h3>Users in database:</h3>";
        $stmt = $pdo->query("SELECT id, username, email, first_name, last_name, status, created_at FROM users ORDER BY id");
        $users = $stmt->fetchAll();
        
        if (empty($users)) {
            echo "❌ No users found in database<br>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Status</th><th>Created</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['first_name']} {$user['last_name']}</td>";
                echo "<td>{$user['status']}</td>";
                echo "<td>{$user['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "❌ Users table does not exist<br>";
    }
    
    echo "<h2>3. Testing User Roles Table</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_roles'");
    if ($stmt->rowCount() > 0) {
        echo "✅ User roles table exists<br>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_roles");
        $count = $stmt->fetch()['count'];
        echo "📊 Total roles: $count<br>";
        
        if ($count > 0) {
            echo "<h4>Available roles:</h4>";
            $stmt = $pdo->query("SELECT id, name, display_name_ar FROM user_roles");
            $roles = $stmt->fetchAll();
            echo "<ul>";
            foreach ($roles as $role) {
                echo "<li>ID: {$role['id']}, Name: {$role['name']}, Display: {$role['display_name_ar']}</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "❌ User roles table does not exist<br>";
    }
    
    echo "<h2>4. Testing Subscription Plans Table</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'subscription_plans'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Subscription plans table exists<br>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM subscription_plans");
        $count = $stmt->fetch()['count'];
        echo "📊 Total subscription plans: $count<br>";
    } else {
        echo "❌ Subscription plans table does not exist<br>";
    }
    
    echo "<h2>5. Testing Users API Query</h2>";
    
    // Test the exact query from the API
    $stmt = $pdo->prepare("
        SELECT
            u.*,
            ur.display_name_ar as role_name,
            ur.level as role_level,
            sp.display_name_ar as subscription_name,
            sp.max_products,
            sp.max_landing_pages,
            sp.max_storage_mb,
            us.store_name,
            us.store_slug
        FROM users u
        LEFT JOIN user_roles ur ON u.role_id = ur.id
        LEFT JOIN subscription_plans sp ON u.subscription_id = sp.id
        LEFT JOIN user_stores us ON u.store_id = us.id
        ORDER BY u.created_at DESC
    ");
    
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    echo "📊 Query returned " . count($users) . " users<br>";
    
    if (!empty($users)) {
        echo "<h4>Users with full details:</h4>";
        echo "<pre>";
        foreach ($users as $user) {
            echo "ID: {$user['id']}\n";
            echo "Username: {$user['username']}\n";
            echo "Email: {$user['email']}\n";
            echo "Name: {$user['first_name']} {$user['last_name']}\n";
            echo "Role: {$user['role_name']}\n";
            echo "Subscription: {$user['subscription_name']}\n";
            echo "Status: {$user['status']}\n";
            echo "---\n";
        }
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>6. Direct API Test</h2>";
echo "<a href='php/api/users.php?action=list' target='_blank'>Test Users API directly</a><br>";
?>
