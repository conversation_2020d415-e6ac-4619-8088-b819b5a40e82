/**
 * Advanced Search System with Arabic Support
 * Provides real-time search, filters, and RTL-optimized interface
 */

class SearchSystem {
    constructor() {
        this.searchData = [];
        this.filteredData = [];
        this.currentFilters = {
            category: '',
            priceMin: '',
            priceMax: '',
            type: '',
            author: ''
        };
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.createSearchInterface();
        this.createSearchStyles();
        this.setupEventListeners();
        this.loadSearchData();
        this.setupKeyboardNavigation();
    }

    /**
     * Create search interface HTML
     */
    createSearchInterface() {
        const searchHTML = `
            <div class="search-container" id="search-container">
                <div class="search-header">
                    <div class="search-input-wrapper">
                        <input type="text"
                               id="search-input"
                               class="search-input"
                               placeholder="ابحث عن المنتجات..."
                               aria-label="البحث في المنتجات"
                               autocomplete="off">
                        <button class="search-btn" aria-label="بحث">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="search-clear" aria-label="مسح البحث" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <button class="filters-toggle" aria-label="إظهار الفلاتر">
                        <i class="fas fa-filter"></i>
                        <span>الفلاتر</span>
                    </button>
                </div>

                <div class="search-suggestions" id="search-suggestions" style="display: none;"></div>

                <div class="search-filters" id="search-filters" style="display: none;">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label for="category-filter">الفئة</label>
                            <select id="category-filter" class="filter-select">
                                <option value="">جميع الفئات</option>
                                <option value="books">كتب</option>
                                <option value="bags">حقائب</option>
                                <option value="laptops">أجهزة لابتوب</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="type-filter">النوع</label>
                            <select id="type-filter" class="filter-select">
                                <option value="">جميع الأنواع</option>
                                <option value="book">كتاب</option>
                                <option value="bag">حقيبة</option>
                                <option value="laptop">لابتوب</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="price-min">السعر من</label>
                            <input type="number" id="price-min" class="filter-input" placeholder="0" min="0">
                        </div>

                        <div class="filter-group">
                            <label for="price-max">السعر إلى</label>
                            <input type="number" id="price-max" class="filter-input" placeholder="1000" min="0">
                        </div>

                        <div class="filter-group">
                            <label for="author-filter">المؤلف</label>
                            <input type="text" id="author-filter" class="filter-input" placeholder="اسم المؤلف">
                        </div>

                        <div class="filter-actions">
                            <button class="apply-filters-btn">تطبيق الفلاتر</button>
                            <button class="clear-filters-btn">مسح الفلاتر</button>
                        </div>
                    </div>
                </div>

                <div class="search-results" id="search-results" style="display: none;">
                    <div class="results-header">
                        <span class="results-count">0 نتيجة</span>
                        <div class="sort-options">
                            <label for="sort-select">ترتيب حسب:</label>
                            <select id="sort-select">
                                <option value="relevance">الصلة</option>
                                <option value="price-asc">السعر: من الأقل للأعلى</option>
                                <option value="price-desc">السعر: من الأعلى للأقل</option>
                                <option value="name-asc">الاسم: أ-ي</option>
                                <option value="name-desc">الاسم: ي-أ</option>
                            </select>
                        </div>
                    </div>
                    <div class="results-grid" id="results-grid"></div>
                    <div class="pagination" id="search-pagination"></div>
                </div>

                <div class="no-results" id="no-results" style="display: none;">
                    <div class="no-results-icon">🔍</div>
                    <h3>لا توجد نتائج</h3>
                    <p>لم نجد أي منتجات تطابق بحثك. جرب:</p>
                    <ul>
                        <li>التحقق من الإملاء</li>
                        <li>استخدام كلمات أقل أو مختلفة</li>
                        <li>استخدام مصطلحات أكثر عمومية</li>
                    </ul>
                </div>
            </div>
        `;

        // Insert search interface into navigation or create dedicated search page
        const nav = document.querySelector('.nav, nav');
        if (nav) {
            const searchToggle = document.createElement('button');
            searchToggle.className = 'search-toggle';
            searchToggle.innerHTML = '<i class="fas fa-search"></i>';
            searchToggle.setAttribute('aria-label', 'فتح البحث');
            nav.appendChild(searchToggle);

            searchToggle.addEventListener('click', () => {
                this.toggleSearchInterface();
            });
        }

        // Create search overlay
        const searchOverlay = document.createElement('div');
        searchOverlay.className = 'search-overlay';
        searchOverlay.innerHTML = searchHTML;
        document.body.appendChild(searchOverlay);
    }

    /**
     * Create CSS styles for search system
     */
    createSearchStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* Search System Styles */
            .search-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                z-index: 10000;
                display: none;
                backdrop-filter: blur(4px);
            }

            .search-overlay.active {
                display: flex;
                align-items: flex-start;
                justify-content: center;
                padding: 2rem;
                overflow-y: auto;
            }

            .search-container {
                background: white;
                border-radius: 12px;
                width: 100%;
                max-width: 800px;
                max-height: 90vh;
                overflow-y: auto;
                direction: rtl;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            }

            .search-header {
                padding: 1.5rem;
                border-bottom: 1px solid #e9ecef;
                display: flex;
                gap: 1rem;
                align-items: center;
            }

            .search-input-wrapper {
                flex: 1;
                position: relative;
            }

            .search-input {
                width: 100%;
                padding: 12px 50px 12px 16px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 16px;
                direction: rtl;
                transition: border-color 0.2s ease;
            }

            .search-input:focus {
                outline: none;
                border-color: #3498db;
                box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            }

            .search-btn, .search-clear {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                background: none;
                border: none;
                padding: 8px;
                cursor: pointer;
                color: #6c757d;
                border-radius: 4px;
                transition: color 0.2s ease;
            }

            .search-btn {
                left: 12px;
            }

            .search-clear {
                left: 45px;
            }

            .search-btn:hover, .search-clear:hover {
                color: #3498db;
                background: rgba(52, 152, 219, 0.1);
            }

            .filters-toggle {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                padding: 12px 16px;
                border-radius: 8px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;
                transition: all 0.2s ease;
                white-space: nowrap;
            }

            .filters-toggle:hover {
                background: #e9ecef;
            }

            .filters-toggle.active {
                background: #3498db;
                color: white;
                border-color: #3498db;
            }

            .search-suggestions {
                max-height: 300px;
                overflow-y: auto;
                border-bottom: 1px solid #e9ecef;
            }

            .suggestion-item {
                padding: 12px 1.5rem;
                cursor: pointer;
                border-bottom: 1px solid #f8f9fa;
                display: flex;
                align-items: center;
                gap: 12px;
                transition: background 0.2s ease;
            }

            .suggestion-item:hover,
            .suggestion-item.highlighted {
                background: #f8f9fa;
            }

            .suggestion-icon {
                color: #6c757d;
                width: 16px;
            }

            .suggestion-text {
                flex: 1;
            }

            .suggestion-type {
                font-size: 0.8rem;
                color: #6c757d;
                background: #e9ecef;
                padding: 2px 8px;
                border-radius: 12px;
            }

            .search-filters {
                padding: 1.5rem;
                border-bottom: 1px solid #e9ecef;
                background: #f8f9fa;
            }

            .filters-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                margin-bottom: 1rem;
            }

            .filter-group {
                display: flex;
                flex-direction: column;
                gap: 6px;
            }

            .filter-group label {
                font-weight: 600;
                color: #2c3e50;
                font-size: 0.9rem;
            }

            .filter-select, .filter-input {
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
                direction: rtl;
                transition: border-color 0.2s ease;
            }

            .filter-select:focus, .filter-input:focus {
                outline: none;
                border-color: #3498db;
            }

            .filter-actions {
                grid-column: 1 / -1;
                display: flex;
                gap: 1rem;
                justify-content: flex-end;
            }

            .apply-filters-btn, .clear-filters-btn {
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                border: none;
            }

            .apply-filters-btn {
                background: #3498db;
                color: white;
            }

            .apply-filters-btn:hover {
                background: #2980b9;
            }

            .clear-filters-btn {
                background: #6c757d;
                color: white;
            }

            .clear-filters-btn:hover {
                background: #5a6268;
            }

            .search-results {
                padding: 1.5rem;
            }

            .results-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1.5rem;
                flex-wrap: wrap;
                gap: 1rem;
            }

            .results-count {
                font-weight: 600;
                color: #2c3e50;
            }

            .sort-options {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .sort-options label {
                font-size: 0.9rem;
                color: #6c757d;
            }

            .sort-options select {
                padding: 6px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }

            .results-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-bottom: 2rem;
            }

            .search-result-card {
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                overflow: hidden;
                transition: all 0.2s ease;
                cursor: pointer;
            }

            .search-result-card:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transform: translateY(-2px);
            }

            .search-result-image {
                width: 100%;
                height: 200px;
                object-fit: cover;
                background: #f8f9fa;
            }

            .search-result-content {
                padding: 1rem;
            }

            .search-result-title {
                font-weight: 600;
                margin-bottom: 0.5rem;
                color: #2c3e50;
                line-height: 1.4;
            }

            .search-result-meta {
                font-size: 0.9rem;
                color: #6c757d;
                margin-bottom: 0.5rem;
            }

            .search-result-price {
                font-weight: bold;
                color: #e74c3c;
                font-size: 1.1rem;
            }

            .search-result-type {
                display: inline-block;
                background: #3498db;
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
                margin-bottom: 0.5rem;
            }

            .no-results {
                text-align: center;
                padding: 3rem 1.5rem;
                color: #6c757d;
            }

            .no-results-icon {
                font-size: 4rem;
                margin-bottom: 1rem;
            }

            .no-results h3 {
                color: #2c3e50;
                margin-bottom: 1rem;
            }

            .no-results ul {
                text-align: right;
                max-width: 300px;
                margin: 1rem auto 0;
            }

            .pagination {
                display: flex;
                justify-content: center;
                gap: 0.5rem;
                margin-top: 2rem;
            }

            .pagination button {
                padding: 8px 12px;
                border: 1px solid #ddd;
                background: white;
                cursor: pointer;
                border-radius: 4px;
                transition: all 0.2s ease;
            }

            .pagination button:hover {
                background: #f8f9fa;
            }

            .pagination button.active {
                background: #3498db;
                color: white;
                border-color: #3498db;
            }

            .pagination button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            /* Mobile Responsive */
            @media (max-width: 768px) {
                .search-overlay {
                    padding: 1rem;
                }

                .search-header {
                    flex-direction: column;
                    gap: 1rem;
                }

                .filters-grid {
                    grid-template-columns: 1fr;
                }

                .results-header {
                    flex-direction: column;
                    align-items: stretch;
                }

                .results-grid {
                    grid-template-columns: 1fr;
                }

                .filter-actions {
                    flex-direction: column;
                }
            }

            /* RTL Specific Styles */
            [dir="rtl"] .search-input {
                padding: 12px 16px 12px 50px;
            }

            [dir="rtl"] .search-btn {
                left: auto;
                right: 12px;
            }

            [dir="rtl"] .search-clear {
                left: auto;
                right: 45px;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search input events
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearchInput(e.target.value);
            });

            searchInput.addEventListener('keydown', (e) => {
                this.handleSearchKeydown(e);
            });
        }

        // Filter events
        const filtersToggle = document.querySelector('.filters-toggle');
        if (filtersToggle) {
            filtersToggle.addEventListener('click', () => {
                this.toggleFilters();
            });
        }

        // Apply filters
        const applyFiltersBtn = document.querySelector('.apply-filters-btn');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.applyFilters();
            });
        }

        // Clear filters
        const clearFiltersBtn = document.querySelector('.clear-filters-btn');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }

        // Sort change
        const sortSelect = document.getElementById('sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortResults(e.target.value);
            });
        }

        // Close search overlay
        const searchOverlay = document.querySelector('.search-overlay');
        if (searchOverlay) {
            searchOverlay.addEventListener('click', (e) => {
                if (e.target === searchOverlay) {
                    this.closeSearchInterface();
                }
            });
        }

        // Clear search
        const searchClear = document.querySelector('.search-clear');
        if (searchClear) {
            searchClear.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeSearchInterface();
            }
        });
    }

    /**
     * Load search data from API
     */
    async loadSearchData() {
        try {
            const response = await fetch('/php/api/products.php');
            const data = await response.json();

            if (data.success && data.data) {
                this.searchData = data.data.map(product => ({
                    id: product.id,
                    title: product.titre,
                    description: product.description,
                    price: parseFloat(product.prix),
                    type: product.type,
                    author: product.auteur || '',
                    material: product.materiel || '',
                    processor: product.processeur || '',
                    ram: product.ram || '',
                    storage: product.stockage || '',
                    capacity: product.capacite || '',
                    image: product.image_url,
                    category: this.getCategoryFromType(product.type)
                }));
            }
        } catch (error) {
            console.error('Error loading search data:', error);
            errorHandler.showError('فشل في تحميل بيانات البحث');
        }
    }

    /**
     * Get category from product type
     */
    getCategoryFromType(type) {
        const categoryMap = {
            'book': 'books',
            'bag': 'bags',
            'laptop': 'laptops'
        };
        return categoryMap[type] || 'other';
    }

    /**
     * Handle search input with debouncing
     */
    handleSearchInput(query) {
        clearTimeout(this.searchTimeout);

        const searchClear = document.querySelector('.search-clear');
        if (searchClear) {
            searchClear.style.display = query ? 'block' : 'none';
        }

        if (query.length < 2) {
            this.hideSuggestions();
            this.hideResults();
            return;
        }

        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
            this.showSuggestions(query);
        }, 300);
    }

    /**
     * Perform search with Arabic text support
     */
    performSearch(query) {
        if (!query || query.length < 2) {
            this.hideResults();
            return;
        }

        // Normalize Arabic text for better search
        const normalizedQuery = this.normalizeArabicText(query.toLowerCase());

        this.filteredData = this.searchData.filter(item => {
            const searchableText = [
                item.title,
                item.description,
                item.author,
                item.material,
                item.processor,
                item.ram,
                item.storage,
                item.capacity
            ].join(' ').toLowerCase();

            const normalizedText = this.normalizeArabicText(searchableText);

            // Check for exact matches and partial matches
            return normalizedText.includes(normalizedQuery) ||
                   this.fuzzyMatch(normalizedQuery, normalizedText);
        });

        // Apply current filters
        this.applyCurrentFilters();

        // Show results
        this.displayResults();
    }

    /**
     * Normalize Arabic text for better search
     */
    normalizeArabicText(text) {
        return text
            .replace(/[أإآ]/g, 'ا')  // Normalize Alef variations
            .replace(/[ىي]/g, 'ي')   // Normalize Ya variations
            .replace(/ة/g, 'ه')      // Normalize Ta Marbuta
            .replace(/[ؤئ]/g, 'و')   // Normalize Waw and Ya with Hamza
            .replace(/[^\u0600-\u06FF\u0750-\u077F\w\s]/g, ' ') // Keep Arabic, Latin, and spaces
            .replace(/\s+/g, ' ')    // Normalize spaces
            .trim();
    }

    /**
     * Fuzzy matching for better search results
     */
    fuzzyMatch(query, text) {
        const queryWords = query.split(' ').filter(word => word.length > 1);
        let matchCount = 0;

        queryWords.forEach(word => {
            if (text.includes(word)) {
                matchCount++;
            }
        });

        // Return true if at least 70% of words match
        return (matchCount / queryWords.length) >= 0.7;
    }

    /**
     * Show search suggestions
     */
    showSuggestions(query) {
        const suggestionsContainer = document.getElementById('search-suggestions');
        if (!suggestionsContainer) return;

        const suggestions = this.generateSuggestions(query);

        if (suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }

        suggestionsContainer.innerHTML = suggestions.map((suggestion, index) => `
            <div class="suggestion-item" data-index="${index}" data-value="${suggestion.text}">
                <i class="suggestion-icon ${suggestion.icon}"></i>
                <span class="suggestion-text">${suggestion.text}</span>
                <span class="suggestion-type">${suggestion.type}</span>
            </div>
        `).join('');

        // Add click handlers
        suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const value = item.getAttribute('data-value');
                document.getElementById('search-input').value = value;
                this.performSearch(value);
                this.hideSuggestions();
            });
        });

        suggestionsContainer.style.display = 'block';
    }

    /**
     * Generate search suggestions
     */
    generateSuggestions(query) {
        const suggestions = [];
        const normalizedQuery = this.normalizeArabicText(query.toLowerCase());

        // Product title suggestions
        const titleMatches = this.searchData
            .filter(item => this.normalizeArabicText(item.title.toLowerCase()).includes(normalizedQuery))
            .slice(0, 3)
            .map(item => ({
                text: item.title,
                type: 'منتج',
                icon: 'fas fa-box'
            }));

        suggestions.push(...titleMatches);

        // Author suggestions
        const authorMatches = [...new Set(this.searchData
            .filter(item => item.author && this.normalizeArabicText(item.author.toLowerCase()).includes(normalizedQuery))
            .map(item => item.author))]
            .slice(0, 2)
            .map(author => ({
                text: author,
                type: 'مؤلف',
                icon: 'fas fa-user'
            }));

        suggestions.push(...authorMatches);

        // Category suggestions
        const categories = {
            'كتب': 'books',
            'حقائب': 'bags',
            'لابتوب': 'laptops'
        };

        Object.keys(categories).forEach(categoryName => {
            if (this.normalizeArabicText(categoryName).includes(normalizedQuery)) {
                suggestions.push({
                    text: categoryName,
                    type: 'فئة',
                    icon: 'fas fa-folder'
                });
            }
        });

        return suggestions.slice(0, 5);
    }

    /**
     * Hide suggestions
     */
    hideSuggestions() {
        const suggestionsContainer = document.getElementById('search-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }

    /**
     * Display search results
     */
    displayResults() {
        const resultsContainer = document.getElementById('search-results');
        const noResultsContainer = document.getElementById('no-results');
        const resultsGrid = document.getElementById('results-grid');
        const resultsCount = document.querySelector('.results-count');

        if (this.filteredData.length === 0) {
            resultsContainer.style.display = 'none';
            noResultsContainer.style.display = 'block';
            return;
        }

        noResultsContainer.style.display = 'none';
        resultsContainer.style.display = 'block';

        // Update results count
        if (resultsCount) {
            resultsCount.textContent = `${this.filteredData.length} نتيجة`;
        }

        // Display results
        resultsGrid.innerHTML = this.filteredData.map(item => this.createResultCard(item)).join('');

        // Setup pagination if needed
        this.setupPagination();
    }

    /**
     * Create result card HTML
     */
    createResultCard(item) {
        const typeLabels = {
            'book': 'كتاب',
            'bag': 'حقيبة',
            'laptop': 'لابتوب'
        };

        return `
            <div class="search-result-card" data-id="${item.id}">
                <img src="${item.image || '/images/placeholder.jpg'}"
                     alt="${item.title}"
                     class="search-result-image"
                     loading="lazy">
                <div class="search-result-content">
                    <div class="search-result-type">${typeLabels[item.type] || item.type}</div>
                    <h3 class="search-result-title">${item.title}</h3>
                    ${item.author ? `<div class="search-result-meta">المؤلف: ${item.author}</div>` : ''}
                    <div class="search-result-price">${item.price} دج</div>
                </div>
            </div>
        `;
    }

    /**
     * Toggle search interface
     */
    toggleSearchInterface() {
        const searchOverlay = document.querySelector('.search-overlay');
        if (searchOverlay) {
            searchOverlay.classList.toggle('active');

            if (searchOverlay.classList.contains('active')) {
                // Focus search input when opened
                setTimeout(() => {
                    const searchInput = document.getElementById('search-input');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }, 100);
            }
        }
    }

    /**
     * Close search interface
     */
    closeSearchInterface() {
        const searchOverlay = document.querySelector('.search-overlay');
        if (searchOverlay) {
            searchOverlay.classList.remove('active');
        }
    }

    /**
     * Toggle filters panel
     */
    toggleFilters() {
        const filtersPanel = document.getElementById('search-filters');
        const filtersToggle = document.querySelector('.filters-toggle');

        if (filtersPanel && filtersToggle) {
            const isVisible = filtersPanel.style.display === 'block';
            filtersPanel.style.display = isVisible ? 'none' : 'block';
            filtersToggle.classList.toggle('active', !isVisible);
        }
    }

    /**
     * Apply filters to search results
     */
    applyFilters() {
        this.currentFilters = {
            category: document.getElementById('category-filter')?.value || '',
            type: document.getElementById('type-filter')?.value || '',
            priceMin: document.getElementById('price-min')?.value || '',
            priceMax: document.getElementById('price-max')?.value || '',
            author: document.getElementById('author-filter')?.value || ''
        };

        this.applyCurrentFilters();
        this.displayResults();

        // Show success message
        errorHandler.showSuccess('تم تطبيق الفلاتر بنجاح');
    }

    /**
     * Apply current filters to filtered data
     */
    applyCurrentFilters() {
        let filtered = [...this.filteredData];

        // Category filter
        if (this.currentFilters.category) {
            filtered = filtered.filter(item => item.category === this.currentFilters.category);
        }

        // Type filter
        if (this.currentFilters.type) {
            filtered = filtered.filter(item => item.type === this.currentFilters.type);
        }

        // Price range filter
        if (this.currentFilters.priceMin) {
            const minPrice = parseFloat(this.currentFilters.priceMin);
            filtered = filtered.filter(item => item.price >= minPrice);
        }

        if (this.currentFilters.priceMax) {
            const maxPrice = parseFloat(this.currentFilters.priceMax);
            filtered = filtered.filter(item => item.price <= maxPrice);
        }

        // Author filter
        if (this.currentFilters.author) {
            const authorQuery = this.normalizeArabicText(this.currentFilters.author.toLowerCase());
            filtered = filtered.filter(item =>
                this.normalizeArabicText(item.author.toLowerCase()).includes(authorQuery)
            );
        }

        this.filteredData = filtered;
    }

    /**
     * Clear all filters
     */
    clearFilters() {
        // Reset filter inputs
        document.getElementById('category-filter').value = '';
        document.getElementById('type-filter').value = '';
        document.getElementById('price-min').value = '';
        document.getElementById('price-max').value = '';
        document.getElementById('author-filter').value = '';

        // Reset current filters
        this.currentFilters = {
            category: '',
            priceMin: '',
            priceMax: '',
            type: '',
            author: ''
        };

        // Re-perform search without filters
        const searchInput = document.getElementById('search-input');
        if (searchInput && searchInput.value) {
            this.performSearch(searchInput.value);
        }

        errorHandler.showInfo('تم مسح جميع الفلاتر');
    }

    /**
     * Sort search results
     */
    sortResults(sortBy) {
        switch (sortBy) {
            case 'price-asc':
                this.filteredData.sort((a, b) => a.price - b.price);
                break;
            case 'price-desc':
                this.filteredData.sort((a, b) => b.price - a.price);
                break;
            case 'name-asc':
                this.filteredData.sort((a, b) => a.title.localeCompare(b.title, 'ar'));
                break;
            case 'name-desc':
                this.filteredData.sort((a, b) => b.title.localeCompare(a.title, 'ar'));
                break;
            case 'relevance':
            default:
                // Keep original search relevance order
                break;
        }

        this.displayResults();
    }

    /**
     * Clear search input and results
     */
    clearSearch() {
        const searchInput = document.getElementById('search-input');
        const searchClear = document.querySelector('.search-clear');

        if (searchInput) {
            searchInput.value = '';
            searchInput.focus();
        }

        if (searchClear) {
            searchClear.style.display = 'none';
        }

        this.hideSuggestions();
        this.hideResults();
    }

    /**
     * Hide search results
     */
    hideResults() {
        const resultsContainer = document.getElementById('search-results');
        const noResultsContainer = document.getElementById('no-results');

        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }

        if (noResultsContainer) {
            noResultsContainer.style.display = 'none';
        }
    }

    /**
     * Setup keyboard navigation for search
     */
    setupKeyboardNavigation() {
        let currentSuggestionIndex = -1;

        document.addEventListener('keydown', (e) => {
            const searchInput = document.getElementById('search-input');
            const suggestionsContainer = document.getElementById('search-suggestions');

            if (!searchInput || document.activeElement !== searchInput) {
                return;
            }

            const suggestions = suggestionsContainer?.querySelectorAll('.suggestion-item') || [];

            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    currentSuggestionIndex = Math.min(currentSuggestionIndex + 1, suggestions.length - 1);
                    this.highlightSuggestion(suggestions, currentSuggestionIndex);
                    break;

                case 'ArrowUp':
                    e.preventDefault();
                    currentSuggestionIndex = Math.max(currentSuggestionIndex - 1, -1);
                    this.highlightSuggestion(suggestions, currentSuggestionIndex);
                    break;

                case 'Enter':
                    e.preventDefault();
                    if (currentSuggestionIndex >= 0 && suggestions[currentSuggestionIndex]) {
                        suggestions[currentSuggestionIndex].click();
                    } else {
                        this.performSearch(searchInput.value);
                        this.hideSuggestions();
                    }
                    break;

                case 'Escape':
                    this.hideSuggestions();
                    currentSuggestionIndex = -1;
                    break;
            }
        });
    }

    /**
     * Highlight suggestion item
     */
    highlightSuggestion(suggestions, index) {
        suggestions.forEach((item, i) => {
            item.classList.toggle('highlighted', i === index);
        });
    }

    /**
     * Handle search input keydown events
     */
    handleSearchKeydown(e) {
        // Additional keydown handling if needed
        if (e.key === 'Enter') {
            e.preventDefault();
            this.performSearch(e.target.value);
            this.hideSuggestions();
        }
    }

    /**
     * Setup pagination for search results
     */
    setupPagination() {
        const paginationContainer = document.getElementById('search-pagination');
        if (!paginationContainer || this.filteredData.length <= 12) {
            paginationContainer.innerHTML = '';
            return;
        }

        const itemsPerPage = 12;
        const totalPages = Math.ceil(this.filteredData.length / itemsPerPage);
        const currentPage = 1; // This would be managed by pagination state

        let paginationHTML = '';

        // Previous button
        paginationHTML += `<button ${currentPage === 1 ? 'disabled' : ''}>السابق</button>`;

        // Page numbers
        for (let i = 1; i <= Math.min(totalPages, 5); i++) {
            paginationHTML += `<button class="${i === currentPage ? 'active' : ''}">${i}</button>`;
        }

        // Next button
        paginationHTML += `<button ${currentPage === totalPages ? 'disabled' : ''}>التالي</button>`;

        paginationContainer.innerHTML = paginationHTML;
    }

    /**
     * Get search statistics
     */
    getSearchStats() {
        return {
            totalProducts: this.searchData.length,
            filteredResults: this.filteredData.length,
            activeFilters: Object.values(this.currentFilters).filter(f => f !== '').length
        };
    }
}

// Initialize search system when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const searchSystem = new SearchSystem();

    // Make available globally
    window.searchSystem = searchSystem;
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchSystem;
}
