<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط لزر التعديل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .edit-btn {
            background: #ffc107;
            color: #333;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
        }
        .edit-btn:hover {
            background: #e0a800;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007cba;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <h1>🧪 اختبار بسيط لزر التعديل</h1>
    
    <div class="test-card">
        <h3>صفحة هبوط تجريبية</h3>
        <p>📦 المنتج: منتج تجريبي</p>
        <p>🔗 /landing-page.php?id=2</p>
        
        <div style="display: flex; gap: 10px; margin-top: 15px;">
            <button class="edit-btn" onclick="testEditButton(2)">
                <i class="fas fa-edit"></i> تعديل
            </button>
        </div>
    </div>

    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            console.log(message);
        }

        async function testEditButton(pageId) {
            log(`🖊️ اختبار زر التعديل للصفحة ID=${pageId}...`, 'info');
            
            try {
                // Test 1: Check API endpoint
                log('📡 اختبار API endpoint...', 'info');
                const response = await fetch(`php/api/landing-pages.php?id=${pageId}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const text = await response.text();
                
                if (!text.trim()) {
                    throw new Error('استجابة فارغة');
                }
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (parseError) {
                    throw new Error('خطأ في تحليل JSON');
                }
                
                if (data.success && data.data) {
                    log(`✅ تم العثور على الصفحة: ${data.data.titre}`, 'success');
                    log(`📦 المنتج: ${data.data.product_title}`, 'info');
                    
                    // Test 2: Check if we can simulate the edit process
                    log('🔄 محاكاة عملية التعديل...', 'info');
                    
                    // Simulate opening modal
                    log('🪟 محاكاة فتح المودال...', 'info');
                    
                    // Simulate filling form
                    log('📝 محاكاة ملء النموذج...', 'info');
                    log(`📝 العنوان: ${data.data.titre}`, 'info');
                    log(`📝 المنتج: ${data.data.produit_id}`, 'info');
                    log(`📝 المحتوى الأيمن: ${data.data.contenu_droit ? 'موجود' : 'فارغ'}`, 'info');
                    log(`📝 المحتوى الأيسر: ${data.data.contenu_gauche ? 'موجود' : 'فارغ'}`, 'info');
                    log(`🖼️ الصور: ${data.data.images ? data.data.images.length : 0}`, 'info');
                    
                    log('✅ اختبار زر التعديل نجح! البيانات متوفرة ويمكن تحميلها.', 'success');
                    
                } else {
                    throw new Error('بيانات غير صحيحة من API');
                }
                
            } catch (error) {
                log(`❌ فشل اختبار زر التعديل: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        // Test on page load
        window.addEventListener('load', () => {
            log('🚀 بدء الاختبار التلقائي...', 'info');
            setTimeout(() => {
                testEditButton(2);
            }, 1000);
        });
    </script>
</body>
</html>
