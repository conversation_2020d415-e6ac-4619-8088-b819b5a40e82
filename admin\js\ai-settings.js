/**
 * AI Settings Management for Mossaab Landing Page Admin
 * Handles AI configuration and testing
 */

let currentConfig = null;

document.addEventListener('DOMContentLoaded', function() {
    loadAISettings();
    setupEventListeners();
});

/**
 * Load AI settings from server
 */
async function loadAISettings() {
    try {
        console.log('🔄 Loading AI settings...');
        const response = await fetch('../api/get-ai-settings.php', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📡 AI settings response:', data);

        if (data.success) {
            currentConfig = data.data;
            updateUI(data.data);
            updateStatusAlert(data.data);
            console.log('✅ AI settings loaded successfully');
        } else {
            console.log('⚠️ AI not configured, showing default settings');
            showDefaultSettings();
        }
    } catch (error) {
        console.error('❌ Error loading AI settings:', error);
        showError('خطأ في تحميل إعدادات الذكاء الاصطناعي: ' + error.message);
    }
}

function showDefaultSettings() {
    // Show default empty settings when AI is not configured
    const defaultData = {
        config: {
            openai_api_key: '',
            anthropic_api_key: '',
            google_api_key: '',
            default_provider: 'openai'
        },
        is_available: false,
        available_providers: []
    };
    updateUI(defaultData);
    updateStatusAlert(defaultData);
}

/**
 * Update UI with current settings
 */
function updateUI(data) {
    // Handle the data structure from get-ai-settings.php
    const providers = data || {};

    // Update OpenAI settings
    const openaiData = providers.openai || {};
    document.getElementById('openaiEnabled').checked = openaiData.enabled || false;
    document.getElementById('openaiApiKey').value = openaiData.api_key || '';
    document.getElementById('openaiModel').value = openaiData.model || 'gpt-3.5-turbo';
    updateProviderCard('openai', openaiData.enabled || false);

    // Update Anthropic settings
    const anthropicData = providers.anthropic || {};
    document.getElementById('anthropicEnabled').checked = anthropicData.enabled || false;
    document.getElementById('anthropicApiKey').value = anthropicData.api_key || '';
    document.getElementById('anthropicModel').value = anthropicData.model || 'claude-3-sonnet';
    updateProviderCard('anthropic', anthropicData.enabled || false);

    // Update Gemini settings
    const geminiData = providers.gemini || {};
    document.getElementById('geminiEnabled').checked = geminiData.enabled || false;
    document.getElementById('geminiApiKey').value = geminiData.api_key || '';
    document.getElementById('geminiModel').value = geminiData.model || 'gemini-pro';
    updateProviderCard('gemini', geminiData.enabled || false);

    // Update enabled providers count
    updateEnabledProvidersCount();

    // Add event listeners to update status when fields change
    ['openaiEnabled', 'anthropicEnabled', 'geminiEnabled'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', updateEnabledProvidersCount);
        }
    });

    ['openaiApiKey', 'anthropicApiKey', 'geminiApiKey'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', updateEnabledProvidersCount);
        }
    });
}

/**
 * Update provider card appearance
 */
function updateProviderCard(provider, enabled) {
    const card = document.getElementById(`${provider}Card`);
    const status = document.getElementById(`${provider}Status`);

    if (enabled) {
        card.className = 'ai-provider-card enabled';
        status.className = 'status-badge status-enabled';
        status.textContent = 'مفعل';
    } else {
        card.className = 'ai-provider-card disabled';
        status.className = 'status-badge status-disabled';
        status.textContent = 'غير مفعل';
    }
}

/**
 * Update status alert
 */
function updateStatusAlert(data) {
    const alert = document.getElementById('aiStatusAlert');
    const text = document.getElementById('aiStatusText');

    if (!alert || !text) return;

    // Count enabled providers with valid API keys
    const enabledProviders = [];
    if (data.openai && data.openai.enabled && data.openai.api_key) enabledProviders.push('OpenAI');
    if (data.anthropic && data.anthropic.enabled && data.anthropic.api_key) enabledProviders.push('Anthropic');
    if (data.gemini && data.gemini.enabled && data.gemini.api_key) enabledProviders.push('Gemini');

    if (enabledProviders.length > 0) {
        alert.className = 'alert alert-success';
        text.innerHTML = `<i class="fas fa-check-circle"></i> الذكاء الاصطناعي متاح - المزودين المفعلين: ${enabledProviders.join(', ')}`;
    } else {
        alert.className = 'alert alert-warning';
        text.innerHTML = '<i class="fas fa-exclamation-triangle"></i> الذكاء الاصطناعي غير متاح - يرجى تكوين مزود واحد على الأقل';
    }
}

/**
 * Update enabled providers count
 */
function updateEnabledProvidersCount() {
    const openaiEnabled = document.getElementById('openaiEnabled').checked;
    const anthropicEnabled = document.getElementById('anthropicEnabled').checked;
    const geminiEnabled = document.getElementById('geminiEnabled').checked;

    const openaiKey = document.getElementById('openaiApiKey').value.trim();
    const anthropicKey = document.getElementById('anthropicApiKey').value.trim();
    const geminiKey = document.getElementById('geminiApiKey').value.trim();

    const enabledCount =
        (openaiEnabled && openaiKey ? 1 : 0) +
        (anthropicEnabled && anthropicKey ? 1 : 0) +
        (geminiEnabled && geminiKey ? 1 : 0);

    // Update the status based on enabled providers
    const mockData = {
        openai: { enabled: openaiEnabled && openaiKey, api_key: openaiKey },
        anthropic: { enabled: anthropicEnabled && anthropicKey, api_key: anthropicKey },
        gemini: { enabled: geminiEnabled && geminiKey, api_key: geminiKey }
    };

    updateStatusAlert(mockData);
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Provider enable/disable toggles
    document.getElementById('openaiEnabled').addEventListener('change', function() {
        updateProviderCard('openai', this.checked);
    });

    document.getElementById('anthropicEnabled').addEventListener('change', function() {
        updateProviderCard('anthropic', this.checked);
    });

    document.getElementById('geminiEnabled').addEventListener('change', function() {
        updateProviderCard('gemini', this.checked);
    });
}

/**
 * Test connection to AI provider
 */
async function testConnection(provider) {
    const button = event.target;
    const originalText = button.innerHTML;

    // Get API key from the form
    const apiKeyInput = document.getElementById(`${provider}ApiKey`);
    const apiKey = apiKeyInput ? apiKeyInput.value.trim() : '';

    // Validate API key is provided
    if (!apiKey) {
        showError(`يرجى إدخال مفتاح API لـ ${getProviderDisplayName(provider)} أولاً`);
        return;
    }

    // Validate API key format
    if (!validateApiKeyFormat(provider, apiKey)) {
        showError(`تنسيق مفتاح API غير صالح لـ ${getProviderDisplayName(provider)}`);
        return;
    }

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';

    try {
        // Create a temporary object with just this provider's settings
        const testData = {};
        testData[provider] = {
            key: apiKey,
            model: document.getElementById(`${provider}Model`).value,
            enabled: true
        };

        // Send POST request with API key to the save endpoint
        // This will validate the key and return status
        const response = await fetch('../api/save-ai-settings.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });

        const result = await response.json();

        if (result.success) {
            showSuccess(`✅ تم حفظ إعدادات ${getProviderDisplayName(provider)} بنجاح!`);

            // Update the UI to show the provider is now enabled
            document.getElementById(`${provider}Enabled`).checked = true;
            updateProviderCard(provider, true);
            updateEnabledProvidersCount();

            console.log(`✅ ${provider} settings saved and enabled`);
        } else {
            const errorMsg = result.error || 'خطأ غير معروف';
            showError(`❌ فشل في حفظ إعدادات ${getProviderDisplayName(provider)}: ${errorMsg}`);
        }
    } catch (error) {
        console.error(`Error testing ${provider}:`, error);
        showError(`❌ خطأ في اختبار الاتصال بـ ${getProviderDisplayName(provider)}: ${error.message}`);
    } finally {
        // Restore button
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

/**
 * Get display name for provider
 */
function getProviderDisplayName(provider) {
    const names = {
        'openai': 'OpenAI',
        'anthropic': 'Anthropic Claude',
        'gemini': 'Google Gemini'
    };
    return names[provider] || provider;
}

/**
 * Validate API key format
 */
function validateApiKeyFormat(provider, apiKey) {
    switch (provider) {
        case 'openai':
            return /^sk-[a-zA-Z0-9\-_]{20,}$/.test(apiKey) || /^sk-proj-[a-zA-Z0-9\-_]{20,}$/.test(apiKey);
        case 'anthropic':
            return /^sk-ant-[a-zA-Z0-9\-_]{95,}$/.test(apiKey);
        case 'gemini':
            return /^AIza[a-zA-Z0-9\-_]{35}$/.test(apiKey);
        default:
            return false;
    }
}

/**
 * Save AI settings
 */
async function saveSettings() {
    try {
        // Collect form data for each provider
        const openai = {
            key: document.getElementById('openaiApiKey').value,
            model: document.getElementById('openaiModel').value,
            enabled: document.getElementById('openaiEnabled').checked
        };

        const anthropic = {
            key: document.getElementById('anthropicApiKey').value,
            model: document.getElementById('anthropicModel').value,
            enabled: document.getElementById('anthropicEnabled').checked
        };

        const gemini = {
            key: document.getElementById('geminiApiKey').value,
            model: document.getElementById('geminiModel').value,
            enabled: document.getElementById('geminiEnabled').checked
        };

        // Show loading state
        const saveButton = event.target;
        const originalText = saveButton.innerHTML;
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

        console.log('💾 Saving AI settings:', { openai, anthropic, gemini });

        // Use the correct API endpoint
        const response = await fetch('../api/save-ai-settings.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                openai,
                anthropic,
                gemini
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📡 Save response:', data);

        if (data.success) {
            showSuccess(`تم حفظ الإعدادات بنجاح! ${data.message}`);

            // Update current config with saved data
            currentConfig = data.data;

            // Update UI to reflect saved state
            updateUI(data.data);
            updateStatusAlert(data.data);

            // Refresh AI Magic Wand if available
            if (window.aiMagicWand) {
                await window.aiMagicWand.checkAvailability();
            }

            console.log('✅ AI settings saved and UI updated');
        } else {
            showError(`فشل في حفظ الإعدادات: ${data.error || 'خطأ غير معروف'}`);
        }

        // Restore button
        saveButton.disabled = false;
        saveButton.innerHTML = originalText;

    } catch (error) {
        console.error('Error saving settings:', error);
        showError('خطأ في حفظ الإعدادات');

        // Restore button if there was an error
        if (event && event.target) {
            event.target.disabled = false;
            event.target.innerHTML = 'حفظ الإعدادات';
        }
    }
}

/**
 * Show success message
 */
function showSuccess(message) {
    // Remove any existing alerts first
    removeExistingAlerts();

    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show ai-alert';
    alert.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-check-circle me-2" style="font-size: 1.2em; color: #0f5132;"></i>
            <span class="flex-grow-1">${message}</span>
            <button type="button" class="btn-close ms-2" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    // Enhanced styling for better visibility and RTL support
    alert.style.cssText = `
        margin-bottom: 1rem;
        direction: rtl;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
        border-radius: 8px;
        font-weight: 500;
        animation: slideInDown 0.3s ease-out;
    `;

    const container = document.querySelector('main');
    container.insertBefore(alert, container.firstChild);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.style.animation = 'slideOutUp 0.3s ease-in';
            setTimeout(() => alert.remove(), 300);
        }
    }, 5000);
}

/**
 * Show error message
 */
function showError(message) {
    // Remove any existing alerts first
    removeExistingAlerts();

    const alert = document.createElement('div');
    alert.className = 'alert alert-danger alert-dismissible fade show ai-alert';
    alert.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-circle me-2" style="font-size: 1.2em; color: #842029;"></i>
            <span class="flex-grow-1">${message}</span>
            <button type="button" class="btn-close ms-2" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    // Enhanced styling for better visibility and RTL support
    alert.style.cssText = `
        margin-bottom: 1rem;
        direction: rtl;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
        border-radius: 8px;
        font-weight: 500;
        animation: slideInDown 0.3s ease-out;
    `;

    const container = document.querySelector('main');
    container.insertBefore(alert, container.firstChild);

    // Auto dismiss after 8 seconds (longer for errors)
    setTimeout(() => {
        if (alert.parentNode) {
            alert.style.animation = 'slideOutUp 0.3s ease-in';
            setTimeout(() => alert.remove(), 300);
        }
    }, 8000);
}

/**
 * Remove existing alert messages
 */
function removeExistingAlerts() {
    const existingAlerts = document.querySelectorAll('.ai-alert');
    existingAlerts.forEach(alert => {
        alert.style.animation = 'slideOutUp 0.3s ease-in';
        setTimeout(() => alert.remove(), 300);
    });
}

/**
 * Toggle password visibility for API key inputs
 */
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
        button.title = 'إخفاء كلمة المرور';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
        button.title = 'إظهار كلمة المرور';
    }
}

/**
 * Update model information display
 */
function updateModelInfo(provider, modelValue) {
    const modelInfoElement = document.getElementById(`${provider}ModelInfo`);
    const selectedOption = document.querySelector(`#${provider}Model option[value="${modelValue}"]`);

    if (!selectedOption || !modelInfoElement) return;

    const cost = selectedOption.getAttribute('data-cost');
    const description = selectedOption.getAttribute('data-description');

    let costBadgeClass = '';
    let costText = '';

    switch (cost) {
        case 'budget':
            costBadgeClass = 'budget';
            costText = 'اقتصادي';
            break;
        case 'balanced':
            costBadgeClass = 'balanced';
            costText = 'متوازن';
            break;
        case 'premium':
            costBadgeClass = 'premium';
            costText = 'متقدم';
            break;
        default:
            costBadgeClass = 'balanced';
            costText = 'عادي';
    }

    modelInfoElement.innerHTML = `
        <div class="model-cost-badge ${costBadgeClass}">${costText}</div>
        <div class="model-description">${description}</div>
    `;
}

/**
 * Update enabled providers count
 */
function updateEnabledProvidersCount() {
    const enabledCount = ['openai', 'anthropic', 'gemini'].filter(provider => {
        const checkbox = document.getElementById(`${provider}Enabled`);
        return checkbox && checkbox.checked;
    }).length;

    const countElement = document.getElementById('enabledProvidersCount');
    if (countElement) {
        countElement.textContent = enabledCount;
    }
}

/**
 * Enhanced update provider status function
 */
function updateProviderStatus(provider, enabled) {
    const statusElement = document.getElementById(`${provider}Status`);
    if (statusElement) {
        statusElement.textContent = enabled ? 'مفعل' : 'غير مفعل';
        statusElement.className = enabled ? 'status-badge ai-status-enabled' : 'status-badge ai-status-disabled';
    }

    // Update the enabled providers count
    updateEnabledProvidersCount();

    // Add visual feedback to the card
    const card = document.getElementById(`${provider}Card`);
    if (card) {
        if (enabled) {
            card.style.borderColor = '#28a745';
            card.style.boxShadow = '0 8px 32px rgba(40, 167, 69, 0.15)';
        } else {
            card.style.borderColor = '#e9ecef';
            card.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.08)';
        }
    }
}

/**
 * Initialize enhanced AI settings
 */
function initializeEnhancedAISettings() {
    // Initialize model info displays
    ['openai', 'anthropic', 'gemini'].forEach(provider => {
        const modelSelect = document.getElementById(`${provider}Model`);
        if (modelSelect && modelSelect.value) {
            updateModelInfo(provider, modelSelect.value);
        }
    });

    // Update enabled providers count
    updateEnabledProvidersCount();

    // Add event listeners for checkboxes
    ['openai', 'anthropic', 'gemini'].forEach(provider => {
        const checkbox = document.getElementById(`${provider}Enabled`);
        if (checkbox) {
            checkbox.addEventListener('change', function() {
                updateProviderStatus(provider, this.checked);
            });
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedAISettings();
});
