<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار عرض لوحة المعلومات فقط</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .admin-frame {
            width: 100%;
            height: 800px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .section-visibility {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #6c757d;
        }
        .section-visibility.visible {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .section-visibility.hidden {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .section-visibility.dashboard {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-eye"></i> اختبار عرض لوحة المعلومات فقط</h1>
        <p class="lead">التحقق من أن لوحة المعلومات تعرض الإحصائيات العامة فقط وليس محتوى الأقسام الأخرى</p>
        
        <div class="alert alert-warning">
            <h4><i class="fas fa-exclamation-triangle"></i> المشكلة المكتشفة</h4>
            <p>لوحة المعلومات تعرض حالياً <strong>جميع محتويات الأقسام الأخرى</strong> بدلاً من الإحصائيات العامة فقط.</p>
            <p>تم تطبيق إصلاحات طارئة للحل.</p>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-play"></i> اختبارات الرؤية</h2>
            
            <button class="btn btn-primary" onclick="checkSectionVisibility()">
                <i class="fas fa-eye"></i> فحص رؤية الأقسام
            </button>
            
            <button class="btn btn-success" onclick="forceDashboardOnly()">
                <i class="fas fa-home"></i> فرض عرض لوحة المعلومات فقط
            </button>
            
            <button class="btn btn-info" onclick="openAdminInNewTab()">
                <i class="fas fa-external-link-alt"></i> فتح لوحة التحكم
            </button>
            
            <button class="btn btn-warning" onclick="clearResults()">
                <i class="fas fa-eraser"></i> مسح النتائج
            </button>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-list"></i> حالة رؤية الأقسام</h2>
            <div id="sectionsVisibility">
                <div class="section-visibility dashboard" data-section="dashboard">
                    <span><i class="fas fa-home"></i> لوحة المعلومات (Dashboard)</span>
                    <span>يجب أن تكون مرئية</span>
                </div>
                <div class="section-visibility" data-section="books">
                    <span><i class="fas fa-box"></i> إدارة المنتجات (Products)</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-visibility" data-section="orders">
                    <span><i class="fas fa-shopping-cart"></i> الطلبات (Orders)</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-visibility" data-section="landingPages">
                    <span><i class="fas fa-bullhorn"></i> صفحات هبوط (Landing Pages)</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-visibility" data-section="reports">
                    <span><i class="fas fa-chart-bar"></i> التقارير (Reports)</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-visibility" data-section="settings">
                    <span><i class="fas fa-cog"></i> الإعدادات (Settings)</span>
                    <span>جاري الفحص...</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-terminal"></i> نتائج الاختبار</h2>
            <div id="testResults" class="test-results">
                <div style="color: #28a745;">🚀 جاهز لفحص رؤية الأقسام...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-browser"></i> معاينة مباشرة</h2>
            <iframe id="adminFrame" class="admin-frame" src="/admin/"></iframe>
            <div class="mt-3">
                <button class="btn btn-secondary" onclick="reloadAdminFrame()">
                    <i class="fas fa-redo"></i> إعادة تحميل الإطار
                </button>
                <button class="btn btn-info" onclick="checkFrameVisibility()">
                    <i class="fas fa-search"></i> فحص الرؤية في الإطار
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Test results logging
        function logResult(message, type = "info") {
            const resultsDiv = document.getElementById("testResults");
            const timestamp = new Date().toLocaleTimeString("ar-DZ");
            const icon = type === "error" ? "❌" : type === "success" ? "✅" : type === "warning" ? "⚠️" : "ℹ️";
            const color = type === "error" ? "#dc3545" : type === "success" ? "#28a745" : type === "warning" ? "#ffc107" : "#6c757d";
            
            const logEntry = document.createElement("div");
            logEntry.style.color = color;
            logEntry.style.marginBottom = "5px";
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Update section visibility status
        function updateSectionVisibility(sectionId, isVisible, details = "") {
            const statusElement = document.querySelector(`[data-section="${sectionId}"].section-visibility`);
            if (statusElement) {
                if (sectionId === 'dashboard') {
                    statusElement.className = `section-visibility dashboard`;
                    statusElement.querySelector("span:last-child").textContent = isVisible ? "مرئية ✅" : "مخفية ❌";
                } else {
                    statusElement.className = `section-visibility ${isVisible ? 'visible' : 'hidden'}`;
                    statusElement.querySelector("span:last-child").textContent = isVisible ? `مرئية ❌ ${details}` : `مخفية ✅ ${details}`;
                }
            }
        }
        
        // Check section visibility
        function checkSectionVisibility() {
            logResult("🔍 فحص رؤية الأقسام...");
            
            const frame = document.getElementById('adminFrame');
            try {
                const frameDoc = frame.contentDocument || frame.contentWindow.document;
                const sections = ['dashboard', 'books', 'orders', 'landingPages', 'reports', 'settings'];
                
                let visibleSections = [];
                let hiddenSections = [];
                
                sections.forEach(sectionId => {
                    const section = frameDoc.getElementById(sectionId);
                    if (section) {
                        const computedStyle = frameDoc.defaultView.getComputedStyle(section);
                        const isVisible = computedStyle.display !== 'none' && 
                                        computedStyle.visibility !== 'hidden' && 
                                        computedStyle.opacity !== '0' &&
                                        section.offsetWidth > 0 && 
                                        section.offsetHeight > 0;
                        
                        if (isVisible) {
                            visibleSections.push(sectionId);
                            logResult(`👁️ القسم ${sectionId} مرئي`, sectionId === 'dashboard' ? "info" : "warning");
                        } else {
                            hiddenSections.push(sectionId);
                            logResult(`🙈 القسم ${sectionId} مخفي`, sectionId === 'dashboard' ? "warning" : "success");
                        }
                        
                        updateSectionVisibility(sectionId, isVisible, `(${computedStyle.display})`);
                    } else {
                        logResult(`❌ القسم ${sectionId} غير موجود`, "error");
                        updateSectionVisibility(sectionId, false, "(غير موجود)");
                    }
                });
                
                // Summary
                if (visibleSections.length === 1 && visibleSections[0] === 'dashboard') {
                    logResult("🎉 ممتاز! لوحة المعلومات فقط مرئية", "success");
                } else if (visibleSections.length > 1) {
                    logResult(`⚠️ مشكلة: ${visibleSections.length} أقسام مرئية (${visibleSections.join(', ')})`, "warning");
                } else {
                    logResult("❌ مشكلة: لا توجد أقسام مرئية", "error");
                }
                
            } catch (error) {
                logResult(`❌ خطأ في فحص الرؤية: ${error.message}`, "error");
            }
        }
        
        // Force dashboard only
        function forceDashboardOnly() {
            logResult("🔧 فرض عرض لوحة المعلومات فقط...");
            
            const frame = document.getElementById('adminFrame');
            try {
                const frameWindow = frame.contentWindow;
                const frameDoc = frame.contentDocument || frameWindow.document;
                
                // Force hide all sections except dashboard
                const sections = frameDoc.querySelectorAll('.content-section');
                sections.forEach(section => {
                    if (section.id !== 'dashboard') {
                        section.style.setProperty('display', 'none', 'important');
                        section.style.setProperty('visibility', 'hidden', 'important');
                        section.style.setProperty('opacity', '0', 'important');
                        section.style.setProperty('position', 'absolute', 'important');
                        section.style.setProperty('left', '-9999px', 'important');
                        section.classList.remove('active');
                    }
                });
                
                // Ensure dashboard is visible
                const dashboard = frameDoc.getElementById('dashboard');
                if (dashboard) {
                    dashboard.style.setProperty('display', 'block', 'important');
                    dashboard.style.setProperty('visibility', 'visible', 'important');
                    dashboard.style.setProperty('opacity', '1', 'important');
                    dashboard.style.setProperty('position', 'static', 'important');
                    dashboard.style.setProperty('left', 'auto', 'important');
                    dashboard.classList.add('active');
                }
                
                // Force clean navigation state if available
                if (frameWindow.adminNavigation && frameWindow.adminNavigation.forceCleanState) {
                    frameWindow.adminNavigation.forceCleanState();
                }
                
                logResult("✅ تم فرض عرض لوحة المعلومات فقط", "success");
                
                // Re-check visibility
                setTimeout(() => {
                    checkSectionVisibility();
                }, 500);
                
            } catch (error) {
                logResult(`❌ خطأ في فرض العرض: ${error.message}`, "error");
            }
        }
        
        // Check frame visibility
        function checkFrameVisibility() {
            logResult("🔍 فحص الرؤية في الإطار...");
            checkSectionVisibility();
        }
        
        // Open admin in new tab
        function openAdminInNewTab() {
            logResult("🔗 فتح لوحة التحكم في نافذة جديدة...");
            window.open('/admin/', '_blank');
        }
        
        // Reload admin frame
        function reloadAdminFrame() {
            logResult("🔄 إعادة تحميل إطار لوحة التحكم...");
            const frame = document.getElementById('adminFrame');
            frame.src = frame.src;
        }
        
        // Clear results
        function clearResults() {
            document.getElementById("testResults").innerHTML = "<div style='color: #28a745;'>🧹 تم مسح النتائج</div>";
            
            // Reset section visibility statuses
            document.querySelectorAll(".section-visibility").forEach(element => {
                if (element.getAttribute('data-section') === 'dashboard') {
                    element.className = "section-visibility dashboard";
                    element.querySelector("span:last-child").textContent = "يجب أن تكون مرئية";
                } else {
                    element.className = "section-visibility";
                    element.querySelector("span:last-child").textContent = "جاري الفحص...";
                }
            });
        }
        
        // Auto-run check when page loads
        document.addEventListener("DOMContentLoaded", function() {
            logResult("تم تحميل صفحة اختبار رؤية الأقسام", "success");
            
            // Wait for admin frame to load, then check visibility
            setTimeout(() => {
                logResult("بدء الفحص التلقائي...");
                checkSectionVisibility();
            }, 3000);
        });
    </script>
</body>
</html>
