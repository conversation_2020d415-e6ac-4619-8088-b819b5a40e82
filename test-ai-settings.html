<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Settings Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .provider-test {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            direction: ltr;
        }
        label {
            display: block;
            margin: 10px 0 5px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 AI Settings Test Page</h1>
        <p>This page tests the AI Settings functionality and fixes.</p>

        <div id="results"></div>

        <h3>Test Actions:</h3>
        <button onclick="testLoadAISettings()">Test Load AI Settings</button>
        <button onclick="testSaveAISettings()">Test Save AI Settings</button>
        <button onclick="testAIAvailability()">Test AI Availability</button>
        <button onclick="testSelectionErrors()">Test Selection Error Fix</button>
        <button onclick="runAllTests()">Run All Tests</button>

        <h3>Manual AI Settings Test:</h3>
        <div class="provider-test">
            <h4>OpenAI Settings</h4>
            <label>API Key:</label>
            <input type="text" id="testOpenaiKey" placeholder="sk-...">
            <label>
                <input type="checkbox" id="testOpenaiEnabled"> Enabled
            </label>
        </div>

        <div class="provider-test">
            <h4>Anthropic Settings</h4>
            <label>API Key:</label>
            <input type="text" id="testAnthropicKey" placeholder="sk-ant-...">
            <label>
                <input type="checkbox" id="testAnthropicEnabled"> Enabled
            </label>
        </div>

        <div class="provider-test">
            <h4>Gemini Settings</h4>
            <label>API Key:</label>
            <input type="text" id="testGeminiKey" placeholder="AIza...">
            <label>
                <input type="checkbox" id="testGeminiEnabled"> Enabled
            </label>
        </div>

        <button onclick="saveTestSettings()">Save Test Settings</button>
    </div>

    <script src="admin/js/selection-error-fix.js"></script>
    <script>
        const results = document.getElementById('results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function testLoadAISettings() {
            addResult('Testing Load AI Settings...', 'info');
            try {
                const response = await fetch('api/get-ai-settings.php');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.success) {
                    addResult('✅ AI Settings loaded successfully', 'success');

                    // Check each provider
                    Object.keys(data.data).forEach(provider => {
                        const config = data.data[provider];
                        const hasKey = config.api_key && config.api_key.trim() !== '';
                        const status = hasKey ?
                            (config.enabled ? '🟢 Enabled' : '🟡 Disabled') :
                            '🔴 No API Key';

                        addResult(`${provider}: ${status} - Model: ${config.model} - Status: ${config.status}`, 'info');
                    });

                    // Test AI availability calculation
                    const enabledProviders = Object.keys(data.data).filter(provider => {
                        const providerData = data.data[provider];
                        return providerData.enabled &&
                               providerData.api_key &&
                               providerData.api_key.trim() !== '';
                    });

                    if (enabledProviders.length > 0) {
                        addResult(`🤖 AI Available with providers: ${enabledProviders.join(', ')}`, 'success');
                    } else {
                        addResult('⚠️ AI Not Available - no enabled providers with valid keys', 'warning');
                    }
                } else {
                    addResult(`❌ Failed to load AI settings: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Load AI settings failed: ${error.message}`, 'error');
            }
        }

        async function testSaveAISettings() {
            addResult('Testing Save AI Settings...', 'info');

            const testData = {
                openai: {
                    key: 'test-key-openai',
                    model: 'gpt-3.5-turbo',
                    enabled: true
                },
                anthropic: {
                    key: 'test-key-anthropic',
                    model: 'claude-3-sonnet',
                    enabled: false
                },
                gemini: {
                    key: 'test-key-gemini',
                    model: 'gemini-pro',
                    enabled: false
                }
            };

            try {
                const response = await fetch('api/save-ai-settings.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();

                if (data.success) {
                    addResult(`✅ AI Settings saved: ${data.message}`, 'success');
                    addResult(`📊 Saved ${data.saved_count} provider(s)`, 'info');
                } else {
                    addResult(`❌ Failed to save AI settings: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Save AI settings failed: ${error.message}`, 'error');
            }
        }

        async function testAIAvailability() {
            addResult('Testing AI Availability Check...', 'info');

            // Simulate the AI Magic Wand availability check
            try {
                const response = await fetch('api/get-ai-settings.php');
                const data = await response.json();

                if (data.success) {
                    const providers = data.data;
                    const enabledProviders = Object.keys(providers).filter(provider => {
                        const providerData = providers[provider];
                        return providerData.enabled &&
                               providerData.api_key &&
                               providerData.api_key.trim() !== '' &&
                               providerData.status !== 'Not configured';
                    });

                    const isAvailable = enabledProviders.length > 0;

                    if (isAvailable) {
                        addResult(`✅ AI is available with providers: ${enabledProviders.join(', ')}`, 'success');
                    } else {
                        addResult('⚠️ AI is not available - no enabled providers with valid keys', 'warning');
                    }
                } else {
                    addResult(`❌ AI availability check failed: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ AI availability test failed: ${error.message}`, 'error');
            }
        }

        function testSelectionErrors() {
            addResult('Testing Selection Error Fix...', 'info');

            try {
                // Test getSelection
                const selection = window.getSelection();
                addResult(`✅ getSelection() works: ${selection ? 'Yes' : 'No'}`, 'success');

                if (selection) {
                    addResult(`📊 rangeCount: ${selection.rangeCount}`, 'info');

                    // Test problematic operations
                    try {
                        selection.removeAllRanges();
                        addResult('✅ removeAllRanges() works', 'success');
                    } catch (error) {
                        addResult(`❌ removeAllRanges() failed: ${error.message}`, 'error');
                    }
                }

                // Test context menu simulation
                const contextEvent = new MouseEvent('contextmenu', {
                    bubbles: true,
                    cancelable: true
                });

                document.body.dispatchEvent(contextEvent);
                addResult('✅ Context menu event handled safely', 'success');

            } catch (error) {
                addResult(`❌ Selection test failed: ${error.message}`, 'error');
            }
        }

        async function saveTestSettings() {
            const testData = {
                openai: {
                    key: document.getElementById('testOpenaiKey').value,
                    model: 'gpt-3.5-turbo',
                    enabled: document.getElementById('testOpenaiEnabled').checked
                },
                anthropic: {
                    key: document.getElementById('testAnthropicKey').value,
                    model: 'claude-3-sonnet',
                    enabled: document.getElementById('testAnthropicEnabled').checked
                },
                gemini: {
                    key: document.getElementById('testGeminiKey').value,
                    model: 'gemini-pro',
                    enabled: document.getElementById('testGeminiEnabled').checked
                }
            };

            addResult('Saving manual test settings...', 'info');

            try {
                const response = await fetch('api/save-ai-settings.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();

                if (data.success) {
                    addResult(`✅ Manual settings saved: ${data.message}`, 'success');
                    // Reload to show updated settings
                    await testLoadAISettings();
                } else {
                    addResult(`❌ Failed to save manual settings: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Manual save failed: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            addResult('🚀 Running all AI Settings tests...', 'info');
            results.innerHTML = '';

            await testLoadAISettings();
            await new Promise(resolve => setTimeout(resolve, 1000));

            await testSaveAISettings();
            await new Promise(resolve => setTimeout(resolve, 1000));

            await testAIAvailability();
            await new Promise(resolve => setTimeout(resolve, 1000));

            testSelectionErrors();

            addResult('✅ All AI Settings tests completed', 'success');
        }

        // Auto-run basic test on page load
        window.addEventListener('DOMContentLoaded', function() {
            addResult('🔧 AI Settings Test Page Loaded', 'success');
            testLoadAISettings();
        });

        // Global error handler
        window.addEventListener('error', function(event) {
            addResult(`🚨 Global error: ${event.error.message}`, 'error');
        });
    </script>
</body>
</html>
