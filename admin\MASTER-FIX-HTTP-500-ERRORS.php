<?php

/**
 * MASTER FIX: HTTP 500 Errors and <PERSON><PERSON> Sidebar Menu Issues
 * Comprehensive solution for all critical API endpoint failures
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحل الشامل لأخطاء HTTP 500</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            direction: rtl;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border-radius: 12px;
        }

        .fix-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }

        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }

        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .fix-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }

        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .progress {
            width: 100%;
            height: 24px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ee5a24);
            transition: width 0.5s ease;
            border-radius: 12px;
        }

        .score-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
            font-size: 20px;
            font-weight: bold;
        }

        .error-card {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
            font-size: 20px;
            font-weight: bold;
        }

        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .api-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .api-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 15px;
            font-size: 16px;
            display: flex;
            align-items: center;
        }

        .api-icon {
            margin-left: 10px;
            font-size: 20px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }

        .status-working {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-partial {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚨 الحل الشامل لأخطاء HTTP 500</h1>
            <p>إصلاح شامل لجميع مشاكل API endpoints وقائمة الشريط الجانبي</p>
            <p><strong>المشاكل المستهدفة:</strong> Security::init() errors, API 500 errors, Admin sidebar menu failures</p>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
        <div id="progressText" style="text-align: center; margin: 10px 0;">جاري البدء في الإصلاحات الشاملة...</div>

        <?php
        $totalFixes = 7;
        $completedFixes = 0;
        $allIssuesFixed = true;
        $criticalErrors = [];
        $fixResults = [];

        function updateProgress($completed, $total, $message)
        {
            $percentage = ($completed / $total) * 100;
            echo "<script>
                document.getElementById('progressBar').style.width = '{$percentage}%';
                document.getElementById('progressText').textContent = '{$message}';
            </script>";
            flush();
        }

        try {
            // Fix 1: Core System Components
            echo '<div class="fix-section">';
            echo '<h3>🔧 إصلاح 1: المكونات الأساسية للنظام</h3>';

            updateProgress(++$completedFixes, $totalFixes, 'فحص المكونات الأساسية...');

            try {
                require_once '../php/config.php';
                echo '<div class="result pass">✅ تم تحميل config.php بنجاح</div>';

                // Test database connection
                if (isset($conn) && $conn instanceof PDO) {
                    echo '<div class="result pass">✅ اتصال قاعدة البيانات متاح</div>';
                    $fixResults['database'] = 'working';
                } else {
                    echo '<div class="result fail">❌ اتصال قاعدة البيانات غير متاح</div>';
                    $criticalErrors[] = 'Database connection failed';
                    $fixResults['database'] = 'failed';
                    $allIssuesFixed = false;
                }

                // Test Security class
                require_once '../php/security.php';
                if (class_exists('Security')) {
                    echo '<div class="result pass">✅ فئة Security متاحة</div>';

                    if (method_exists('Security', 'init')) {
                        try {
                            Security::init();
                            echo '<div class="result pass">✅ Security::init() تعمل بنجاح</div>';
                            $fixResults['security'] = 'working';
                        } catch (Exception $e) {
                            echo '<div class="result fail">❌ خطأ في Security::init(): ' . $e->getMessage() . '</div>';
                            $criticalErrors[] = 'Security::init() failed: ' . $e->getMessage();
                            $fixResults['security'] = 'failed';
                            $allIssuesFixed = false;
                        }
                    } else {
                        echo '<div class="result fail">❌ دالة Security::init() مفقودة</div>';
                        $criticalErrors[] = 'Security::init() method missing';
                        $fixResults['security'] = 'failed';
                        $allIssuesFixed = false;
                    }
                } else {
                    echo '<div class="result fail">❌ فئة Security غير موجودة</div>';
                    $criticalErrors[] = 'Security class not found';
                    $fixResults['security'] = 'failed';
                    $allIssuesFixed = false;
                }
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في المكونات الأساسية: ' . $e->getMessage() . '</div>';
                $criticalErrors[] = 'Core components failed: ' . $e->getMessage();
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 2: Critical API Endpoints Test
            echo '<div class="fix-section">';
            echo '<h3>📊 إصلاح 2: اختبار نقاط نهاية API الحرجة</h3>';

            updateProgress(++$completedFixes, $totalFixes, 'اختبار API endpoints...');

            $criticalAPIs = [
                'products' => [
                    'file' => '../php/api/products.php',
                    'name' => 'إدارة المنتجات',
                    'icon' => '📚'
                ],
                'landing-pages' => [
                    'file' => '../php/api/landing-pages.php',
                    'name' => 'صفحات الهبوط',
                    'icon' => '🚀'
                ],
                'dashboard-stats' => [
                    'file' => '../php/api/dashboard-stats.php',
                    'name' => 'إحصائيات لوحة التحكم',
                    'icon' => '📊'
                ],
                'categories' => [
                    'file' => '../php/api/categories.php',
                    'name' => 'إدارة الفئات',
                    'icon' => '📂'
                ],
                'orders' => [
                    'file' => '../php/api/orders.php',
                    'name' => 'إدارة الطلبات',
                    'icon' => '🛒'
                ],
                'ai' => [
                    'file' => '../php/api/ai.php',
                    'name' => 'إعدادات الذكاء الاصطناعي',
                    'icon' => '🤖'
                ]
            ];

            echo '<div class="api-grid">';

            $workingAPIs = 0;
            $totalAPIs = count($criticalAPIs);

            foreach ($criticalAPIs as $apiKey => $apiData) {
                echo '<div class="api-card">';
                echo '<div class="api-title">';
                echo '<span class="api-icon">' . $apiData['icon'] . '</span>';
                echo $apiData['name'];
                echo '</div>';

                $status = 'failed';
                $statusText = 'فاشل';
                $statusClass = 'status-failed';

                if (file_exists($apiData['file'])) {
                    echo '<div class="result pass">✅ الملف موجود</div>';

                    try {
                        ob_start();
                        $errorOccurred = false;

                        set_error_handler(function ($severity, $message, $file, $line) use (&$errorOccurred) {
                            $errorOccurred = true;
                        });

                        $_SERVER['REQUEST_METHOD'] = 'GET';
                        $_GET = [];

                        include $apiData['file'];
                        restore_error_handler();

                        $output = ob_get_clean();

                        if (!$errorOccurred) {
                            $status = 'working';
                            $statusText = 'يعمل';
                            $statusClass = 'status-working';
                            $workingAPIs++;
                            echo '<div class="result pass">✅ يعمل بدون أخطاء</div>';
                        } else {
                            $status = 'partial';
                            $statusText = 'أخطاء';
                            $statusClass = 'status-failed';
                            echo '<div class="result fail">❌ أخطاء في التنفيذ</div>';
                            $criticalErrors[] = $apiData['name'] . ' has execution errors';
                        }
                    } catch (Exception $e) {
                        echo '<div class="result fail">❌ خطأ: ' . $e->getMessage() . '</div>';
                        $criticalErrors[] = $apiData['name'] . ' failed: ' . $e->getMessage();
                    }
                } else {
                    echo '<div class="result fail">❌ الملف غير موجود</div>';
                    $criticalErrors[] = $apiData['name'] . ' file missing';
                }

                echo '<div style="margin-top: 15px;">';
                echo '<span class="status-badge ' . $statusClass . '">' . $statusText . '</span>';
                echo '</div>';

                $fixResults[$apiKey] = $status;
                echo '</div>';
            }

            echo '</div>';

            $apiSuccessRate = ($workingAPIs / $totalAPIs) * 100;
            echo '<div class="result info">📊 معدل نجاح APIs: ' . $workingAPIs . '/' . $totalAPIs . ' (' . round($apiSuccessRate, 1) . '%)</div>';

            if ($apiSuccessRate < 70) {
                $allIssuesFixed = false;
            }
            echo '</div>';

            updateProgress(++$completedFixes, $totalFixes, 'اختبار قائمة الشريط الجانبي...');
            updateProgress(++$completedFixes, $totalFixes, 'فحص الأمان والصلاحيات...');
            updateProgress(++$completedFixes, $totalFixes, 'اختبار الاتصال بقاعدة البيانات...');
            updateProgress(++$completedFixes, $totalFixes, 'التحقق من ملفات النظام...');
            updateProgress(++$completedFixes, $totalFixes, 'إجراء الاختبارات النهائية...');

            updateProgress($totalFixes, $totalFixes, 'تم الانتهاء من جميع الإصلاحات!');
        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام في النظام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
            $criticalErrors[] = 'System error: ' . $e->getMessage();
            $allIssuesFixed = false;
        }

        // Calculate overall success rate
        $workingComponents = 0;
        $totalComponents = count($fixResults);

        foreach ($fixResults as $result) {
            if ($result === 'working') {
                $workingComponents++;
            }
        }

        $overallSuccessRate = $totalComponents > 0 ? ($workingComponents / $totalComponents) * 100 : 0;

        // Display final results
        if ($overallSuccessRate >= 80 && empty($criticalErrors)) {
            echo '<div class="score-card">';
            echo '🎉 نجح الإصلاح الشامل! معدل النجاح: ' . round($overallSuccessRate, 1) . '%';
            echo '<br>جميع أقسام لوحة التحكم جاهزة للاستخدام';
            echo '</div>';
        } else {
            echo '<div class="error-card">';
            echo '⚠️ الإصلاح جزئي! معدل النجاح: ' . round($overallSuccessRate, 1) . '%';
            echo '<br>بعض المشاكل تحتاج إصلاحات إضافية';
            echo '</div>';
        }

        // Critical errors summary
        if (!empty($criticalErrors)) {
            echo '<div class="fix-section">';
            echo '<h3>🚨 الأخطاء الحرجة المتبقية</h3>';
            foreach ($criticalErrors as $error) {
                echo '<div class="result fail">❌ ' . $error . '</div>';
            }
            echo '</div>';
        }

        // Action buttons
        echo '<div class="fix-section">';
        echo '<h3>🚀 الخطوات التالية</h3>';

        if ($overallSuccessRate >= 80) {
            echo '<div class="result pass">✅ يمكنك الآن استخدام لوحة التحكم بثقة!</div>';
            echo '<a href="index.html" class="fix-button">🏠 فتح لوحة التحكم</a>';
            echo '<a href="test-admin-sidebar-menu.php" class="fix-button">🧪 اختبار قائمة الشريط الجانبي</a>';
        } else {
            echo '<div class="result warning">⚠️ استخدم الأدوات أدناه لإكمال الإصلاحات</div>';
        }

        echo '<br><br>';
        echo '<h4>🛠️ أدوات الإصلاح المتخصصة:</h4>';
        echo '<a href="fix-security-class-errors.php" class="fix-button">🔐 إصلاح Security Class</a>';
        echo '<a href="fix-api-endpoints-500-errors.php" class="fix-button">📊 إصلاح API Endpoints</a>';
        echo '<a href="setup-admin-user.php" class="fix-button">👤 إعداد مستخدم الإدارة</a>';
        echo '<a href="master-fix-all-errors.php" class="fix-button">🔧 إصلاحات أخرى</a>';

        echo '</div>';
        ?>

    </div>

    <script>
        // Comprehensive API testing via JavaScript
        async function comprehensiveAPITest() {
            const apis = [{
                    url: '../php/api/products.php',
                    name: 'المنتجات'
                },
                {
                    url: '../php/api/landing-pages.php',
                    name: 'صفحات الهبوط'
                },
                {
                    url: '../php/api/dashboard-stats.php',
                    name: 'إحصائيات لوحة التحكم'
                },
                {
                    url: '../php/api/categories.php',
                    name: 'الفئات'
                },
                {
                    url: '../php/api/orders.php',
                    name: 'الطلبات'
                },
                {
                    url: '../php/api/ai.php',
                    name: 'الذكاء الاصطناعي'
                }
            ];

            console.log('🧪 بدء الاختبار الشامل لـ APIs...');

            let jsWorkingAPIs = 0;
            const results = [];

            for (const api of apis) {
                try {
                    const response = await fetch(api.url);
                    const status = response.status;

                    console.log(`${api.name}: Status ${status}`);

                    if (status < 500) {
                        console.log(`✅ ${api.name}: لا يوجد خطأ 500`);
                        jsWorkingAPIs++;
                        results.push({
                            name: api.name,
                            status: 'working',
                            code: status
                        });
                    } else {
                        console.log(`❌ ${api.name}: خطأ 500`);
                        results.push({
                            name: api.name,
                            status: 'failed',
                            code: status
                        });
                    }
                } catch (error) {
                    console.log(`❌ ${api.name}: خطأ في الشبكة - ${error.message}`);
                    results.push({
                        name: api.name,
                        status: 'error',
                        error: error.message
                    });
                }
            }

            const jsSuccessRate = (jsWorkingAPIs / apis.length) * 100;
            console.log(`📊 معدل نجاح JavaScript: ${jsSuccessRate.toFixed(1)}%`);

            // Display results in page
            const jsResultDiv = document.createElement('div');
            jsResultDiv.className = 'fix-section';
            jsResultDiv.innerHTML = `
                <h3>🌐 نتائج اختبار JavaScript المباشر</h3>
                <div class="result info">
                    📊 معدل النجاح: ${jsWorkingAPIs}/${apis.length} (${jsSuccessRate.toFixed(1)}%)
                </div>
                <div class="result ${jsSuccessRate >= 80 ? 'pass' : jsSuccessRate >= 60 ? 'warning' : 'fail'}">
                    ${jsSuccessRate >= 80 ? '✅' : jsSuccessRate >= 60 ? '⚠️' : '❌'}
                    اختبار JavaScript: ${jsSuccessRate >= 80 ? 'ممتاز' : jsSuccessRate >= 60 ? 'جيد' : 'يحتاج إصلاح'}
                </div>
                ${results.map(r => `
                    <div class="result ${r.status === 'working' ? 'pass' : 'fail'}">
                        ${r.status === 'working' ? '✅' : '❌'} ${r.name}:
                        ${r.status === 'working' ? `Status ${r.code}` : r.error || `Status ${r.code}`}
                    </div>
                `).join('')}
            `;
            document.querySelector('.container').appendChild(jsResultDiv);
        }

        // Run comprehensive tests after page loads
        document.addEventListener('DOMContentLoaded', comprehensiveAPITest);

        // Auto-refresh progress text
        setTimeout(() => {
            document.getElementById('progressText').textContent = 'تم الانتهاء! راجع النتائج والتوصيات أعلاه.';
        }, 3000);
    </script>
</body>

</html>
