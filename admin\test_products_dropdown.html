<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Products Dropdown</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        select { width: 100%; padding: 10px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Test Products Dropdown</h1>
    
    <div class="test-section">
        <h2>Test 1: Direct API Call</h2>
        <button onclick="testDirectAPI()">Test Products API</button>
        <div id="apiResults"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Dropdown Population</h2>
        <label>اختر المنتج:</label>
        <select id="productSelect">
            <option value="">جاري التحميل...</option>
        </select>
        <button onclick="loadProductsIntoDropdown()">Load Products</button>
        <div id="dropdownResults"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Raw Response</h2>
        <pre id="rawResponse"></pre>
    </div>

    <script>
        // Copy of safeApiCall from landing-pages.js
        async function safeApiCall(url, options = {}) {
            try {
                console.log('🔄 Making API call to:', url);
                const response = await fetch(url, options);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const text = await response.text();
                console.log('📄 Raw response:', text.substring(0, 200) + '...');

                if (!text.trim()) {
                    console.warn('Empty response from API:', url);
                    return [];
                }

                try {
                    return JSON.parse(text);
                } catch (parseError) {
                    console.error('JSON parse error for response:', text);
                    throw new Error('Invalid JSON response from server');
                }
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        }

        async function testDirectAPI() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<p>🔄 Testing API...</p>';
            
            try {
                const response = await safeApiCall('../php/api/products.php');
                console.log('API Response:', response);
                
                document.getElementById('rawResponse').textContent = JSON.stringify(response, null, 2);
                
                if (response.success && response.products) {
                    resultsDiv.innerHTML = `<p class="success">✅ API Success! Found ${response.products.length} products</p>`;
                } else if (Array.isArray(response)) {
                    resultsDiv.innerHTML = `<p class="success">✅ API Success! Found ${response.length} products (array format)</p>`;
                } else {
                    resultsDiv.innerHTML = `<p class="warning">⚠️ Unexpected response format</p>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p class="error">❌ API Error: ${error.message}</p>`;
                console.error('API Test Error:', error);
            }
        }

        async function loadProductsIntoDropdown() {
            const select = document.getElementById('productSelect');
            const resultsDiv = document.getElementById('dropdownResults');
            
            select.innerHTML = '<option value="">جاري التحميل...</option>';
            resultsDiv.innerHTML = '<p>🔄 Loading products...</p>';
            
            try {
                const response = await safeApiCall('../php/api/products.php');
                console.log('Dropdown load response:', response);
                
                let products = [];
                if (Array.isArray(response)) {
                    products = response;
                } else if (response.success && Array.isArray(response.products)) {
                    products = response.products;
                } else if (response.products) {
                    products = response.products;
                }

                select.innerHTML = '<option value="">اختر منتجاً</option>';

                // Filter only active products
                const activeProducts = products.filter(product => product.actif == 1 || product.actif === true);

                if (activeProducts.length === 0) {
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = 'لا توجد منتجات مفعلة';
                    option.disabled = true;
                    select.appendChild(option);
                    resultsDiv.innerHTML = '<p class="warning">⚠️ No active products found</p>';
                    return;
                }

                // Add active products to the dropdown
                activeProducts.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.id;
                    option.textContent = `${product.titre || product.title || `Product ${product.id}`} (${getProductTypeText(product.type)})`;
                    option.setAttribute('data-product-type', product.type);
                    select.appendChild(option);
                });

                resultsDiv.innerHTML = `<p class="success">✅ Loaded ${activeProducts.length} active products successfully!</p>`;
            } catch (error) {
                select.innerHTML = '<option value="">خطأ في تحميل المنتجات</option>';
                resultsDiv.innerHTML = `<p class="error">❌ Error: ${error.message}</p>`;
                console.error('Dropdown load error:', error);
            }
        }

        function getProductTypeText(type) {
            const types = {
                'book': 'كتاب',
                'laptop': 'حاسوب محمول',
                'bag': 'حقيبة'
            };
            return types[type] || type;
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('🚀 Page loaded, running tests...');
            testDirectAPI();
        });
    </script>
</body>
</html>
