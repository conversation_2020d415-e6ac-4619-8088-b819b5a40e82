<?php
/**
 * Test Script for Image Optimization System
 * Verifies WebP generation, responsive variants, and lazy loading
 */

require_once 'php/ImageOptimizer.php';

echo "<h1>🖼️ Image Optimization Test</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
    .success { color: #28a745; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .image-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
    .image-card { border: 1px solid #ddd; padding: 10px; border-radius: 8px; text-align: center; }
    .image-card img { max-width: 100%; height: auto; border-radius: 4px; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>\n";

try {
    // Test 1: Check ImageOptimizer Class
    echo "<div class='test-section'>\n";
    echo "<h2>📋 Test 1: ImageOptimizer Class Verification</h2>\n";
    
    $imageOptimizer = new ImageOptimizer('uploads/products/');
    
    // Check if GD extension is loaded
    if (extension_loaded('gd')) {
        echo "<p class='success'>✅ GD Extension: Loaded</p>\n";
        
        $gdInfo = gd_info();
        echo "<p><strong>GD Version:</strong> " . $gdInfo['GD Version'] . "</p>\n";
        echo "<p><strong>WebP Support:</strong> " . ($gdInfo['WebP Support'] ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . "</p>\n";
        echo "<p><strong>JPEG Support:</strong> " . ($gdInfo['JPEG Support'] ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . "</p>\n";
        echo "<p><strong>PNG Support:</strong> " . ($gdInfo['PNG Support'] ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . "</p>\n";
    } else {
        echo "<p class='error'>❌ GD Extension: Not loaded</p>\n";
    }
    
    // Check upload directories
    $directories = ['uploads/products/', 'uploads/products/thumbnail/', 'uploads/products/medium/', 'uploads/products/large/'];
    foreach ($directories as $dir) {
        if (is_dir($dir)) {
            echo "<p class='success'>✅ Directory exists: $dir</p>\n";
            echo "<p>Permissions: " . substr(sprintf('%o', fileperms($dir)), -4) . "</p>\n";
        } else {
            echo "<p class='warning'>⚠️ Directory missing: $dir (will be created automatically)</p>\n";
        }
    }
    echo "</div>\n";
    
    // Test 2: Analyze Existing Images
    echo "<div class='test-section'>\n";
    echo "<h2>🔍 Test 2: Existing Images Analysis</h2>\n";
    
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];
    $existingImages = [];
    
    foreach ($imageExtensions as $ext) {
        $files = glob("uploads/products/*.{$ext}");
        $existingImages = array_merge($existingImages, $files);
    }
    
    if (empty($existingImages)) {
        echo "<p class='warning'>⚠️ No existing images found in uploads/products/</p>\n";
        echo "<p>Upload a test image through the admin panel to test optimization.</p>\n";
    } else {
        echo "<p class='success'>✅ Found " . count($existingImages) . " existing images</p>\n";
        
        echo "<table>\n";
        echo "<tr><th>Image</th><th>Size</th><th>Dimensions</th><th>WebP Exists</th><th>Variants</th></tr>\n";
        
        foreach (array_slice($existingImages, 0, 10) as $imagePath) {
            $filename = basename($imagePath);
            $size = filesize($imagePath);
            $imageInfo = getimagesize($imagePath);
            $dimensions = $imageInfo ? $imageInfo[0] . 'x' . $imageInfo[1] : 'Unknown';
            
            // Check for WebP version
            $pathInfo = pathinfo($filename);
            $webpPath = 'uploads/products/' . $pathInfo['filename'] . '.webp';
            $webpExists = file_exists($webpPath);
            
            // Check for variants
            $variants = [];
            foreach (['thumbnail', 'medium', 'large'] as $variant) {
                if (file_exists("uploads/products/$variant/$filename")) {
                    $variants[] = $variant;
                }
            }
            
            echo "<tr>";
            echo "<td>$filename</td>";
            echo "<td>" . number_format($size / 1024, 1) . " KB</td>";
            echo "<td>$dimensions</td>";
            echo "<td>" . ($webpExists ? '<span class="success">Yes</span>' : '<span class="warning">No</span>') . "</td>";
            echo "<td>" . (empty($variants) ? '<span class="warning">None</span>' : implode(', ', $variants)) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    echo "</div>\n";
    
    // Test 3: Create Test Image (if possible)
    echo "<div class='test-section'>\n";
    echo "<h2>🎨 Test 3: Image Processing Test</h2>\n";
    
    // Create a simple test image
    $testImage = imagecreatetruecolor(400, 300);
    $backgroundColor = imagecolorallocate($testImage, 240, 248, 255);
    $textColor = imagecolorallocate($testImage, 0, 100, 200);
    
    imagefill($testImage, 0, 0, $backgroundColor);
    
    // Add text
    $text = "Test Image";
    $font = 5;
    $textWidth = imagefontwidth($font) * strlen($text);
    $textHeight = imagefontheight($font);
    $x = (400 - $textWidth) / 2;
    $y = (300 - $textHeight) / 2;
    
    imagestring($testImage, $font, $x, $y, $text, $textColor);
    
    // Save test image
    $testImagePath = 'uploads/products/test_image_' . date('Y-m-d_H-i-s') . '.jpg';
    
    if (imagejpeg($testImage, $testImagePath, 85)) {
        echo "<p class='success'>✅ Test image created: $testImagePath</p>\n";
        
        // Test optimization
        $fakeUpload = [
            'tmp_name' => $testImagePath,
            'name' => basename($testImagePath),
            'size' => filesize($testImagePath),
            'type' => 'image/jpeg'
        ];
        
        // Simulate file upload validation
        if (is_uploaded_file($testImagePath)) {
            echo "<p class='warning'>⚠️ Cannot test with simulated upload (security restriction)</p>\n";
        } else {
            echo "<p class='warning'>⚠️ Testing with local file (not uploaded file)</p>\n";
            
            // Test the optimization methods directly
            try {
                $result = $imageOptimizer->getOptimizedImageUrl(basename($testImagePath), 'medium');
                echo "<p><strong>Optimized URL:</strong> $result</p>\n";
                
                $stats = $imageOptimizer->getOptimizationStats(basename($testImagePath));
                if ($stats) {
                    echo "<p><strong>Optimization Stats:</strong></p>\n";
                    echo "<pre>" . print_r($stats, true) . "</pre>\n";
                }
            } catch (Exception $e) {
                echo "<p class='error'>Error testing optimization: " . $e->getMessage() . "</p>\n";
            }
        }
        
        // Clean up test image
        unlink($testImagePath);
        echo "<p>Test image cleaned up</p>\n";
    } else {
        echo "<p class='error'>❌ Failed to create test image</p>\n";
    }
    
    imagedestroy($testImage);
    echo "</div>\n";
    
    // Test 4: Lazy Loading JavaScript Test
    echo "<div class='test-section'>\n";
    echo "<h2>⚡ Test 4: Lazy Loading System</h2>\n";
    
    echo "<p>Testing lazy loading implementation...</p>\n";
    
    // Check if utils.js exists and contains LazyLoadManager
    if (file_exists('js/utils.js')) {
        $utilsContent = file_get_contents('js/utils.js');
        
        if (strpos($utilsContent, 'LazyLoadManager') !== false) {
            echo "<p class='success'>✅ LazyLoadManager found in utils.js</p>\n";
            
            if (strpos($utilsContent, 'IntersectionObserver') !== false) {
                echo "<p class='success'>✅ Intersection Observer implementation found</p>\n";
            } else {
                echo "<p class='warning'>⚠️ Intersection Observer not found</p>\n";
            }
            
            if (strpos($utilsContent, 'data-src') !== false) {
                echo "<p class='success'>✅ Lazy loading attributes supported</p>\n";
            } else {
                echo "<p class='warning'>⚠️ Lazy loading attributes not found</p>\n";
            }
        } else {
            echo "<p class='error'>❌ LazyLoadManager not found in utils.js</p>\n";
        }
    } else {
        echo "<p class='error'>❌ utils.js file not found</p>\n";
    }
    
    // Test lazy loading HTML structure
    echo "<h3>Sample Lazy Loading HTML:</h3>\n";
    echo "<div class='image-grid'>\n";
    
    if (!empty($existingImages)) {
        $sampleImage = basename($existingImages[0]);
        $imageOptimizer = new ImageOptimizer('uploads/products/');
        
        echo "<div class='image-card'>\n";
        echo "<h4>Original Implementation</h4>\n";
        echo "<img src='uploads/products/$sampleImage' alt='Original' style='max-height: 150px;'>\n";
        echo "<p>Direct loading</p>\n";
        echo "</div>\n";
        
        echo "<div class='image-card'>\n";
        echo "<h4>Lazy Loading Implementation</h4>\n";
        $thumbnailUrl = $imageOptimizer->getOptimizedImageUrl($sampleImage, 'thumbnail');
        $mediumUrl = $imageOptimizer->getOptimizedImageUrl($sampleImage, 'medium');
        
        echo "<img data-src='$mediumUrl' src='$thumbnailUrl' alt='Lazy Loaded' class='lazy-load' style='max-height: 150px;'>\n";
        echo "<p>Lazy loading with thumbnail placeholder</p>\n";
        echo "</div>\n";
    }
    
    echo "</div>\n";
    echo "</div>\n";
    
    // Test 5: Performance Recommendations
    echo "<div class='test-section'>\n";
    echo "<h2>💡 Test 5: Optimization Recommendations</h2>\n";
    
    $recommendations = [];
    
    if (!extension_loaded('gd')) {
        $recommendations[] = "Install or enable GD extension for image processing";
    } elseif (!function_exists('imagewebp')) {
        $recommendations[] = "Enable WebP support in GD extension for better compression";
    }
    
    if (empty($existingImages)) {
        $recommendations[] = "Upload test images to verify optimization system";
    }
    
    $cacheDir = 'cache/';
    if (!is_dir($cacheDir)) {
        $recommendations[] = "Create cache directory for API response caching";
    }
    
    if (empty($recommendations)) {
        echo "<p class='success'>🎉 All systems appear to be working correctly!</p>\n";
        echo "<h3>Next Steps:</h3>\n";
        echo "<ul>\n";
        echo "<li>Upload a new product image through the admin panel</li>\n";
        echo "<li>Verify WebP variants are generated automatically</li>\n";
        echo "<li>Test lazy loading on the main product page</li>\n";
        echo "<li>Run the full performance test script</li>\n";
        echo "</ul>\n";
    } else {
        echo "<h3>Recommendations:</h3>\n";
        echo "<ul>\n";
        foreach ($recommendations as $rec) {
            echo "<li class='warning'>⚠️ $rec</li>\n";
        }
        echo "</ul>\n";
    }
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='test-section' style='border-left-color: #dc3545;'>\n";
    echo "<h2>❌ Error During Image Optimization Testing</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<p class='error'>Stack trace:</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    echo "</div>\n";
}
?>

<script>
// Test lazy loading JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('🖼️ Image optimization test page loaded');
    
    // Test if LazyLoadManager is available
    if (typeof LazyLoadManager !== 'undefined') {
        console.log('✅ LazyLoadManager is available');
        LazyLoadManager.init();
    } else {
        console.log('❌ LazyLoadManager not found - include utils.js');
    }
});
</script>
