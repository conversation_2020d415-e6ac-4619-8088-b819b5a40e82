<?php
/**
 * Manual SQL Commands for Database Setup
 * Provides SQL commands to run manually if automated scripts fail
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أوامر SQL اليدوية لإعداد قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sql-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .sql-code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
            direction: ltr;
            text-align: left;
        }
        .copy-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 0;
            font-size: 12px;
        }
        .copy-button:hover {
            background: #0056b3;
        }
        .instruction {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 أوامر SQL اليدوية لإعداد قاعدة البيانات</h1>
        <p>إذا فشلت السكريبتات التلقائية، يمكنك تشغيل هذه الأوامر يدوياً في phpMyAdmin أو MySQL Command Line.</p>

        <div class="warning">
            ⚠️ <strong>تحذير:</strong> تأكد من تشغيل خدمة MySQL/MariaDB قبل تنفيذ هذه الأوامر.
        </div>

        <!-- Step 1: Create Database -->
        <div class="sql-section">
            <h3>1️⃣ إنشاء قاعدة البيانات</h3>
            <div class="instruction">
                📋 استبدل <code>mossaab_landing_page</code> باسم قاعدة البيانات المحدد في ملف .env
            </div>
            
            <button class="copy-button" onclick="copyToClipboard('create-db')">نسخ الأمر</button>
            <div class="sql-code" id="create-db">-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `mossaab_landing_page` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE `mossaab_landing_page`;</div>
        </div>

        <!-- Step 2: Create AI Settings Table -->
        <div class="sql-section">
            <h3>2️⃣ إنشاء جدول إعدادات الذكاء الاصطناعي</h3>
            
            <button class="copy-button" onclick="copyToClipboard('ai-settings-table')">نسخ الأمر</button>
            <div class="sql-code" id="ai-settings-table">-- إنشاء جدول إعدادات الذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `ai_settings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `setting_key` VARCHAR(100) NOT NULL UNIQUE,
    `setting_value` TEXT,
    `provider` VARCHAR(50),
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_provider (provider)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;</div>
        </div>

        <!-- Step 3: Insert Default AI Settings -->
        <div class="sql-section">
            <h3>3️⃣ إدراج الإعدادات الافتراضية للذكاء الاصطناعي</h3>
            
            <button class="copy-button" onclick="copyToClipboard('ai-default-settings')">نسخ الأمر</button>
            <div class="sql-code" id="ai-default-settings">-- إدراج الإعدادات الافتراضية للذكاء الاصطناعي
INSERT IGNORE INTO `ai_settings` (`setting_key`, `setting_value`, `provider`) VALUES
('openai_enabled', '0', 'openai'),
('openai_api_key', '', 'openai'),
('openai_model', 'gpt-4', 'openai'),
('openai_max_tokens', '1000', 'openai'),
('openai_temperature', '0.7', 'openai'),
('anthropic_enabled', '0', 'anthropic'),
('anthropic_api_key', '', 'anthropic'),
('anthropic_model', 'claude-3-sonnet-20240229', 'anthropic'),
('anthropic_max_tokens', '1000', 'anthropic'),
('anthropic_temperature', '0.7', 'anthropic'),
('gemini_enabled', '0', 'gemini'),
('gemini_api_key', '', 'gemini'),
('gemini_model', 'gemini-pro', 'gemini'),
('gemini_max_tokens', '1000', 'gemini'),
('gemini_temperature', '0.7', 'gemini'),
('default_provider', 'openai', 'general'),
('rate_limit', '60', 'general'),
('rate_period', '60', 'general');</div>
        </div>

        <!-- Step 4: Create Categories Table -->
        <div class="sql-section">
            <h3>4️⃣ إنشاء جدول الفئات</h3>
            
            <button class="copy-button" onclick="copyToClipboard('categories-table')">نسخ الأمر</button>
            <div class="sql-code" id="categories-table">-- إنشاء جدول الفئات
CREATE TABLE IF NOT EXISTS `categories` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_ar` VARCHAR(255) NOT NULL,
    `nom_en` VARCHAR(255),
    `description_ar` TEXT,
    `description_en` TEXT,
    `icone` VARCHAR(100) DEFAULT 'fas fa-tag',
    `couleur` VARCHAR(7) DEFAULT '#007bff',
    `ordre` INT DEFAULT 0,
    `actif` TINYINT(1) DEFAULT 1,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ordre (ordre),
    INDEX idx_actif (actif)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;</div>
        </div>

        <!-- Step 5: Insert Sample Categories -->
        <div class="sql-section">
            <h3>5️⃣ إدراج فئات تجريبية</h3>
            
            <button class="copy-button" onclick="copyToClipboard('sample-categories')">نسخ الأمر</button>
            <div class="sql-code" id="sample-categories">-- إدراج فئات تجريبية
INSERT IGNORE INTO `categories` (`nom_ar`, `nom_en`, `description_ar`, `icone`, `couleur`, `ordre`, `actif`) VALUES
('كتب', 'Books', 'كتب ومراجع متنوعة', 'fas fa-book', '#007bff', 1, 1),
('حاسوب', 'Laptops', 'أجهزة حاسوب محمولة', 'fas fa-laptop', '#28a745', 2, 1),
('حقائب', 'Bags', 'حقائب ظهر وحقائب يد', 'fas fa-shopping-bag', '#ffc107', 3, 1),
('ملابس', 'Clothing', 'ملابس وأزياء', 'fas fa-tshirt', '#dc3545', 4, 1),
('أجهزة', 'Electronics', 'أجهزة إلكترونية', 'fas fa-mobile-alt', '#6f42c1', 5, 1);</div>
        </div>

        <!-- Step 6: Create Products Table -->
        <div class="sql-section">
            <h3>6️⃣ إنشاء جدول المنتجات</h3>
            
            <button class="copy-button" onclick="copyToClipboard('products-table')">نسخ الأمر</button>
            <div class="sql-code" id="products-table">-- إنشاء جدول المنتجات
CREATE TABLE IF NOT EXISTS `produits` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `titre` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `prix` DECIMAL(10,2) NOT NULL,
    `stock` INT DEFAULT 0,
    `type` VARCHAR(50) DEFAULT 'book',
    `category_id` INT DEFAULT NULL,
    `image_url` VARCHAR(500),
    `auteur` VARCHAR(255),
    `actif` TINYINT(1) DEFAULT 1,
    `has_landing_page` TINYINT(1) DEFAULT 0,
    `meta_title` VARCHAR(255),
    `meta_description` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_id (category_id),
    INDEX idx_actif (actif),
    INDEX idx_type (type),
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;</div>
        </div>

        <!-- Step 7: Create Payment Settings Table -->
        <div class="sql-section">
            <h3>7️⃣ إنشاء جدول إعدادات الدفع</h3>
            
            <button class="copy-button" onclick="copyToClipboard('payment-settings-table')">نسخ الأمر</button>
            <div class="sql-code" id="payment-settings-table">-- إنشاء جدول إعدادات الدفع
CREATE TABLE IF NOT EXISTS `payment_settings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `setting_key` VARCHAR(100) NOT NULL UNIQUE,
    `setting_value` TEXT,
    `setting_type` ENUM('string', 'integer', 'float', 'boolean', 'json') DEFAULT 'string',
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;</div>
        </div>

        <!-- Step 8: Create Landing Pages Table -->
        <div class="sql-section">
            <h3>8️⃣ إنشاء جدول صفحات الهبوط</h3>
            
            <button class="copy-button" onclick="copyToClipboard('landing-pages-table')">نسخ الأمر</button>
            <div class="sql-code" id="landing-pages-table">-- إنشاء جدول صفحات الهبوط
CREATE TABLE IF NOT EXISTS `landing_pages` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `produit_id` INT NOT NULL,
    `titre` VARCHAR(255) NOT NULL,
    `lien_url` VARCHAR(255) NOT NULL,
    `contenu` LONGTEXT,
    `meta_title` VARCHAR(255),
    `meta_description` TEXT,
    `actif` TINYINT(1) DEFAULT 1,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_produit_id (produit_id),
    INDEX idx_actif (actif),
    FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;</div>
        </div>

        <!-- Instructions -->
        <div class="sql-section">
            <h3>📋 تعليمات التنفيذ</h3>
            
            <div class="instruction">
                <strong>طريقة 1: استخدام phpMyAdmin</strong><br>
                1. افتح phpMyAdmin في المتصفح<br>
                2. انقر على "SQL" في الشريط العلوي<br>
                3. انسخ والصق كل أمر SQL على حدة<br>
                4. انقر على "تنفيذ" لكل أمر
            </div>

            <div class="instruction">
                <strong>طريقة 2: استخدام MySQL Command Line</strong><br>
                1. افتح Command Prompt كمدير<br>
                2. اكتب: <code>mysql -u username -p</code><br>
                3. أدخل كلمة المرور<br>
                4. انسخ والصق الأوامر واحداً تلو الآخر
            </div>

            <div class="instruction">
                <strong>طريقة 3: استخدام MySQL Workbench</strong><br>
                1. افتح MySQL Workbench<br>
                2. اتصل بالخادم<br>
                3. افتح نافذة SQL جديدة<br>
                4. انسخ والصق الأوامر وشغلها
            </div>
        </div>

        <!-- Verification -->
        <div class="sql-section">
            <h3>✅ التحقق من النجاح</h3>
            
            <div class="success">
                بعد تنفيذ جميع الأوامر، يمكنك التحقق من النجاح بتشغيل:
            </div>
            
            <button class="copy-button" onclick="copyToClipboard('verification')">نسخ أمر التحقق</button>
            <div class="sql-code" id="verification">-- التحقق من الجداول المُنشأة
SHOW TABLES;

-- التحقق من بيانات الفئات
SELECT * FROM categories;

-- التحقق من إعدادات الذكاء الاصطناعي
SELECT * FROM ai_settings;</div>
        </div>

        <div class="success">
            🎉 بعد تنفيذ جميع الأوامر بنجاح، ستكون قاعدة البيانات جاهزة للاستخدام!
        </div>

        <p style="text-align: center; margin-top: 30px;">
            <a href="create-database.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">العودة إلى الإنشاء التلقائي</a>
            <a href="diagnose-database.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">تشخيص قاعدة البيانات</a>
            <a href="index.html" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">العودة إلى لوحة التحكم</a>
        </p>

    </div>

    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'تم النسخ!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#007bff';
                }, 2000);
            }).catch(function(err) {
                console.error('فشل في نسخ النص: ', err);
                alert('فشل في نسخ النص. يرجى النسخ يدوياً.');
            });
        }
    </script>
</body>
</html>
