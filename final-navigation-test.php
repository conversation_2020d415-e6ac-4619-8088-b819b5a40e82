<?php
/**
 * Final Navigation Test
 * اختبار نهائي شامل لنظام التنقل المحدث
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار نهائي - نظام التنقل المحدث</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-passed { border: 3px solid #28a745; background: #d4edda; }
        .test-failed { border: 3px solid #dc3545; background: #f8d7da; }
        h1, h2, h3 { color: #333; }
        .test-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .test-failed-result { border-left-color: #dc3545; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; white-space: pre-wrap; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .test-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .test-card.passed { border-left-color: #28a745; background: #d4edda; }
        .test-card.failed { border-left-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 اختبار نهائي - نظام التنقل المحدث</h1>";
echo "<p>فحص شامل للتأكد من حل جميع مشاكل التنقل في لوحة التحكم</p>";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

try {
    // TEST 1: Check Navigation Fix Script
    echo "<div class='section test-passed'>";
    echo "<h2>✅ الاختبار 1: فحص سكريبت إصلاح التنقل</h2>";
    
    echo "<div class='test-result'>";
    echo "<h4>فحص admin-sections-fix.js</h4>";
    
    $totalTests++;
    if (file_exists('admin/js/admin-sections-fix.js')) {
        $fixScriptContent = file_get_contents('admin/js/admin-sections-fix.js');
        $fixScriptSize = filesize('admin/js/admin-sections-fix.js');
        
        // Check for key functions
        $keyFunctions = [
            'initializeNavigation' => 'تهيئة نظام التنقل',
            'hideAllSections' => 'إخفاء جميع الأقسام',
            'showSection' => 'إظهار قسم محدد',
            'navigateToSection' => 'التنقل إلى قسم',
            'setupNavigationListeners' => 'إعداد مستمعي التنقل'
        ];
        
        $foundFunctions = 0;
        foreach ($keyFunctions as $func => $description) {
            if (strpos($fixScriptContent, $func) !== false) {
                $foundFunctions++;
                echo "<div class='success'>✅ {$description}: موجود</div>";
            } else {
                echo "<div class='error'>❌ {$description}: غير موجود</div>";
            }
        }
        
        if ($foundFunctions >= 4) {
            echo "<div class='success'>✅ سكريبت إصلاح التنقل: مكتمل - الحجم: " . number_format($fixScriptSize) . " بايت</div>";
            $passedTests++;
            $testResults[] = "Navigation fix script: PASSED";
        } else {
            echo "<div class='error'>❌ سكريبت إصلاح التنقل: ناقص ({$foundFunctions}/5 وظائف)</div>";
            $testResults[] = "Navigation fix script: FAILED";
        }
    } else {
        echo "<div class='error'>❌ admin-sections-fix.js غير موجود</div>";
        $testResults[] = "Navigation fix script: MISSING";
    }
    
    echo "</div>";
    echo "</div>";
    
    // TEST 2: Check Dashboard Cleanup Script
    echo "<div class='section test-passed'>";
    echo "<h2>✅ الاختبار 2: فحص سكريبت تنظيف لوحة المعلومات</h2>";
    
    echo "<div class='test-result'>";
    echo "<h4>فحص dashboard-cleanup.js</h4>";
    
    $totalTests++;
    if (file_exists('admin/js/dashboard-cleanup.js')) {
        $cleanupScriptContent = file_get_contents('admin/js/dashboard-cleanup.js');
        $cleanupScriptSize = filesize('admin/js/dashboard-cleanup.js');
        
        // Check for key functions
        $cleanupFunctions = [
            'cleanDashboardContent' => 'تنظيف محتوى لوحة المعلومات',
            'loadDashboardStats' => 'تحميل إحصائيات لوحة المعلومات',
            'updateDashboardStats' => 'تحديث الإحصائيات',
            'updateRecentOrders' => 'تحديث الطلبات الأخيرة'
        ];
        
        $foundCleanupFunctions = 0;
        foreach ($cleanupFunctions as $func => $description) {
            if (strpos($cleanupScriptContent, $func) !== false) {
                $foundCleanupFunctions++;
                echo "<div class='success'>✅ {$description}: موجود</div>";
            } else {
                echo "<div class='error'>❌ {$description}: غير موجود</div>";
            }
        }
        
        if ($foundCleanupFunctions >= 3) {
            echo "<div class='success'>✅ سكريبت تنظيف لوحة المعلومات: مكتمل - الحجم: " . number_format($cleanupScriptSize) . " بايت</div>";
            $passedTests++;
            $testResults[] = "Dashboard cleanup script: PASSED";
        } else {
            echo "<div class='error'>❌ سكريبت تنظيف لوحة المعلومات: ناقص ({$foundCleanupFunctions}/4 وظائف)</div>";
            $testResults[] = "Dashboard cleanup script: FAILED";
        }
    } else {
        echo "<div class='error'>❌ dashboard-cleanup.js غير موجود</div>";
        $testResults[] = "Dashboard cleanup script: MISSING";
    }
    
    echo "</div>";
    echo "</div>";
    
    // TEST 3: Check Script Integration in HTML
    echo "<div class='section test-passed'>";
    echo "<h2>✅ الاختبار 3: فحص دمج السكريبتات في HTML</h2>";
    
    echo "<div class='test-result'>";
    echo "<h4>فحص admin/index.html</h4>";
    
    $totalTests++;
    if (file_exists('admin/index.html')) {
        $adminHtmlContent = file_get_contents('admin/index.html');
        
        $requiredScripts = [
            'admin-sections-fix.js' => 'سكريبت إصلاح التنقل',
            'dashboard-cleanup.js' => 'سكريبت تنظيف لوحة المعلومات'
        ];
        
        $foundScripts = 0;
        foreach ($requiredScripts as $script => $description) {
            if (strpos($adminHtmlContent, $script) !== false) {
                $foundScripts++;
                echo "<div class='success'>✅ {$description}: مدمج في HTML</div>";
            } else {
                echo "<div class='error'>❌ {$description}: غير مدمج</div>";
            }
        }
        
        if ($foundScripts >= 2) {
            echo "<div class='success'>✅ جميع السكريبتات مدمجة بشكل صحيح</div>";
            $passedTests++;
            $testResults[] = "Script integration: PASSED";
        } else {
            echo "<div class='error'>❌ بعض السكريبتات غير مدمجة ({$foundScripts}/2)</div>";
            $testResults[] = "Script integration: FAILED";
        }
    } else {
        echo "<div class='error'>❌ admin/index.html غير موجود</div>";
        $testResults[] = "Script integration: MISSING";
    }
    
    echo "</div>";
    echo "</div>";
    
    // TEST 4: Check Navigation Structure
    echo "<div class='section test-passed'>";
    echo "<h2>✅ الاختبار 4: فحص هيكل التنقل</h2>";
    
    echo "<div class='test-result'>";
    echo "<h4>فحص عناصر التنقل والأقسام</h4>";
    
    $totalTests++;
    if (file_exists('admin/index.html')) {
        $adminHtmlContent = file_get_contents('admin/index.html');
        
        // Check navigation items
        $expectedNavItems = [
            'data-section="dashboard"' => 'الرئيسية',
            'data-section="books"' => 'إدارة المنتجات',
            'data-section="orders"' => 'الطلبات',
            'data-section="landingPages"' => 'صفحات هبوط',
            'data-section="reports"' => 'التقارير',
            'data-section="settings"' => 'الإعدادات'
        ];
        
        $foundNavItems = 0;
        foreach ($expectedNavItems as $navItem => $description) {
            if (strpos($adminHtmlContent, $navItem) !== false) {
                $foundNavItems++;
                echo "<div class='success'>✅ عنصر التنقل {$description}: موجود</div>";
            } else {
                echo "<div class='error'>❌ عنصر التنقل {$description}: غير موجود</div>";
            }
        }
        
        // Check content sections
        $expectedSections = [
            'id="dashboard"' => 'قسم الرئيسية',
            'id="books"' => 'قسم إدارة المنتجات',
            'id="orders"' => 'قسم الطلبات',
            'id="landingPages"' => 'قسم صفحات هبوط',
            'id="reports"' => 'قسم التقارير',
            'id="settings"' => 'قسم الإعدادات'
        ];
        
        $foundSections = 0;
        foreach ($expectedSections as $section => $description) {
            if (strpos($adminHtmlContent, $section) !== false) {
                $foundSections++;
                echo "<div class='success'>✅ {$description}: موجود</div>";
            } else {
                echo "<div class='error'>❌ {$description}: غير موجود</div>";
            }
        }
        
        if ($foundNavItems >= 5 && $foundSections >= 5) {
            echo "<div class='success'>✅ هيكل التنقل مكتمل</div>";
            $passedTests++;
            $testResults[] = "Navigation structure: PASSED";
        } else {
            echo "<div class='error'>❌ هيكل التنقل ناقص (عناصر: {$foundNavItems}/6، أقسام: {$foundSections}/6)</div>";
            $testResults[] = "Navigation structure: FAILED";
        }
    }
    
    echo "</div>";
    echo "</div>";
    
    // TEST 5: Check CSS Classes
    echo "<div class='section test-passed'>";
    echo "<h2>✅ الاختبار 5: فحص فئات CSS</h2>";
    
    echo "<div class='test-result'>";
    echo "<h4>فحص admin.css للفئات المطلوبة</h4>";
    
    $totalTests++;
    if (file_exists('admin/css/admin.css')) {
        $adminCssContent = file_get_contents('admin/css/admin.css');
        
        $requiredCssClasses = [
            '.content-section' => 'فئة الأقسام',
            '.content-section.active' => 'فئة القسم النشط',
            '.admin-nav ul li.active' => 'فئة عنصر التنقل النشط'
        ];
        
        $foundCssClasses = 0;
        foreach ($requiredCssClasses as $cssClass => $description) {
            if (strpos($adminCssContent, $cssClass) !== false) {
                $foundCssClasses++;
                echo "<div class='success'>✅ {$description}: موجود</div>";
            } else {
                echo "<div class='error'>❌ {$description}: غير موجود</div>";
            }
        }
        
        if ($foundCssClasses >= 3) {
            echo "<div class='success'>✅ جميع فئات CSS المطلوبة موجودة</div>";
            $passedTests++;
            $testResults[] = "CSS classes: PASSED";
        } else {
            echo "<div class='error'>❌ بعض فئات CSS مفقودة ({$foundCssClasses}/3)</div>";
            $testResults[] = "CSS classes: FAILED";
        }
    } else {
        echo "<div class='error'>❌ admin.css غير موجود</div>";
        $testResults[] = "CSS classes: MISSING";
    }
    
    echo "</div>";
    echo "</div>";
    
    // FINAL RESULTS
    $successRate = ($passedTests / $totalTests) * 100;
    
    echo "<div class='section'>";
    echo "<h2>📊 النتائج النهائية</h2>";
    
    if ($successRate >= 90) {
        echo "<div class='success'>";
        echo "<h3>🎉 ممتاز! جميع الاختبارات نجحت ({$passedTests}/{$totalTests})</h3>";
        echo "<p>نسبة النجاح: " . round($successRate, 1) . "%</p>";
        echo "<p><strong>نظام التنقل جاهز للاستخدام!</strong></p>";
        echo "</div>";
    } elseif ($successRate >= 70) {
        echo "<div class='warning'>";
        echo "<h3>⚠️ جيد! معظم الاختبارات نجحت ({$passedTests}/{$totalTests})</h3>";
        echo "<p>نسبة النجاح: " . round($successRate, 1) . "%</p>";
        echo "<p>يحتاج بعض التحسينات الطفيفة</p>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ يحتاج النظام إلى إصلاحات إضافية ({$passedTests}/{$totalTests})</h3>";
        echo "<p>نسبة النجاح: " . round($successRate, 1) . "%</p>";
        echo "</div>";
    }
    
    echo "<div class='test-grid'>";
    foreach ($testResults as $result) {
        $status = strpos($result, 'PASSED') !== false ? 'passed' : 'failed';
        $icon = $status === 'passed' ? '✅' : '❌';
        echo "<div class='test-card {$status}'>";
        echo "<h4>{$icon} {$result}</h4>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>🔗 اختبار النظام:</h3>";
    echo "<ul>";
    echo "<li><a href='/admin/' target='_blank'>فتح لوحة التحكم</a> - اختبار مباشر</li>";
    echo "<li><a href='/test-navigation-fix.html' target='_blank'>اختبار التنقل التفاعلي</a> - اختبار شامل</li>";
    echo "<li><a href='/final-sections-test.html' target='_blank'>اختبار الأقسام النهائي</a> - اختبار متقدم</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h3>✅ المشاكل التي تم حلها:</h3>";
    echo "<ul>";
    echo "<li>✅ إصلاح التنقل بين الأقسام - الآن جميع الأقسام قابلة للنقر</li>";
    echo "<li>✅ إخفاء جميع الأقسام عدا القسم النشط</li>";
    echo "<li>✅ تنظيف لوحة المعلومات لعرض الإحصائيات فقط</li>";
    echo "<li>✅ تحسين معالجة الأحداث والتنقل</li>";
    echo "<li>✅ ضمان حالة نظيفة عند تحميل الصفحة</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h3>📋 كيفية اختبار النظام:</h3>";
    echo "<ol>";
    echo "<li>افتح لوحة التحكم: <a href='/admin/' target='_blank'>/admin/</a></li>";
    echo "<li>انقر على كل قسم في الشريط الجانبي</li>";
    echo "<li>تأكد من أن قسم واحد فقط مرئي في كل مرة</li>";
    echo "<li>تحقق من أن لوحة المعلومات تعرض الإحصائيات فقط</li>";
    echo "<li>اختبر التنقل بين جميع الأقسام</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام في الاختبار: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
