<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Toggle Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #ddd;
            margin: 10px 0;
            border-radius: 5px;
        }
        .product-info {
            flex: 1;
            margin-left: 15px;
        }
        .toggle-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        .toggle-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .status-active {
            background: #e67e22;
            color: white;
        }
        .status-inactive {
            background: #27ae60;
            color: white;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Product Toggle Functionality</h1>
        
        <div id="products-container">
            <p>Loading products...</p>
        </div>
        
        <h3>Debug Log:</h3>
        <div id="debug-log" class="log"></div>
    </div>

    <script>
        // Debug logging function
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // Enhanced toggle function with detailed logging
        async function toggleProductStatus(productId, currentStatus) {
            log(`Toggle called for product ${productId}, current status: ${currentStatus}`);
            
            const button = document.querySelector(`[data-product-id="${productId}"]`);
            const originalContent = button ? button.innerHTML : '';

            if (button) {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            }

            try {
                const formData = new FormData();
                formData.append('productId', productId.toString());
                formData.append('active', !currentStatus ? '1' : '0');

                log(`Sending request: productId=${productId}, active=${!currentStatus ? '1' : '0'}`);

                const response = await fetch('php/api/products.php?action=toggle-active', {
                    method: 'POST',
                    body: formData
                });

                log(`Response status: ${response.status}`);

                const responseText = await response.text();
                log(`Response text: ${responseText}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}, response: ${responseText}`);
                }

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    log(`JSON parse error: ${parseError.message}`);
                    throw new Error(`Invalid JSON response: ${responseText}`);
                }

                log(`Parsed response: ${JSON.stringify(data)}`);

                if (data.success) {
                    const newStatus = !currentStatus;
                    if (button) {
                        button.className = `toggle-btn ${newStatus ? 'status-active' : 'status-inactive'}`;
                        button.innerHTML = newStatus ? 'تعطيل' : 'تفعيل';
                        button.setAttribute('data-current-status', newStatus);
                        button.onclick = () => toggleProductStatus(productId, newStatus);
                        button.disabled = false;
                    }
                    log(`✓ Toggle successful: ${newStatus ? 'Active' : 'Inactive'}`);
                } else {
                    throw new Error(data.message || 'Toggle failed');
                }

            } catch (error) {
                log(`✗ Error: ${error.message}`);
                if (button) {
                    button.disabled = false;
                    button.innerHTML = originalContent;
                }
            }
        }

        // Load products
        async function loadProducts() {
            try {
                log('Loading products...');
                const response = await fetch('php/api/products.php');
                const data = await response.json();
                
                log(`Loaded ${data.products ? data.products.length : 0} products`);
                
                const container = document.getElementById('products-container');
                
                if (data.products && data.products.length > 0) {
                    container.innerHTML = data.products.map(product => `
                        <div class="product-item">
                            <div class="product-info">
                                <strong>${product.titre}</strong><br>
                                <small>ID: ${product.id} | Type: ${product.type} | Status: ${product.actif ? 'Active' : 'Inactive'}</small>
                            </div>
                            <button 
                                class="toggle-btn ${product.actif ? 'status-active' : 'status-inactive'}"
                                data-product-id="${product.id}"
                                data-current-status="${product.actif}"
                                onclick="toggleProductStatus(${product.id}, ${product.actif})"
                            >
                                ${product.actif ? 'تعطيل' : 'تفعيل'}
                            </button>
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<p>No products found</p>';
                }
                
            } catch (error) {
                log(`Error loading products: ${error.message}`);
                document.getElementById('products-container').innerHTML = '<p>Error loading products</p>';
            }
        }

        // Load products on page load
        document.addEventListener('DOMContentLoaded', loadProducts);
    </script>
</body>
</html>
