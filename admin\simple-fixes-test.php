<?php
require_once __DIR__ . '/../php/config.php';

echo "🔧 SIMPLE FIXES VERIFICATION\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    $pdo = getPDOConnection();
    
    // Test 1: Admin Panel JavaScript Reference
    echo "📋 Test 1: Admin Panel JavaScript\n";
    echo "-" . str_repeat("-", 30) . "\n";
    
    $adminHtml = file_get_contents(__DIR__ . '/index.html');
    
    if (strpos($adminHtml, 'js/stores-management.js') !== false) {
        echo "✅ stores-management.js is referenced in admin panel\n";
    } else {
        echo "❌ stores-management.js not found in admin panel\n";
    }
    
    // Test 2: API Path Fix
    echo "\n📋 Test 2: API Path Fix\n";
    echo "-" . str_repeat("-", 20) . "\n";
    
    $storesJs = file_get_contents(__DIR__ . '/js/stores-management.js');
    
    if (strpos($storesJs, "fetch('php/api/stores.php')") !== false) {
        echo "✅ API path corrected to 'php/api/stores.php'\n";
    } else {
        echo "❌ API path still incorrect\n";
    }
    
    // Test 3: Stores API Functionality
    echo "\n📋 Test 3: Stores API\n";
    echo "-" . str_repeat("-", 18) . "\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/stores.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "✅ Stores API working: {$data['total']} stores found\n";
        } else {
            echo "❌ Stores API error: " . ($data['message'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "❌ Stores API not accessible (HTTP {$httpCode})\n";
    }
    
    // Test 4: Store Page Access
    echo "\n📋 Test 4: Store Page\n";
    echo "-" . str_repeat("-", 18) . "\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/store/mossaab-store');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $storeResponse = curl_exec($ch);
    $storeHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($storeHttpCode === 200) {
        echo "✅ Store page accessible (HTTP {$storeHttpCode})\n";
        
        if (strpos($storeResponse, 'متجر مصعب') !== false) {
            echo "✅ Store name displayed correctly\n";
        } else {
            echo "❌ Store name not found\n";
        }
        
        // Check for products section
        if (strpos($storeResponse, 'منتجاتنا') !== false) {
            echo "✅ Products section present\n";
        } else {
            echo "❌ Products section missing\n";
        }
        
    } else {
        echo "❌ Store page not accessible (HTTP {$storeHttpCode})\n";
    }
    
    // Test 5: Database State
    echo "\n📋 Test 5: Database\n";
    echo "-" . str_repeat("-", 16) . "\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM stores WHERE store_slug = 'mossaab-store' AND status = 'active'");
    $stmt->execute();
    $storeCount = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM produits WHERE store_id = 1 AND actif = 1");
    $stmt->execute();
    $storeProducts = $stmt->fetchColumn();
    
    echo "✅ Demo store exists: " . ($storeCount > 0 ? 'Yes' : 'No') . "\n";
    echo "✅ Store products: {$storeProducts}\n";
    
    // SUMMARY
    echo "\n🎯 SUMMARY\n";
    echo "=" . str_repeat("=", 40) . "\n";
    
    $allGood = (
        strpos($adminHtml, 'js/stores-management.js') !== false &&
        strpos($storesJs, "fetch('php/api/stores.php')") !== false &&
        $httpCode === 200 &&
        $storeHttpCode === 200 &&
        $storeCount > 0 &&
        $storeProducts > 0
    );
    
    if ($allGood) {
        echo "🎉 ALL FIXES SUCCESSFULLY IMPLEMENTED!\n\n";
        echo "✅ Admin Panel Store Management:\n";
        echo "   • JavaScript properly loaded\n";
        echo "   • API path corrected\n";
        echo "   • Should load within 3 seconds\n\n";
        echo "✅ Store Page Products Display:\n";
        echo "   • Page accessible with correct content\n";
        echo "   • {$storeProducts} products in database\n";
        echo "   • Products section present in HTML\n\n";
        echo "🔗 READY TO TEST:\n";
        echo "   1. http://localhost:8000/admin/ → إعدادات النظام → المتاجر\n";
        echo "   2. http://localhost:8000/store/mossaab-store\n\n";
        echo "💡 If products still not visible, clear browser cache (Ctrl+F5)\n";
    } else {
        echo "⚠️ Some issues detected. Check individual test results above.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
