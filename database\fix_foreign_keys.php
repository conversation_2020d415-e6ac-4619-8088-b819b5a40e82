<?php
/**
 * Fix Foreign Key Constraints
 * This script fixes foreign key constraints to reference produits instead of livres
 */

require_once __DIR__ . '/../php/config.php';

try {
    echo "=== Fixing Foreign Key Constraints ===\n";
    
    $pdo = getPDOConnection();
    
    // Disable foreign key checks temporarily
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    echo "✓ Disabled foreign key checks\n";
    
    // Fix details_commande foreign key
    echo "\n1. Fixing details_commande foreign key...\n";
    try {
        $pdo->exec("ALTER TABLE details_commande DROP FOREIGN KEY details_commande_ibfk_2");
        echo "   ✓ Dropped old constraint details_commande_ibfk_2\n";
    } catch (Exception $e) {
        echo "   ℹ Old constraint may not exist: " . $e->getMessage() . "\n";
    }
    
    try {
        $pdo->exec("ALTER TABLE details_commande ADD CONSTRAINT details_commande_ibfk_2 FOREIGN KEY (livre_id) REFERENCES produits(id)");
        echo "   ✓ Added new constraint details_commande_ibfk_2 -> produits\n";
    } catch (Exception $e) {
        echo "   ✗ Failed to add constraint: " . $e->getMessage() . "\n";
    }
    
    // Fix panier foreign key
    echo "\n2. Fixing panier foreign key...\n";
    try {
        $pdo->exec("ALTER TABLE panier DROP FOREIGN KEY panier_ibfk_1");
        echo "   ✓ Dropped old constraint panier_ibfk_1\n";
    } catch (Exception $e) {
        echo "   ℹ Old constraint may not exist: " . $e->getMessage() . "\n";
    }
    
    try {
        $pdo->exec("ALTER TABLE panier ADD CONSTRAINT panier_ibfk_1 FOREIGN KEY (livre_id) REFERENCES produits(id) ON DELETE CASCADE");
        echo "   ✓ Added new constraint panier_ibfk_1 -> produits\n";
    } catch (Exception $e) {
        echo "   ✗ Failed to add constraint: " . $e->getMessage() . "\n";
    }
    
    // Fix product_content_blocks foreign key
    echo "\n3. Fixing product_content_blocks foreign key...\n";
    try {
        $pdo->exec("ALTER TABLE product_content_blocks DROP FOREIGN KEY product_content_blocks_ibfk_1");
        echo "   ✓ Dropped old constraint product_content_blocks_ibfk_1\n";
    } catch (Exception $e) {
        echo "   ℹ Old constraint may not exist: " . $e->getMessage() . "\n";
    }
    
    try {
        $pdo->exec("ALTER TABLE product_content_blocks ADD CONSTRAINT product_content_blocks_ibfk_1 FOREIGN KEY (product_id) REFERENCES produits(id) ON DELETE CASCADE");
        echo "   ✓ Added new constraint product_content_blocks_ibfk_1 -> produits\n";
    } catch (Exception $e) {
        echo "   ✗ Failed to add constraint: " . $e->getMessage() . "\n";
    }
    
    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "\n✓ Re-enabled foreign key checks\n";
    
    echo "\n=== Foreign Key Fix Completed ===\n";
    echo "All foreign key constraints have been updated to reference 'produits' table.\n";
    
} catch (Exception $e) {
    echo "\n✗ ERROR: " . $e->getMessage() . "\n";
    exit(1);
}
?>
