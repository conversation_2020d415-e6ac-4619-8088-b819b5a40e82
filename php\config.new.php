<?php

/**
 * Centralized Configuration System for Mossaab Landing Page
 */

class Config
{
    private static $instance = null;
    private $config = [];
    private $cache = [];
    private $envPath;
    private $envBackupPath;

    private function __construct()
    {
        if (!defined('SECURITY_CHECK')) {
            define('SECURITY_CHECK', true);
        }

        $this->envPath = __DIR__ . '/../.env';
        $this->envBackupPath = __DIR__ . '/../.env.backup';
        $this->loadEnv();

        // Enable error reporting based on APP_DEBUG
        ini_set('display_errors', $this->getBool('APP_DEBUG', false) ? 1 : 0);
        ini_set('display_startup_errors', $this->getBool('APP_DEBUG', false) ? 1 : 0);
        error_reporting($this->getBool('APP_DEBUG', false) ? E_ALL : E_ERROR);

        // Check required PHP extensions
        $required_extensions = ['pdo', 'pdo_mysql'];
        foreach ($required_extensions as $ext) {
            if (!extension_loaded($ext)) {
                die("L'extension PHP {$ext} est requise mais n'est pas installée.");
            }
        }
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    // Static accessors
    public static function get($key, $default = null)
    {
        return self::getInstance()->getValue($key, $default);
    }

    public static function getBool($key, $default = false)
    {
        $value = self::get($key, $default);
        if (is_bool($value)) return $value;
        return filter_var($value, FILTER_VALIDATE_BOOLEAN);
    }

    public static function set($key, $value)
    {
        return self::getInstance()->setValue($key, $value);
    }

    // Environment file management
    private function loadEnv()
    {
        if (!file_exists($this->envPath)) {
            throw new Exception('Environment file not found');
        }

        $lines = file($this->envPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                list($key, $value) = array_map('trim', explode('=', $line, 2));
                $this->config[$key] = $this->parseEnvValue($value);
            }
        }
    }

    private function parseEnvValue($value)
    {
        switch (strtolower($value)) {
            case 'true':
            case '(true)':
                return true;
            case 'false':
            case '(false)':
                return false;
            case 'empty':
            case '(empty)':
                return '';
            case 'null':
            case '(null)':
                return null;
        }

        if (preg_match('/\A([\'"])(.*)\1\z/', $value, $matches)) {
            return $matches[2];
        }

        return $value;
    }

    // Value management
    public function getValue($key, $default = null)
    {
        if (isset($this->cache[$key])) {
            return $this->cache[$key];
        }

        $value = $this->config[$key] ?? $default;
        $this->cache[$key] = $value;
        return $value;
    }

    public function setValue($key, $value)
    {
        $this->config[$key] = $value;
        $this->cache[$key] = $value;
        $this->updateEnvVar($key, $value);
        return $value;
    }

    // Environment variable management
    public function updateEnvVar($key, $value)
    {
        if (!file_exists($this->envPath)) {
            throw new Exception('Environment file not found');
        }

        // Create backup before updating
        $this->backupEnv();

        $lines = file($this->envPath, FILE_IGNORE_NEW_LINES);
        $updated = false;

        foreach ($lines as $i => $line) {
            if (strpos($line, "$key=") === 0) {
                $lines[$i] = "$key=" . $this->formatEnvValue($value);
                $updated = true;
                break;
            }
        }

        if (!$updated) {
            $lines[] = "$key=" . $this->formatEnvValue($value);
        }

        file_put_contents($this->envPath, implode(PHP_EOL, $lines));
        $this->clearCache();
    }

    private function formatEnvValue($value)
    {
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }
        if (is_null($value)) {
            return 'null';
        }
        if (is_string($value) && (
            strpos($value, ' ') !== false ||
            strpos($value, '#') !== false ||
            strpos($value, '"') !== false
        )) {
            return '"' . str_replace('"', '\\"', $value) . '"';
        }
        return $value;
    }

    // Backup and restore
    public function backupEnv()
    {
        if (!copy($this->envPath, $this->envBackupPath)) {
            throw new Exception('Failed to create environment backup');
        }
        return true;
    }

    public function restoreEnv()
    {
        if (!file_exists($this->envBackupPath)) {
            throw new Exception('No environment backup found');
        }
        if (!copy($this->envBackupPath, $this->envPath)) {
            throw new Exception('Failed to restore environment from backup');
        }
        $this->clearCache();
        $this->loadEnv();
        return true;
    }

    // Cache management
    public function clearCache()
    {
        $this->cache = [];
    }

    // Type validation helpers
    public function validateType($value, $type)
    {
        switch ($type) {
            case 'string':
                return is_string($value);
            case 'int':
                return is_numeric($value) && (string)(int)$value === (string)$value;
            case 'float':
                return is_numeric($value);
            case 'bool':
                return is_bool($value) || in_array(strtolower($value), ['true', 'false', '1', '0']);
            case 'array':
                return is_array(json_decode($value, true));
            default:
                return true;
        }
    }

    // Configuration grouping methods
    public function getDatabaseConfig()
    {
        return [
            'host' => $this->get('DB_HOST', 'localhost'),
            'name' => $this->get('DB_NAME'),
            'user' => $this->get('DB_USER'),
            'pass' => $this->get('DB_PASS'),
            'charset' => $this->get('DB_CHARSET', 'utf8mb4')
        ];
    }

    public function getAIConfig()
    {
        return [
            'openai_key' => $this->get('OPENAI_API_KEY'),
            'anthropic_key' => $this->get('ANTHROPIC_API_KEY'),
            'gemini_key' => $this->get('GEMINI_API_KEY'),
            'default_provider' => $this->get('DEFAULT_AI_PROVIDER', 'openai'),
            'rate_limit' => $this->get('AI_RATE_LIMIT', 60),
            'rate_period' => $this->get('AI_RATE_PERIOD', 60)
        ];
    }

    public function getSecurityConfig()
    {
        return [
            'app_key' => $this->get('APP_KEY'),
            'csrf_enabled' => $this->getBool('CSRF_ENABLED', true),
            'rate_limit_enabled' => $this->getBool('RATE_LIMIT_ENABLED', true),
            'debug_mode' => $this->getBool('APP_DEBUG', false)
        ];
    }
}
