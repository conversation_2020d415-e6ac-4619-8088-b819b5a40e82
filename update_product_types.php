<?php
/**
 * Update the produits table to support more product types
 */

// Security check
define('SECURITY_CHECK', true);

require_once 'php/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تحديث أنواع المنتجات</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
    </style>
</head>
<body>";

echo "<h1>🔧 تحديث أنواع المنتجات في قاعدة البيانات</h1>";

try {
    $pdo = getPDOConnection();
    
    // Check current type column definition
    echo "<h2>📋 فحص التعريف الحالي لعمود النوع</h2>";
    $stmt = $pdo->query("SHOW COLUMNS FROM produits LIKE 'type'");
    $currentType = $stmt->fetch();
    
    if ($currentType) {
        echo "<div class='info'>📝 التعريف الحالي: {$currentType['Type']}</div>";
    }
    
    // Update the type column to support more product types
    echo "<h2>🔄 تحديث عمود النوع لدعم المزيد من أنواع المنتجات</h2>";
    
    $updateTypeSQL = "
        ALTER TABLE produits 
        MODIFY COLUMN type ENUM(
            'book', 
            'bag', 
            'laptop', 
            'smartphone', 
            'accessory', 
            'sports', 
            'beauty', 
            'game',
            'clothing',
            'home'
        ) NOT NULL
    ";
    
    $pdo->exec($updateTypeSQL);
    echo "<div class='success'>✅ تم تحديث عمود النوع بنجاح</div>";
    
    // Verify the update
    echo "<h2>✅ التحقق من التحديث</h2>";
    $stmt = $pdo->query("SHOW COLUMNS FROM produits LIKE 'type'");
    $updatedType = $stmt->fetch();
    
    if ($updatedType) {
        echo "<div class='success'>📝 التعريف الجديد: {$updatedType['Type']}</div>";
    }
    
    echo "<div class='success'>🎉 تم تحديث قاعدة البيانات بنجاح! يمكنك الآن إضافة المنتجات الجديدة.</div>";
    
    // Add a link to run the products script again
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='add_categories_and_products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إضافة المنتجات الآن</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
}

echo "</body></html>";
?>
