<?php
require_once __DIR__ . '/../php/config.php';

echo "🎯 FINAL COMPREHENSIVE TEST - BOTH CRITICAL ISSUES\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    $pdo = getPDOConnection();
    
    // TEST 1: Admin Panel Store Management
    echo "🏪 TEST 1: ADMIN PANEL STORE MANAGEMENT\n";
    echo "-" . str_repeat("-", 45) . "\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/stores.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $apiResponse = curl_exec($ch);
    $apiHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $issue1Fixed = false;
    
    if ($apiHttpCode === 200) {
        $apiData = json_decode($apiResponse, true);
        if ($apiData && $apiData['success'] && isset($apiData['total'])) {
            echo "✅ Stores API working correctly\n";
            echo "   HTTP Status: {$apiHttpCode}\n";
            echo "   Success: true\n";
            echo "   Total stores: {$apiData['total']}\n";
            echo "   Message: {$apiData['message']}\n";
            $issue1Fixed = true;
        } else {
            echo "❌ API returns invalid data\n";
        }
    } else {
        echo "❌ API not accessible (HTTP {$apiHttpCode})\n";
    }
    
    // TEST 2: Demo Store Product Filtering
    echo "\n🛍️ TEST 2: DEMO STORE PRODUCT FILTERING\n";
    echo "-" . str_repeat("-", 45) . "\n";
    
    $storeId = 1;
    $stmt = $pdo->prepare("
        SELECT p.*
        FROM produits p
        WHERE p.store_id = ? AND p.actif = 1
        ORDER BY p.created_at DESC
    ");
    $stmt->execute([$storeId]);
    $storeProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Also check total products to ensure we're not showing all
    $stmt = $pdo->query("SELECT COUNT(*) FROM produits WHERE actif = 1");
    $totalProducts = $stmt->fetchColumn();
    
    $issue2Fixed = false;
    
    echo "Store-specific products: " . count($storeProducts) . "\n";
    echo "Total products in database: {$totalProducts}\n";
    
    if (count($storeProducts) === 10 && $totalProducts === 45) {
        echo "✅ Product filtering working correctly\n";
        echo "   Showing 10 store-specific products instead of all 45\n";
        $issue2Fixed = true;
    } elseif (count($storeProducts) !== 10) {
        echo "❌ Wrong number of store products (" . count($storeProducts) . ", expected 10)\n";
    } else {
        echo "❌ Product filtering issue detected\n";
    }
    
    // Test store page accessibility
    echo "\n🌐 Testing Store Page...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/store/mossaab-store');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $storeResponse = curl_exec($ch);
    $storeHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $storePageWorking = false;
    
    if ($storeHttpCode === 200) {
        echo "✅ Store page accessible (HTTP {$storeHttpCode})\n";
        
        if (strpos($storeResponse, 'متجر مصعب') !== false) {
            echo "✅ Store name displayed correctly\n";
        }
        
        // Check for sample products
        $sampleProducts = ['كتاب الطبخ الجزائري الأصيل', 'Samsung Galaxy A54 5G'];
        $foundCount = 0;
        foreach ($sampleProducts as $product) {
            if (strpos($storeResponse, $product) !== false) {
                $foundCount++;
            }
        }
        
        if ($foundCount > 0) {
            echo "✅ Store-specific products visible on page\n";
            $storePageWorking = true;
        } else {
            echo "❌ Store products not visible\n";
        }
    } else {
        echo "❌ Store page not accessible (HTTP {$storeHttpCode})\n";
    }
    
    // FINAL RESULTS
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "🎯 FINAL RESULTS\n";
    echo str_repeat("=", 60) . "\n\n";
    
    if ($issue1Fixed && $issue2Fixed && $storePageWorking) {
        echo "🎉 ALL CRITICAL ISSUES SUCCESSFULLY RESOLVED!\n\n";
        
        echo "✅ ISSUE 1 - Admin Panel Store Management:\n";
        echo "   • Stores API working correctly\n";
        echo "   • Returns valid JSON with store data\n";
        echo "   • Admin panel should load store management properly\n\n";
        
        echo "✅ ISSUE 2 - Demo Store Product Filtering:\n";
        echo "   • Shows exactly 10 store-specific products\n";
        echo "   • No longer shows all 45 products\n";
        echo "   • Store page displays correctly\n\n";
        
        echo "🔗 TESTING INSTRUCTIONS:\n";
        echo "1. Admin Panel: http://localhost:8000/admin/\n";
        echo "   → إعدادات النظام → المتاجر\n";
        echo "   → Should load store management interface\n\n";
        echo "2. Demo Store: http://localhost:8000/store/mossaab-store\n";
        echo "   → Should show 10 products instead of 45\n";
        echo "   → Should display 'متجر مصعب' branding\n\n";
        
        echo "🎯 BOTH CRITICAL ISSUES ARE NOW FIXED!\n";
        
    } else {
        echo "⚠️ SOME ISSUES REMAIN:\n\n";
        
        if (!$issue1Fixed) {
            echo "❌ Issue 1 (Admin Panel Store Management): NOT FIXED\n";
        } else {
            echo "✅ Issue 1 (Admin Panel Store Management): FIXED\n";
        }
        
        if (!$issue2Fixed) {
            echo "❌ Issue 2 (Demo Store Product Filtering): NOT FIXED\n";
        } else {
            echo "✅ Issue 2 (Demo Store Product Filtering): FIXED\n";
        }
        
        if (!$storePageWorking) {
            echo "❌ Store Page: NOT WORKING\n";
        } else {
            echo "✅ Store Page: WORKING\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Critical Error: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "Test completed at " . date('Y-m-d H:i:s') . "\n";
?>
