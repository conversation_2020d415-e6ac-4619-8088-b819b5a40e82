<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المتجر - متجر مصعب</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
</head>
<body>
    <!-- Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="logo-section">
                <h1><i class="fas fa-store"></i> لوحة تحكم المتجر</h1>
            </div>
            <div class="user-section">
                <span class="user-name" id="userName">مصعب التجريبي</span>
                <div class="user-menu">
                    <button class="user-menu-btn" onclick="toggleUserMenu()">
                        <i class="fas fa-user-circle"></i>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" onclick="viewProfile()"><i class="fas fa-user"></i> الملف الشخصي</a>
                        <a href="#" onclick="viewStore()"><i class="fas fa-store"></i> عرض المتجر</a>
                        <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#dashboard" onclick="showSection('dashboard')" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#products" onclick="showSection('products')" class="nav-link">
                            <i class="fas fa-box"></i>
                            <span>إدارة المنتجات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#orders" onclick="showSection('orders')" class="nav-link">
                            <i class="fas fa-shopping-cart"></i>
                            <span>الطلبات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#analytics" onclick="showSection('analytics')" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>التحليلات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" onclick="showSection('settings')" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>إعدادات المتجر</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section active">
                <div class="section-header">
                    <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>
                    <p>مرحباً بك في لوحة تحكم متجرك</p>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalProducts">10</h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalOrders">0</h3>
                            <p>إجمالي الطلبات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalRevenue">0</h3>
                            <p>إجمالي الإيرادات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="storeViews">0</h3>
                            <p>زيارات المتجر</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3>إجراءات سريعة</h3>
                    <div class="actions-grid">
                        <button class="action-btn" onclick="showSection('products')">
                            <i class="fas fa-plus"></i>
                            إضافة منتج جديد
                        </button>
                        <button class="action-btn" onclick="viewStore()">
                            <i class="fas fa-external-link-alt"></i>
                            عرض المتجر
                        </button>
                        <button class="action-btn" onclick="showSection('orders')">
                            <i class="fas fa-list"></i>
                            عرض الطلبات
                        </button>
                        <button class="action-btn" onclick="showSection('settings')">
                            <i class="fas fa-cog"></i>
                            إعدادات المتجر
                        </button>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="recent-activity">
                    <h3>النشاط الأخير</h3>
                    <div class="activity-list" id="activityList">
                        <div class="activity-item">
                            <i class="fas fa-plus text-success"></i>
                            <span>تم إضافة 10 منتجات جديدة إلى المتجر</span>
                            <small>منذ دقائق</small>
                        </div>
                        <div class="activity-item">
                            <i class="fas fa-store text-info"></i>
                            <span>تم إنشاء المتجر بنجاح</span>
                            <small>اليوم</small>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Products Section -->
            <section id="products-section" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-box"></i> إدارة المنتجات</h2>
                    <button class="btn btn-primary" onclick="showAddProductModal()">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </button>
                </div>

                <!-- Products Filter -->
                <div class="filter-section">
                    <div class="filter-group">
                        <label>البحث:</label>
                        <input type="text" id="productSearch" placeholder="ابحث عن منتج..." onkeyup="filterProducts()">
                    </div>
                    <div class="filter-group">
                        <label>النوع:</label>
                        <select id="productTypeFilter" onchange="filterProducts()">
                            <option value="">جميع الأنواع</option>
                            <option value="book">كتب</option>
                            <option value="laptop">لابتوب</option>
                            <option value="smartphone">هواتف</option>
                            <option value="bag">حقائب</option>
                            <option value="clothing">ملابس</option>
                            <option value="home">منزلية</option>
                        </select>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>النوع</th>
                                <th>السعر</th>
                                <th>المخزون</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <!-- Products will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Orders Section -->
            <section id="orders-section" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-shopping-cart"></i> الطلبات</h2>
                </div>
                
                <div class="empty-state">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>لا توجد طلبات حتى الآن</h3>
                    <p>عندما يقوم العملاء بطلب منتجات من متجرك، ستظهر هنا</p>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics-section" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-bar"></i> التحليلات</h2>
                </div>
                
                <div class="analytics-grid">
                    <div class="chart-container">
                        <h3>المبيعات الشهرية</h3>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-line"></i>
                            <p>لا توجد بيانات كافية لعرض الرسم البياني</p>
                        </div>
                    </div>
                    <div class="chart-container">
                        <h3>المنتجات الأكثر مبيعاً</h3>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-pie"></i>
                            <p>لا توجد بيانات كافية لعرض الرسم البياني</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings-section" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-cog"></i> إعدادات المتجر</h2>
                </div>

                <div class="settings-form">
                    <div class="form-group">
                        <label>اسم المتجر:</label>
                        <input type="text" id="storeName" value="متجر مصعب">
                    </div>
                    <div class="form-group">
                        <label>وصف المتجر:</label>
                        <textarea id="storeDescription" rows="4">متجر شامل للكتب والإلكترونيات والحقائب</textarea>
                    </div>
                    <div class="form-group">
                        <label>لون المتجر:</label>
                        <input type="color" id="storeColor" value="#667eea">
                    </div>
                    <div class="form-group">
                        <label>العملة:</label>
                        <select id="storeCurrency">
                            <option value="DZD" selected>دينار جزائري (DZD)</option>
                            <option value="USD">دولار أمريكي (USD)</option>
                            <option value="EUR">يورو (EUR)</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" onclick="saveStoreSettings()">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                </div>
            </section>
        </main>
    </div>

    <!-- Add Product Modal -->
    <div id="addProductModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة منتج جديد</h3>
                <button class="close-btn" onclick="closeAddProductModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addProductForm">
                    <div class="form-group">
                        <label>اسم المنتج:</label>
                        <input type="text" id="newProductName" required>
                    </div>
                    <div class="form-group">
                        <label>النوع:</label>
                        <select id="newProductType" required>
                            <option value="">اختر النوع</option>
                            <option value="book">كتاب</option>
                            <option value="laptop">لابتوب</option>
                            <option value="smartphone">هاتف ذكي</option>
                            <option value="bag">حقيبة</option>
                            <option value="clothing">ملابس</option>
                            <option value="home">منزلية</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>السعر (DZD):</label>
                        <input type="number" id="newProductPrice" min="0" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>المخزون:</label>
                        <input type="number" id="newProductStock" min="0" required>
                    </div>
                    <div class="form-group">
                        <label>الوصف:</label>
                        <textarea id="newProductDescription" rows="4"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeAddProductModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="addProduct()">إضافة المنتج</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/dashboard.js"></script>
</body>
</html>
