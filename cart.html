<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سلة المشتريات - متجر الكتب</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/cart.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">
                    <a href="index.html"><h1>متجر الكتب</h1></a>
                </div>
                <div class="cart-icon">
                    <a href="cart.html">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count">0</span>
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <main class="cart-page">
        <div class="container">
            <h2>سلة المشتريات</h2>
            
            <div class="cart-container">
                <div class="cart-items" id="cartItems">
                    <!-- Les éléments du panier seront ajoutés ici dynamiquement -->
                </div>

                <div class="cart-summary">
                    <h3>ملخص الطلب</h3>
                    <div class="summary-item">
                        <span>المجموع الفرعي:</span>
                        <span id="subtotal">0 دج</span>
                    </div>
                    <div class="summary-item">
                        <span>رسوم التوصيل:</span>
                        <span>500 دج</span>
                    </div>
                    <div class="summary-item total">
                        <span>المجموع الكلي:</span>
                        <span id="total">0 دج</span>
                    </div>
                    <button id="checkoutButton" class="checkout-button">متابعة الدفع</button>
                </div>
            </div>

            <div class="empty-cart" id="emptyCart" style="display: none;">
                <i class="fas fa-shopping-cart"></i>
                <h3>سلة المشتريات فارغة</h3>
                <p>لم تقم بإضافة أي كتب إلى سلة المشتريات بعد</p>
                <a href="index.html" class="continue-shopping">العودة للتسوق</a>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>جميع الحقوق محفوظة © 2024</p>
        </div>
    </footer>

    <script src="js/cart.js"></script>
</body>
</html>