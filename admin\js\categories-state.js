// Categories state management
let categoriesState = {
    items: [],
    filter: '',
    sortField: 'ordre',
    sortDirection: 'asc',
    lastUpdate: null
};

// Reset categories state
function resetCategories() {
    console.log('🔄 Resetting categories state...');
    categoriesState = {
        items: [],
        filter: '',
        sortField: 'ordre',
        sortDirection: 'asc',
        lastUpdate: null
    };

    // Clear the display
    const container = document.getElementById('categoriesTree');
    if (container) {
        container.innerHTML = '<div class="loading">جاري إعادة تحميل الفئات...</div>';
    }

    // Reset statistics
    updateStatistics();

    // Reload categories
    loadCategories().catch(error => {
        console.error('Error reloading categories:', error);
        showError('فشل في إعادة تحميل الفئات');
    });
}

// Export for global access
window.resetCategories = resetCategories;
