/* Product Landing Page Styles */
.field-group {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.field-group h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2rem;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

/* Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    -webkit-user-select: none;
    user-select: none;
    margin: 10px 0;
}

.checkbox-label input[type="checkbox"] {
    margin-left: 10px;
    width: 18px;
    height: 18px;
}

/* Content Blocks */
.content-block {
    background: #fff;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.block-title {
    flex: 1;
    margin-left: 10px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.remove-block {
    background: #dc3545;
    color: #fff;
    border: none;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.remove-block:hover {
    background: #c82333;
}

.block-content {
    width: 100%;
    min-height: 150px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    resize: vertical;
}

/* Image Gallery */
.file-input {
    margin: 10px 0;
    width: 100%;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.preview-image {
    position: relative;
    width: 100%;
    padding-bottom: 100%;
    background-size: cover;
    background-position: center;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.remove-image {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(220, 53, 69, 0.9);
    color: #fff;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.remove-image:hover {
    background: rgba(200, 35, 51, 1);
}

/* Rich Text Editor (TinyMCE) Customization */
.tox.tox-tinymce {
    direction: rtl;
    border-radius: 4px;
    border-color: #ddd;
}

.tox.tox-tinymce--toolbar-sticky-on .tox-editor-header {
    background-color: #fff;
}

/* URL Copy Button */
.copy-url {
    display: inline-flex;
    align-items: center;
    background: #6c757d;
    color: #fff;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-top: 10px;
    transition: background-color 0.3s ease;
}

.copy-url i {
    margin-left: 8px;
}

.copy-url:hover {
    background: #5a6268;
}

/* Preview Button */
.preview-landing {
    display: inline-flex;
    align-items: center;
    background: #28a745;
    color: #fff;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-top: 10px;
    margin-right: 10px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.preview-landing i {
    margin-left: 8px;
}

.preview-landing:hover {
    background: #218838;
    color: #fff;
    text-decoration: none;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .image-preview {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
    
    .block-header {
        flex-direction: column;
    }
    
    .block-title {
        margin: 0 0 10px 0;
        width: 100%;
    }
    
    .remove-block {
        width: 100%;
    }
}