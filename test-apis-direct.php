<?php
/**
 * Test APIs Directly (without cURL)
 * Direct testing of API endpoints to bypass network issues
 */

require_once 'php/config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار APIs مباشرة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
        .api-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .json-output { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; font-size: 12px; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🧪 اختبار APIs مباشرة</h1>";

try {
    echo "<div class='section'>";
    echo "<h2>1️⃣ اختبار Templates API</h2>";
    
    echo "<div class='api-result'>";
    echo "<h4>Templates API</h4>";
    
    try {
        // Simulate the templates API call
        $_GET['action'] = 'get_templates';
        
        ob_start();
        include 'php/api/templates.php';
        $templates_response = ob_get_clean();
        
        echo "<div class='success'>✅ Templates API يعمل بشكل صحيح</div>";
        
        $templates_data = json_decode($templates_response, true);
        if ($templates_data && isset($templates_data['success'])) {
            echo "<div class='info'>📊 عدد القوالب: " . count($templates_data['templates']) . "</div>";
            
            echo "<h5>القوالب المتاحة:</h5>";
            echo "<ul>";
            foreach ($templates_data['templates'] as $template) {
                echo "<li>{$template['name']} (ID: {$template['id']})</li>";
            }
            echo "</ul>";
        }
        
        echo "<details><summary>عرض الاستجابة الكاملة</summary>";
        echo "<div class='json-output'>" . htmlspecialchars($templates_response) . "</div>";
        echo "</details>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في Templates API: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2️⃣ اختبار SubscriptionLimits API</h2>";
    
    echo "<div class='api-result'>";
    echo "<h4>Subscription Limits API</h4>";
    
    try {
        // Test subscription limits directly
        require_once 'php/SubscriptionLimits.php';
        
        $limitsManager = new SubscriptionLimits();
        
        // Get demo user
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $demoUser = $stmt->fetch();
        
        if ($demoUser) {
            $userId = $demoUser['id'];
            
            // Test all methods
            $usage = $limitsManager->getUserUsage($userId);
            echo "<div class='success'>✅ getUserUsage() يعمل</div>";
            
            $limits = $limitsManager->getUserLimits($userId);
            echo "<div class='success'>✅ getUserLimits() يعمل</div>";
            
            $limitsAndUsage = $limitsManager->getUserLimitsAndUsage($userId);
            echo "<div class='success'>✅ getUserLimitsAndUsage() يعمل</div>";
            
            echo "<div class='info'>";
            echo "<h5>📊 إحصائيات المستخدم التجريبي:</h5>";
            echo "<ul>";
            echo "<li>المنتجات: {$usage['products']}</li>";
            echo "<li>صفحات الهبوط: {$usage['landing_pages']}</li>";
            echo "<li>الفئات: {$usage['categories']}</li>";
            echo "<li>التخزين: {$usage['storage_mb']} ميجابايت</li>";
            echo "</ul>";
            echo "</div>";
            
            // Test validation methods
            $canAddProduct = $limitsManager->canAddProduct($userId);
            echo "<div class='info'>يمكن إضافة منتج: " . ($canAddProduct['allowed'] ? 'نعم' : 'لا') . "</div>";
            
            $canAddLandingPage = $limitsManager->canAddLandingPage($userId);
            echo "<div class='info'>يمكن إضافة صفحة هبوط: " . ($canAddLandingPage['allowed'] ? 'نعم' : 'لا') . "</div>";
            
        } else {
            echo "<div class='error'>❌ المستخدم التجريبي غير موجود</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في Subscription Limits: " . $e->getMessage() . "</div>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3️⃣ اختبار User Authentication</h2>";
    
    echo "<div class='api-result'>";
    echo "<h4>User Authentication</h4>";
    
    try {
        // Test user authentication directly
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute(['<EMAIL>']);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "<div class='success'>✅ المستخدم التجريبي موجود ونشط</div>";
            
            // Test password verification
            $password_valid = password_verify('demo123', $user['password']);
            if ($password_valid) {
                echo "<div class='success'>✅ كلمة المرور صحيحة</div>";
            } else {
                echo "<div class='error'>❌ كلمة المرور غير صحيحة</div>";
            }
            
            echo "<div class='info'>";
            echo "<h5>👤 معلومات المستخدم:</h5>";
            echo "<ul>";
            echo "<li>ID: {$user['id']}</li>";
            echo "<li>الاسم: {$user['first_name']} {$user['last_name']}</li>";
            echo "<li>البريد الإلكتروني: {$user['email']}</li>";
            echo "<li>الدور: {$user['role_id']}</li>";
            echo "<li>الاشتراك: {$user['subscription_id']}</li>";
            echo "<li>Store ID: {$user['store_id']}</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div class='error'>❌ المستخدم التجريبي غير موجود أو غير نشط</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في User Authentication: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4️⃣ اختبار Users Management</h2>";
    
    echo "<div class='api-result'>";
    echo "<h4>Users Management</h4>";
    
    try {
        // Test users query directly
        $stmt = $pdo->prepare("
            SELECT
                u.*,
                ur.display_name_ar as role_name,
                ur.level as role_level,
                sp.display_name_ar as subscription_name,
                sp.max_products,
                sp.max_landing_pages,
                sp.max_storage_mb,
                s.store_name,
                s.store_slug
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.id
            LEFT JOIN subscription_plans sp ON u.subscription_id = sp.id
            LEFT JOIN stores s ON u.store_id = s.id
            ORDER BY u.created_at DESC
        ");
        
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='success'>✅ استعلام المستخدمين يعمل بشكل صحيح</div>";
        echo "<div class='info'>📊 عدد المستخدمين: " . count($users) . "</div>";
        
        if (count($users) > 0) {
            echo "<h5>👥 المستخدمين:</h5>";
            echo "<ul>";
            foreach (array_slice($users, 0, 5) as $user) {
                $name = trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''));
                echo "<li>{$name} ({$user['email']}) - {$user['role_name']} - {$user['subscription_name']}</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في Users Management: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5️⃣ اختبار Landing Pages</h2>";
    
    echo "<div class='api-result'>";
    echo "<h4>Landing Pages</h4>";
    
    try {
        // Test landing pages query
        $stmt = $pdo->prepare("
            SELECT lp.*, p.titre as product_title, p.type as product_type
            FROM landing_pages lp
            LEFT JOIN produits p ON lp.produit_id = p.id
            ORDER BY lp.created_at DESC
            LIMIT 10
        ");
        
        $stmt->execute();
        $landingPages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='success'>✅ استعلام صفحات الهبوط يعمل بشكل صحيح</div>";
        echo "<div class='info'>📊 عدد صفحات الهبوط: " . count($landingPages) . "</div>";
        
        if (count($landingPages) > 0) {
            echo "<h5>🌐 صفحات الهبوط:</h5>";
            echo "<ul>";
            foreach ($landingPages as $page) {
                echo "<li>{$page['titre']} - {$page['product_title']} ({$page['product_type']})</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في Landing Pages: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>✅ ملخص الاختبار المباشر</h2>";
    
    echo "<div class='success'>";
    echo "<h3>🎉 نتائج الاختبار المباشر</h3>";
    echo "<p>جميع الاختبارات المباشرة تشير إلى أن الكود يعمل بشكل صحيح.</p>";
    echo "<p>إذا كانت هناك مشاكل في APIs عبر HTTP، فالمشكلة في:</p>";
    echo "<ul>";
    echo "<li>إعدادات الخادم المحلي</li>";
    echo "<li>صلاحيات الملفات</li>";
    echo "<li>إعدادات PHP</li>";
    echo "<li>مشاكل الشبكة المحلية</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🔧 الخطوات التالية:</h4>";
    echo "<ul>";
    echo "<li>تشغيل سكريبت إصلاح قاعدة البيانات: <a href='/fix-database-schema-issues.php' target='_blank'>fix-database-schema-issues.php</a></li>";
    echo "<li>فحص إعدادات الخادم المحلي</li>";
    echo "<li>التحقق من ملفات السجل</li>";
    echo "<li>اختبار APIs من المتصفح مباشرة</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
