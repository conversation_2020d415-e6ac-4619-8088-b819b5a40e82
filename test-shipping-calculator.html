<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Shipping Calculator</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .shipping-result {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚚 Test Shipping Calculator - Yalidine Express</h1>
        <p>اختبار حاسبة تكلفة الشحن مع قاعدة البيانات</p>

        <div class="test-section">
            <h3>🧪 Database & API Tests</h3>
            <button class="test-button" onclick="testShippingAPI()">
                1. اختبار API الشحن
            </button>
            <button class="test-button" onclick="testWilayasAPI()">
                2. اختبار API الولايات
            </button>
            <button class="test-button" onclick="testZonesAPI()">
                3. اختبار API المناطق
            </button>
            <button class="test-button" onclick="testDatabaseTables()">
                4. اختبار جداول قاعدة البيانات
            </button>
        </div>

        <div class="test-section">
            <h3>📊 Shipping Calculator Test</h3>
            <div class="form-group">
                <label for="testWilayaSelect">اختر الولاية للاختبار:</label>
                <select id="testWilayaSelect">
                    <option value="">-- اختر الولاية --</option>
                    <option value="09">Blida (Zone 1 - 450 DA)</option>
                    <option value="15">Tizi Ouzou (Zone 2 - 550 DA)</option>
                    <option value="25">Constantine (Zone 2 - 750 DA)</option>
                    <option value="17">Djelfa (Zone 3 - 650 DA)</option>
                    <option value="31">Oran (Zone 4 - 750 DA)</option>
                    <option value="03">Laghouat (Zone 5 - 850 DA)</option>
                    <option value="11">Tamanrasset (Zone 5 - 1800 DA)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="testWeight">وزن الطرد (كيلوغرام):</label>
                <input type="number" id="testWeight" min="0.1" max="30" step="0.1" value="1.0">
            </div>
            <button class="test-button" onclick="testShippingCalculation()">
                احسب تكلفة الشحن
            </button>
            
            <div id="calculationResult" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Weight-based Testing</h3>
            <button class="test-button" onclick="testWeightScenarios()">
                اختبار سيناريوهات الوزن
            </button>
            <button class="test-button" onclick="testAllZones()">
                اختبار جميع المناطق
            </button>
        </div>

        <div class="test-section">
            <h3>🚀 Comprehensive Tests</h3>
            <button class="test-button" onclick="runAllShippingTests()">
                تشغيل جميع اختبارات الشحن
            </button>
            <button class="test-button" onclick="clearResults()">
                مسح النتائج
            </button>
        </div>

        <div id="test-results"></div>
        <div id="console-output"></div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function logToConsole(message) {
            const output = document.getElementById('console-output');
            output.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        // API TESTS
        async function testShippingAPI() {
            addTestResult('🧪 اختبار API الشحن...', 'info');
            logToConsole('Testing Shipping API...');
            
            try {
                const response = await fetch('php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=09&weight=1.0');
                logToConsole(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                logToConsole(`API Response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success && data.zone_info && data.cost_breakdown) {
                    addTestResult('✅ Shipping API يعمل بشكل صحيح', 'success');
                    logToConsole(`Zone: ${data.zone_info.zone_name}, Cost: ${data.cost_breakdown.total_cost} DA`);
                } else {
                    addTestResult('❌ Shipping API فشل: ' + (data.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في Shipping API: ' + error.message, 'error');
                logToConsole(`Shipping API error: ${error.message}`);
            }
        }

        async function testWilayasAPI() {
            addTestResult('🧪 اختبار API الولايات...', 'info');
            logToConsole('Testing Wilayas API...');
            
            try {
                const response = await fetch('php/api/payment-settings.php?module=shipping&action=wilayas');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                logToConsole(`Wilayas API Response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success && data.wilayas && data.wilayas.length > 0) {
                    addTestResult(`✅ تم تحميل ${data.wilayas.length} ولاية بنجاح`, 'success');
                    logToConsole(`Found wilayas: ${data.wilayas.map(w => w.wilaya_name).join(', ')}`);
                } else {
                    addTestResult('❌ فشل في تحميل الولايات', 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في Wilayas API: ' + error.message, 'error');
                logToConsole(`Wilayas API error: ${error.message}`);
            }
        }

        async function testZonesAPI() {
            addTestResult('🧪 اختبار API المناطق...', 'info');
            logToConsole('Testing Zones API...');
            
            try {
                const response = await fetch('php/api/payment-settings.php?module=shipping&action=zones');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                logToConsole(`Zones API Response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success && data.zones && data.zones.length > 0) {
                    addTestResult(`✅ تم تحميل ${data.zones.length} منطقة شحن`, 'success');
                    data.zones.forEach(zone => {
                        logToConsole(`${zone.zone_name}: ${zone.wilaya_count} wilayas, ${zone.min_cost}-${zone.max_cost} DA`);
                    });
                } else {
                    addTestResult('❌ فشل في تحميل المناطق', 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في Zones API: ' + error.message, 'error');
                logToConsole(`Zones API error: ${error.message}`);
            }
        }

        async function testDatabaseTables() {
            addTestResult('🧪 اختبار جداول قاعدة البيانات...', 'info');
            logToConsole('Testing database tables...');
            
            // Test if tables are created by checking API responses
            try {
                const [wilayasResponse, zonesResponse] = await Promise.all([
                    fetch('php/api/payment-settings.php?module=shipping&action=wilayas'),
                    fetch('php/api/payment-settings.php?module=shipping&action=zones')
                ]);
                
                const wilayasData = await wilayasResponse.json();
                const zonesData = await zonesResponse.json();
                
                if (wilayasData.success && zonesData.success) {
                    addTestResult('✅ جداول قاعدة البيانات تعمل بشكل صحيح', 'success');
                    logToConsole('Database tables: shipping_zones ✓, shipping_settings ✓');
                } else {
                    addTestResult('❌ مشكلة في جداول قاعدة البيانات', 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في الاتصال بقاعدة البيانات: ' + error.message, 'error');
                logToConsole(`Database error: ${error.message}`);
            }
        }

        async function testShippingCalculation() {
            const wilayaCode = document.getElementById('testWilayaSelect').value;
            const weight = parseFloat(document.getElementById('testWeight').value) || 1.0;
            
            if (!wilayaCode) {
                addTestResult('⚠️ يرجى اختيار الولاية أولاً', 'warning');
                return;
            }
            
            addTestResult(`🧪 حساب الشحن للولاية ${wilayaCode} بوزن ${weight} كغ...`, 'info');
            logToConsole(`Calculating shipping for wilaya: ${wilayaCode}, weight: ${weight}kg`);
            
            try {
                const response = await fetch(`php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilayaCode}&weight=${weight}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                logToConsole(`Calculation result: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    const resultDiv = document.getElementById('calculationResult');
                    resultDiv.innerHTML = `
                        <div class="shipping-result">
                            <h4>نتيجة الحساب:</h4>
                            <p><strong>المنطقة:</strong> ${data.zone_info.zone_name}</p>
                            <p><strong>الولاية:</strong> ${data.zone_info.wilaya_name}</p>
                            <p><strong>مدة التوصيل:</strong> ${data.zone_info.delivery_time}</p>
                            <p><strong>التكلفة الأساسية:</strong> ${data.cost_breakdown.base_cost} دج</p>
                            <p><strong>الوزن:</strong> ${data.cost_breakdown.weight} كغ</p>
                            ${data.cost_breakdown.extra_weight > 0 ? 
                                `<p><strong>رسوم إضافية:</strong> ${data.cost_breakdown.surcharge_total} دج</p>` : ''}
                            <p><strong style="color: #28a745; font-size: 1.2em;">إجمالي التكلفة: ${data.cost_breakdown.total_cost} دج</strong></p>
                        </div>
                    `;
                    resultDiv.style.display = 'block';
                    
                    addTestResult(`✅ تم حساب التكلفة: ${data.cost_breakdown.total_cost} دج`, 'success');
                } else {
                    addTestResult('❌ فشل في حساب التكلفة: ' + data.message, 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في حساب التكلفة: ' + error.message, 'error');
                logToConsole(`Calculation error: ${error.message}`);
            }
        }

        async function testWeightScenarios() {
            addTestResult('🧪 اختبار سيناريوهات الوزن...', 'info');
            logToConsole('Testing weight scenarios...');
            
            const weights = [0.5, 1.0, 3.0, 5.0, 7.5, 10.0, 15.0];
            const wilayaCode = '09'; // Blida
            
            for (const weight of weights) {
                try {
                    const response = await fetch(`php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilayaCode}&weight=${weight}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        logToConsole(`Weight ${weight}kg: ${data.cost_breakdown.total_cost} DA (base: ${data.cost_breakdown.base_cost}, surcharge: ${data.cost_breakdown.surcharge_total})`);
                    }
                } catch (error) {
                    logToConsole(`Error testing weight ${weight}kg: ${error.message}`);
                }
                
                await new Promise(resolve => setTimeout(resolve, 200)); // Small delay
            }
            
            addTestResult('✅ اكتمل اختبار سيناريوهات الوزن', 'success');
        }

        async function testAllZones() {
            addTestResult('🧪 اختبار جميع المناطق...', 'info');
            logToConsole('Testing all zones...');
            
            const testWilayas = [
                { code: '09', name: 'Blida', zone: 'Zone 1' },
                { code: '15', name: 'Tizi Ouzou', zone: 'Zone 2' },
                { code: '17', name: 'Djelfa', zone: 'Zone 3' },
                { code: '31', name: 'Oran', zone: 'Zone 4' },
                { code: '11', name: 'Tamanrasset', zone: 'Zone 5' }
            ];
            
            for (const wilaya of testWilayas) {
                try {
                    const response = await fetch(`php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilaya.code}&weight=1.0`);
                    const data = await response.json();
                    
                    if (data.success) {
                        logToConsole(`${wilaya.name} (${wilaya.zone}): ${data.cost_breakdown.total_cost} DA, delivery: ${data.zone_info.delivery_time}`);
                    }
                } catch (error) {
                    logToConsole(`Error testing ${wilaya.name}: ${error.message}`);
                }
                
                await new Promise(resolve => setTimeout(resolve, 300)); // Small delay
            }
            
            addTestResult('✅ اكتمل اختبار جميع المناطق', 'success');
        }

        async function runAllShippingTests() {
            clearResults();
            addTestResult('🚀 بدء تشغيل جميع اختبارات الشحن...', 'info');
            logToConsole('Starting comprehensive shipping tests...');
            
            await testDatabaseTables();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testWilayasAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testZonesAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testShippingAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testWeightScenarios();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testAllZones();
            
            addTestResult('🎉 انتهاء جميع اختبارات الشحن', 'info');
            logToConsole('All shipping tests completed');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('console-output').textContent = '';
            document.getElementById('calculationResult').style.display = 'none';
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('📋 تشغيل اختبار أساسي تلقائياً...', 'info');
                testShippingAPI();
            }, 1000);
        });
    </script>
</body>
</html>
