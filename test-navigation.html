<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navigation</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        .test-item.success {
            border-left-color: #27ae60;
            background: #d5f4e6;
        }
        .test-item.error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .test-result {
            font-size: 0.9em;
            color: #666;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test de navigation de l'admin</h1>
        
        <div id="test-results"></div>
        
        <button onclick="testNavigation()">Tester la navigation</button>
        <button onclick="testAdminPage()">Tester la page admin</button>
    </div>

    <script>
        const testResults = document.getElementById('test-results');
        
        function addTestResult(title, result, success = true) {
            const testItem = document.createElement('div');
            testItem.className = `test-item ${success ? 'success' : 'error'}`;
            testItem.innerHTML = `
                <div class="test-title">${title}</div>
                <div class="test-result">${result}</div>
            `;
            testResults.appendChild(testItem);
        }
        
        function testNavigation() {
            testResults.innerHTML = '';
            
            // Test if we can access the admin page
            fetch('/admin/index.html')
                .then(response => {
                    if (response.ok) {
                        addTestResult('Admin Page Access', 'Page admin accessible', true);
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(html => {
                    // Check if navigation elements exist
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    
                    const navItems = doc.querySelectorAll('.admin-nav li[data-section]');
                    if (navItems.length > 0) {
                        addTestResult('Navigation Elements', `${navItems.length} éléments de navigation trouvés`, true);
                        
                        navItems.forEach(item => {
                            const section = item.getAttribute('data-section');
                            const sectionElement = doc.getElementById(section);
                            if (sectionElement) {
                                addTestResult(`Section ${section}`, 'Section correspondante trouvée', true);
                            } else {
                                addTestResult(`Section ${section}`, 'Section correspondante manquante', false);
                            }
                        });
                    } else {
                        addTestResult('Navigation Elements', 'Aucun élément de navigation trouvé', false);
                    }
                    
                    // Check for JavaScript files
                    const scripts = doc.querySelectorAll('script[src]');
                    const adminJs = Array.from(scripts).find(script => script.src.includes('admin.js'));
                    if (adminJs) {
                        addTestResult('Admin JS', 'Fichier admin.js trouvé', true);
                    } else {
                        addTestResult('Admin JS', 'Fichier admin.js manquant', false);
                    }
                    
                    const tinymceConfig = Array.from(scripts).find(script => script.src.includes('tinymce-config.js'));
                    if (tinymceConfig) {
                        addTestResult('TinyMCE Config', 'Fichier tinymce-config.js trouvé', true);
                    } else {
                        addTestResult('TinyMCE Config', 'Fichier tinymce-config.js manquant', false);
                    }
                })
                .catch(error => {
                    addTestResult('Admin Page Access', `Erreur: ${error.message}`, false);
                });
        }
        
        function testAdminPage() {
            // Test admin.js functionality
            fetch('/admin/js/admin.js')
                .then(response => {
                    if (response.ok) {
                        addTestResult('Admin JS File', 'Fichier admin.js accessible', true);
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(jsContent => {
                    // Check for key functions
                    const functions = [
                        'initNavigation',
                        'initFormHandlers',
                        'initModalHandlers',
                        'initSettingsHandlers',
                        'loadDashboard',
                        'loadBooks',
                        'loadOrders',
                        'loadSettings'
                    ];
                    
                    functions.forEach(func => {
                        if (jsContent.includes(`function ${func}`)) {
                            addTestResult(`Function ${func}`, 'Fonction trouvée', true);
                        } else {
                            addTestResult(`Function ${func}`, 'Fonction manquante', false);
                        }
                    });
                    
                    // Check for DOMContentLoaded
                    if (jsContent.includes('DOMContentLoaded')) {
                        addTestResult('Initialization', 'Initialisation DOMContentLoaded trouvée', true);
                    } else {
                        addTestResult('Initialization', 'Initialisation DOMContentLoaded manquante', false);
                    }
                })
                .catch(error => {
                    addTestResult('Admin JS File', `Erreur: ${error.message}`, false);
                });
        }
        
        // Run initial test
        document.addEventListener('DOMContentLoaded', () => {
            addTestResult('Test Page Load', 'Page de test chargée avec succès', true);
        });
    </script>
</body>
</html>
