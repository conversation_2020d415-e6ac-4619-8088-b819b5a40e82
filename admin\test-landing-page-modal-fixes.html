<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات مودال صفحات الهبوط</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #5a67d8;
        }
        .success {
            color: #10b981;
            background: #d1fae5;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .error {
            color: #ef4444;
            background: #fee2e2;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .info {
            color: #3b82f6;
            background: #dbeafe;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .log {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار إصلاحات مودال صفحات الهبوط</h1>
        <p>اختبار الإصلاحات المطبقة على مشاكل مودال إضافة وتعديل صفحات الهبوط</p>

        <div class="test-section">
            <h2>📋 ملخص الإصلاحات المطبقة</h2>
            <div class="info">
                <h3>✅ الإصلاحات المطبقة:</h3>
                <ul>
                    <li><strong>إصلاح مودال التعديل الفارغ:</strong> تم تصحيح معرف العنصر من 'contentCreationStep' إلى 'contentStep'</li>
                    <li><strong>إضافة زر تخطي القالب:</strong> يمكن للمستخدمين الآن تخطي اختيار القالب والانتقال مباشرة للنموذج</li>
                    <li><strong>تحسين التنقل بين الخطوات:</strong> تم إضافة دالة goToContentStep() موحدة</li>
                    <li><strong>إضافة التحقق من العناصر:</strong> دالة validateModalElements() للتأكد من وجود جميع العناصر</li>
                    <li><strong>تحسين رسائل التصحيح:</strong> إضافة المزيد من رسائل console.log للمساعدة في التشخيص</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 اختبارات التحقق</h2>
            <button class="test-button" onclick="loadAdminPanel()">1. تحميل لوحة التحكم</button>
            <button class="test-button" onclick="testAddModal()">2. اختبار مودال الإضافة</button>
            <button class="test-button" onclick="testEditModal()">3. اختبار مودال التعديل</button>
            <button class="test-button" onclick="testSkipTemplate()">4. اختبار تخطي القالب</button>
            <button class="test-button" onclick="testElementValidation()">5. اختبار التحقق من العناصر</button>
            
            <div id="testResults" class="log"></div>
        </div>

        <div class="test-section">
            <h2>🖥️ لوحة التحكم</h2>
            <iframe id="adminFrame" src="index.html" style="display: none;"></iframe>
            <div id="loadingMessage" class="info">انقر على "تحميل لوحة التحكم" لبدء الاختبار</div>
        </div>

        <div class="test-section">
            <h2>📝 تعليمات الاختبار اليدوي</h2>
            <div class="info">
                <h3>لاختبار مودال الإضافة:</h3>
                <ol>
                    <li>انتقل إلى قسم "صفحات الهبوط" في لوحة التحكم</li>
                    <li>انقر على زر "إضافة صفحة هبوط جديدة"</li>
                    <li>يجب أن ترى خطوة اختيار القالب أولاً</li>
                    <li>يمكنك اختيار قالب والنقر على "التالي" أو النقر على "تخطي القالب"</li>
                    <li>في الخطوة الثانية، يجب أن ترى حقل "عنوان الصفحة" وجميع الحقول الأخرى</li>
                </ol>

                <h3>لاختبار مودال التعديل:</h3>
                <ol>
                    <li>في قائمة صفحات الهبوط، انقر على زر "تعديل" لأي صفحة</li>
                    <li>يجب أن يفتح المودال مباشرة في خطوة المحتوى (تخطي اختيار القالب)</li>
                    <li>يجب أن ترى جميع الحقول مملوءة بالبيانات الحالية</li>
                    <li>يجب أن يكون عنوان المودال "تعديل صفحة الهبوط"</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        let testLog = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logMessage = `[${timestamp}] ${message}`;
            testLog.push(logMessage);
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = testLog.join('\n');
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            console.log(logMessage);
        }

        function loadAdminPanel() {
            log('🔄 تحميل لوحة التحكم...');
            
            const iframe = document.getElementById('adminFrame');
            const loadingMessage = document.getElementById('loadingMessage');
            
            iframe.style.display = 'block';
            loadingMessage.style.display = 'none';
            
            iframe.onload = function() {
                log('✅ تم تحميل لوحة التحكم بنجاح');
                
                // Wait a bit for scripts to load
                setTimeout(() => {
                    log('🔍 فحص تحميل السكريبتات...');
                    
                    try {
                        const adminDoc = iframe.contentDocument;
                        const landingPagesManager = iframe.contentWindow.landingPagesManager;
                        
                        if (landingPagesManager) {
                            log('✅ تم العثور على landingPagesManager');
                            log(`📊 حالة التهيئة: ${landingPagesManager.initialized ? 'مهيأ' : 'غير مهيأ'}`);
                        } else {
                            log('❌ لم يتم العثور على landingPagesManager');
                        }
                        
                        const modal = adminDoc.getElementById('landingPageModal');
                        if (modal) {
                            log('✅ تم العثور على مودال صفحات الهبوط');
                        } else {
                            log('❌ لم يتم العثور على مودال صفحات الهبوط');
                        }
                        
                    } catch (error) {
                        log('❌ خطأ في فحص السكريبتات: ' + error.message);
                    }
                }, 2000);
            };
            
            iframe.onerror = function() {
                log('❌ فشل في تحميل لوحة التحكم');
            };
        }

        function testAddModal() {
            log('🧪 اختبار مودال الإضافة...');
            
            const iframe = document.getElementById('adminFrame');
            if (!iframe.contentDocument) {
                log('❌ يجب تحميل لوحة التحكم أولاً');
                return;
            }
            
            try {
                const landingPagesManager = iframe.contentWindow.landingPagesManager;
                if (landingPagesManager) {
                    log('🚀 فتح مودال الإضافة...');
                    landingPagesManager.openModal();
                    
                    setTimeout(() => {
                        const modal = iframe.contentDocument.getElementById('landingPageModal');
                        if (modal && modal.style.display !== 'none') {
                            log('✅ تم فتح مودال الإضافة بنجاح');
                            
                            // Check if template step is visible
                            const templateStep = iframe.contentDocument.getElementById('templateSelectionStep');
                            if (templateStep && templateStep.style.display !== 'none') {
                                log('✅ خطوة اختيار القالب مرئية');
                            } else {
                                log('❌ خطوة اختيار القالب غير مرئية');
                            }
                        } else {
                            log('❌ فشل في فتح مودال الإضافة');
                        }
                    }, 500);
                } else {
                    log('❌ landingPagesManager غير متاح');
                }
            } catch (error) {
                log('❌ خطأ في اختبار مودال الإضافة: ' + error.message);
            }
        }

        function testEditModal() {
            log('🧪 اختبار مودال التعديل...');
            
            const iframe = document.getElementById('adminFrame');
            if (!iframe.contentDocument) {
                log('❌ يجب تحميل لوحة التحكم أولاً');
                return;
            }
            
            try {
                const landingPagesManager = iframe.contentWindow.landingPagesManager;
                if (landingPagesManager) {
                    log('🚀 فتح مودال التعديل (محاكاة)...');
                    
                    // Simulate edit with sample data
                    const sampleData = {
                        id: 1,
                        titre: 'صفحة هبوط تجريبية',
                        produit_id: 1,
                        contenu_droit: 'محتوى تجريبي',
                        contenu_gauche: 'محتوى تجريبي آخر'
                    };
                    
                    landingPagesManager.editPage(sampleData);
                    
                    setTimeout(() => {
                        const modal = iframe.contentDocument.getElementById('landingPageModal');
                        if (modal && modal.style.display !== 'none') {
                            log('✅ تم فتح مودال التعديل بنجاح');
                            
                            // Check if content step is visible
                            const contentStep = iframe.contentDocument.getElementById('contentStep');
                            if (contentStep && contentStep.style.display !== 'none') {
                                log('✅ خطوة المحتوى مرئية في مودال التعديل');
                            } else {
                                log('❌ خطوة المحتوى غير مرئية في مودال التعديل');
                            }
                            
                            // Check if title field is filled
                            const titleInput = iframe.contentDocument.getElementById('landingPageTitle');
                            if (titleInput && titleInput.value) {
                                log('✅ حقل العنوان مملوء في مودال التعديل');
                            } else {
                                log('❌ حقل العنوان فارغ في مودال التعديل');
                            }
                        } else {
                            log('❌ فشل في فتح مودال التعديل');
                        }
                    }, 1000);
                } else {
                    log('❌ landingPagesManager غير متاح');
                }
            } catch (error) {
                log('❌ خطأ في اختبار مودال التعديل: ' + error.message);
            }
        }

        function testSkipTemplate() {
            log('🧪 اختبار تخطي القالب...');
            
            const iframe = document.getElementById('adminFrame');
            if (!iframe.contentDocument) {
                log('❌ يجب تحميل لوحة التحكم أولاً');
                return;
            }
            
            try {
                const landingPagesManager = iframe.contentWindow.landingPagesManager;
                if (landingPagesManager) {
                    log('🚀 فتح مودال الإضافة...');
                    landingPagesManager.openModal();
                    
                    setTimeout(() => {
                        log('⏭️ تخطي اختيار القالب...');
                        landingPagesManager.skipTemplateSelection();
                        
                        setTimeout(() => {
                            const contentStep = iframe.contentDocument.getElementById('contentStep');
                            if (contentStep && contentStep.style.display !== 'none') {
                                log('✅ تم تخطي القالب والانتقال لخطوة المحتوى');
                                
                                const titleInput = iframe.contentDocument.getElementById('landingPageTitle');
                                if (titleInput) {
                                    log('✅ حقل العنوان متاح بعد تخطي القالب');
                                } else {
                                    log('❌ حقل العنوان غير متاح بعد تخطي القالب');
                                }
                            } else {
                                log('❌ فشل في الانتقال لخطوة المحتوى بعد تخطي القالب');
                            }
                        }, 500);
                    }, 500);
                } else {
                    log('❌ landingPagesManager غير متاح');
                }
            } catch (error) {
                log('❌ خطأ في اختبار تخطي القالب: ' + error.message);
            }
        }

        function testElementValidation() {
            log('🧪 اختبار التحقق من العناصر...');
            
            const iframe = document.getElementById('adminFrame');
            if (!iframe.contentDocument) {
                log('❌ يجب تحميل لوحة التحكم أولاً');
                return;
            }
            
            try {
                const landingPagesManager = iframe.contentWindow.landingPagesManager;
                if (landingPagesManager && landingPagesManager.validateModalElements) {
                    log('🔍 تشغيل التحقق من العناصر...');
                    const isValid = landingPagesManager.validateModalElements();
                    
                    if (isValid) {
                        log('✅ جميع عناصر المودال موجودة');
                    } else {
                        log('❌ بعض عناصر المودال مفقودة (راجع console للتفاصيل)');
                    }
                } else {
                    log('❌ دالة validateModalElements غير متاحة');
                }
            } catch (error) {
                log('❌ خطأ في اختبار التحقق من العناصر: ' + error.message);
            }
        }

        // Initialize
        log('🚀 مرحباً بك في اختبار إصلاحات مودال صفحات الهبوط');
        log('📋 انقر على "تحميل لوحة التحكم" لبدء الاختبارات');
    </script>
</body>
</html>
