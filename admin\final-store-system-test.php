<?php

/**
 * Final Store System Test
 * Comprehensive test of all store management fixes
 */

require_once __DIR__ . '/../php/config.php';

echo "🎯 Final Store System Test\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    $pdo = getPDOConnection();

    // Test 1: Database Schema Verification
    echo "📋 Test 1: Database Schema Verification\n";
    echo "-" . str_repeat("-", 40) . "\n";

    // Check users table
    $stmt = $pdo->query("DESCRIBE users");
    $userColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    $requiredUserColumns = ['first_name', 'last_name', 'email', 'phone'];
    $missingUserColumns = array_diff($requiredUserColumns, $userColumns);

    if (empty($missingUserColumns)) {
        echo "✅ Users table schema: CORRECT\n";
    } else {
        echo "❌ Users table missing columns: " . implode(', ', $missingUserColumns) . "\n";
    }

    // Check stores table
    $stmt = $pdo->query("DESCRIBE stores");
    $storeColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    $requiredStoreColumns = ['id', 'user_id', 'store_name', 'store_slug', 'status'];
    $missingStoreColumns = array_diff($requiredStoreColumns, $storeColumns);

    if (empty($missingStoreColumns)) {
        echo "✅ Stores table schema: CORRECT\n";
    } else {
        echo "❌ Stores table missing columns: " . implode(', ', $missingStoreColumns) . "\n";
    }
    echo "\n";

    // Test 2: Demo Store Configuration
    echo "📋 Test 2: Demo Store Configuration\n";
    echo "-" . str_repeat("-", 40) . "\n";

    $stmt = $pdo->query("SELECT store_name, store_slug, status FROM stores WHERE store_slug = 'mossaab-store'");
    $demoStore = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($demoStore) {
        echo "✅ Demo store found:\n";
        echo "   Name: {$demoStore['store_name']}\n";
        echo "   Slug: {$demoStore['store_slug']}\n";
        echo "   Status: {$demoStore['status']}\n";

        if ($demoStore['store_slug'] === 'mossaab-store' && $demoStore['store_name'] === 'متجر مصعب') {
            echo "✅ Demo store configuration: CORRECT\n";
        } else {
            echo "⚠️ Demo store configuration needs adjustment\n";
        }
    } else {
        echo "❌ Demo store not found\n";
    }
    echo "\n";

    // Test 3: API Response Test
    echo "📋 Test 3: Stores API Response\n";
    echo "-" . str_repeat("-", 40) . "\n";

    $sql = "
        SELECT
            s.*,
            CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as owner_name,
            u.email as owner_email,
            u.phone as owner_phone,
            u.created_at as user_created_at
        FROM stores s
        LEFT JOIN users u ON s.user_id = u.id
        ORDER BY s.created_at DESC
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $response = [
        'success' => true,
        'message' => 'تم تحميل المتاجر بنجاح',
        'total' => count($stores),
        'stores' => $stores
    ];

    $jsonResponse = json_encode($response, JSON_UNESCAPED_UNICODE);

    if ($jsonResponse !== false) {
        echo "✅ API query execution: SUCCESS\n";
        echo "✅ JSON encoding: SUCCESS\n";
        echo "📊 Total stores: " . count($stores) . "\n";
        echo "📊 Response size: " . strlen($jsonResponse) . " bytes\n";
    } else {
        echo "❌ JSON encoding: FAILED\n";
    }
    echo "\n";

    // Test 4: HTTP API Test
    echo "📋 Test 4: HTTP API Test\n";
    echo "-" . str_repeat("-", 40) . "\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/stores.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        echo "❌ cURL error: {$error}\n";
    } else {
        echo "📊 HTTP Response Code: {$httpCode}\n";

        if ($httpCode === 200) {
            $data = json_decode($response, true);
            if ($data && isset($data['success']) && $data['success']) {
                echo "✅ HTTP API: SUCCESS\n";
                echo "📊 API Success: true\n";
                echo "📊 API Total: {$data['total']}\n";
                echo "📊 API Message: {$data['message']}\n";
            } else {
                echo "❌ HTTP API: Invalid response\n";
            }
        } else {
            echo "❌ HTTP API: Error {$httpCode}\n";
        }
    }
    echo "\n";

    // Test 5: File Existence Check
    echo "📋 Test 5: Required Files Check\n";
    echo "-" . str_repeat("-", 40) . "\n";

    $requiredFiles = [
        'admin/stores-management.html' => 'Store Management HTML',
        'admin/js/stores-management.js' => 'Store Management JavaScript',
        'store.php' => 'Individual Store Page',
        '.htaccess' => 'URL Routing Configuration'
    ];

    foreach ($requiredFiles as $file => $description) {
        if (file_exists($file)) {
            echo "✅ {$description}: EXISTS\n";
        } else {
            echo "❌ {$description}: MISSING\n";
        }
    }
    echo "\n";

    // Test 6: Store Page Test
    echo "📋 Test 6: Individual Store Page Test\n";
    echo "-" . str_repeat("-", 40) . "\n";

    if ($demoStore) {
        $storeUrl = "http://localhost:8000/store/{$demoStore['store_slug']}";
        echo "🔗 Demo store URL: {$storeUrl}\n";

        // Test store page accessibility
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $storeUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        $storeResponse = curl_exec($ch);
        $storeHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $storeError = curl_error($ch);
        curl_close($ch);

        if ($storeError) {
            echo "❌ Store page cURL error: {$storeError}\n";
        } else {
            echo "📊 Store page HTTP code: {$storeHttpCode}\n";

            if ($storeHttpCode === 200) {
                echo "✅ Store page: ACCESSIBLE\n";

                // Check if page contains store name
                if (strpos($storeResponse, $demoStore['store_name']) !== false) {
                    echo "✅ Store page content: CORRECT\n";
                } else {
                    echo "⚠️ Store page content: May need verification\n";
                }
            } else {
                echo "❌ Store page: Error {$storeHttpCode}\n";
            }
        }
    } else {
        echo "⚠️ Cannot test store page - no demo store found\n";
    }
    echo "\n";

    // Test 7: Product Association Test
    echo "📋 Test 7: Product Association Test\n";
    echo "-" . str_repeat("-", 40) . "\n";

    if ($demoStore) {
        // Get store owner's user ID
        $stmt = $pdo->prepare("SELECT user_id FROM stores WHERE store_slug = ?");
        $stmt->execute([$demoStore['store_slug']]);
        $storeUserId = $stmt->fetchColumn();

        if ($storeUserId) {
            // Check products for this store (products with matching store_id or NULL for demo)
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM produits
                WHERE (store_id = ? OR store_id IS NULL) AND actif = 1
            ");
            $stmt->execute([$demoStore['id']]);
            $productCount = $stmt->fetchColumn();

            echo "📊 Store owner user ID: {$storeUserId}\n";
            echo "📊 Products for this store: {$productCount}\n";

            if ($productCount > 0) {
                echo "✅ Product association: WORKING\n";
            } else {
                echo "⚠️ No products found for this store\n";
            }
        } else {
            echo "❌ Store user ID not found\n";
        }
    }
    echo "\n";

    // Final Summary
    echo "📋 FINAL SUMMARY\n";
    echo "=" . str_repeat("=", 50) . "\n";

    $allTestsPassed = (
        empty($missingUserColumns) &&
        empty($missingStoreColumns) &&
        $demoStore &&
        $httpCode === 200 &&
        file_exists('admin/stores-management.html') &&
        file_exists('admin/js/stores-management.js') &&
        file_exists('store.php')
    );

    if ($allTestsPassed) {
        echo "🎉 ALL TESTS PASSED!\n\n";
        echo "✅ Database schema: Fixed\n";
        echo "✅ Demo store: Configured (mossaab-store)\n";
        echo "✅ API: Working (HTTP 200)\n";
        echo "✅ Files: All present\n";
        echo "✅ Store page: Accessible\n\n";

        echo "🔗 NEXT STEPS:\n";
        echo "1. Visit: http://localhost:8000/admin/\n";
        echo "2. Click: إدارة المتاجر\n";
        echo "3. Verify: No stuck loading message\n";
        echo "4. Test: http://localhost:8000/store/mossaab-store\n\n";

        echo "🎯 EXPECTED RESULTS:\n";
        echo "- Store management loads within 3 seconds\n";
        echo "- Demo store displays correctly\n";
        echo "- Individual store page shows products\n";
        echo "- Clear separation between platform and store pages\n";
    } else {
        echo "⚠️ SOME ISSUES DETECTED\n\n";
        echo "Please review the test results above and address any failed tests.\n";
    }
} catch (Exception $e) {
    echo "❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
}
