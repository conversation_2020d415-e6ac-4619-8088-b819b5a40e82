<?php
/**
 * Test Schema Fix for MariaDB Database
 * Quick test to verify the column mapping works correctly
 */

require_once 'php/config.php';

echo "<h1>🧪 Schema Fix Test - MariaDB Port 3307</h1>\n";
echo "<style>
body{font-family:Arial;margin:20px;background:#f8f9fa;} 
.container{max-width:1000px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 4px 6px rgba(0,0,0,0.1);}
.success{color:#28a745;font-weight:bold;} 
.error{color:#dc3545;font-weight:bold;} 
.warning{color:#ffc107;font-weight:bold;} 
.info{color:#17a2b8;font-weight:bold;} 
pre{background:#f8f9fa;padding:15px;border-radius:5px;border-left:4px solid #007bff;} 
table{border-collapse:collapse;width:100%;margin:15px 0;} 
th,td{border:1px solid #dee2e6;padding:12px;text-align:left;} 
th{background:#e9ecef;font-weight:bold;}
.section{background:#f0f8ff;padding:25px;margin:20px 0;border-radius:12px;border-left:5px solid #007bff;}
.highlight{background:#fff3cd;padding:15px;border-radius:8px;margin:15px 0;}
</style>\n";

echo "<div class='container'>\n";

try {
    $pdo = getPDOConnection();
    echo "<p class='success'>✅ Connected to MariaDB successfully</p>\n";
    
    // Test 1: Analyze produits table structure
    echo "<div class='section'>\n";
    echo "<h2>🔍 Test 1: Produits Table Analysis</h2>\n";
    
    $stmt = $pdo->query("DESCRIBE produits");
    $actualColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $availableColumns = array_column($actualColumns, 'Field');
    echo "<p class='info'>Available columns: " . implode(', ', $availableColumns) . "</p>\n";
    
    // Create column mapping
    $columnMapping = [];
    $expectedColumns = ['id', 'category_id', 'type', 'titre', 'description', 'prix', 'stock', 'auteur', 'materiel', 'actif'];
    
    foreach ($expectedColumns as $expected) {
        if (in_array($expected, $availableColumns)) {
            $columnMapping[$expected] = $expected;
        } else {
            // Try to find alternative column names
            foreach ($availableColumns as $actual) {
                if (stripos($actual, str_replace('_', '', $expected)) !== false || 
                    stripos($expected, str_replace('_', '', $actual)) !== false) {
                    $columnMapping[$expected] = $actual;
                    break;
                }
            }
        }
    }
    
    echo "<h3>Column Mapping Results:</h3>\n";
    echo "<table>\n";
    echo "<tr><th>Expected</th><th>Mapped To</th><th>Status</th></tr>\n";
    foreach ($expectedColumns as $expected) {
        $mapped = $columnMapping[$expected] ?? 'NOT FOUND';
        $status = isset($columnMapping[$expected]) ? '✅ OK' : '❌ MISSING';
        echo "<tr><td>$expected</td><td>$mapped</td><td>$status</td></tr>\n";
    }
    echo "</table>\n";
    echo "</div>\n";
    
    // Test 2: Test Product Creation Logic
    echo "<div class='section'>\n";
    echo "<h2>🧪 Test 2: Product Creation Logic Test</h2>\n";
    
    $testProduct = [
        'type' => 'test',
        'titre' => 'Test Product Schema Fix - ' . date('Y-m-d H:i:s'),
        'description' => 'Test product to verify schema fix works',
        'prix' => 999.99,
        'stock' => 5,
        'auteur' => 'Test Author',
        'materiel' => 'Test Material',
        'actif' => 1
    ];
    
    // Get a category ID for testing
    $catStmt = $pdo->query("SELECT id FROM categories WHERE actif = 1 LIMIT 1");
    $categoryId = $catStmt->fetch()['id'] ?? null;
    
    if ($categoryId) {
        $testProduct['category_id'] = $categoryId;
        echo "<p class='info'>Using category ID: $categoryId</p>\n";
    } else {
        echo "<p class='warning'>No active category found, skipping category_id</p>\n";
    }
    
    // Build dynamic SQL
    $insertColumns = [];
    $insertPlaceholders = [];
    $insertValues = [];
    
    foreach ($testProduct as $key => $value) {
        if (isset($columnMapping[$key]) && $value !== null) {
            $actualColumn = $columnMapping[$key];
            $insertColumns[] = $actualColumn;
            $insertPlaceholders[] = '?';
            $insertValues[] = $value;
        }
    }
    
    if (!empty($insertColumns)) {
        $sql = "INSERT INTO produits (" . implode(', ', $insertColumns) . ") VALUES (" . implode(', ', $insertPlaceholders) . ")";
        
        echo "<h3>Generated SQL:</h3>\n";
        echo "<pre>$sql</pre>\n";
        
        echo "<h3>Values to Insert:</h3>\n";
        echo "<table>\n";
        echo "<tr><th>Column</th><th>Value</th></tr>\n";
        for ($i = 0; $i < count($insertColumns); $i++) {
            echo "<tr><td>{$insertColumns[$i]}</td><td>{$insertValues[$i]}</td></tr>\n";
        }
        echo "</table>\n";
        
        // Actually try to insert the test product
        echo "<h3>Attempting Test Insert:</h3>\n";
        try {
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($insertValues);
            
            if ($result) {
                $productId = $pdo->lastInsertId();
                echo "<p class='success'>✅ Test product created successfully with ID: $productId</p>\n";
                
                // Clean up - delete the test product
                $deleteStmt = $pdo->prepare("DELETE FROM produits WHERE id = ?");
                $deleteStmt->execute([$productId]);
                echo "<p class='info'>🧹 Test product cleaned up</p>\n";
            } else {
                echo "<p class='error'>❌ Test product creation failed</p>\n";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Test insert failed: " . $e->getMessage() . "</p>\n";
        }
    } else {
        echo "<p class='error'>❌ No valid columns found for insertion</p>\n";
    }
    echo "</div>\n";
    
    // Test 3: Verify Existing Products
    echo "<div class='section'>\n";
    echo "<h2>📦 Test 3: Existing Products Verification</h2>\n";
    
    $titleColumn = $columnMapping['titre'] ?? 'titre';
    $typeColumn = $columnMapping['type'] ?? 'type';
    $prixColumn = $columnMapping['prix'] ?? 'prix';
    $actifColumn = $columnMapping['actif'] ?? 'actif';
    
    $stmt = $pdo->query("SELECT id, $titleColumn as titre, $typeColumn as type, $prixColumn as prix FROM produits WHERE $actifColumn = 1 LIMIT 10");
    $existingProducts = $stmt->fetchAll();
    
    echo "<p class='info'>Found " . count($existingProducts) . " active products</p>\n";
    
    if (!empty($existingProducts)) {
        echo "<table>\n";
        echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>Price</th></tr>\n";
        foreach ($existingProducts as $product) {
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>" . htmlspecialchars($product['titre']) . "</td>";
            echo "<td>{$product['type']}</td>";
            echo "<td>{$product['prix']} DZD</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p class='warning'>⚠️ No active products found</p>\n";
    }
    echo "</div>\n";
    
    // Test 4: Final Recommendations
    echo "<div class='section'>\n";
    echo "<h2>🎯 Test 4: Final Recommendations</h2>\n";
    
    $mappedCount = count(array_filter($columnMapping));
    $totalExpected = count($expectedColumns);
    $mappingPercentage = ($mappedCount / $totalExpected) * 100;
    
    echo "<div class='highlight'>\n";
    echo "<h3>Schema Compatibility Results:</h3>\n";
    echo "<p><strong>Mapped Columns:</strong> $mappedCount / $totalExpected (" . round($mappingPercentage, 1) . "%)</p>\n";
    
    if ($mappingPercentage >= 80) {
        echo "<p class='success'>🎉 Excellent compatibility! The fixed script should work perfectly.</p>\n";
        echo "<p><strong>Next Step:</strong> Run the complete_system_rebuild.php script</p>\n";
    } elseif ($mappingPercentage >= 60) {
        echo "<p class='warning'>⚠️ Good compatibility with some missing columns.</p>\n";
        echo "<p><strong>Next Step:</strong> Run the script, some features may be limited</p>\n";
    } else {
        echo "<p class='error'>❌ Low compatibility. Manual schema adjustments may be needed.</p>\n";
        echo "<p><strong>Next Step:</strong> Review missing columns and consider adding them</p>\n";
    }
    
    echo "<h3>Missing Columns:</h3>\n";
    $missingColumns = array_diff($expectedColumns, array_keys($columnMapping));
    if (!empty($missingColumns)) {
        echo "<ul>\n";
        foreach ($missingColumns as $missing) {
            echo "<li>$missing</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p class='success'>✅ All expected columns are mapped!</p>\n";
    }
    echo "</div>\n";
    
    echo "<h3>🚀 Ready to Proceed:</h3>\n";
    echo "<div style='display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:15px;margin:20px 0;'>\n";
    echo "<a href='complete_system_rebuild.php' style='background:#28a745;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🔧 Run Fixed Rebuild Script</a>\n";
    echo "<a href='test_complete_system.php' style='background:#007bff;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🧪 Run Complete Test</a>\n";
    echo "<a href='analyze_database_schema.php' style='background:#6f42c1;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🔍 Detailed Schema Analysis</a>\n";
    echo "</div>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='section'>\n";
    echo "<h2 class='error'>❌ Test Error</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    echo "</div>\n";
}

echo "</div>\n";
?>

<script>
console.log('🧪 Schema fix test completed');
</script>
