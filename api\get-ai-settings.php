<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Prevent any output before JSON
ob_start();

try {
    // Simple includes without bootstrap to avoid circular dependencies
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/../config/database.php';

    // Initialize Config
    Config::init();

    $db = Database::getInstance();
    $pdo = $db->getPDO();

    $providers = ['openai', 'anthropic', 'gemini'];
    $settings = [];

    foreach ($providers as $provider) {
        // Get from database first
        $stmt = $pdo->prepare("
            SELECT provider, enabled, api_key, model, status_message, updated_at
            FROM ai_settings
            WHERE provider = :provider
        ");
        $stmt->execute(['provider' => $provider]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            // Decrypt API key if it exists
            $apiKey = $result['api_key'];
            if (!empty($apiKey) && function_exists('openssl_decrypt')) {
                // Simple decryption - in production use proper encryption
                $apiKey = base64_decode($apiKey);
            }

            $settings[$provider] = [
                'enabled' => (bool)$result['enabled'],
                'api_key' => $apiKey ?: '',
                'model' => $result['model'] ?: getDefaultModel($provider),
                'status' => $result['status_message'] ?: 'Ready',
                'last_tested' => $result['updated_at']
            ];
        } else {
            // Fallback to .env file
            $envKey = strtoupper($provider) . '_API_KEY';
            $apiKey = Config::get($envKey, '');

            $settings[$provider] = [
                'enabled' => !empty($apiKey),
                'api_key' => $apiKey,
                'model' => getDefaultModel($provider),
                'status' => !empty($apiKey) ? 'Loaded from environment' : 'Not configured',
                'last_tested' => null
            ];
        }
    }

    // Clear any output buffer
    ob_clean();

    echo json_encode([
        'success' => true,
        'data' => $settings
    ]);
} catch (Exception $e) {
    // Clear any output buffer
    ob_clean();

    error_log("AI Settings Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function getDefaultModel($provider)
{
    $models = [
        'openai' => 'gpt-3.5-turbo',
        'anthropic' => 'claude-3-sonnet-20240229',
        'gemini' => 'gemini-pro'
    ];

    return $models[$provider] ?? 'default';
}
