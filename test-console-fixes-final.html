<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات وحدة التحكم النهائي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Ultimate Visibility Fix CSS -->
    <link rel="stylesheet" href="admin/css/ultimate-visibility-fix.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: "Se<PERSON>e UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error-log {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success-log {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .books-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
            min-height: 100px;
            border: 2px dashed #ddd;
            padding: 20px;
        }
        .book-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-bug"></i> اختبار إصلاحات وحدة التحكم النهائي</h1>
        <p>اختبار شامل لجميع الإصلاحات المطبقة على أخطاء JavaScript</p>
        
        <div class="test-section">
            <h2><i class="fas fa-play"></i> اختبار الوظائف الأساسية</h2>
            
            <button class="btn btn-primary" onclick="testMainJsFunctionality()">
                <i class="fas fa-test-tube"></i> اختبار main.js
            </button>
            
            <button class="btn btn-success" onclick="testSelectionFunctionality()">
                <i class="fas fa-mouse-pointer"></i> اختبار التحديد
            </button>
            
            <button class="btn btn-info" onclick="testUIVisibility()">
                <i class="fas fa-eye"></i> اختبار ظهور الواجهة
            </button>
            
            <button class="btn btn-warning" onclick="clearConsole()">
                <i class="fas fa-eraser"></i> مسح وحدة التحكم
            </button>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-terminal"></i> وحدة التحكم</h2>
            <div id="consoleOutput" class="console-output">
                <div class="success-log">🚀 جاهز للاختبار...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-grid-3x3"></i> اختبار Books Grid</h2>
            <div class="books-grid" id="booksGrid">
                <div style="text-align: center; color: #666;">
                    <i class="fas fa-book"></i>
                    <p>سيتم ملء هذا القسم بواسطة JavaScript</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-plus"></i> اختبار أزرار إدارة صفحات الهبوط</h2>
            
            <button id="addLandingPageBtn" class="btn btn-primary action-button">
                <i class="fas fa-plus"></i> أَضف صفحة هبوط
            </button>
            
            <div id="landingPagesContainer" style="margin-top: 20px; min-height: 50px; border: 1px dashed #ddd; padding: 15px;">
                <p style="text-align: center; color: #666;">حاوي صفحات الهبوط</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-clipboard-check"></i> نتائج الاختبار</h2>
            <div id="testResults">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> جاري فحص النظام...
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Selection Error Fix (load first) -->
    <script src="admin/js/selection-error-fix.js"></script>
    
    <script>
        // Console logging function
        function logToConsole(message, type = "info") {
            const console = document.getElementById("consoleOutput");
            const timestamp = new Date().toLocaleTimeString("ar-DZ");
            const icon = type === "error" ? "❌" : type === "success" ? "✅" : type === "warning" ? "⚠️" : "ℹ️";
            
            const logEntry = document.createElement("div");
            logEntry.className = type === "error" ? "error-log" : type === "success" ? "success-log" : "";
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            console.appendChild(logEntry);
            console.scrollTop = console.scrollHeight;
        }
        
        // Test main.js functionality (simulate the fixed version)
        function testMainJsFunctionality() {
            logToConsole("اختبار وظائف main.js...");
            
            try {
                // Test if booksGrid exists (this is the fix we applied)
                const booksGrid = document.querySelector('.books-grid');
                if (!booksGrid) {
                    logToConsole("⚠️ عنصر books-grid غير موجود - هذا متوقع في هذه الصفحة", "warning");
                    return;
                }
                
                logToConsole("✅ عنصر books-grid موجود", "success");
                
                // Simulate the fixed loadProducts logic
                const sampleProducts = [
                    { id: 1, titre: "كتاب تجريبي 1", type: "book" },
                    { id: 2, titre: "كتاب تجريبي 2", type: "book" },
                    { id: 3, titre: "كتاب تجريبي 3", type: "book" }
                ];
                
                booksGrid.innerHTML = '';
                sampleProducts.forEach(product => {
                    const productCard = document.createElement('div');
                    productCard.className = 'book-card';
                    productCard.innerHTML = `
                        <h5>${product.titre}</h5>
                        <p>النوع: ${product.type}</p>
                    `;
                    booksGrid.appendChild(productCard);
                });
                
                logToConsole(`✅ تم تحميل ${sampleProducts.length} منتجات تجريبية بنجاح`, "success");
                logToConsole("✅ إصلاح main.js يعمل بشكل صحيح", "success");
                
            } catch (error) {
                logToConsole(`❌ خطأ في اختبار main.js: ${error.message}`, "error");
            }
        }
        
        // Test selection functionality
        function testSelectionFunctionality() {
            logToConsole("اختبار وظائف التحديد...");
            
            try {
                // Test getSelection
                const selection = window.getSelection();
                if (selection) {
                    logToConsole("✅ window.getSelection() يعمل", "success");
                    logToConsole(`rangeCount: ${selection.rangeCount}`, "info");
                    
                    // Test rangeCount access (this was the main issue)
                    try {
                        const count = selection.rangeCount;
                        logToConsole("✅ يمكن الوصول إلى rangeCount بدون أخطاء", "success");
                    } catch (e) {
                        logToConsole(`❌ خطأ في الوصول إلى rangeCount: ${e.message}`, "error");
                    }
                } else {
                    logToConsole("❌ window.getSelection() يعيد null", "error");
                }
                
                // Test createRange
                const range = document.createRange();
                if (range) {
                    logToConsole("✅ document.createRange() يعمل", "success");
                } else {
                    logToConsole("❌ document.createRange() يعيد null", "error");
                }
                
            } catch (error) {
                logToConsole(`❌ خطأ في اختبار التحديد: ${error.message}`, "error");
            }
        }
        
        // Test UI visibility
        function testUIVisibility() {
            logToConsole("اختبار ظهور عناصر الواجهة...");
            
            const elementsToTest = [
                { selector: '#addLandingPageBtn', name: 'زر إضافة صفحة الهبوط' },
                { selector: '.books-grid', name: 'شبكة الكتب' },
                { selector: '#landingPagesContainer', name: 'حاوي صفحات الهبوط' }
            ];
            
            elementsToTest.forEach(element => {
                const el = document.querySelector(element.selector);
                if (el) {
                    const rect = el.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0;
                    
                    if (isVisible) {
                        logToConsole(`✅ ${element.name}: مرئي (${rect.width}x${rect.height})`, "success");
                    } else {
                        logToConsole(`⚠️ ${element.name}: موجود لكن غير مرئي`, "warning");
                        
                        // Apply visibility fix
                        el.style.display = 'block';
                        el.style.visibility = 'visible';
                        el.style.opacity = '1';
                        
                        logToConsole(`🔧 تم تطبيق إصلاح الظهور على ${element.name}`, "info");
                    }
                } else {
                    logToConsole(`❌ ${element.name}: غير موجود`, "error");
                }
            });
        }
        
        // Clear console
        function clearConsole() {
            document.getElementById("consoleOutput").innerHTML = "<div class='success-log'>🧹 تم مسح وحدة التحكم</div>";
        }
        
        // System status check
        function checkSystemStatus() {
            const statusDiv = document.getElementById("testResults");
            let statusHTML = "";
            
            const checks = [
                {
                    name: "Selection Error Fix",
                    status: typeof window.getSelection === "function",
                    details: "إصلاح أخطاء التحديد"
                },
                {
                    name: "Books Grid Element",
                    status: document.querySelector('.books-grid') !== null,
                    details: "عنصر شبكة الكتب"
                },
                {
                    name: "Add Button",
                    status: document.getElementById("addLandingPageBtn") !== null,
                    details: "زر إضافة صفحة الهبوط"
                },
                {
                    name: "Ultimate Visibility CSS",
                    status: document.querySelector('link[href*="ultimate-visibility-fix"]') !== null,
                    details: "CSS إصلاح الظهور"
                }
            ];
            
            checks.forEach(check => {
                const alertClass = check.status ? "alert-success" : "alert-danger";
                const icon = check.status ? "fas fa-check-circle" : "fas fa-times-circle";
                
                statusHTML += `
                    <div class="alert ${alertClass}">
                        <i class="${icon}"></i> 
                        <strong>${check.name}:</strong> ${check.details} - ${check.status ? 'يعمل' : 'لا يعمل'}
                    </div>
                `;
            });
            
            statusDiv.innerHTML = statusHTML;
        }
        
        // Initialize when page loads
        document.addEventListener("DOMContentLoaded", function() {
            logToConsole("تم تحميل الصفحة", "success");
            
            // Check system status
            setTimeout(checkSystemStatus, 1000);
            
            // Test basic functionality
            setTimeout(() => {
                logToConsole("بدء الاختبارات التلقائية...", "info");
                testSelectionFunctionality();
                testUIVisibility();
            }, 2000);
        });
        
        // Override console methods to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            logToConsole(args.join(" "), "info");
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logToConsole(args.join(" "), "error");
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            logToConsole(args.join(" "), "warning");
            originalWarn.apply(console, args);
        };
    </script>
</body>
</html>
