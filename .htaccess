# Activer le moteur de réécriture

RewriteEngine On

# Définir l'encodage par défaut

AddDefaultCharset UTF-8

# Forcer HTTPS (décommentez quand le certificat SSL est installé)

# RewriteCond %{HTTPS} off

# RewriteRule ^(.\*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# HTTPS Enforcement for production (currently commented for localhost development)

# Uncomment the following lines when deploying with SSL certificate:

# RewriteCond %{HTTPS} off

# RewriteCond %{HTTP_HOST} !^localhost [NC]

# RewriteRule ^(.\*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# URLs des pages de produits

RewriteRule ^product/([^/]+)/?$ product-landing.php?slug=$1 [L,QSA]

# URLs des landing pages

RewriteRule ^landing/product-([0-9]+)-([a-zA-Z0-9]+)/?$ landing-page-template.php?url=$1-$2 [L,QSA]

# URLs des magasins individuels

RewriteRule ^store/([a-zA-Z0-9\-]+)/?$ store.php?store=$1 [L,QSA]

# Protection du répertoire admin

<FilesMatch "^(admin|config)\.php$">
Order Deny,Allow
Deny from all
</FilesMatch>

# Protection des fichiers sensibles (étendue)

<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|git|sql|conf|backup|old|tmp|env)$">
Require all denied
</FilesMatch>

# Protection des fichiers PHP de configuration

<Files "config.php">
Require all denied
</Files>

<Files "security.php">
Require all denied
</Files>

<Files "SecurityHeaders.php">
Require all denied
</Files>

# Protection du répertoire cache

<IfModule mod_alias.c>
    RedirectMatch 403 ^/cache/.*$
    RedirectMatch 403 ^/logs/.*$
    RedirectMatch 403 ^/uploads/.*\.php$
</IfModule>

# Désactiver l'affichage du contenu des répertoires

Options -Indexes

# Headers de sécurité renforcés

<IfModule mod_headers.c>
    # Protection contre le clickjacking (plus strict)
    Header always set X-Frame-Options "DENY"

    # Protection XSS
    Header always set X-XSS-Protection "1; mode=block"

    # Désactiver la détection automatique du type MIME
    Header always set X-Content-Type-Options "nosniff"

    # Référer Policy (plus strict pour la sécurité)
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Content Security Policy (plus strict, adapté pour l'arabe)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.tiny.cloud; style-src 'self' 'unsafe-inline' fonts.googleapis.com cdn.tiny.cloud; font-src 'self' fonts.gstatic.com; img-src 'self' data: blob: cdn.tiny.cloud; connect-src 'self' cdn.tiny.cloud; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'"

    # HSTS (décommentez pour HTTPS)
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

    # Permissions Policy (anciennement Feature Policy)
    Header always set Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()"

    # Supprimer les signatures du serveur
    Header always unset Server
    Header always unset X-Powered-By

    # Cache control pour les zones sensibles
    <If "%{REQUEST_URI} =~ m#^/admin/#">
        Header always set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
        Header always set Pragma "no-cache"
        Header always set Expires "Thu, 01 Jan 1970 00:00:00 GMT"
    </If>

</IfModule>

# Compression GZIP

<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Mise en cache des fichiers statiques

<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Enable MIME type handling for static assets

<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
</IfModule>

# Ensure proper caching for static assets

<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
</IfModule>

# Protection contre les injections SQL et XSS

<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} ([^\w\s\d:/@.\-]) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2})
    RewriteRule ^(.*)$ index.php [F,L]
</IfModule>

# Redirection des erreurs

ErrorDocument 400 /error.html
ErrorDocument 401 /error.html
ErrorDocument 403 /error.html
ErrorDocument 404 /error.html
ErrorDocument 500 /error.html
