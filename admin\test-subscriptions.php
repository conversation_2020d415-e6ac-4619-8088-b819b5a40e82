<?php
/**
 * Test Subscription Management System
 * Check if subscription tables exist and create them if needed
 */

require_once '../php/config.php';

// Set content type and charset
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الاشتراكات</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: right; }
        th { background: #f8f9fa; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 اختبار نظام الاشتراكات</h1>
            <p>فحص وإنشاء جداول الاشتراكات</p>
        </div>

<?php
try {
    $pdo = getPDOConnection();
    echo '<div class="success">✅ تم الاتصال بقاعدة البيانات بنجاح</div>';

    // Check if subscription_plans table exists
    echo '<div class="section">';
    echo '<h2>📋 فحص جدول خطط الاشتراك</h2>';
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'subscription_plans'");
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        echo '<div class="error">❌ جدول subscription_plans غير موجود</div>';
        echo '<div class="info">🔧 إنشاء جدول subscription_plans...</div>';
        
        $createTableSQL = "
        CREATE TABLE subscription_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL UNIQUE,
            display_name_ar VARCHAR(100) NOT NULL,
            display_name_en VARCHAR(100) NOT NULL,
            description_ar TEXT,
            description_en TEXT,
            price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            currency VARCHAR(3) NOT NULL DEFAULT 'DZD',
            duration_days INT NOT NULL DEFAULT 30,
            max_products INT NOT NULL DEFAULT 5,
            max_landing_pages INT NOT NULL DEFAULT 2,
            max_storage_mb INT NOT NULL DEFAULT 100,
            max_templates INT NOT NULL DEFAULT 5,
            features JSON,
            is_active TINYINT(1) NOT NULL DEFAULT 1,
            sort_order INT NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_name (name),
            INDEX idx_is_active (is_active),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createTableSQL);
        echo '<div class="success">✅ تم إنشاء جدول subscription_plans بنجاح</div>';
        
        // Insert default subscription plans
        echo '<div class="info">📦 إضافة خطط الاشتراك الافتراضية...</div>';
        
        $defaultPlans = [
            [
                'name' => 'free',
                'display_name_ar' => 'مجاني',
                'display_name_en' => 'Free',
                'description_ar' => 'خطة مجانية للمبتدئين',
                'description_en' => 'Free plan for beginners',
                'price' => 0.00,
                'currency' => 'DZD',
                'duration_days' => 365,
                'max_products' => 3,
                'max_landing_pages' => 1,
                'max_storage_mb' => 50,
                'max_templates' => 2,
                'features' => '["basic_support"]',
                'is_active' => 1,
                'sort_order' => 1
            ],
            [
                'name' => 'basic',
                'display_name_ar' => 'أساسي',
                'display_name_en' => 'Basic',
                'description_ar' => 'خطة أساسية للأفراد',
                'description_en' => 'Basic plan for individuals',
                'price' => 2000.00,
                'currency' => 'DZD',
                'duration_days' => 30,
                'max_products' => 10,
                'max_landing_pages' => 5,
                'max_storage_mb' => 200,
                'max_templates' => 10,
                'features' => '["email_support", "basic_analytics"]',
                'is_active' => 1,
                'sort_order' => 2
            ],
            [
                'name' => 'premium',
                'display_name_ar' => 'مميز',
                'display_name_en' => 'Premium',
                'description_ar' => 'خطة مميزة للشركات',
                'description_en' => 'Premium plan for businesses',
                'price' => 5000.00,
                'currency' => 'DZD',
                'duration_days' => 30,
                'max_products' => -1,
                'max_landing_pages' => -1,
                'max_storage_mb' => 1000,
                'max_templates' => -1,
                'features' => '["priority_support", "advanced_analytics", "custom_domain", "api_access"]',
                'is_active' => 1,
                'sort_order' => 3
            ]
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO subscription_plans (
                name, display_name_ar, display_name_en, description_ar, description_en,
                price, currency, duration_days, max_products, max_landing_pages,
                max_storage_mb, max_templates, features, is_active, sort_order
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($defaultPlans as $plan) {
            $insertStmt->execute([
                $plan['name'],
                $plan['display_name_ar'],
                $plan['display_name_en'],
                $plan['description_ar'],
                $plan['description_en'],
                $plan['price'],
                $plan['currency'],
                $plan['duration_days'],
                $plan['max_products'],
                $plan['max_landing_pages'],
                $plan['max_storage_mb'],
                $plan['max_templates'],
                $plan['features'],
                $plan['is_active'],
                $plan['sort_order']
            ]);
        }
        
        echo '<div class="success">✅ تم إضافة ' . count($defaultPlans) . ' خطة اشتراك افتراضية</div>';
        
    } else {
        echo '<div class="success">✅ جدول subscription_plans موجود</div>';
    }
    echo '</div>';

    // Display current subscription plans
    echo '<div class="section">';
    echo '<h2>📊 خطط الاشتراك الحالية</h2>';
    
    $stmt = $pdo->query("SELECT * FROM subscription_plans ORDER BY sort_order");
    $plans = $stmt->fetchAll();
    
    if ($plans) {
        echo '<table>';
        echo '<tr><th>المعرف</th><th>الاسم</th><th>الاسم العربي</th><th>السعر</th><th>المدة</th><th>المنتجات</th><th>صفحات الهبوط</th><th>الحالة</th></tr>';
        foreach ($plans as $plan) {
            echo '<tr>';
            echo '<td>' . $plan['id'] . '</td>';
            echo '<td>' . $plan['name'] . '</td>';
            echo '<td>' . $plan['display_name_ar'] . '</td>';
            echo '<td>' . number_format($plan['price'], 2) . ' ' . $plan['currency'] . '</td>';
            echo '<td>' . $plan['duration_days'] . ' يوم</td>';
            echo '<td>' . ($plan['max_products'] == -1 ? 'غير محدود' : $plan['max_products']) . '</td>';
            echo '<td>' . ($plan['max_landing_pages'] == -1 ? 'غير محدود' : $plan['max_landing_pages']) . '</td>';
            echo '<td>' . ($plan['is_active'] ? '✅ نشط' : '❌ غير نشط') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<div class="error">❌ لا توجد خطط اشتراك</div>';
    }
    echo '</div>';

    // Test API
    echo '<div class="section">';
    echo '<h2>🌐 اختبار API الاشتراكات</h2>';
    
    $apiUrl = 'http://localhost:8000/php/api/subscriptions.php?action=plans';
    echo '<div class="info">🔗 رابط API: <a href="' . $apiUrl . '" target="_blank">' . $apiUrl . '</a></div>';
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);
    
    $response = @file_get_contents($apiUrl, false, $context);
    
    if ($response === false) {
        echo '<div class="error">❌ فشل في الوصول إلى API</div>';
    } else {
        echo '<div class="success">✅ تم الوصول إلى API بنجاح</div>';
        
        $data = json_decode($response, true);
        if ($data === null) {
            echo '<div class="error">❌ فشل في تحليل JSON</div>';
            echo '<pre>' . htmlspecialchars($response) . '</pre>';
        } else {
            echo '<div class="success">✅ تم تحليل JSON بنجاح</div>';
            echo '<div class="info">📋 عدد الخطط المُرجعة: ' . count($data['plans'] ?? []) . '</div>';
        }
    }
    echo '</div>';

} catch (Exception $e) {
    echo '<div class="error">❌ خطأ: ' . $e->getMessage() . '</div>';
}
?>

        <div class="section">
            <h2>✅ تم الانتهاء من الاختبار</h2>
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.html" class="btn">⚙️ لوحة التحكم</a>
                <a href="../" class="btn">🏠 الصفحة الرئيسية</a>
            </div>
        </div>
    </div>
</body>
</html>
