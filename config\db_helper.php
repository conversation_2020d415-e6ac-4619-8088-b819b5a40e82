<?php

/**
 * Database Helper Functions
 * Provides common database operations and utilities
 */

require_once __DIR__ . '/database.php';

class DB
{
    private static function getDB()
    {
        return Database::getInstance();
    }

    public static function query($sql, $params = [])
    {
        return self::getDB()->query($sql, $params);
    }

    public static function fetchAll($sql, $params = [])
    {
        return self::query($sql, $params)->fetchAll();
    }

    public static function fetchOne($sql, $params = [])
    {
        return self::query($sql, $params)->fetch();
    }

    public static function execute($sql, $params = [])
    {
        return self::query($sql, $params)->rowCount();
    }

    public static function transaction(callable $callback)
    {
        $db = self::getDB();
        try {
            $db->beginTransaction();
            $result = $callback($db);
            $db->commit();
            return $result;
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }

    public static function insert($table, array $data)
    {
        $fields = array_keys($data);
        $placeholders = array_fill(0, count($fields), '?');

        $sql = sprintf(
            "INSERT INTO %s (%s) VALUES (%s)",
            $table,
            implode(', ', $fields),
            implode(', ', $placeholders)
        );

        self::execute($sql, array_values($data));
        return self::getDB()->lastInsertId();
    }

    public static function update($table, array $data, $where, array $whereParams = [])
    {
        $fields = array_map(function ($field) {
            return "$field = ?";
        }, array_keys($data));

        $sql = sprintf(
            "UPDATE %s SET %s WHERE %s",
            $table,
            implode(', ', $fields),
            $where
        );

        $params = array_merge(array_values($data), $whereParams);
        return self::execute($sql, $params);
    }

    public static function delete($table, $where, array $params = [])
    {
        $sql = sprintf("DELETE FROM %s WHERE %s", $table, $where);
        return self::execute($sql, $params);
    }

    public static function quote($value)
    {
        return self::getDB()->quote($value);
    }
}
