<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات صفحات الهبوط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .test-result {
            background: #fff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
        .url-test {
            background: #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار إصلاحات صفحات الهبوط</h1>
    
    <div class="test-section">
        <h2>1. اختبار نسخ الرابط الكامل</h2>
        <p>اختبار وظيفة نسخ الرابط مع إضافة النطاق الكامل</p>
        
        <div class="url-test">
            <strong>رابط نسبي:</strong> /landing-page.php?id=4
        </div>
        <div class="url-test">
            <strong>رابط كامل متوقع:</strong> <span id="expectedUrl"></span>
        </div>
        
        <button onclick="testCopyUrl('/landing-page.php?id=4')">اختبار نسخ الرابط النسبي</button>
        <button onclick="testCopyUrl('http://localhost:8000/landing-page.php?id=4')">اختبار نسخ الرابط الكامل</button>
        
        <div id="copyResults"></div>
    </div>

    <div class="test-section">
        <h2>2. اختبار API التعديل</h2>
        <p>اختبار وظيفة GET لصفحة محددة</p>
        
        <button onclick="testGetSpecificPage(2)">اختبار GET للصفحة ID=2</button>
        <button onclick="testGetSpecificPage(4)">اختبار GET للصفحة ID=4</button>
        
        <div id="editResults"></div>
    </div>

    <div class="test-section">
        <h2>3. اختبار عام للAPI</h2>
        <button onclick="testAllPages()">اختبار جميع الصفحات</button>
        <div id="allPagesResults"></div>
    </div>

    <script>
        // Set expected URL
        document.getElementById('expectedUrl').textContent = window.location.origin + '/landing-page.php?id=4';

        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(div);
            console.log(message);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // Test copy URL function (simulate the fixed function)
        function copyPageUrl(url) {
            let fullUrl = url;
            
            if (url && !url.startsWith('http')) {
                const currentOrigin = window.location.origin;
                const cleanUrl = url.startsWith('/') ? url.substring(1) : url;
                fullUrl = `${currentOrigin}/${cleanUrl}`;
            }
            
            return fullUrl;
        }

        function testCopyUrl(testUrl) {
            clearResults('copyResults');
            
            const result = copyPageUrl(testUrl);
            log('copyResults', `الرابط الأصلي: ${testUrl}`, 'info');
            log('copyResults', `الرابط المعالج: ${result}`, 'success');
            
            // Test if it's a complete URL
            if (result.startsWith('http')) {
                log('copyResults', '✅ الرابط كامل ويحتوي على النطاق', 'success');
            } else {
                log('copyResults', '❌ الرابط غير كامل', 'error');
            }

            // Try to copy to clipboard
            if (navigator.clipboard) {
                navigator.clipboard.writeText(result).then(() => {
                    log('copyResults', '✅ تم نسخ الرابط إلى الحافظة بنجاح', 'success');
                }).catch(() => {
                    log('copyResults', '❌ فشل في نسخ الرابط إلى الحافظة', 'error');
                });
            }
        }

        async function testGetSpecificPage(pageId) {
            clearResults('editResults');
            
            try {
                log('editResults', `🔍 اختبار GET للصفحة ID=${pageId}...`, 'info');
                
                const response = await fetch(`php/api/landing-pages.php?id=${pageId}`);
                log('editResults', `📡 حالة الاستجابة: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                log('editResults', `📄 البيانات المستلمة: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                
                if (data.success && data.data) {
                    log('editResults', `✅ تم العثور على الصفحة: ${data.data.titre}`, 'success');
                    log('editResults', `📦 المنتج: ${data.data.product_title}`, 'info');
                    log('editResults', `🖼️ عدد الصور: ${data.data.images ? data.data.images.length : 0}`, 'info');
                } else {
                    log('editResults', '❌ لم يتم العثور على الصفحة', 'error');
                }
                
            } catch (error) {
                log('editResults', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        async function testAllPages() {
            clearResults('allPagesResults');
            
            try {
                log('allPagesResults', '🔍 اختبار جميع الصفحات...', 'info');
                
                const response = await fetch('php/api/landing-pages.php');
                log('allPagesResults', `📡 حالة الاستجابة: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success && data.data) {
                    log('allPagesResults', `✅ تم العثور على ${data.data.length} صفحة`, 'success');
                    
                    data.data.forEach((page, index) => {
                        log('allPagesResults', `📄 ${index + 1}. ${page.titre} (ID: ${page.id})`, 'info');
                    });
                } else {
                    log('allPagesResults', '❌ لا توجد صفحات', 'error');
                }
                
            } catch (error) {
                log('allPagesResults', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('🚀 بدء الاختبارات التلقائية...');
            testAllPages();
        });
    </script>
</body>
</html>
