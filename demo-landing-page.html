<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Demo Landing Page - فن اللامبالاة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .carousel {
            position: relative;
            height: 400px;
            overflow: hidden;
        }
        .carousel img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            position: absolute;
            opacity: 0;
            transition: opacity 0.5s;
        }
        .carousel img.active {
            opacity: 1;
        }
        .content {
            padding: 40px;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }
        .content-block {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }
        .content-block h3 {
            color: #333;
            margin-bottom: 20px;
        }
        .cta {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }
        .cta-button {
            background: white;
            color: #667eea;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.3s;
        }
        .cta-button:hover {
            transform: translateY(-3px);
        }
        .indicators {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }
        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: background 0.3s;
        }
        .indicator.active {
            background: white;
        }
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎯 فن اللامبالاة</h1>
            <p>نهج معاكس للعيش حياة جيدة - مارك مانسون</p>
            <p style="font-size: 1.5rem; color: #ffd700;">السعر: 1800 دج فقط!</p>
        </div>

        <!-- Image Carousel -->
        <div class="carousel" id="carousel">
            <img src="images/book3.svg" alt="فن اللامبالاة" class="active">
            <img src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&h=600&fit=crop" alt="قراءة الكتب">
            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop" alt="مكتب الكاتب">
            <img src="https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=800&h=600&fit=crop" alt="مجموعة كتب">
            
            <div class="indicators">
                <div class="indicator active" onclick="goToSlide(0)"></div>
                <div class="indicator" onclick="goToSlide(1)"></div>
                <div class="indicator" onclick="goToSlide(2)"></div>
                <div class="indicator" onclick="goToSlide(3)"></div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="two-column">
                <!-- Right Content -->
                <div class="content-block">
                    <h3>🎯 لماذا هذا الكتاب مميز؟</h3>
                    <ul>
                        <li><strong>نهج جديد في التفكير:</strong> يقدم مارك مانسون طريقة مختلفة تماماً للنظر إلى الحياة</li>
                        <li><strong>أسلوب مباشر وصريح:</strong> بدون تعقيدات أو كلام منمق، فقط الحقائق</li>
                        <li><strong>نصائح عملية:</strong> يمكنك تطبيقها في حياتك اليومية فوراً</li>
                        <li><strong>مترجم بعناية:</strong> ترجمة احترافية تحافظ على روح النص الأصلي</li>
                    </ul>
                    
                    <h3>💡 ما ستتعلمه من هذا الكتاب:</h3>
                    <p>• كيف تختار معاركك بحكمة<br>
                    • فن قول "لا" بثقة<br>
                    • التخلص من القلق غير المبرر<br>
                    • بناء حياة أكثر معنى وأقل توتراً</p>
                </div>
                
                <!-- Left Content -->
                <div class="content-block">
                    <h3>📚 عن المؤلف مارك مانسون</h3>
                    <p>مارك مانسون كاتب أمريكي ومدون مشهور، اشتهر بأسلوبه المباشر والصريح في الكتابة. يركز في أعماله على علم النفس الإيجابي والتطوير الذاتي بطريقة عملية وواقعية.</p>
                    
                    <h3>🌟 آراء القراء</h3>
                    <blockquote style="border-left: 3px solid #667eea; padding-left: 15px; margin: 20px 0; font-style: italic;">
                        "كتاب غيّر نظرتي للحياة تماماً. أسلوب مانسون المباشر يجعلك تواجه الحقائق بشجاعة."
                        <cite style="display: block; margin-top: 10px; font-weight: bold;">- أحمد محمد، قارئ</cite>
                    </blockquote>
                    
                    <blockquote style="border-left: 3px solid #667eea; padding-left: 15px; margin: 20px 0; font-style: italic;">
                        "أخيراً كتاب تطوير ذات بدون كلام منمق! نصائح عملية يمكن تطبيقها فعلاً."
                        <cite style="display: block; margin-top: 10px; font-weight: bold;">- فاطمة علي، طالبة جامعية</cite>
                    </blockquote>
                    
                    <h3>🎁 عرض خاص</h3>
                    <p><strong>احصل على الكتاب الآن بسعر مخفض!</strong><br>
                    السعر العادي: 2500 دج<br>
                    <span style="color: #e74c3c; font-size: 1.2em;">السعر الحالي: 1800 دج فقط!</span></p>
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="cta">
            <h2>🚀 احصل على هذا الكتاب الآن!</h2>
            <p>عرض محدود - لا تفوت الفرصة</p>
            <button class="cta-button" onclick="alert('🛒 سيتم توجيهك لصفحة الطلب')">
                🛒 اطلب الآن - 1800 دج
            </button>
            <button class="cta-button" onclick="alert('📞 سيتم الاتصال بك قريباً')">
                📞 اتصل بنا
            </button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('#carousel img');
        const indicators = document.querySelectorAll('.indicator');
        const totalSlides = slides.length;

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('active', i === index);
            });
        }

        function goToSlide(index) {
            currentSlide = index;
            showSlide(currentSlide);
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        }

        // Auto-play carousel
        setInterval(nextSlide, 4000);

        // Add some interactive effects
        document.querySelectorAll('.content-block').forEach(block => {
            block.addEventListener('mouseenter', () => {
                block.style.transform = 'translateY(-5px)';
                block.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1)';
            });
            
            block.addEventListener('mouseleave', () => {
                block.style.transform = 'translateY(0)';
                block.style.boxShadow = 'none';
            });
        });

        console.log('🎯 Demo Landing Page loaded successfully!');
        console.log('📸 Carousel with', totalSlides, 'images');
        console.log('📝 Rich content blocks with interactive effects');
        console.log('📱 Responsive design for mobile and desktop');
    </script>
</body>
</html>
