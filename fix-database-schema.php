<?php
/**
 * Fix Database Schema Issues
 * This script fixes missing columns and tables
 */

require_once 'php/config.php';

echo "<h1>🔧 Database Schema Fix</h1>";

try {
    $pdo = getPDOConnection();
    echo "<p class='success'>✅ Database connection successful</p>";
    
    echo "<h2>1. Checking and Fixing Categories Table</h2>";
    
    // Check if categories table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Creating categories table...</p>";
        
        $createCategories = "
        CREATE TABLE categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom_ar VARCHAR(255) NOT NULL,
            nom_en VARCHAR(255),
            description_ar TEXT,
            description_en TEXT,
            icone VARCHAR(100) DEFAULT 'fas fa-tag',
            couleur VARCHAR(7) DEFAULT '#007bff',
            ordre INT DEFAULT 0,
            actif TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_ordre (ordre),
            INDEX idx_actif (actif)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createCategories);
        echo "<p class='success'>✅ Categories table created</p>";
    } else {
        echo "<p class='info'>ℹ️ Categories table exists, checking columns...</p>";
        
        // Check if 'ordre' column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM categories LIKE 'ordre'");
        if ($stmt->rowCount() == 0) {
            echo "<p class='warning'>⚠️ Adding missing 'ordre' column to categories table...</p>";
            $pdo->exec("ALTER TABLE categories ADD COLUMN ordre INT DEFAULT 0 AFTER couleur");
            $pdo->exec("ALTER TABLE categories ADD INDEX idx_ordre (ordre)");
            echo "<p class='success'>✅ Added 'ordre' column to categories table</p>";
        } else {
            echo "<p class='success'>✅ 'ordre' column exists in categories table</p>";
        }
    }
    
    echo "<h2>2. Checking and Fixing Subscription Plans Table</h2>";
    
    // Check if subscription_plans table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'subscription_plans'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Creating subscription_plans table...</p>";
        
        $createSubscriptionPlans = "
        CREATE TABLE subscription_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL UNIQUE,
            display_name_ar VARCHAR(100) NOT NULL,
            display_name_en VARCHAR(100) NOT NULL,
            description_ar TEXT,
            description_en TEXT,
            price DECIMAL(10,2) DEFAULT 0.00,
            currency VARCHAR(3) DEFAULT 'DZD',
            duration_days INT DEFAULT 30,
            max_products INT DEFAULT 5,
            max_landing_pages INT DEFAULT 2,
            max_storage_mb INT DEFAULT 100,
            max_templates INT DEFAULT 5,
            features JSON,
            is_active TINYINT(1) DEFAULT 1,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_name (name),
            INDEX idx_is_active (is_active),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createSubscriptionPlans);
        echo "<p class='success'>✅ Subscription plans table created</p>";
        
        // Insert default subscription plans
        echo "<p class='info'>📦 Adding default subscription plans...</p>";
        
        $defaultPlans = [
            [1, 'free', 'مجاني', 'Free', 'خطة مجانية للمبتدئين', 'Free plan for beginners', 0.00, 'DZD', 365, 3, 1, 50, 2, '["basic_support"]'],
            [2, 'premium', 'بريميوم', 'Premium', 'خطة بريميوم للمحترفين', 'Premium plan for professionals', 2500.00, 'DZD', 30, 50, 10, 1000, 20, '["priority_support", "advanced_analytics", "custom_domain"]'],
            [3, 'business', 'أعمال', 'Business', 'خطة الأعمال للشركات', 'Business plan for companies', 5000.00, 'DZD', 30, 200, 50, 5000, 100, '["24_7_support", "white_label", "api_access", "team_collaboration"]'],
            [4, 'unlimited', 'غير محدود', 'Unlimited', 'خطة غير محدودة للمؤسسات', 'Unlimited plan for enterprises', 10000.00, 'DZD', 30, -1, -1, -1, -1, '["everything", "24_7_support", "white_label", "api_access"]']
        ];
        
        $insertPlan = $pdo->prepare("INSERT INTO subscription_plans (id, name, display_name_ar, display_name_en, description_ar, description_en, price, currency, duration_days, max_products, max_landing_pages, max_storage_mb, max_templates, features) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($defaultPlans as $plan) {
            $insertPlan->execute($plan);
        }
        
        echo "<p class='success'>✅ Added " . count($defaultPlans) . " default subscription plans</p>";
    } else {
        echo "<p class='success'>✅ Subscription plans table exists</p>";
        
        // Check if we have any plans
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM subscription_plans");
        $count = $stmt->fetch()['count'];
        echo "<p class='info'>📊 Found $count subscription plans</p>";
    }
    
    echo "<h2>3. Checking and Fixing User Roles Table</h2>";
    
    // Check if user_roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_roles'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Creating user_roles table...</p>";
        
        $createUserRoles = "
        CREATE TABLE user_roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL UNIQUE,
            display_name_ar VARCHAR(100) NOT NULL,
            display_name_en VARCHAR(100) NOT NULL,
            description TEXT,
            permissions JSON,
            level INT DEFAULT 1,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_name (name),
            INDEX idx_level (level),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createUserRoles);
        echo "<p class='success'>✅ User roles table created</p>";
        
        // Insert default roles
        echo "<p class='info'>📦 Adding default user roles...</p>";
        
        $defaultRoles = [
            [1, 'super_admin', 'مدير عام', 'Super Admin', 'مدير عام للنظام', '["all"]', 100],
            [2, 'store_owner', 'مالك متجر', 'Store Owner', 'مالك متجر', '["manage_store", "manage_products", "view_orders"]', 50],
            [3, 'moderator', 'مشرف', 'Moderator', 'مشرف على المحتوى', '["moderate_content", "view_reports"]', 30],
            [4, 'customer', 'عميل', 'Customer', 'عميل عادي', '["place_orders", "view_profile"]', 10]
        ];
        
        $insertRole = $pdo->prepare("INSERT INTO user_roles (id, name, display_name_ar, display_name_en, description, permissions, level) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($defaultRoles as $role) {
            $insertRole->execute($role);
        }
        
        echo "<p class='success'>✅ Added " . count($defaultRoles) . " default user roles</p>";
    } else {
        echo "<p class='success'>✅ User roles table exists</p>";
        
        // Check if we have any roles
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_roles");
        $count = $stmt->fetch()['count'];
        echo "<p class='info'>📊 Found $count user roles</p>";
    }
    
    echo "<h2>4. Checking Users Table</h2>";
    
    // Check users table
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $count = $stmt->fetch()['count'];
        echo "<p class='success'>✅ Users table exists with $count users</p>";
        
        // Show users
        if ($count > 0) {
            echo "<h4>Current users:</h4>";
            $stmt = $pdo->query("SELECT id, username, email, first_name, last_name, role_id, subscription_id, status FROM users");
            $users = $stmt->fetchAll();
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Role ID</th><th>Subscription ID</th><th>Status</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['first_name']} {$user['last_name']}</td>";
                echo "<td>{$user['role_id']}</td>";
                echo "<td>{$user['subscription_id']}</td>";
                echo "<td>{$user['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p class='error'>❌ Users table does not exist!</p>";
    }
    
    echo "<h2>✅ Database Schema Fix Complete</h2>";
    echo "<p class='success'>All required tables and columns have been checked and fixed.</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
