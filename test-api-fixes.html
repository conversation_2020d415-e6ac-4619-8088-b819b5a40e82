<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Fixes</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test API Fixes</h1>
        <p>اختبار إصلاحات API والتحقق من الأخطاء</p>

        <div class="test-section">
            <h3>🛠️ Critical API Fixes</h3>
            <button class="test-button" onclick="testProductsBulkDeleteAPI()">
                1. اختبار Products Bulk Delete API
            </button>
            <button class="test-button" onclick="testLandingPagesBulkDeleteAPI()">
                2. اختبار Landing Pages Bulk Delete API
            </button>
            <button class="test-button" onclick="testPaymentSettingsAPI()">
                3. اختبار Payment Settings API
            </button>
        </div>

        <div class="test-section">
            <h3>🎯 UI Elements Check</h3>
            <button class="test-button" onclick="checkAdminPanelElements()">
                1. فحص عناصر لوحة التحكم
            </button>
            <button class="test-button" onclick="checkDashboardElements()">
                2. فحص عناصر لوحة المعلومات
            </button>
            <button class="test-button" onclick="checkBulkDeletionElements()">
                3. فحص عناصر الحذف المجمع
            </button>
        </div>

        <div class="test-section">
            <h3>🚀 Comprehensive Tests</h3>
            <button class="test-button" onclick="runAllAPITests()">
                تشغيل جميع اختبارات API
            </button>
            <button class="test-button" onclick="clearResults()">
                مسح النتائج
            </button>
        </div>

        <div id="test-results"></div>
        <div id="console-output"></div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function logToConsole(message) {
            const output = document.getElementById('console-output');
            output.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        // API TESTS
        async function testProductsBulkDeleteAPI() {
            addTestResult('🧪 اختبار Products Bulk Delete API...', 'info');
            logToConsole('Testing Products Bulk Delete API...');
            
            try {
                const startTime = Date.now();
                
                const response = await fetch('php/api/products.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'bulk_delete',
                        ids: []
                    })
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                logToConsole(`Response status: ${response.status}`);
                logToConsole(`Response time: ${responseTime}ms`);
                
                const text = await response.text();
                logToConsole(`Response text: ${text.substring(0, 200)}...`);
                
                if (response.status === 500) {
                    addTestResult('❌ Products API returns HTTP 500 - Server Error', 'error');
                    logToConsole('ERROR: API should return proper JSON error, not HTTP 500');
                } else {
                    try {
                        const result = JSON.parse(text);
                        if (!result.success && result.message.includes('No valid product IDs')) {
                            addTestResult('✅ Products Bulk Delete API handles errors correctly', 'success');
                            logToConsole('SUCCESS: API returns proper error response');
                        } else {
                            addTestResult('⚠️ Products API response unexpected', 'warning');
                        }
                    } catch (parseError) {
                        addTestResult('❌ Products API returns invalid JSON', 'error');
                        logToConsole(`JSON Parse Error: ${parseError.message}`);
                    }
                }
            } catch (error) {
                addTestResult('❌ Products API network error: ' + error.message, 'error');
                logToConsole(`Network error: ${error.message}`);
            }
        }

        async function testLandingPagesBulkDeleteAPI() {
            addTestResult('🧪 اختبار Landing Pages Bulk Delete API...', 'info');
            logToConsole('Testing Landing Pages Bulk Delete API...');
            
            try {
                const startTime = Date.now();
                
                const response = await fetch('php/api/landing-pages.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'bulk_delete',
                        ids: []
                    })
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                logToConsole(`Response status: ${response.status}`);
                logToConsole(`Response time: ${responseTime}ms`);
                
                const text = await response.text();
                logToConsole(`Response text: ${text.substring(0, 200)}...`);
                
                if (response.status === 500) {
                    addTestResult('❌ Landing Pages API returns HTTP 500 - Server Error', 'error');
                    logToConsole('ERROR: API should return proper JSON error, not HTTP 500');
                } else {
                    try {
                        const result = JSON.parse(text);
                        if (!result.success && result.message.includes('No valid landing page IDs')) {
                            addTestResult('✅ Landing Pages Bulk Delete API handles errors correctly', 'success');
                            logToConsole('SUCCESS: API returns proper error response');
                        } else {
                            addTestResult('⚠️ Landing Pages API response unexpected', 'warning');
                        }
                    } catch (parseError) {
                        addTestResult('❌ Landing Pages API returns invalid JSON', 'error');
                        logToConsole(`JSON Parse Error: ${parseError.message}`);
                    }
                }
            } catch (error) {
                addTestResult('❌ Landing Pages API network error: ' + error.message, 'error');
                logToConsole(`Network error: ${error.message}`);
            }
        }

        async function testPaymentSettingsAPI() {
            addTestResult('🧪 اختبار Payment Settings API...', 'info');
            logToConsole('Testing Payment Settings API...');
            
            try {
                const response = await fetch('php/api/payment-settings.php');
                logToConsole(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                logToConsole(`API Response: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    addTestResult('✅ Payment Settings API يعمل بشكل صحيح', 'success');
                    logToConsole('Payment Settings API working correctly');
                    
                    if (result.settings) {
                        logToConsole(`Found settings for: ${Object.keys(result.settings).join(', ')}`);
                    }
                    
                    if (result.statistics) {
                        logToConsole(`Statistics: ${JSON.stringify(result.statistics)}`);
                    }
                } else {
                    addTestResult('❌ Payment Settings API فشل', 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في Payment Settings API: ' + error.message, 'error');
                logToConsole(`Payment Settings API error: ${error.message}`);
            }
        }

        // UI ELEMENT CHECKS
        function checkAdminPanelElements() {
            addTestResult('🧪 فحص عناصر لوحة التحكم...', 'info');
            logToConsole('Checking admin panel elements...');
            
            // Check if we're in the admin panel context
            const isAdminPanel = window.location.pathname.includes('/admin/');
            
            if (!isAdminPanel) {
                addTestResult('⚠️ هذا الاختبار يجب تشغيله من داخل لوحة التحكم', 'warning');
                logToConsole('Test should be run from within admin panel');
                return;
            }
            
            // Check for admin panel elements
            const adminElements = [
                'totalLandingPages',
                'selectAllProducts',
                'deleteSelectedProductsBtn',
                'selectedProductsCount',
                'selectAllLandingPages',
                'deleteSelectedLandingPagesBtn',
                'selectedLandingPagesCount',
                'landingPagesBulkControls'
            ];
            
            let foundElements = 0;
            adminElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    foundElements++;
                    logToConsole(`✅ Found element: ${elementId}`);
                } else {
                    logToConsole(`❌ Missing element: ${elementId}`);
                }
            });
            
            if (foundElements === adminElements.length) {
                addTestResult('✅ جميع عناصر لوحة التحكم موجودة', 'success');
            } else {
                addTestResult(`⚠️ تم العثور على ${foundElements}/${adminElements.length} عناصر`, 'warning');
            }
        }

        function checkDashboardElements() {
            addTestResult('🧪 فحص عناصر لوحة المعلومات...', 'info');
            logToConsole('Checking dashboard elements...');
            
            const dashboardElements = ['totalLandingPages', 'totalBooks', 'newOrders', 'totalSales'];
            let foundElements = 0;
            
            dashboardElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    foundElements++;
                    logToConsole(`✅ Found dashboard element: ${elementId}`);
                } else {
                    logToConsole(`❌ Missing dashboard element: ${elementId}`);
                }
            });
            
            if (foundElements >= 3) {
                addTestResult('✅ معظم عناصر لوحة المعلومات موجودة', 'success');
            } else {
                addTestResult(`⚠️ تم العثور على ${foundElements}/${dashboardElements.length} عناصر لوحة المعلومات`, 'warning');
            }
        }

        function checkBulkDeletionElements() {
            addTestResult('🧪 فحص عناصر الحذف المجمع...', 'info');
            logToConsole('Checking bulk deletion elements...');
            
            const bulkElements = [
                'selectAllProducts',
                'deleteSelectedProductsBtn',
                'selectAllLandingPages',
                'deleteSelectedLandingPagesBtn'
            ];
            
            let foundElements = 0;
            bulkElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    foundElements++;
                    logToConsole(`✅ Found bulk element: ${elementId}`);
                } else {
                    logToConsole(`❌ Missing bulk element: ${elementId}`);
                }
            });
            
            if (foundElements === bulkElements.length) {
                addTestResult('✅ جميع عناصر الحذف المجمع موجودة', 'success');
            } else {
                addTestResult(`⚠️ تم العثور على ${foundElements}/${bulkElements.length} عناصر حذف مجمع`, 'warning');
            }
        }

        async function runAllAPITests() {
            clearResults();
            addTestResult('🚀 بدء تشغيل جميع اختبارات API...', 'info');
            logToConsole('Starting comprehensive API tests...');
            
            await testProductsBulkDeleteAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testLandingPagesBulkDeleteAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testPaymentSettingsAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            checkAdminPanelElements();
            checkDashboardElements();
            checkBulkDeletionElements();
            
            addTestResult('🎉 انتهاء جميع الاختبارات', 'info');
            logToConsole('All API tests completed');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('console-output').textContent = '';
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('📋 تشغيل اختبار أساسي تلقائياً...', 'info');
                testPaymentSettingsAPI();
            }, 1000);
        });
    </script>
</body>
</html>
