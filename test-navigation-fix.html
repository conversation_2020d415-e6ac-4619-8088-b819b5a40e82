<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح التنقل - لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .admin-frame {
            width: 100%;
            height: 800px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            max-height: 500px;
            overflow-y: auto;
        }
        .section-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid #6c757d;
            transition: all 0.3s ease;
        }
        .section-status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .section-status.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .section-status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .section-status.testing {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .test-button {
            margin: 5px;
        }
        .navigation-test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .nav-test-card {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .nav-test-card:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .nav-test-card.active {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-route"></i> اختبار إصلاح التنقل - لوحة التحكم</h1>
        <p class="lead">اختبار شامل للتأكد من أن إصلاح التنقل يعمل بشكل صحيح</p>
        
        <div class="alert alert-info">
            <h4><i class="fas fa-info-circle"></i> الإصلاحات المطبقة</h4>
            <ul>
                <li>✅ نظام تنقل محسن ومعاد كتابته بالكامل</li>
                <li>✅ إخفاء جميع الأقسام عدا القسم النشط</li>
                <li>✅ معالجة أحداث محسنة للنقر</li>
                <li>✅ تحميل محتوى خاص بكل قسم</li>
                <li>✅ حالة نظيفة مضمونة عند التحميل</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-play"></i> اختبارات التنقل</h2>
            
            <button class="btn btn-primary test-button" onclick="runNavigationTest()">
                <i class="fas fa-route"></i> اختبار التنقل الكامل
            </button>
            
            <button class="btn btn-success test-button" onclick="testSectionVisibility()">
                <i class="fas fa-eye"></i> اختبار ظهور الأقسام
            </button>
            
            <button class="btn btn-info test-button" onclick="openAdminInNewTab()">
                <i class="fas fa-external-link-alt"></i> فتح لوحة التحكم
            </button>
            
            <button class="btn btn-warning test-button" onclick="clearResults()">
                <i class="fas fa-eraser"></i> مسح النتائج
            </button>
            
            <button class="btn btn-secondary test-button" onclick="forceReinitialize()">
                <i class="fas fa-redo"></i> إعادة تهيئة التنقل
            </button>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-mouse-pointer"></i> اختبار التنقل التفاعلي</h2>
            <p>انقر على الأقسام أدناه لاختبار التنقل مباشرة في الإطار:</p>
            
            <div class="navigation-test-grid">
                <div class="nav-test-card" data-section="dashboard" onclick="testNavigateToSection('dashboard')">
                    <i class="fas fa-home fa-2x"></i>
                    <h5>الرئيسية</h5>
                    <small>Dashboard</small>
                </div>
                <div class="nav-test-card" data-section="books" onclick="testNavigateToSection('books')">
                    <i class="fas fa-box fa-2x"></i>
                    <h5>إدارة المنتجات</h5>
                    <small>Products</small>
                </div>
                <div class="nav-test-card" data-section="orders" onclick="testNavigateToSection('orders')">
                    <i class="fas fa-shopping-cart fa-2x"></i>
                    <h5>الطلبات</h5>
                    <small>Orders</small>
                </div>
                <div class="nav-test-card" data-section="landingPages" onclick="testNavigateToSection('landingPages')">
                    <i class="fas fa-bullhorn fa-2x"></i>
                    <h5>صفحات هبوط</h5>
                    <small>Landing Pages</small>
                </div>
                <div class="nav-test-card" data-section="reports" onclick="testNavigateToSection('reports')">
                    <i class="fas fa-chart-bar fa-2x"></i>
                    <h5>التقارير</h5>
                    <small>Reports</small>
                </div>
                <div class="nav-test-card" data-section="settings" onclick="testNavigateToSection('settings')">
                    <i class="fas fa-cog fa-2x"></i>
                    <h5>الإعدادات</h5>
                    <small>Settings</small>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-chart-line"></i> حالة الأقسام</h2>
            <div id="sectionsStatus">
                <div class="section-status" data-section="dashboard">
                    <span><i class="fas fa-home"></i> الرئيسية (Dashboard)</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status" data-section="books">
                    <span><i class="fas fa-box"></i> إدارة المنتجات (Products)</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status" data-section="orders">
                    <span><i class="fas fa-shopping-cart"></i> الطلبات (Orders)</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status" data-section="landingPages">
                    <span><i class="fas fa-bullhorn"></i> صفحات هبوط (Landing Pages)</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status" data-section="reports">
                    <span><i class="fas fa-chart-bar"></i> التقارير والإحصائيات (Reports)</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status" data-section="settings">
                    <span><i class="fas fa-cog"></i> إعدادات النظام (Settings)</span>
                    <span>جاري الفحص...</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-terminal"></i> نتائج الاختبار</h2>
            <div id="testResults" class="test-results">
                <div style="color: #28a745;">🚀 جاهز لبدء اختبار التنقل...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-browser"></i> معاينة مباشرة</h2>
            <iframe id="adminFrame" class="admin-frame" src="/admin/"></iframe>
            <div class="mt-3">
                <button class="btn btn-secondary" onclick="reloadAdminFrame()">
                    <i class="fas fa-redo"></i> إعادة تحميل الإطار
                </button>
                <button class="btn btn-info" onclick="checkFrameNavigation()">
                    <i class="fas fa-search"></i> فحص التنقل في الإطار
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Test results logging
        function logResult(message, type = "info") {
            const resultsDiv = document.getElementById("testResults");
            const timestamp = new Date().toLocaleTimeString("ar-DZ");
            const icon = type === "error" ? "❌" : type === "success" ? "✅" : type === "warning" ? "⚠️" : "ℹ️";
            const color = type === "error" ? "#dc3545" : type === "success" ? "#28a745" : type === "warning" ? "#ffc107" : "#6c757d";
            
            const logEntry = document.createElement("div");
            logEntry.style.color = color;
            logEntry.style.marginBottom = "5px";
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Update section status
        function updateSectionStatus(sectionId, status, message) {
            const statusElement = document.querySelector(`[data-section="${sectionId}"].section-status`);
            if (statusElement) {
                statusElement.className = `section-status ${status}`;
                statusElement.querySelector("span:last-child").textContent = message;
            }
        }
        
        // Test navigation to specific section
        function testNavigateToSection(sectionId) {
            logResult(`🧭 اختبار التنقل إلى: ${sectionId}`);
            
            // Update visual feedback
            document.querySelectorAll('.nav-test-card').forEach(card => {
                card.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionId}"].nav-test-card`).classList.add('active');
            
            const frame = document.getElementById('adminFrame');
            try {
                const frameWindow = frame.contentWindow;
                
                if (frameWindow.adminNavigation && frameWindow.adminNavigation.navigateToSection) {
                    frameWindow.adminNavigation.navigateToSection(sectionId);
                    logResult(`✅ تم التنقل إلى ${sectionId} بنجاح`, "success");
                    updateSectionStatus(sectionId, "success", "تم التنقل بنجاح");
                    
                    // Check if section is visible
                    setTimeout(() => {
                        checkSectionVisibility(sectionId, frameWindow);
                    }, 500);
                } else {
                    logResult(`❌ نظام التنقل غير متوفر في الإطار`, "error");
                    updateSectionStatus(sectionId, "error", "نظام التنقل غير متوفر");
                }
                
            } catch (error) {
                logResult(`❌ خطأ في التنقل إلى ${sectionId}: ${error.message}`, "error");
                updateSectionStatus(sectionId, "error", "خطأ في التنقل");
            }
        }
        
        // Check section visibility
        function checkSectionVisibility(sectionId, frameWindow) {
            try {
                const frameDoc = frameWindow.document;
                const section = frameDoc.getElementById(sectionId);
                
                if (section) {
                    const isVisible = section.classList.contains('active') && 
                                    section.style.display !== 'none' &&
                                    section.style.opacity !== '0';
                    
                    if (isVisible) {
                        logResult(`✅ القسم ${sectionId} مرئي بشكل صحيح`, "success");
                        updateSectionStatus(sectionId, "success", "مرئي وفعال");
                    } else {
                        logResult(`⚠️ القسم ${sectionId} غير مرئي`, "warning");
                        updateSectionStatus(sectionId, "warning", "غير مرئي");
                    }
                } else {
                    logResult(`❌ القسم ${sectionId} غير موجود`, "error");
                    updateSectionStatus(sectionId, "error", "غير موجود");
                }
            } catch (error) {
                logResult(`❌ خطأ في فحص ظهور ${sectionId}: ${error.message}`, "error");
            }
        }
        
        // Run complete navigation test
        function runNavigationTest() {
            logResult("🚀 بدء اختبار التنقل الكامل...");
            
            const sections = ['dashboard', 'books', 'orders', 'landingPages', 'reports', 'settings'];
            let currentIndex = 0;
            
            function testNextSection() {
                if (currentIndex < sections.length) {
                    const sectionId = sections[currentIndex];
                    updateSectionStatus(sectionId, "testing", "جاري الاختبار...");
                    testNavigateToSection(sectionId);
                    currentIndex++;
                    setTimeout(testNextSection, 2000);
                } else {
                    logResult("🎉 اكتمل اختبار التنقل الكامل!", "success");
                }
            }
            
            testNextSection();
        }
        
        // Test section visibility
        function testSectionVisibility() {
            logResult("👁️ اختبار ظهور الأقسام...");
            
            const frame = document.getElementById('adminFrame');
            try {
                const frameDoc = frame.contentDocument || frame.contentWindow.document;
                const sections = frameDoc.querySelectorAll('.content-section');
                
                let visibleCount = 0;
                let activeSection = null;
                
                sections.forEach(section => {
                    if (section.classList.contains('active') && section.style.display !== 'none') {
                        visibleCount++;
                        activeSection = section.id;
                    }
                });
                
                if (visibleCount === 1) {
                    logResult(`✅ قسم واحد فقط مرئي: ${activeSection}`, "success");
                } else if (visibleCount === 0) {
                    logResult("⚠️ لا توجد أقسام مرئية", "warning");
                } else {
                    logResult(`❌ عدة أقسام مرئية (${visibleCount}) - يجب أن يكون قسم واحد فقط`, "error");
                }
                
            } catch (error) {
                logResult(`❌ خطأ في فحص ظهور الأقسام: ${error.message}`, "error");
            }
        }
        
        // Check frame navigation
        function checkFrameNavigation() {
            logResult("🔍 فحص نظام التنقل في الإطار...");
            
            const frame = document.getElementById('adminFrame');
            try {
                const frameWindow = frame.contentWindow;
                
                if (frameWindow.adminNavigation) {
                    logResult("✅ نظام التنقل متوفر", "success");
                    
                    const currentSection = frameWindow.adminNavigation.getCurrentSection();
                    logResult(`📍 القسم الحالي: ${currentSection}`, "info");
                    
                    // Test navigation functions
                    if (typeof frameWindow.adminNavigation.navigateToSection === 'function') {
                        logResult("✅ وظيفة التنقل متوفرة", "success");
                    } else {
                        logResult("❌ وظيفة التنقل غير متوفرة", "error");
                    }
                    
                } else {
                    logResult("❌ نظام التنقل غير متوفر", "error");
                }
                
            } catch (error) {
                logResult(`❌ خطأ في فحص نظام التنقل: ${error.message}`, "error");
            }
        }
        
        // Force reinitialize navigation
        function forceReinitialize() {
            logResult("🔄 إعادة تهيئة نظام التنقل...");
            
            const frame = document.getElementById('adminFrame');
            try {
                const frameWindow = frame.contentWindow;
                
                if (frameWindow.adminNavigation && frameWindow.adminNavigation.reinitialize) {
                    frameWindow.adminNavigation.reinitialize();
                    logResult("✅ تم إعادة تهيئة نظام التنقل", "success");
                } else {
                    logResult("❌ لا يمكن إعادة تهيئة نظام التنقل", "error");
                }
                
            } catch (error) {
                logResult(`❌ خطأ في إعادة التهيئة: ${error.message}`, "error");
            }
        }
        
        // Open admin in new tab
        function openAdminInNewTab() {
            logResult("🔗 فتح لوحة التحكم في نافذة جديدة...");
            window.open('/admin/', '_blank');
        }
        
        // Reload admin frame
        function reloadAdminFrame() {
            logResult("🔄 إعادة تحميل إطار لوحة التحكم...");
            const frame = document.getElementById('adminFrame');
            frame.src = frame.src;
        }
        
        // Clear results
        function clearResults() {
            document.getElementById("testResults").innerHTML = "<div style='color: #28a745;'>🧹 تم مسح النتائج</div>";
            
            // Reset section statuses
            document.querySelectorAll(".section-status").forEach(element => {
                element.className = "section-status";
                element.querySelector("span:last-child").textContent = "جاري الفحص...";
            });
            
            // Reset nav test cards
            document.querySelectorAll('.nav-test-card').forEach(card => {
                card.classList.remove('active');
            });
        }
        
        // Auto-run initial test when page loads
        document.addEventListener("DOMContentLoaded", function() {
            logResult("تم تحميل صفحة اختبار التنقل", "success");
            
            // Wait for admin frame to load, then run initial checks
            setTimeout(() => {
                logResult("بدء الفحص التلقائي...");
                checkFrameNavigation();
                testSectionVisibility();
            }, 3000);
        });
    </script>
</body>
</html>
