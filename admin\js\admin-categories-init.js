// Categories Management Initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Initializing admin categories system...');

    // Load required scripts
    loadScript('/admin/js/categories-state.js')
        .then(() => loadScript('/admin/js/categories-management.js'))
        .then(() => {
            console.log('✅ Categories management scripts loaded');
            if (window.initializeCategoriesManagement) {
                window.initializeCategoriesManagement();
            }
        })
        .catch(error => {
            console.error('❌ Error loading categories scripts:', error);
        });
});

// Utility function to load scripts
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
        document.head.appendChild(script);
    });
}
