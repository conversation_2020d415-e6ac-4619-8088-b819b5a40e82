<?php
/**
 * Create Stores Table Migration
 * Creates the stores table for multi-user store management system
 */

require_once '../config.php';

try {
    $pdo = getPDOConnection();
    
    echo "🚀 Creating stores table...\n";
    
    // Create stores table
    $sql = "
    CREATE TABLE IF NOT EXISTS stores (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        store_name VARCHAR(255) NOT NULL,
        store_slug VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        logo_url VARCHAR(500),
        status ENUM('pending', 'active', 'suspended', 'closed') DEFAULT 'pending',
        domain VARCHAR(255) NULL,
        theme VARCHAR(100) DEFAULT 'default',
        settings JSON,
        total_products INT DEFAULT 0,
        total_orders INT DEFAULT 0,
        total_revenue DECIMAL(10,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_user_id (user_id),
        INDEX idx_status (status),
        INDEX idx_store_slug (store_slug),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    echo "✅ Stores table created successfully\n";
    
    // Create store_categories table for store categorization
    $sql = "
    CREATE TABLE IF NOT EXISTS store_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        store_id INT NOT NULL,
        category_name VARCHAR(255) NOT NULL,
        description TEXT,
        display_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
        INDEX idx_store_id (store_id),
        INDEX idx_display_order (display_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    echo "✅ Store categories table created successfully\n";
    
    // Create store_products table to link products to stores
    $sql = "
    CREATE TABLE IF NOT EXISTS store_products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        store_id INT NOT NULL,
        product_id INT NOT NULL,
        is_featured BOOLEAN DEFAULT FALSE,
        display_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES produits(id) ON DELETE CASCADE,
        UNIQUE KEY unique_store_product (store_id, product_id),
        INDEX idx_store_id (store_id),
        INDEX idx_product_id (product_id),
        INDEX idx_featured (is_featured)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    echo "✅ Store products table created successfully\n";
    
    // Create store_orders table to track orders per store
    $sql = "
    CREATE TABLE IF NOT EXISTS store_orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        store_id INT NOT NULL,
        order_id INT NOT NULL,
        commission_rate DECIMAL(5,2) DEFAULT 0.00,
        commission_amount DECIMAL(10,2) DEFAULT 0.00,
        status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
        FOREIGN KEY (order_id) REFERENCES commandes(id) ON DELETE CASCADE,
        UNIQUE KEY unique_store_order (store_id, order_id),
        INDEX idx_store_id (store_id),
        INDEX idx_order_id (order_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    echo "✅ Store orders table created successfully\n";
    
    // Add store_id column to existing tables if not exists
    
    // Add store_id to produits table
    $sql = "
    ALTER TABLE produits 
    ADD COLUMN IF NOT EXISTS store_id INT NULL AFTER id,
    ADD INDEX IF NOT EXISTS idx_store_id (store_id);
    ";
    
    try {
        $pdo->exec($sql);
        echo "✅ Added store_id to produits table\n";
    } catch (Exception $e) {
        echo "ℹ️ Store_id column may already exist in produits table\n";
    }
    
    // Add store_id to landing_pages table
    $sql = "
    ALTER TABLE landing_pages 
    ADD COLUMN IF NOT EXISTS store_id INT NULL AFTER id,
    ADD INDEX IF NOT EXISTS idx_store_id (store_id);
    ";
    
    try {
        $pdo->exec($sql);
        echo "✅ Added store_id to landing_pages table\n";
    } catch (Exception $e) {
        echo "ℹ️ Store_id column may already exist in landing_pages table\n";
    }
    
    // Insert sample stores for testing
    echo "\n🌱 Creating sample stores...\n";
    
    // Check if users exist first
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    
    if ($userCount > 0) {
        // Get first user ID
        $stmt = $pdo->query("SELECT id FROM users LIMIT 1");
        $userId = $stmt->fetchColumn();
        
        // Insert sample store
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO stores (user_id, store_name, store_slug, description, status, settings) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $settings = json_encode([
            'currency' => 'DZD',
            'language' => 'ar',
            'timezone' => 'Africa/Algiers',
            'theme_color' => '#667eea',
            'allow_reviews' => true,
            'auto_approve_products' => false
        ]);
        
        $stmt->execute([
            $userId,
            'متجر مصعب الرئيسي',
            'mossaab-main-store',
            'متجر شامل للكتب والإلكترونيات والحقائب',
            'active',
            $settings
        ]);
        
        echo "✅ Sample store created successfully\n";
    } else {
        echo "ℹ️ No users found, skipping sample store creation\n";
    }
    
    echo "\n🎉 Store management system database setup completed successfully!\n";
    echo "\n📋 Tables created:\n";
    echo "   - stores (main stores table)\n";
    echo "   - store_categories (store categories)\n";
    echo "   - store_products (product-store relationships)\n";
    echo "   - store_orders (order-store relationships)\n";
    echo "\n📋 Columns added:\n";
    echo "   - produits.store_id (links products to stores)\n";
    echo "   - landing_pages.store_id (links landing pages to stores)\n";
    
} catch (Exception $e) {
    echo "❌ Error creating stores table: " . $e->getMessage() . "\n";
    exit(1);
}
?>
