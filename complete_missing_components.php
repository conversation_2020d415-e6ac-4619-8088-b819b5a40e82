<?php
/**
 * Complete Missing Components for MariaDB Landing Page System
 * Adds 2 missing products and creates 5 landing pages
 */

require_once 'php/config.php';

echo "<h1>🎯 Complete Missing Components - MariaDB Port 3307</h1>\n";
echo "<style>
body{font-family:Arial;margin:20px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);min-height:100vh;} 
.container{max-width:1200px;margin:0 auto;background:white;padding:30px;border-radius:15px;box-shadow:0 10px 30px rgba(0,0,0,0.2);}
.success{color:#28a745;font-weight:bold;} 
.error{color:#dc3545;font-weight:bold;} 
.warning{color:#ffc107;font-weight:bold;} 
.info{color:#17a2b8;font-weight:bold;} 
pre{background:#f8f9fa;padding:15px;border-radius:5px;border-left:4px solid #007bff;} 
table{border-collapse:collapse;width:100%;margin:15px 0;} 
th,td{border:1px solid #dee2e6;padding:12px;text-align:left;} 
th{background:#e9ecef;font-weight:bold;}
.step{background:#f0f8ff;padding:25px;margin:20px 0;border-radius:12px;border-left:5px solid #007bff;}
.highlight{background:#fff3cd;padding:15px;border-radius:8px;margin:15px 0;}
.progress{width:100%;height:20px;background:#e9ecef;border-radius:10px;overflow:hidden;margin:15px 0;}
.progress-bar{height:100%;background:linear-gradient(90deg,#28a745,#20c997);transition:width 0.5s ease;}
</style>\n";

echo "<div class='container'>\n";

$totalSteps = 4;
$currentStep = 0;

function updateProgress($step, $total) {
    $percentage = ($step / $total) * 100;
    echo "<div class='progress'><div class='progress-bar' style='width: {$percentage}%'></div></div>\n";
    echo "<p class='info'>Progress: Step $step of $total ({$percentage}%)</p>\n";
}

try {
    $pdo = getPDOConnection();
    echo "<div class='highlight'>\n";
    echo "<h2>✅ Connected to MariaDB 11.5.2</h2>\n";
    echo "<p>Database: mossab-landing-page on localhost:3307</p>\n";
    echo "</div>\n";
    
    // STEP 1: Analyze Current Status
    $currentStep++;
    updateProgress($currentStep, $totalSteps);
    
    echo "<div class='step'>\n";
    echo "<h2>📊 Step 1: Current System Analysis</h2>\n";
    
    // Check existing products
    $stmt = $pdo->query("SELECT id, titre, type, prix FROM produits WHERE actif = 1");
    $existingProducts = $stmt->fetchAll();
    
    echo "<h3>Current Products (" . count($existingProducts) . "/5):</h3>\n";
    echo "<table>\n";
    echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>Price</th></tr>\n";
    foreach ($existingProducts as $product) {
        echo "<tr>";
        echo "<td>{$product['id']}</td>";
        echo "<td>" . htmlspecialchars($product['titre']) . "</td>";
        echo "<td>{$product['type']}</td>";
        echo "<td>{$product['prix']} DZD</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Check existing landing pages
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM landing_pages");
    $landingPagesCount = $stmt->fetch()['count'];
    echo "<p class='info'>Current Landing Pages: $landingPagesCount/5</p>\n";
    
    // Check categories
    $stmt = $pdo->query("SELECT id, nom_ar, nom_en FROM categories WHERE actif = 1");
    $categories = $stmt->fetchAll();
    echo "<p class='info'>Active Categories: " . count($categories) . "</p>\n";
    echo "</div>\n";
    
    // STEP 2: Add Missing Products
    $currentStep++;
    updateProgress($currentStep, $totalSteps);
    
    echo "<div class='step'>\n";
    echo "<h2>📦 Step 2: Adding Missing Products</h2>\n";
    
    // Define the 2 missing products
    $missingProducts = [
        [
            'titre' => 'قميص قطني كلاسيكي للرجال',
            'type' => 'clothing',
            'description' => '<h3>👔 قميص قطني فاخر</h3><p><strong>مصنوع من أجود أنواع القطن لإطلالة أنيقة ومريحة</strong></p><h4>✨ المميزات:</h4><ul><li><strong>قطن 100%:</strong> نسيج عالي الجودة وناعم الملمس</li><li><strong>تصميم كلاسيكي:</strong> أناقة لا تتأثر بالموضة</li><li><strong>مقاسات متنوعة:</strong> من S إلى XXL</li><li><strong>ألوان متعددة:</strong> أبيض، أزرق، رمادي</li><li><strong>سهل العناية:</strong> قابل للغسيل في الغسالة</li><li><strong>مقاوم للتجعد:</strong> يحافظ على شكله الأنيق</li></ul><h4>🎯 مناسب لـ:</h4><ul><li>بيئة العمل المهنية</li><li>المناسبات الرسمية</li><li>الاستخدام اليومي</li><li>الاجتماعات المهمة</li></ul><p><strong>العناية:</strong> غسيل عادي في الغسالة، كي على حرارة متوسطة</p>',
            'prix' => 3200.00,
            'stock' => 40,
            'auteur' => null,
            'materiel' => 'قطن 100%',
            'category_en' => 'clothing'
        ],
        [
            'titre' => 'خلاط كهربائي متعدد الاستخدامات',
            'type' => 'home',
            'description' => '<h3>🏠 خلاط كهربائي قوي</h3><p><strong>الحل الأمثل لجميع احتياجات المطبخ العصري</strong></p><h4>⚡ المواصفات التقنية:</h4><ul><li><strong>القوة:</strong> 1000 واط عالي الأداء</li><li><strong>السرعات:</strong> 5 سرعات مختلفة + نبضات</li><li><strong>الوعاء:</strong> زجاج مقوى سعة 1.5 لتر</li><li><strong>الشفرات:</strong> ستانلس ستيل عالي الجودة</li><li><strong>القاعدة:</strong> مانعة للانزلاق مع ماصات اهتزاز</li></ul><h4>🍹 الاستخدامات:</h4><ul><li>تحضير العصائر الطازجة</li><li>خلط الشوربات والصلصات</li><li>طحن المكسرات والتوابل</li><li>تحضير العجائن الخفيفة</li><li>صنع المشروبات الصحية</li></ul><h4>🛡️ الأمان والجودة:</h4><ul><li>نظام حماية من الحرارة الزائدة</li><li>قفل أمان للغطاء</li><li>سهل التنظيف والصيانة</li></ul><p><strong>الضمان:</strong> سنة كاملة ضد عيوب التصنيع</p>',
            'prix' => 12000.00,
            'stock' => 25,
            'auteur' => null,
            'materiel' => 'بلاستيك عالي الجودة، شفرات ستانلس ستيل',
            'category_en' => 'home'
        ]
    ];
    
    $addedProducts = 0;
    foreach ($missingProducts as $index => $product) {
        // Check if product already exists
        $checkStmt = $pdo->prepare("SELECT id FROM produits WHERE titre = ?");
        $checkStmt->execute([$product['titre']]);
        
        if ($checkStmt->rowCount() > 0) {
            echo "<p class='info'>ℹ️ Product already exists: " . htmlspecialchars($product['titre']) . "</p>\n";
            continue;
        }
        
        echo "<p>Creating missing product " . ($index + 1) . ": " . htmlspecialchars($product['titre']) . "</p>\n";
        
        // Get category ID
        $catStmt = $pdo->prepare("SELECT id FROM categories WHERE nom_en = ?");
        $catStmt->execute([$product['category_en']]);
        $categoryId = $catStmt->fetch()['id'] ?? null;
        
        if (!$categoryId) {
            echo "<p class='error'>❌ Category '{$product['category_en']}' not found</p>\n";
            continue;
        }
        
        // Insert product
        $sql = "INSERT INTO produits (category_id, type, titre, description, prix, stock, auteur, materiel, actif) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $categoryId,
            $product['type'],
            $product['titre'],
            $product['description'],
            $product['prix'],
            $product['stock'],
            $product['auteur'],
            $product['materiel']
        ]);
        
        if ($result) {
            $productId = $pdo->lastInsertId();
            echo "<p class='success'>✅ Product created with ID: $productId</p>\n";
            $addedProducts++;
        } else {
            $errorInfo = $stmt->errorInfo();
            echo "<p class='error'>❌ Failed to create product: " . $errorInfo[2] . "</p>\n";
        }
    }
    
    // Verify total products
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM produits WHERE actif = 1");
    $totalProducts = $stmt->fetch()['total'];
    echo "<p class='success'>✅ Total active products: $totalProducts/5</p>\n";
    echo "</div>\n";
    
    // STEP 3: Create Landing Pages Tables (if needed)
    $currentStep++;
    updateProgress($currentStep, $totalSteps);
    
    echo "<div class='step'>\n";
    echo "<h2>🎨 Step 3: Setting Up Landing Pages System</h2>\n";
    
    // Create landing_pages table if not exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'landing_pages'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Creating landing_pages table...</p>\n";
        
        $createLandingPages = "
        CREATE TABLE landing_pages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            titre VARCHAR(255) NOT NULL,
            produit_id INT NOT NULL,
            template_id VARCHAR(50) NOT NULL DEFAULT 'modern',
            contenu_droit TEXT,
            contenu_gauche TEXT,
            image_position ENUM('left', 'right', 'center') DEFAULT 'center',
            text_position ENUM('left', 'right', 'split') DEFAULT 'split',
            meta_description TEXT,
            meta_keywords TEXT,
            lien_url VARCHAR(500),
            actif TINYINT(1) DEFAULT 1,
            date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createLandingPages);
        echo "<p class='success'>✅ Landing pages table created</p>\n";
    } else {
        echo "<p class='success'>✅ Landing pages table exists</p>\n";
    }
    
    // Create landing_page_images table if not exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'landing_page_images'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Creating landing_page_images table...</p>\n";
        
        $createLandingPageImages = "
        CREATE TABLE landing_page_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            landing_page_id INT NOT NULL,
            image_url VARCHAR(500) NOT NULL,
            ordre INT DEFAULT 0,
            date_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (landing_page_id) REFERENCES landing_pages(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createLandingPageImages);
        echo "<p class='success'>✅ Landing page images table created</p>\n";
    } else {
        echo "<p class='success'>✅ Landing page images table exists</p>\n";
    }
    echo "</div>\n";
    
    // STEP 4: Create 5 Landing Pages
    $currentStep++;
    updateProgress($currentStep, $totalSteps);
    
    echo "<div class='step'>\n";
    echo "<h2>🚀 Step 4: Creating 5 Landing Pages</h2>\n";
    
    // Get all products
    $stmt = $pdo->query("SELECT id, titre, type, prix, description FROM produits WHERE actif = 1 ORDER BY id");
    $allProducts = $stmt->fetchAll();
    
    echo "<p class='info'>Found " . count($allProducts) . " products for landing page creation</p>\n";
    
    $templates = ['modern', 'classic', 'minimal', 'elegant', 'professional'];
    $createdPages = 0;
    
    foreach ($allProducts as $index => $product) {
        $template = $templates[$index % count($templates)];
        $landingTitle = "صفحة هبوط - " . $product['titre'];
        
        // Check if landing page already exists for this product
        $checkStmt = $pdo->prepare("SELECT id FROM landing_pages WHERE produit_id = ?");
        $checkStmt->execute([$product['id']]);
        
        if ($checkStmt->rowCount() > 0) {
            echo "<p class='info'>ℹ️ Landing page already exists for: " . htmlspecialchars($product['titre']) . "</p>\n";
            continue;
        }
        
        echo "<p>Creating landing page for: " . htmlspecialchars($product['titre']) . " (Template: $template)</p>\n";
        
        // Generate content based on product
        $rightContent = generateProductFeatures($product);
        $leftContent = generateCallToAction($product);
        
        // Generate meta description
        $metaDescription = "اكتشف " . $product['titre'] . " بأفضل الأسعار. " . 
                          "منتج عالي الجودة مع ضمان وتوصيل مجاني. اطلب الآن!";
        
        // Generate meta keywords
        $metaKeywords = $product['titre'] . ", " . $product['type'] . ", شراء, توصيل مجاني, ضمان, جودة عالية";
        
        $insertStmt = $pdo->prepare("
            INSERT INTO landing_pages (titre, produit_id, template_id, contenu_droit, contenu_gauche, 
                                     meta_description, meta_keywords, lien_url, actif) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
        ");
        
        $result = $insertStmt->execute([
            $landingTitle,
            $product['id'],
            $template,
            $rightContent,
            $leftContent,
            $metaDescription,
            $metaKeywords,
            "/landing-page.php?id=" . $product['id']
        ]);
        
        if ($result) {
            $landingPageId = $pdo->lastInsertId();
            echo "<p class='success'>✅ Landing page created with ID: $landingPageId</p>\n";
            $createdPages++;
        } else {
            $errorInfo = $insertStmt->errorInfo();
            echo "<p class='error'>❌ Failed to create landing page: " . $errorInfo[2] . "</p>\n";
        }
    }
    
    // Final verification
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM landing_pages");
    $totalLandingPages = $stmt->fetch()['total'];
    echo "<p class='success'>✅ Total landing pages: $totalLandingPages/5</p>\n";
    echo "</div>\n";
    
    // Final Summary
    echo "<div class='step'>\n";
    echo "<h2>🎉 System Completion Summary</h2>\n";
    
    // Get final statistics
    $finalStats = [
        'Products' => $pdo->query("SELECT COUNT(*) FROM produits WHERE actif = 1")->fetchColumn(),
        'Categories' => $pdo->query("SELECT COUNT(*) FROM categories WHERE actif = 1")->fetchColumn(),
        'Landing Pages' => $pdo->query("SELECT COUNT(*) FROM landing_pages")->fetchColumn(),
        'Landing Page Images' => $pdo->query("SELECT COUNT(*) FROM landing_page_images")->fetchColumn()
    ];
    
    echo "<table>\n";
    echo "<tr><th>Component</th><th>Count</th><th>Target</th><th>Status</th></tr>\n";
    
    $statusMap = [
        'Products' => ['target' => 5, 'icon' => '📦'],
        'Categories' => ['target' => 5, 'icon' => '🗂️'],
        'Landing Pages' => ['target' => 5, 'icon' => '🎨'],
        'Landing Page Images' => ['target' => 0, 'icon' => '🖼️']
    ];
    
    $allComplete = true;
    foreach ($finalStats as $component => $count) {
        $target = $statusMap[$component]['target'];
        $icon = $statusMap[$component]['icon'];
        $status = ($count >= $target) ? '✅ Complete' : '⚠️ Incomplete';
        if ($count < $target && $component !== 'Landing Page Images') $allComplete = false;
        
        echo "<tr>";
        echo "<td>$icon $component</td>";
        echo "<td>$count</td>";
        echo "<td>$target</td>";
        echo "<td>$status</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<div class='highlight'>\n";
    if ($allComplete) {
        echo "<h3>🎉 System Fully Operational!</h3>\n";
        echo "<p class='success'>All required components have been successfully created and configured.</p>\n";
    } else {
        echo "<h3>⚠️ System Partially Complete</h3>\n";
        echo "<p class='warning'>Some components may need additional attention.</p>\n";
    }
    
    echo "<h4>🔗 Quick Access Links:</h4>\n";
    echo "<div style='display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin:20px 0;'>\n";
    echo "<a href='admin/' style='background:#007bff;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🏠 Admin Panel</a>\n";
    echo "<a href='test_complete_system.php' style='background:#28a745;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🧪 System Test</a>\n";
    echo "<a href='php/api/products.php' style='background:#fd7e14;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>📦 Products API</a>\n";
    echo "<a href='php/api/landing-pages.php' style='background:#6f42c1;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🎨 Landing Pages API</a>\n";
    echo "</div>\n";
    echo "</div>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='step'>\n";
    echo "<h2 class='error'>❌ Critical Error</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    echo "</div>\n";
}

// Helper functions for content generation
function generateProductFeatures($product) {
    $content = "<h3>🎯 مميزات " . htmlspecialchars($product['titre']) . "</h3>\n";
    $content .= "<div style='background:#f8f9fa;padding:20px;border-radius:10px;margin:15px 0;'>\n";
    
    // Extract features from description or create generic ones
    if (strpos($product['description'], '<ul>') !== false) {
        // Use existing description if it has structured content
        $content .= $product['description'];
    } else {
        // Generate features based on product type
        switch ($product['type']) {
            case 'book':
                $content .= "<h4>📚 لماذا هذا الكتاب؟</h4>\n";
                $content .= "<ul><li>محتوى عملي وقابل للتطبيق</li><li>أسلوب سهل ومفهوم</li><li>تجارب حقيقية وملهمة</li><li>نصائح عملية للحياة اليومية</li></ul>\n";
                break;
            case 'laptop':
                $content .= "<h4>💻 مواصفات متقدمة</h4>\n";
                $content .= "<ul><li>أداء سريع وموثوق</li><li>تصميم أنيق ومحمول</li><li>بطارية طويلة المدى</li><li>ضمان شامل</li></ul>\n";
                break;
            case 'bag':
                $content .= "<h4>🎒 تصميم عملي</h4>\n";
                $content .= "<ul><li>مقاوم للماء والعوامل الجوية</li><li>جيوب متعددة للتنظيم</li><li>راحة في الحمل</li><li>جودة عالية ومتانة</li></ul>\n";
                break;
            case 'clothing':
                $content .= "<h4>👔 جودة استثنائية</h4>\n";
                $content .= "<ul><li>خامات عالية الجودة</li><li>تصميم أنيق وعصري</li><li>راحة في الارتداء</li><li>سهولة في العناية</li></ul>\n";
                break;
            case 'home':
                $content .= "<h4>🏠 كفاءة عالية</h4>\n";
                $content .= "<ul><li>أداء قوي وموثوق</li><li>سهولة في الاستخدام</li><li>توفير في الوقت والجهد</li><li>ضمان وخدمة ما بعد البيع</li></ul>\n";
                break;
            default:
                $content .= "<h4>✨ مميزات المنتج</h4>\n";
                $content .= "<ul><li>جودة عالية ومضمونة</li><li>سعر منافس ومناسب</li><li>خدمة عملاء ممتازة</li><li>ضمان شامل</li></ul>\n";
        }
    }
    
    $content .= "</div>\n";
    $content .= "<div style='background:#e8f5e9;padding:15px;border-radius:8px;text-align:center;'>\n";
    $content .= "<h4 style='color:#28a745;margin:0;'>💰 السعر: " . number_format($product['prix'], 0) . " دج</h4>\n";
    $content .= "<p style='margin:10px 0 0 0;color:#666;'>شامل التوصيل المجاني</p>\n";
    $content .= "</div>\n";
    
    return $content;
}

function generateCallToAction($product) {
    $content = "<h3>📞 اطلب الآن واحصل على عرض خاص!</h3>\n";
    $content .= "<div style='background:#fff3cd;padding:20px;border-radius:10px;margin:15px 0;'>\n";
    $content .= "<h4>🎁 عروض محدودة الوقت:</h4>\n";
    $content .= "<ul><li>خصم 15% للطلبات اليوم</li><li>توصيل مجاني لجميع أنحاء الجزائر</li><li>إمكانية الدفع عند الاستلام</li><li>ضمان استرداد المال خلال 7 أيام</li></ul>\n";
    $content .= "</div>\n";
    
    $content .= "<div style='background:#e8f4fd;padding:20px;border-radius:10px;margin:15px 0;'>\n";
    $content .= "<h4>📱 طرق التواصل:</h4>\n";
    $content .= "<ul>\n";
    $content .= "<li><strong>الهاتف:</strong> 0123 456 789</li>\n";
    $content .= "<li><strong>واتساب:</strong> 0123 456 789</li>\n";
    $content .= "<li><strong>البريد الإلكتروني:</strong> <EMAIL></li>\n";
    $content .= "<li><strong>العنوان:</strong> الجزائر العاصمة</li>\n";
    $content .= "</ul>\n";
    $content .= "</div>\n";
    
    $content .= "<div style='background:#f8d7da;padding:15px;border-radius:8px;text-align:center;'>\n";
    $content .= "<h4 style='color:#721c24;margin:0;'>⏰ العرض ساري لفترة محدودة!</h4>\n";
    $content .= "<p style='margin:10px 0 0 0;'>لا تفوت هذه الفرصة الذهبية</p>\n";
    $content .= "</div>\n";
    
    return $content;
}

echo "</div>\n";
?>

<script>
console.log('🎯 Missing components completion finished!');
console.log('MariaDB database: mossab-landing-page on localhost:3307');
</script>
