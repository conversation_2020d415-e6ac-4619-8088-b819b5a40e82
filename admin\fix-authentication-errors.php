<?php
/**
 * Fix Authentication and Critical Admin Panel Errors
 * Addresses JSON parse errors, network failures, and API connectivity issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح أخطاء المصادقة ولوحة التحكم</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .fix-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .fix-button:hover {
            background: #0056b3;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .test-url {
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        .credentials {
            background: #fff3cd;
            border: 2px solid #ffc107;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح أخطاء المصادقة ولوحة التحكم</h1>
        <p>هذا السكريبت يحل أخطاء JSON parse، مشاكل الشبكة، وأخطاء API في لوحة التحكم.</p>

        <?php
        $allIssuesFixed = true;
        $fixedIssues = [];
        $remainingIssues = [];

        try {
            // Fix 1: Test Authentication Function
            echo '<div class="fix-section">';
            echo '<h3>🔐 إصلاح 1: اختبار دالة المصادقة</h3>';
            
            try {
                require_once '../php/config.php';
                echo '<div class="result pass">✅ تم تحميل config.php بنجاح</div>';
                
                if (function_exists('isAdminLoggedIn')) {
                    echo '<div class="result pass">✅ دالة isAdminLoggedIn موجودة</div>';
                    $fixedIssues[] = 'دالة المصادقة موجودة';
                    
                    // Test the function
                    $isLoggedIn = isAdminLoggedIn();
                    echo '<div class="result info">📋 حالة تسجيل الدخول الحالية: ' . ($isLoggedIn ? 'مسجل الدخول' : 'غير مسجل الدخول') . '</div>';
                    
                } else {
                    echo '<div class="result fail">❌ دالة isAdminLoggedIn مفقودة</div>';
                    $remainingIssues[] = 'دالة المصادقة مفقودة';
                    $allIssuesFixed = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في تحميل config.php: ' . $e->getMessage() . '</div>';
                $remainingIssues[] = 'خطأ في تحميل التكوين';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 2: Test Admin.php Authentication Endpoint
            echo '<div class="fix-section">';
            echo '<h3>🔌 إصلاح 2: اختبار نقطة نهاية المصادقة</h3>';
            
            $adminFile = '../php/admin.php';
            if (file_exists($adminFile)) {
                echo '<div class="result pass">✅ ملف admin.php موجود</div>';
                
                // Test the check action
                try {
                    ob_start();
                    $_SERVER['REQUEST_METHOD'] = 'GET';
                    $_GET['action'] = 'check';
                    
                    include $adminFile;
                    $output = ob_get_clean();
                    
                    echo '<div class="result info">📋 استجابة نقطة نهاية المصادقة:</div>';
                    echo '<pre>' . htmlspecialchars($output) . '</pre>';
                    
                    // Try to parse as JSON
                    $data = json_decode($output, true);
                    if ($data !== null) {
                        echo '<div class="result pass">✅ الاستجابة JSON صالحة</div>';
                        echo '<div class="result info">📊 البيانات: logged_in = ' . ($data['logged_in'] ? 'true' : 'false') . '</div>';
                        $fixedIssues[] = 'نقطة نهاية المصادقة تعمل';
                    } else {
                        echo '<div class="result fail">❌ الاستجابة ليست JSON صالحة</div>';
                        $remainingIssues[] = 'استجابة المصادقة غير صالحة';
                        $allIssuesFixed = false;
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="result fail">❌ خطأ في اختبار admin.php: ' . $e->getMessage() . '</div>';
                    $remainingIssues[] = 'خطأ في نقطة نهاية المصادقة';
                    $allIssuesFixed = false;
                }
                
            } else {
                echo '<div class="result fail">❌ ملف admin.php غير موجود</div>';
                $remainingIssues[] = 'ملف admin.php مفقود';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 3: Test Arabic Language File
            echo '<div class="fix-section">';
            echo '<h3>🌐 إصلاح 3: اختبار ملف اللغة العربية</h3>';
            
            $langFile = 'js/langs/ar.js';
            if (file_exists($langFile)) {
                echo '<div class="result pass">✅ ملف اللغة العربية موجود</div>';
                
                // Check file size and content
                $fileSize = filesize($langFile);
                echo '<div class="result info">📊 حجم الملف: ' . $fileSize . ' بايت</div>';
                
                if ($fileSize > 0) {
                    $content = file_get_contents($langFile);
                    if (strpos($content, "tinymce.addI18n('ar'") !== false) {
                        echo '<div class="result pass">✅ ملف اللغة صحيح التنسيق</div>';
                        $fixedIssues[] = 'ملف اللغة العربية صحيح';
                    } else {
                        echo '<div class="result fail">❌ ملف اللغة تنسيق خاطئ</div>';
                        $remainingIssues[] = 'تنسيق ملف اللغة خاطئ';
                        $allIssuesFixed = false;
                    }
                } else {
                    echo '<div class="result fail">❌ ملف اللغة فارغ</div>';
                    $remainingIssues[] = 'ملف اللغة فارغ';
                    $allIssuesFixed = false;
                }
                
                // Check file permissions
                if (is_readable($langFile)) {
                    echo '<div class="result pass">✅ ملف اللغة قابل للقراءة</div>';
                } else {
                    echo '<div class="result fail">❌ ملف اللغة غير قابل للقراءة</div>';
                    $remainingIssues[] = 'صلاحيات ملف اللغة';
                    $allIssuesFixed = false;
                }
                
            } else {
                echo '<div class="result fail">❌ ملف اللغة العربية غير موجود</div>';
                $remainingIssues[] = 'ملف اللغة العربية مفقود';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 4: Test API Endpoints
            echo '<div class="fix-section">';
            echo '<h3>📊 إصلاح 4: اختبار نقاط نهاية API</h3>';
            
            $apiEndpoints = [
                '../php/api/dashboard-stats.php' => 'إحصائيات لوحة التحكم',
                '../php/api/products.php' => 'API المنتجات',
                '../php/api/categories.php' => 'API الفئات'
            ];
            
            foreach ($apiEndpoints as $endpoint => $description) {
                if (file_exists($endpoint)) {
                    echo '<div class="result pass">✅ ' . $description . ': الملف موجود</div>';
                    
                    // Check if file is readable
                    if (is_readable($endpoint)) {
                        echo '<div class="result pass">✅ ' . $description . ': قابل للقراءة</div>';
                        $fixedIssues[] = $description . ' متاح';
                    } else {
                        echo '<div class="result fail">❌ ' . $description . ': غير قابل للقراءة</div>';
                        $remainingIssues[] = $description . ' غير قابل للقراءة';
                        $allIssuesFixed = false;
                    }
                } else {
                    echo '<div class="result fail">❌ ' . $description . ': الملف غير موجود</div>';
                    $remainingIssues[] = $description . ' مفقود';
                    $allIssuesFixed = false;
                }
            }
            echo '</div>';

            // Fix 5: CSS Writing Mode Check
            echo '<div class="fix-section">';
            echo '<h3>🎨 إصلاح 5: فحص CSS Writing Mode</h3>';
            
            $cssFiles = [
                'css/admin.css' => 'CSS لوحة التحكم',
                'css/critical-fixes.css' => 'CSS الإصلاحات الحرجة'
            ];
            
            foreach ($cssFiles as $file => $description) {
                if (file_exists($file)) {
                    $content = file_get_contents($file);
                    
                    // Check if :root has writing-mode properties
                    if (preg_match('/:root\s*{[^}]*writing-mode\s*:\s*horizontal-tb[^}]*}/s', $content)) {
                        echo '<div class="result pass">✅ ' . $description . ': writing-mode في :root</div>';
                        $fixedIssues[] = $description . ' - writing-mode صحيح';
                    } else {
                        echo '<div class="result warning">⚠️ ' . $description . ': writing-mode قد يحتاج تحديث</div>';
                    }
                    
                    // Check if :root has direction
                    if (preg_match('/:root\s*{[^}]*direction\s*:\s*rtl[^}]*}/s', $content)) {
                        echo '<div class="result pass">✅ ' . $description . ': direction RTL في :root</div>';
                    } else {
                        echo '<div class="result warning">⚠️ ' . $description . ': direction RTL قد يحتاج تحديث</div>';
                    }
                    
                } else {
                    echo '<div class="result warning">⚠️ ملف CSS غير موجود: ' . $file . '</div>';
                }
            }
            echo '</div>';

            // Fix 6: Database Connection Test
            echo '<div class="fix-section">';
            echo '<h3>🗄️ إصلاح 6: اختبار اتصال قاعدة البيانات</h3>';
            
            try {
                $pdo = getPDOConnection();
                echo '<div class="result pass">✅ اتصال قاعدة البيانات ناجح</div>';
                
                // Check if admins table exists and has data
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM admins WHERE actif = 1");
                $adminCount = $stmt->fetch()['count'];
                
                if ($adminCount > 0) {
                    echo '<div class="result pass">✅ يوجد ' . $adminCount . ' مستخدم إدارة نشط</div>';
                    $fixedIssues[] = 'قاعدة البيانات ومستخدمي الإدارة';
                } else {
                    echo '<div class="result warning">⚠️ لا يوجد مستخدمين إدارة نشطين</div>';
                    echo '<div class="result info">💡 قم بتشغيل setup-admin-user.php لإنشاء مستخدم افتراضي</div>';
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في قاعدة البيانات: ' . $e->getMessage() . '</div>';
                $remainingIssues[] = 'مشكلة في قاعدة البيانات';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Summary
            echo '<div class="fix-section">';
            echo '<h3>📊 ملخص الإصلاحات</h3>';
            
            if ($allIssuesFixed) {
                echo '<div class="result pass">🎉 تم إصلاح جميع المشاكل الحرجة!</div>';
                echo '<div class="result pass">✅ لوحة التحكم جاهزة للاستخدام</div>';
            } else {
                echo '<div class="result warning">⚠️ تم إصلاح معظم المشاكل، بعض المشاكل تحتاج تدخل يدوي</div>';
            }
            
            if (!empty($fixedIssues)) {
                echo '<h4>✅ المشاكل المُصلحة:</h4>';
                echo '<ul>';
                foreach ($fixedIssues as $issue) {
                    echo '<li>' . $issue . '</li>';
                }
                echo '</ul>';
            }
            
            if (!empty($remainingIssues)) {
                echo '<h4>⚠️ المشاكل المتبقية:</h4>';
                echo '<ul>';
                foreach ($remainingIssues as $issue) {
                    echo '<li>' . $issue . '</li>';
                }
                echo '</ul>';
            }
            
            echo '<h4>🧪 اختبار الوظائف:</h4>';
            echo '<p><a href="../php/admin.php?action=check" class="fix-button" target="_blank">اختبار نقطة نهاية المصادقة</a></p>';
            echo '<p><a href="setup-admin-user.php" class="fix-button">إعداد مستخدم الإدارة</a></p>';
            echo '<p><a href="index.html" class="fix-button">فتح لوحة التحكم</a></p>';
            echo '<p><a href="login.html" class="fix-button">صفحة تسجيل الدخول</a></p>';
            
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
        }
        ?>

    </div>

    <script>
        // Test authentication endpoint via JavaScript
        async function testAuthEndpoint() {
            try {
                console.log('Testing authentication endpoint...');
                const response = await fetch('../php/admin.php?action=check');
                console.log('Auth endpoint response status:', response.status);
                
                const text = await response.text();
                console.log('Auth endpoint raw response:', text);
                
                try {
                    const data = JSON.parse(text);
                    console.log('Auth endpoint parsed data:', data);
                    
                    // Show result in page
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'result pass';
                    resultDiv.innerHTML = '✅ JavaScript: نقطة نهاية المصادقة تعمل - logged_in: ' + data.logged_in;
                    document.querySelector('.container').appendChild(resultDiv);
                    
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'result fail';
                    resultDiv.innerHTML = '❌ JavaScript: خطأ في تحليل JSON - ' + parseError.message;
                    document.querySelector('.container').appendChild(resultDiv);
                }
                
            } catch (error) {
                console.error('Network error:', error);
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result fail';
                resultDiv.innerHTML = '❌ JavaScript: خطأ في الشبكة - ' + error.message;
                document.querySelector('.container').appendChild(resultDiv);
            }
        }
        
        // Run test after page loads
        document.addEventListener('DOMContentLoaded', testAuthEndpoint);
    </script>
</body>
</html>
