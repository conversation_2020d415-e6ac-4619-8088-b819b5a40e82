<?php

/**
 * Security Manager for Mossaab Landing Page
 * Handles API key validation, CSRF protection, and rate limiting
 */

class Security
{
    private static $instance = null;
    private $rateLimits = [];
    private $csrfToken;

    private function __construct()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $this->initializeCsrfToken();
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Validate API key format and check connection for AI providers
     */
    public static function checkAIProviderStatus($provider, $apiKey)
    {
        if (empty($apiKey)) {
            return [
                'enabled' => false,
                'status' => 'error',
                'message' => 'مفتاح API غير محدد',
                'ar_message' => 'مفتاح API غير محدد'
            ];
        }

        // Validate API key format
        if (!self::validateApiKeyFormat($provider, $apiKey)) {
            return [
                'enabled' => false,
                'status' => 'error',
                'message' => 'تنسيق مفتاح API غير صالح',
                'ar_message' => 'تنسيق مفتاح API غير صالح'
            ];
        }

        // Test connection
        try {
            $testResult = self::testProviderConnection($provider, $apiKey);
            return [
                'enabled' => $testResult['success'],
                'status' => $testResult['success'] ? 'connected' : 'error',
                'message' => $testResult['message'],
                'ar_message' => $testResult['ar_message'] ?? $testResult['message']
            ];
        } catch (Exception $e) {
            error_log("AI Provider Connection Error ($provider): " . $e->getMessage());
            return [
                'enabled' => false,
                'status' => 'error',
                'message' => 'خطأ في الاتصال: ' . $e->getMessage(),
                'ar_message' => 'خطأ في الاتصال: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate API key format for different providers
     */
    private static function validateApiKeyFormat($provider, $apiKey)
    {
        switch ($provider) {
            case 'openai':
                return preg_match('/^sk-(test|live|proj)-[a-zA-Z0-9-]{32,}$/', $apiKey);
            case 'anthropic':
                return preg_match('/^sk-ant-[a-zA-Z0-9-]{32,}$/', $apiKey);
            case 'gemini':
                return preg_match('/^AIza[0-9A-Za-z-_]{35}$/', $apiKey);
            default:
                return false;
        }
    }

    /**
     * Test connection to AI provider
     */
    private static function testProviderConnection($provider, $apiKey)
    {
        switch ($provider) {
            case 'openai':
                return self::testOpenAIConnection($apiKey);
            case 'anthropic':
                return self::testAnthropicConnection($apiKey);
            case 'gemini':
                return self::testGeminiConnection($apiKey);
            default:
                throw new Exception('مزود AI غير معروف');
        }
    }

    private static function testOpenAIConnection($apiKey)
    {
        $ch = curl_init('https://api.openai.com/v1/models');
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $apiKey,
                'Content-Type: application/json'
            ]
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            return [
                'success' => true,
                'message' => 'OpenAI API متصل',
                'ar_message' => 'OpenAI API متصل'
            ];
        }

        $error = json_decode($response, true);
        return [
            'success' => false,
            'message' => $error['error']['message'] ?? 'فشل الاتصال بـ OpenAI API',
            'ar_message' => 'فشل الاتصال بـ OpenAI API'
        ];
    }

    private static function testAnthropicConnection($apiKey)
    {
        $ch = curl_init('https://api.anthropic.com/v1/messages');
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'x-api-key: ' . $apiKey,
                'anthropic-version: 2023-06-01',
                'Content-Type: application/json'
            ]
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200 || $httpCode === 401) { // 401 means key is valid but request format is wrong
            return [
                'success' => true,
                'message' => 'Anthropic API متصل',
                'ar_message' => 'Anthropic API متصل'
            ];
        }

        return [
            'success' => false,
            'message' => 'فشل الاتصال بـ Anthropic API',
            'ar_message' => 'فشل الاتصال بـ Anthropic API'
        ];
    }

    private static function testGeminiConnection($apiKey)
    {
        $ch = curl_init('https://generativelanguage.googleapis.com/v1beta/models?key=' . $apiKey);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => ['Content-Type: application/json']
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            return [
                'success' => true,
                'message' => 'Google Gemini API متصل',
                'ar_message' => 'Google Gemini API متصل'
            ];
        }

        return [
            'success' => false,
            'message' => 'فشل الاتصال بـ Google Gemini API',
            'ar_message' => 'فشل الاتصال بـ Google Gemini API'
        ];
    }

    /**
     * CSRF Protection
     */
    private function initializeCsrfToken()
    {
        if (empty($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        $this->csrfToken = $_SESSION['csrf_token'];
    }

    public function getCsrfToken()
    {
        return $this->csrfToken;
    }

    public function validateCsrfToken($token)
    {
        if (empty($token) || $token !== $this->csrfToken) {
            throw new Exception('CSRF التحقق من صحة الرمز فشل');
        }
        return true;
    }

    /**
     * Rate Limiting
     */
    public function checkRateLimit($key, $limit = 60, $window = 60)
    {
        $now = time();
        if (!isset($this->rateLimits[$key])) {
            $this->rateLimits[$key] = ['count' => 0, 'window_start' => $now];
        }

        if ($now - $this->rateLimits[$key]['window_start'] > $window) {
            $this->rateLimits[$key] = ['count' => 0, 'window_start' => $now];
        }

        $this->rateLimits[$key]['count']++;

        if ($this->rateLimits[$key]['count'] > $limit) {
            throw new Exception('تم تجاوز حد معدل الطلبات. يرجى المحاولة مرة أخرى لاحقًا.');
        }

        return true;
    }

    /**
     * Encrypt sensitive data
     */
    public static function encryptData($data, $key = null)
    {
        if ($key === null) {
            $key = Config::get('APP_KEY');
        }

        $ivLength = openssl_cipher_iv_length('AES-256-CBC');
        $iv = openssl_random_pseudo_bytes($ivLength);

        $encrypted = openssl_encrypt(
            $data,
            'AES-256-CBC',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );

        return base64_encode($iv . $encrypted);
    }

    /**
     * Decrypt sensitive data
     */
    public static function decryptData($data, $key = null)
    {
        if ($key === null) {
            $key = Config::get('APP_KEY');
        }

        $data = base64_decode($data);
        $ivLength = openssl_cipher_iv_length('AES-256-CBC');
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);

        return openssl_decrypt(
            $encrypted,
            'AES-256-CBC',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );
    }
}
