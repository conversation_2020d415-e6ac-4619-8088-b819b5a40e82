<?php
/**
 * Test Templates API Directly
 */

echo "Testing Templates API directly...\n\n";

// Change to the API directory context
chdir('php/api');

echo "Current directory: " . getcwd() . "\n";
echo "Config file exists: " . (file_exists('../config.php') ? 'YES' : 'NO') . "\n\n";

// Set up the environment for the API
$_GET['action'] = 'get_templates';

echo "Testing templates.php inclusion:\n";

try {
    ob_start();
    include 'templates.php';
    $output = ob_get_clean();
    
    echo "✅ Templates API included successfully\n";
    echo "Output length: " . strlen($output) . " characters\n";
    
    // Try to parse as JSON
    $data = json_decode($output, true);
    if ($data) {
        echo "✅ Valid JSON response\n";
        if (isset($data['success'])) {
            echo "Success status: " . ($data['success'] ? 'true' : 'false') . "\n";
        }
        if (isset($data['templates'])) {
            echo "Templates count: " . count($data['templates']) . "\n";
        }
    } else {
        echo "⚠️ Response is not valid JSON\n";
        echo "Raw output: " . substr($output, 0, 200) . "...\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
}

echo "\nDone.\n";
?>
