<?php
require_once __DIR__ . '/bootstrap.php';

class Database
{
    private static $instance = null;
    private $pdo;

    private function __construct()
    {
        $this->connect();
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection()
    {
        return $this->pdo;
    }

    private function connect()
    {
        try {
            $dbConfig = Config::getDbConfig();

            $dsn = sprintf(
                "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
                $dbConfig['host'],
                $dbConfig['port'],
                $dbConfig['database']
            );

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];

            $this->pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);

            // Set timezone to match PHP's timezone
            $timezone = date('P');
            $this->pdo->exec("SET time_zone='$timezone';");
            $this->pdo->exec("SET NAMES utf8mb4;");
        } catch (PDOException $e) {
            error_log('Database Connection Error: ' . $e->getMessage());
            throw new Exception('Database connection failed. Please check your configuration.');
        }
    }

    public function query($sql, $params = [])
    {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log('Database Query Error: ' . $e->getMessage());
            throw new Exception('Database query failed.');
        }
    }

    public function beginTransaction()
    {
        return $this->pdo->beginTransaction();
    }

    public function commit()
    {
        return $this->pdo->commit();
    }

    public function rollBack()
    {
        return $this->pdo->rollBack();
    }

    public function lastInsertId()
    {
        return $this->pdo->lastInsertId();
    }

    public function quote($value)
    {
        return $this->pdo->quote($value);
    }
}

// Helper function for backward compatibility
function getDatabaseConnection()
{
    return Database::getInstance()->getConnection();
}

// Initialize database connection
try {
    $db = Database::getInstance();
} catch (Exception $e) {
    if (Config::get('APP_DEBUG', false)) {
        die('Database Error: ' . $e->getMessage());
    } else {
        die('A database error occurred. Please try again later.');
    }
}
