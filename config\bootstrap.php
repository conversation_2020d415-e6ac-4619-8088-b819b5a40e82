<?php

/**
 * Application Bootstrap File
 * Initializes core components and configuration
 */

// Set error reporting based on environment
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Define root path
define('ROOT_PATH', dirname(__DIR__));

// Load configuration
require_once ROOT_PATH . '/config/config.php';

// Initialize configuration
if (!class_exists('Config')) {
    die('Configuration system not found');
}

Config::init();

// Set error display based on configuration
if (Config::get('APP_DEBUG', false)) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}

// Set timezone
date_default_timezone_set('UTC');

// Initialize error logging
$logFile = ROOT_PATH . '/logs/app.log';
if (!file_exists(dirname($logFile))) {
    mkdir(dirname($logFile), 0777, true);
}
ini_set('error_log', $logFile);

// Function to handle uncaught exceptions
function handleException($e)
{
    error_log($e->getMessage());
    if (Config::get('APP_DEBUG', false)) {
        echo 'Error: ' . $e->getMessage();
    } else {
        echo 'An application error occurred. Please try again later.';
    }
    exit(1);
}
set_exception_handler('handleException');
