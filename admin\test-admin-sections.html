<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أقسام الإدارة المحسنة - مصعب لاندينغ بيج</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }

        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #f8fafc;
        }

        .test-section h3 {
            color: #1e293b;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .test-button {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }

        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            display: none;
        }

        .test-result.success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }

        .test-result.error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }

        .test-result.info {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .feature-test {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .feature-test h4 {
            color: #374151;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 0.5rem;
        }

        .status-indicator.pending {
            background: #f59e0b;
        }

        .status-indicator.success {
            background: #10b981;
        }

        .status-indicator.error {
            background: #ef4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> اختبار أقسام الإدارة المحسنة</h1>
            <p>اختبار شامل لجميع الأقسام المحسنة في لوحة الإدارة</p>
        </div>

        <!-- Store Settings Test -->
        <div class="test-section">
            <h3><i class="fas fa-store"></i> اختبار إعدادات المتجر</h3>
            <div class="feature-test">
                <h4>
                    <i class="fas fa-cog"></i>
                    تحميل إعدادات المتجر
                    <span class="status-indicator pending" id="storeSettingsStatus"></span>
                </h4>
                <button class="test-button" onclick="testStoreSettings()">
                    <i class="fas fa-play"></i>
                    اختبار إعدادات المتجر
                </button>
                <div class="test-result" id="storeSettingsResult"></div>
            </div>
        </div>

        <!-- Categories Management Test -->
        <div class="test-section">
            <h3><i class="fas fa-tags"></i> اختبار إدارة الفئات</h3>
            <div class="feature-test">
                <h4>
                    <i class="fas fa-list"></i>
                    تحميل وإدارة الفئات
                    <span class="status-indicator pending" id="categoriesStatus"></span>
                </h4>
                <button class="test-button" onclick="testCategoriesManagement()">
                    <i class="fas fa-play"></i>
                    اختبار إدارة الفئات
                </button>
                <div class="test-result" id="categoriesResult"></div>
            </div>
        </div>

        <!-- Payment Settings Test -->
        <div class="test-section">
            <h3><i class="fas fa-credit-card"></i> اختبار إعدادات الدفع</h3>
            <div class="test-grid">
                <div class="feature-test">
                    <h4>
                        <i class="fas fa-money-bill-wave"></i>
                        الدفع عند الاستلام
                        <span class="status-indicator pending" id="codStatus"></span>
                    </h4>
                    <button class="test-button" onclick="testCODPayment()">
                        <i class="fas fa-play"></i>
                        اختبار الدفع عند الاستلام
                    </button>
                    <div class="test-result" id="codResult"></div>
                </div>

                <div class="feature-test">
                    <h4>
                        <i class="fas fa-university"></i>
                        الحساب الجاري البريدي
                        <span class="status-indicator pending" id="ccpStatus"></span>
                    </h4>
                    <button class="test-button" onclick="testCCPPayment()">
                        <i class="fas fa-play"></i>
                        اختبار CCP
                    </button>
                    <div class="test-result" id="ccpResult"></div>
                </div>

                <div class="feature-test">
                    <h4>
                        <i class="fas fa-mobile-alt"></i>
                        بريدي موب
                        <span class="status-indicator pending" id="baridimobStatus"></span>
                    </h4>
                    <button class="test-button" onclick="testBaridiMobPayment()">
                        <i class="fas fa-play"></i>
                        اختبار بريدي موب
                    </button>
                    <div class="test-result" id="baridimobResult"></div>
                </div>
            </div>
        </div>

        <!-- User Management Test -->
        <div class="test-section">
            <h3><i class="fas fa-users"></i> اختبار إدارة المستخدمين</h3>
            <div class="test-grid">
                <div class="feature-test">
                    <h4>
                        <i class="fas fa-user-plus"></i>
                        تحميل المستخدمين
                        <span class="status-indicator pending" id="usersLoadStatus"></span>
                    </h4>
                    <button class="test-button" onclick="testUsersLoad()">
                        <i class="fas fa-play"></i>
                        اختبار تحميل المستخدمين
                    </button>
                    <div class="test-result" id="usersLoadResult"></div>
                </div>

                <div class="feature-test">
                    <h4>
                        <i class="fas fa-crown"></i>
                        إدارة الاشتراكات
                        <span class="status-indicator pending" id="subscriptionStatus"></span>
                    </h4>
                    <button class="test-button" onclick="testSubscriptionManagement()">
                        <i class="fas fa-play"></i>
                        اختبار الاشتراكات
                    </button>
                    <div class="test-result" id="subscriptionResult"></div>
                </div>

                <div class="feature-test">
                    <h4>
                        <i class="fas fa-tasks"></i>
                        الإجراءات المجمعة
                        <span class="status-indicator pending" id="bulkActionsStatus"></span>
                    </h4>
                    <button class="test-button" onclick="testBulkActions()">
                        <i class="fas fa-play"></i>
                        اختبار الإجراءات المجمعة
                    </button>
                    <div class="test-result" id="bulkActionsResult"></div>
                </div>
            </div>
        </div>

        <!-- Multi-User System Test -->
        <div class="test-section">
            <h3><i class="fas fa-users-cog"></i> اختبار النظام متعدد المستخدمين</h3>
            <div class="test-grid">
                <div class="feature-test">
                    <h4>
                        <i class="fas fa-user-shield"></i>
                        إدارة الأدوار
                        <span class="status-indicator pending" id="rolesStatus"></span>
                    </h4>
                    <button class="test-button" onclick="testRolesManagement()">
                        <i class="fas fa-play"></i>
                        اختبار الأدوار
                    </button>
                    <div class="test-result" id="rolesResult"></div>
                </div>

                <div class="feature-test">
                    <h4>
                        <i class="fas fa-crown"></i>
                        إدارة الاشتراكات
                        <span class="status-indicator pending" id="subscriptionsStatus"></span>
                    </h4>
                    <button class="test-button" onclick="testSubscriptionsManagement()">
                        <i class="fas fa-play"></i>
                        اختبار الاشتراكات
                    </button>
                    <div class="test-result" id="subscriptionsResult"></div>
                </div>

                <div class="feature-test">
                    <h4>
                        <i class="fas fa-database"></i>
                        قاعدة البيانات متعددة المستخدمين
                        <span class="status-indicator pending" id="multiUserDbStatus"></span>
                    </h4>
                    <button class="test-button" onclick="testMultiUserDatabase()">
                        <i class="fas fa-play"></i>
                        اختبار قاعدة البيانات
                    </button>
                    <div class="test-result" id="multiUserDbResult"></div>
                </div>

                <div class="feature-test">
                    <h4>
                        <i class="fas fa-store"></i>
                        المتاجر الشخصية
                        <span class="status-indicator pending" id="personalStoresStatus"></span>
                    </h4>
                    <button class="test-button" onclick="testPersonalStores()">
                        <i class="fas fa-play"></i>
                        اختبار المتاجر الشخصية
                    </button>
                    <div class="test-result" id="personalStoresResult"></div>
                </div>
            </div>
        </div>

        <!-- Dashboard Database Test -->
        <div class="test-section">
            <h3><i class="fas fa-chart-line"></i> اختبار لوحة المعلومات</h3>
            <div class="feature-test">
                <h4>
                    <i class="fas fa-database"></i>
                    اتصال قاعدة البيانات
                    <span class="status-indicator pending" id="dashboardDbStatus"></span>
                </h4>
                <button class="test-button" onclick="testDashboardDatabase()">
                    <i class="fas fa-play"></i>
                    اختبار لوحة المعلومات
                </button>
                <div class="test-result" id="dashboardDbResult"></div>
            </div>
        </div>

        <!-- Integration Test -->
        <div class="test-section">
            <h3><i class="fas fa-puzzle-piece"></i> اختبار التكامل الشامل</h3>
            <div class="feature-test">
                <h4>
                    <i class="fas fa-check-double"></i>
                    اختبار جميع الأقسام معاً
                    <span class="status-indicator pending" id="integrationStatus"></span>
                </h4>
                <button class="test-button" onclick="runFullIntegrationTest()">
                    <i class="fas fa-rocket"></i>
                    تشغيل الاختبار الشامل
                </button>
                <div class="test-result" id="integrationResult"></div>
            </div>
        </div>

        <!-- Test Summary -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> ملخص النتائج</h3>
            <div id="testSummary" class="test-grid">
                <div class="feature-test">
                    <h4>إجمالي الاختبارات: <span id="totalTests">0</span></h4>
                </div>
                <div class="feature-test">
                    <h4>اختبارات ناجحة: <span id="passedTests">0</span></h4>
                </div>
                <div class="feature-test">
                    <h4>اختبارات فاشلة: <span id="failedTests">0</span></h4>
                </div>
                <div class="feature-test">
                    <h4>معدل النجاح: <span id="successRate">0%</span></h4>
                </div>
            </div>
        </div>
    </div>

    <!-- Include all admin scripts -->
    <script src="js/store-settings.js"></script>
    <script src="js/categories-management.js"></script>
    <script src="js/payment-settings.js"></script>
    <script src="js/user-management.js"></script>
    <script src="js/admin.js"></script>

    <!-- Test Script -->
    <script src="js/admin-tests.js"></script>
</body>
</html>
