# Deployment Guide: Hosting Mossaab-LandingPage on a VPS Subdomain

## Prerequisites
- A VPS server (Ubuntu/Debian recommended)
- Root or sudo access
- A registered domain and access to DNS management
- SSH client (e.g., PuTTY, Terminal)

## 1. Server Preparation
1. **Update your server:**
   ```sh
   sudo apt update && sudo apt upgrade -y
   ```
2. **Install Apache, PHP, and MySQL:**
   ```sh
   sudo apt install apache2 php php-mysql libapache2-mod-php unzip -y
   sudo apt install mysql-server -y
   ```
3. **Enable Apache modules:**
   ```sh
   sudo a2enmod rewrite
   sudo systemctl restart apache2
   ```

## 2. Domain & Subdomain Setup
1. **Create a subdomain** (e.g., `app.yourdomain.com`) in your DNS provider's dashboard, pointing it to your VPS IP address.
2. **Create a virtual host for the subdomain:**
   ```sh
   sudo nano /etc/apache2/sites-available/app.yourdomain.com.conf
   ```
   Paste:
   ```
   <VirtualHost *:80>
       ServerName app.yourdomain.com
       DocumentRoot /var/www/mossaab-landingpage
       <Directory /var/www/mossaab-landingpage>
           AllowOverride All
           Require all granted
       </Directory>
       ErrorLog ${APACHE_LOG_DIR}/app_error.log
       CustomLog ${APACHE_LOG_DIR}/app_access.log combined
   </VirtualHost>
   ```
   Enable the site:
   ```sh
   sudo a2ensite app.yourdomain.com.conf
   sudo systemctl reload apache2
   ```

## 3. Upload Application Files
1. **Copy all project files** to `/var/www/mossaab-landingpage` using SFTP, SCP, or rsync:
   ```sh
   scp -r * user@your_vps_ip:/var/www/mossaab-landingpage
   ```
2. **Set permissions:**
   ```sh
   sudo chown -R www-data:www-data /var/www/mossaab-landingpage
   sudo chmod -R 755 /var/www/mossaab-landingpage
   ```

## 4. Database Setup
1. **Log into MySQL:**
   ```sh
   sudo mysql -u root -p
   ```
2. **Create database and user:**
   ```sql
   CREATE DATABASE `mossab-landing-page` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'mossaab'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON `mossab-landing-page`.* TO 'mossaab'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```
3. **Import the SQL schema/data:**
   ```sh
   mysql -u mossaab -p mossab-landing-page < /var/www/mossaab-landingpage/mossab-landing-page.sql
   ```

## 5. Configure the Application
1. **Edit `php/config.php`:**
   - Set `DB_HOST`, `DB_PORT`, `DB_NAME`, `DB_USER`, and `DB_PASS` to match your MySQL setup.
2. **Check file upload directories:**
   - Ensure `/var/www/mossaab-landingpage/uploads/products/` and `/var/www/mossaab-landingpage/uploads/products/gallery/` exist and are writable by `www-data`.

## 6. Final Steps
- **Test the app:** Visit `http://app.yourdomain.com` in your browser.
- **(Optional) Enable HTTPS:**
   ```sh
   sudo apt install certbot python3-certbot-apache -y
   sudo certbot --apache -d app.yourdomain.com
   ```
- **Check logs:**
   - Apache: `/var/log/apache2/app_error.log`
   - PHP: `/var/log/apache2/error.log` or as configured

## 7. Troubleshooting
- Use `phpinfo()` to check PHP modules.
- Use `test-db-connection.php` and `check-database.php` for diagnostics.
- Check permissions if uploads or images do not work.

---
**Contact your server provider or developer for advanced issues.**