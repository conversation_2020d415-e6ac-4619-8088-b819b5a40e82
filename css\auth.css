/* Authentication Pages Styles */

.auth-body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Noto Sans Arabic', sans-serif;
    padding: 20px;
}

.auth-container {
    width: 100%;
    max-width: 450px;
    animation: fadeInUp 0.6s ease-out;
}

.auth-box {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 40px;
    position: relative;
    overflow: hidden;
}

.auth-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.register-box {
    max-width: 500px;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

.auth-header .logo i {
    font-size: 2rem;
    color: #667eea;
}

.auth-header .logo h1 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin: 0;
    font-weight: 700;
}

.auth-header h2 {
    color: #2c3e50;
    font-size: 1.6rem;
    margin: 0 0 10px 0;
    font-weight: 600;
}

.auth-header p {
    color: #7f8c8d;
    margin: 0;
    font-size: 0.95rem;
}

.auth-form {
    margin-top: 30px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group label i {
    color: #667eea;
    width: 16px;
}

.form-group input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e0e6ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: 'Noto Sans Arabic', sans-serif;
    background: #f8f9fa;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: #fff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
}

.checkbox-container input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e0e6ed;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #764ba2;
}

.error-message, .success-message {
    font-size: 0.9rem;
    margin-bottom: 15px;
    text-align: center;
    min-height: 20px;
    padding: 10px;
    border-radius: 8px;
    display: none;
}

.error-message {
    color: #e74c3c;
    background: #fdf2f2;
    border: 1px solid #f5c6cb;
}

.success-message {
    color: #27ae60;
    background: #f2f8f2;
    border: 1px solid #c3e6cb;
}

.error-message:not(:empty), .success-message:not(:empty) {
    display: block;
}

.auth-button {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Noto Sans Arabic', sans-serif;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.auth-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.auth-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.auth-divider {
    text-align: center;
    margin: 30px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e6ed;
}

.auth-divider span {
    background: #fff;
    padding: 0 20px;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.auth-links {
    text-align: center;
    margin: 20px 0;
}

.auth-links p {
    color: #7f8c8d;
    margin: 0;
}

.auth-links a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-links a:hover {
    color: #764ba2;
}

.demo-account {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
    border: 1px solid #e0e6ed;
}

.demo-account h4 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.demo-account p {
    color: #6c757d;
    margin: 0 0 15px 0;
    font-size: 0.9rem;
}

.demo-credentials {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #e0e6ed;
}

.demo-credentials p {
    margin: 5px 0;
    font-size: 0.85rem;
    color: #495057;
}

.demo-login-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.demo-login-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.back-to-site {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e6ed;
}

.back-to-site a {
    color: #7f8c8d;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: color 0.3s ease;
}

.back-to-site a:hover {
    color: #667eea;
}

.password-strength {
    margin-top: 8px;
}

.strength-bar {
    height: 4px;
    background: #e0e6ed;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-text {
    font-size: 0.8rem;
    font-weight: 500;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        max-width: 100%;
        padding: 10px;
    }
    
    .auth-box {
        padding: 30px 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .form-options {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
}
