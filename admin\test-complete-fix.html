<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاح الشامل - إدارة المتاجر</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .success {
            color: #10b981;
            background: #d1fae5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #10b981;
        }
        .error {
            color: #ef4444;
            background: #fee2e2;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ef4444;
        }
        .warning {
            color: #f59e0b;
            background: #fef3c7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #f59e0b;
        }
        .info {
            color: #3b82f6;
            background: #dbeafe;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #3b82f6;
        }
        .output {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #5a67d8;
        }
        .button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .button.success {
            background: #10b981;
        }
        .test-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #2d3748;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-pending {
            background: #f59e0b;
        }
        .status-success {
            background: #10b981;
        }
        .status-error {
            background: #ef4444;
        }
        .json-viewer {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار الإصلاح الشامل - إدارة المتاجر</h1>

        <div class="info">
            <h3>📋 الاختبارات المطلوبة:</h3>
            <ul>
                <li>✅ إصلاح خطأ SQL في فحص قاعدة البيانات (MariaDB)</li>
                <li>✅ إصلاح خطأ 500 في API المتاجر</li>
                <li>✅ إصلاح مشكلة أعمدة قاعدة البيانات (nom → first_name/last_name)</li>
                <li>✅ اختبار استجابة API مع JSON صحيح</li>
                <li>✅ التحقق من تحميل واجهة إدارة المتاجر</li>
            </ul>
        </div>

        <div class="test-section">
            <h3><span class="status-indicator status-pending" id="test1Status"></span>اختبار 1: فحص قاعدة البيانات (إصلاح SQL)</h3>
            <button class="button" onclick="runTest1()" id="test1Btn">تشغيل الاختبار</button>
            <div id="test1Output"></div>
        </div>

        <div class="test-section">
            <h3><span class="status-indicator status-pending" id="test2Status"></span>اختبار 2: إصلاح مخطط قاعدة البيانات</h3>
            <button class="button" onclick="runTest2()" id="test2Btn">تشغيل الاختبار</button>
            <div id="test2Output"></div>
        </div>

        <div class="test-section">
            <h3><span class="status-indicator status-pending" id="test3Status"></span>اختبار 3: API المتاجر (إصلاح 500 Error)</h3>
            <button class="button" onclick="runTest3()" id="test3Btn">تشغيل الاختبار</button>
            <div id="test3Output"></div>
        </div>

        <div class="test-section">
            <h3><span class="status-indicator status-pending" id="test4Status"></span>اختبار 4: تحليل استجابة JSON</h3>
            <button class="button" onclick="runTest4()" id="test4Btn">تشغيل الاختبار</button>
            <div id="test4Output"></div>
        </div>

        <div class="test-section">
            <h3><span class="status-indicator status-pending" id="test5Status"></span>اختبار 5: واجهة إدارة المتاجر</h3>
            <button class="button" onclick="runTest5()" id="test5Btn">تشغيل الاختبار</button>
            <div id="test5Output"></div>
        </div>

        <div class="test-section">
            <h3><span class="status-indicator status-pending" id="test6Status"></span>اختبار 6: التشخيص المتقدم</h3>
            <button class="button" onclick="runTest6()" id="test6Btn">تشغيل التشخيص</button>
            <div id="test6Output"></div>
        </div>

        <div id="finalResults"></div>
    </div>

    <script>
        function setStatus(testId, status) {
            const indicator = document.getElementById(testId + 'Status');
            indicator.className = `status-indicator status-${status}`;
        }

        async function runTest1() {
            const btn = document.getElementById('test1Btn');
            const output = document.getElementById('test1Output');

            btn.disabled = true;
            btn.textContent = '⏳ جاري الاختبار...';
            setStatus('test1', 'pending');

            try {
                const response = await fetch('test-database-check.php');
                const text = await response.text();

                if (response.ok && !text.includes('SQLSTATE[42000]')) {
                    output.innerHTML = `<div class="success">✅ تم إصلاح خطأ SQL بنجاح!</div><div class="output">${text}</div>`;
                    setStatus('test1', 'success');
                } else {
                    output.innerHTML = `<div class="error">❌ لا يزال هناك خطأ SQL</div><div class="output">${text}</div>`;
                    setStatus('test1', 'error');
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في الاختبار: ${error.message}</div>`;
                setStatus('test1', 'error');
            }

            btn.disabled = false;
            btn.textContent = 'إعادة الاختبار';
        }

        async function runTest2() {
            const btn = document.getElementById('test2Btn');
            const output = document.getElementById('test2Output');

            btn.disabled = true;
            btn.textContent = '⏳ جاري الاختبار...';
            setStatus('test2', 'pending');

            try {
                const response = await fetch('test-schema-fix.php');
                const text = await response.text();

                if (response.ok && text.includes('All tests passed')) {
                    output.innerHTML = `<div class="success">✅ تم إصلاح مخطط قاعدة البيانات بنجاح!</div><div class="output">${text}</div>`;
                    setStatus('test2', 'success');
                } else {
                    output.innerHTML = `<div class="error">❌ مشكلة في مخطط قاعدة البيانات</div><div class="output">${text}</div>`;
                    setStatus('test2', 'error');
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في الاختبار: ${error.message}</div>`;
                setStatus('test2', 'error');
            }

            btn.disabled = false;
            btn.textContent = 'إعادة الاختبار';
        }

        async function runTest3() {
            const btn = document.getElementById('test3Btn');
            const output = document.getElementById('test3Output');

            btn.disabled = true;
            btn.textContent = '⏳ جاري الاختبار...';
            setStatus('test3', 'pending');

            try {
                const response = await fetch('../php/api/stores.php');
                const status = response.status;

                if (status === 200) {
                    const data = await response.json();
                    output.innerHTML = `
                        <div class="success">✅ تم إصلاح خطأ 500! API يعمل بشكل صحيح</div>
                        <div class="info">📊 HTTP Status: ${status}</div>
                        <div class="info">📊 Success: ${data.success}</div>
                        <div class="info">📊 Total Stores: ${data.total || 0}</div>
                    `;
                    setStatus('test3', 'success');
                } else {
                    const text = await response.text();
                    output.innerHTML = `
                        <div class="error">❌ لا يزال هناك خطأ ${status}</div>
                        <div class="output">${text}</div>
                    `;
                    setStatus('test3', 'error');
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في الاختبار: ${error.message}</div>`;
                setStatus('test3', 'error');
            }

            btn.disabled = false;
            btn.textContent = 'إعادة الاختبار';
        }

        async function runTest4() {
            const btn = document.getElementById('test4Btn');
            const output = document.getElementById('test4Output');

            btn.disabled = true;
            btn.textContent = '⏳ جاري التحليل...';
            setStatus('test4', 'pending');

            try {
                const response = await fetch('../php/api/stores.php');
                const data = await response.json();

                if (data && typeof data === 'object') {
                    output.innerHTML = `
                        <div class="success">✅ JSON صحيح ومُحلل بنجاح!</div>
                        <div class="info">📋 تحليل البيانات:</div>
                        <div class="json-viewer">${JSON.stringify(data, null, 2)}</div>
                    `;
                    setStatus('test4', 'success');
                } else {
                    output.innerHTML = `<div class="error">❌ JSON غير صحيح</div>`;
                    setStatus('test4', 'error');
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في تحليل JSON: ${error.message}</div>`;
                setStatus('test4', 'error');
            }

            btn.disabled = false;
            btn.textContent = 'إعادة التحليل';
        }

        async function runTest5() {
            const btn = document.getElementById('test5Btn');
            const output = document.getElementById('test5Output');

            btn.disabled = true;
            btn.textContent = '⏳ جاري الاختبار...';
            setStatus('test5', 'pending');

            try {
                // Test if stores management HTML loads
                const htmlResponse = await fetch('stores-management.html');
                const htmlOk = htmlResponse.ok;

                // Test if stores management JS loads
                const jsResponse = await fetch('js/stores-management.js');
                const jsOk = jsResponse.ok;

                // Test API again
                const apiResponse = await fetch('../php/api/stores.php');
                const apiData = await apiResponse.json();
                const apiOk = apiResponse.ok && apiData.success;

                if (htmlOk && jsOk && apiOk) {
                    output.innerHTML = `
                        <div class="success">✅ جميع مكونات إدارة المتاجر تعمل بشكل صحيح!</div>
                        <div class="info">✅ HTML: متاح</div>
                        <div class="info">✅ JavaScript: متاح</div>
                        <div class="info">✅ API: يعمل بشكل صحيح</div>
                        <div class="info">📊 عدد المتاجر: ${apiData.total}</div>
                        <a href="index.html#storesManagement" class="button success" style="text-decoration: none; display: inline-block; margin-top: 15px;">
                            🏪 اختبار في لوحة التحكم
                        </a>
                    `;
                    setStatus('test5', 'success');
                } else {
                    output.innerHTML = `
                        <div class="error">❌ بعض المكونات لا تعمل</div>
                        <div class="info">HTML: ${htmlOk ? '✅' : '❌'}</div>
                        <div class="info">JavaScript: ${jsOk ? '✅' : '❌'}</div>
                        <div class="info">API: ${apiOk ? '✅' : '❌'}</div>
                    `;
                    setStatus('test5', 'error');
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في اختبار الواجهة: ${error.message}</div>`;
                setStatus('test5', 'error');
            }

            btn.disabled = false;
            btn.textContent = 'إعادة الاختبار';
        }

        async function runTest6() {
            const btn = document.getElementById('test6Btn');
            const output = document.getElementById('test6Output');

            btn.disabled = true;
            btn.textContent = '⏳ جاري التشخيص...';
            setStatus('test6', 'pending');

            try {
                const response = await fetch('debug-stores-api.php');
                const text = await response.text();

                output.innerHTML = `
                    <div class="info">📋 تقرير التشخيص المتقدم:</div>
                    <div class="output">${text}</div>
                `;
                setStatus('test6', 'success');
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في التشخيص: ${error.message}</div>`;
                setStatus('test6', 'error');
            }

            btn.disabled = false;
            btn.textContent = 'إعادة التشخيص';
        }

        // Auto-run tests
        window.addEventListener('load', () => {
            setTimeout(() => {
                runTest1();
                setTimeout(() => runTest2(), 2000);
                setTimeout(() => runTest3(), 4000);
                setTimeout(() => runTest4(), 6000);
                setTimeout(() => runTest5(), 8000);
            }, 1000);
        });
    </script>
</body>
</html>
