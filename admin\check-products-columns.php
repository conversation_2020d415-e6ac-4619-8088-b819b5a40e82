<?php
require_once __DIR__ . '/../php/config.php';

try {
    $pdo = getPDOConnection();
    
    echo "📋 Products Table Columns:\n";
    echo "=" . str_repeat("=", 30) . "\n";
    
    $stmt = $pdo->query('SHOW COLUMNS FROM produits');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $columnNames = [];
    foreach ($columns as $column) {
        $columnNames[] = $column['Field'];
        echo "  - {$column['Field']} ({$column['Type']})\n";
    }
    
    echo "\n🔍 Checking for user/store relationship columns:\n";
    echo "=" . str_repeat("=", 40) . "\n";
    
    $hasUserId = in_array('user_id', $columnNames);
    $hasStoreId = in_array('store_id', $columnNames);
    
    echo "user_id column: " . ($hasUserId ? '✅ EXISTS' : '❌ MISSING') . "\n";
    echo "store_id column: " . ($hasStoreId ? '✅ EXISTS' : '❌ MISSING') . "\n";
    
    if (!$hasUserId && !$hasStoreId) {
        echo "\n⚠️ No user/store relationship column found!\n";
        echo "Products cannot be associated with stores or users.\n";
        echo "Consider adding one of these columns:\n";
        echo "  - user_id INT (to associate with users)\n";
        echo "  - store_id INT (to associate with stores)\n";
    } elseif ($hasUserId && !$hasStoreId) {
        echo "\n✅ Using user_id for product-user association\n";
    } elseif (!$hasUserId && $hasStoreId) {
        echo "\n✅ Using store_id for product-store association\n";
    } else {
        echo "\n✅ Both columns available - can use either approach\n";
    }
    
    echo "\n📊 Sample Products:\n";
    echo "=" . str_repeat("=", 20) . "\n";
    
    $stmt = $pdo->query('SELECT * FROM produits LIMIT 3');
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "No products found.\n";
    } else {
        foreach ($products as $index => $product) {
            echo "Product " . ($index + 1) . ":\n";
            echo "  ID: {$product['id']}\n";
            echo "  Title: {$product['titre']}\n";
            if ($hasUserId) {
                echo "  User ID: " . ($product['user_id'] ?? 'NULL') . "\n";
            }
            if ($hasStoreId) {
                echo "  Store ID: " . ($product['store_id'] ?? 'NULL') . "\n";
            }
            echo "  Active: " . ($product['actif'] ?? 'N/A') . "\n";
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
