<?php
/**
 * Test User Management API
 */

require_once 'php/config.php';

header('Content-Type: text/html');

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Test User Management API</h1>";

try {
    echo "<h2>Testing Database Connection</h2>";
    $pdo = getPDOConnection();
    echo "<p>✅ Database connection successful</p>";
    
    echo "<h2>Testing Users API</h2>";
    echo "<h3>Direct API Call</h3>";
    
    // Make a direct call to the users API
    $ch = curl_init('http://localhost:8000/php/api/users.php?action=list');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p>Status Code: $status</p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    // Parse the response
    $data = json_decode($response, true);
    if ($data && isset($data['success']) && $data['success']) {
        echo "<p>✅ API returned success response</p>";
        echo "<p>Total users: " . count($data['users']) . "</p>";
        
        // Display user data
        echo "<h3>Users Data</h3>";
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Role</th><th>Status</th></tr>";
        
        foreach ($data['users'] as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . htmlspecialchars($user['status']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>❌ API returned error response</p>";
        echo "<pre>" . print_r($data, true) . "</pre>";
    }
    
    echo "<h2>Testing Admin User Management JavaScript</h2>";
    echo "<p>Check the JavaScript console for errors when loading the User Management section in the admin panel.</p>";
    echo "<p>The issue might be in how the JavaScript processes the API response.</p>";
    
    echo "<h3>Debugging Steps</h3>";
    echo "<ol>";
    echo "<li>Verify the API URL is correct in user-management.js</li>";
    echo "<li>Check if the response format matches what the JavaScript expects</li>";
    echo "<li>Look for any JavaScript errors in the console</li>";
    echo "<li>Check if the user data is being properly processed and displayed</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h2>Error</h2>";
    echo "<p>❌ " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
