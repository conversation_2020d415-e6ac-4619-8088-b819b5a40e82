/**
 * Emergency Loading Fix for Admin Panel
 * Forces the admin panel to show content even if there are JavaScript errors
 */

(function() {
    "use strict";
    
    console.log("🚨 Emergency Loading Fix activated");
    
    let loadingFixed = false;
    
    function forceShowContent() {
        if (loadingFixed) return;
        
        console.log("🔧 Forcing admin content to show...");
        
        try {
            // Add content-loaded class to body
            document.body.classList.add("content-loaded");
            
            // Hide loading indicator
            const loadingIndicator = document.getElementById("loading-indicator");
            if (loadingIndicator) {
                loadingIndicator.style.display = "none";
                console.log("✅ Loading indicator hidden");
            }
            
            // Force body visibility
            document.body.style.visibility = "visible";
            document.body.style.opacity = "1";
            
            // Show main content areas
            const mainContent = document.querySelector(".main-content");
            if (mainContent) {
                mainContent.style.display = "block";
                mainContent.style.visibility = "visible";
                mainContent.style.opacity = "1";
                console.log("✅ Main content forced visible");
            }
            
            // Show sidebar
            const sidebar = document.querySelector(".sidebar");
            if (sidebar) {
                sidebar.style.display = "block";
                sidebar.style.visibility = "visible";
                sidebar.style.opacity = "1";
                console.log("✅ Sidebar forced visible");
            }
            
            // Show all content sections
            const contentSections = document.querySelectorAll(".content-section");
            contentSections.forEach(section => {
                section.style.display = "block";
                section.style.visibility = "visible";
                section.style.opacity = "1";
            });
            
            if (contentSections.length > 0) {
                console.log(`✅ ${contentSections.length} content sections forced visible`);
            }
            
            // Show admin sections
            const adminSections = document.querySelectorAll(".admin-section");
            adminSections.forEach(section => {
                section.style.display = "block";
                section.style.visibility = "visible";
                section.style.opacity = "1";
            });
            
            if (adminSections.length > 0) {
                console.log(`✅ ${adminSections.length} admin sections forced visible`);
            }
            
            loadingFixed = true;
            console.log("🎉 Emergency loading fix completed successfully");
            
        } catch (error) {
            console.error("❌ Emergency loading fix error:", error);
        }
    }
    
    // Try to fix loading immediately
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", forceShowContent);
    } else {
        forceShowContent();
    }
    
    // Fallback: Force show content after 3 seconds regardless
    setTimeout(function() {
        if (!loadingFixed) {
            console.log("⏰ Fallback: Forcing content to show after 3 seconds");
            forceShowContent();
        }
    }, 3000);
    
    // Additional fallback: Force show content after 5 seconds
    setTimeout(function() {
        console.log("⏰ Final fallback: Ensuring content is visible after 5 seconds");
        forceShowContent();
    }, 5000);
    
    // Override window.load event to ensure our fix runs
    window.addEventListener("load", function() {
        console.log("🔄 Window load event fired, ensuring content is visible");
        forceShowContent();
    });
    
    // Global error handler to catch any errors that might prevent loading
    window.addEventListener("error", function(event) {
        console.warn("🛡️ Global error caught, ensuring content remains visible:", event.error?.message);
        setTimeout(forceShowContent, 100);
    });
    
    console.log("✅ Emergency Loading Fix initialized");
    
})();