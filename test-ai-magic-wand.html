<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🪄 AI Magic Wand Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f7fa;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            background: #fafbfc;
        }
        .test-section h3 {
            color: #667eea;
            margin-top: 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            font-family: inherit;
            direction: rtl;
        }
        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .result-item {
            margin: 8px 0;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
        }
        .result-item.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .result-item.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .result-item.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .result-item.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }

        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .status-available { background: #d4edda; color: #155724; }
        .status-unavailable { background: #f8d7da; color: #721c24; }
    </style>

    <!-- TinyMCE -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
</head>
<body>
    <div class="container">
        <h1>🪄 AI Magic Wand Functionality Test</h1>
        <p>This page tests the AI Magic Wand functionality for automatic content generation in Arabic.</p>

        <div id="aiStatus" class="status-indicator status-unavailable">
            ⚠️ Checking AI availability...
        </div>

        <div class="results" id="testResults">
            <h4>Test Results:</h4>
            <div id="resultsContainer"></div>
        </div>

        <!-- Product Description Test -->
        <div class="test-section">
            <h3>🛍️ Product Description Test</h3>
            <p>Test AI Magic Wand for product descriptions with TinyMCE editor.</p>

            <div class="form-group">
                <label for="productTitle">Product Title (for context):</label>
                <input type="text" id="productTitle" value="فن اللامبالاة - كتاب تطوير الذات" placeholder="Enter product title...">
            </div>

            <div class="form-group">
                <label for="productDescription">Product Description:</label>
                <textarea id="productDescription" class="tinymce" placeholder="AI will generate product description here..."></textarea>
            </div>

            <button class="test-button" onclick="testProductDescription()">Test Product Description AI</button>
        </div>

        <!-- Landing Page Title Test -->
        <div class="test-section">
            <h3>📄 Landing Page Title Test</h3>
            <p>Test AI Magic Wand for landing page titles.</p>

            <div class="form-group">
                <label for="landingPageTitle">Landing Page Title:</label>
                <input type="text" id="landingPageTitle" placeholder="AI will generate landing page title here...">
            </div>

            <button class="test-button" onclick="testLandingPageTitle()">Test Landing Page Title AI</button>
        </div>

        <!-- Landing Page Content Test -->
        <div class="test-section">
            <h3>📝 Landing Page Content Test</h3>
            <p>Test AI Magic Wand for landing page content with TinyMCE editors.</p>

            <div class="form-group">
                <label for="rightContent">Right Content:</label>
                <textarea id="rightContent" class="tinymce" placeholder="AI will generate right content here..."></textarea>
            </div>

            <div class="form-group">
                <label for="leftContent">Left Content:</label>
                <textarea id="leftContent" class="tinymce" placeholder="AI will generate left content here..."></textarea>
            </div>

            <button class="test-button" onclick="testLandingPageContent()">Test Landing Page Content AI</button>
        </div>

        <!-- Manual Test Controls -->
        <div class="test-section">
            <h3>🔧 Manual Test Controls</h3>
            <button class="test-button" onclick="checkAIAvailability()">Check AI Availability</button>
            <button class="test-button" onclick="initializeAIMagicWand()">Initialize AI Magic Wand</button>
            <button class="test-button" onclick="addMagicWandsToPage()">Add Magic Wands to Page</button>
            <button class="test-button" onclick="testTinyMCEIntegration()">Test TinyMCE Integration</button>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="js/utils.js"></script>
    <script src="admin/js/selection-error-fix.js"></script>
    <script src="admin/js/ai-magic-wand.js"></script>

    <script>
        // Override the API URL for testing
        if (window.AIMagicWand) {
            const originalGenerateText = window.AIMagicWand.prototype.generateText;
            window.AIMagicWand.prototype.generateText = function(field, type, editor = null) {
                // Use the simplified API
                return originalGenerateText.call(this, field, type, editor);
            };
        }
    </script>

    <script>
        let testResults = [];

        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            testResults.push({ message, type, timestamp });

            const container = document.getElementById('resultsContainer');
            const div = document.createElement('div');
            div.className = `result-item ${type}`;
            div.innerHTML = `${timestamp}: ${message}`;
            container.appendChild(div);

            // Auto-scroll to bottom
            container.scrollTop = container.scrollHeight;

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Initialize TinyMCE
        tinymce.init({
            selector: '.tinymce',
            height: 200,
            language: 'ar',
            directionality: 'rtl',
            plugins: 'lists link image table code',
            toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code',
            content_style: 'body { font-family: Arial, sans-serif; direction: rtl; }',
            setup: function(editor) {
                editor.on('init', function() {
                    addResult(`✅ TinyMCE editor initialized: ${editor.id}`, 'success');
                });
            }
        });

        // Test functions
        async function checkAIAvailability() {
            addResult('🔍 Checking AI availability...', 'info');

            if (window.aiMagicWand) {
                await window.aiMagicWand.checkAvailability();
                const status = window.aiMagicWand.isAvailable;
                const providers = window.aiMagicWand.providers;

                if (status) {
                    addResult(`✅ AI is available with providers: ${providers.join(', ')}`, 'success');
                    document.getElementById('aiStatus').className = 'status-indicator status-available';
                    document.getElementById('aiStatus').innerHTML = `🤖 AI Available (${providers.join(', ')})`;
                } else {
                    addResult('❌ AI is not available', 'error');
                    document.getElementById('aiStatus').className = 'status-indicator status-unavailable';
                    document.getElementById('aiStatus').innerHTML = '⚠️ AI Not Available';
                }
            } else {
                addResult('❌ AI Magic Wand not initialized', 'error');
            }
        }

        function initializeAIMagicWand() {
            addResult('🪄 Initializing AI Magic Wand...', 'info');

            if (typeof window.initializeAIMagicWand === 'function') {
                window.initializeAIMagicWand();
                addResult('✅ AI Magic Wand initialized', 'success');
            } else {
                addResult('❌ initializeAIMagicWand function not found', 'error');
            }
        }

        function addMagicWandsToPage() {
            addResult('✨ Adding magic wands to page...', 'info');

            if (window.aiMagicWand) {
                window.aiMagicWand.addMagicWandsToPage();
                addResult('✅ Magic wands added to page', 'success');
            } else {
                addResult('❌ AI Magic Wand not available', 'error');
            }
        }

        function testTinyMCEIntegration() {
            addResult('🔧 Testing TinyMCE integration...', 'info');

            if (typeof tinymce !== 'undefined' && tinymce.editors) {
                const editors = tinymce.editors;
                addResult(`📝 Found ${editors.length} TinyMCE editors`, 'info');

                editors.forEach(editor => {
                    addResult(`  - Editor: ${editor.id}`, 'info');

                    // Check if magic wand button exists
                    if (editor.ui && editor.ui.registry) {
                        addResult(`    ✅ UI registry available for ${editor.id}`, 'success');
                    } else {
                        addResult(`    ❌ UI registry not available for ${editor.id}`, 'error');
                    }
                });
            } else {
                addResult('❌ TinyMCE not available', 'error');
            }
        }

        async function testProductDescription() {
            addResult('🛍️ Testing product description generation...', 'info');

            if (!window.aiMagicWand || !window.aiMagicWand.isAvailable) {
                addResult('❌ AI Magic Wand not available', 'error');
                return;
            }

            const field = document.getElementById('productDescription');
            const editor = tinymce.get('productDescription');

            if (editor) {
                addResult('📝 Using TinyMCE editor for product description', 'info');
                await window.aiMagicWand.generateText(field, 'product_description', editor);
            } else {
                addResult('📝 Using textarea for product description', 'info');
                await window.aiMagicWand.generateText(field, 'product_description');
            }
        }

        async function testLandingPageTitle() {
            addResult('📄 Testing landing page title generation...', 'info');

            if (!window.aiMagicWand || !window.aiMagicWand.isAvailable) {
                addResult('❌ AI Magic Wand not available', 'error');
                return;
            }

            const field = document.getElementById('landingPageTitle');
            await window.aiMagicWand.generateText(field, 'landing_page_title');
        }

        async function testLandingPageContent() {
            addResult('📝 Testing landing page content generation...', 'info');

            if (!window.aiMagicWand || !window.aiMagicWand.isAvailable) {
                addResult('❌ AI Magic Wand not available', 'error');
                return;
            }

            // Test right content
            const rightField = document.getElementById('rightContent');
            const rightEditor = tinymce.get('rightContent');

            if (rightEditor) {
                addResult('📝 Testing right content with TinyMCE', 'info');
                await window.aiMagicWand.generateText(rightField, 'landing_page_content', rightEditor);
            }

            // Test left content
            setTimeout(async () => {
                const leftField = document.getElementById('leftContent');
                const leftEditor = tinymce.get('leftContent');

                if (leftEditor) {
                    addResult('📝 Testing left content with TinyMCE', 'info');
                    await window.aiMagicWand.generateText(leftField, 'landing_page_content', leftEditor);
                }
            }, 2000);
        }

        // Auto-run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 AI Magic Wand Test Page loaded', 'success');

            // Wait for AI Magic Wand to initialize
            setTimeout(() => {
                checkAIAvailability();
                addMagicWandsToPage();
            }, 1000);
        });
    </script>
</body>
</html>
