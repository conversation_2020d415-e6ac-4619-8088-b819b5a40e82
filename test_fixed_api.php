<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🧪 Testing Fixed Products API\n\n";

// Test the fixed query directly
require_once 'php/config.php';

try {
    echo "1. Testing fixed query:\n";
    $stmt = $conn->prepare(
        "SELECT p.*,
                CASE
                    WHEN lp.id IS NOT NULL THEN true
                    ELSE false
                END as has_landing_page,
                lp.lien_url as landing_url
         FROM produits p
         LEFT JOIN (
             SELECT produit_id, id, lien_url 
             FROM landing_pages 
             GROUP BY produit_id
         ) lp ON p.id = lp.produit_id
         WHERE p.actif = 1"
    );
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "✅ Found " . count($products) . " active products (no duplicates)\n";
    
    foreach ($products as $product) {
        echo "- ID: {$product['id']} | {$product['titre']} | Type: {$product['type']}\n";
    }
    
    echo "\n2. Testing API response format:\n";
    $response = [
        'success' => true,
        'products' => $products,
        'total_count' => count($products)
    ];
    
    echo "Response structure: " . json_encode(array_keys($response)) . "\n";
    echo "Products count in response: " . count($response['products']) . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
