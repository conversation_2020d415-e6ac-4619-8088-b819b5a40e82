<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

/**
 * Geographic Data API
 * Handles Algerian wilayas and communes data for shipping calculations
 */

function createGeographicTables()
{
    global $conn;

    try {
        // Create wilayas table
        $sql = "
        CREATE TABLE IF NOT EXISTS wilayas (
            id INT AUTO_INCREMENT PRIMARY KEY,
            wilaya_code VARCHAR(10) NOT NULL UNIQUE,
            wilaya_name_ar VARCHAR(100) NOT NULL,
            wilaya_name_fr VARCHAR(100) NOT NULL,
            zone_number INT DEFAULT 1,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_wilaya_code (wilaya_code),
            INDEX idx_zone_number (zone_number)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $conn->exec($sql);

        // Create communes table
        $sql = "
        CREATE TABLE IF NOT EXISTS communes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            commune_code VARCHAR(20) NOT NULL UNIQUE,
            commune_name_ar VARCHAR(150) NOT NULL,
            commune_name_fr VARCHAR(150) NOT NULL,
            wilaya_code VARCHAR(10) NOT NULL,
            postal_code VARCHAR(10),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (wilaya_code) REFERENCES wilayas(wilaya_code) ON UPDATE CASCADE,
            INDEX idx_wilaya_code (wilaya_code),
            INDEX idx_commune_code (commune_code)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $conn->exec($sql);

        // Create shipping_overrides table for custom rates
        $sql = "
        CREATE TABLE IF NOT EXISTS shipping_overrides (
            id INT AUTO_INCREMENT PRIMARY KEY,
            location_type ENUM('wilaya', 'commune') NOT NULL,
            location_code VARCHAR(20) NOT NULL,
            shipping_cost DECIMAL(10,2) NOT NULL,
            delivery_days VARCHAR(50) DEFAULT '2-4 أيام عمل',
            is_active BOOLEAN DEFAULT TRUE,
            created_by VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_location (location_type, location_code)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $conn->exec($sql);

        // Initialize data
        initializeWilayasData();
        initializeCommunesData();

        return true;
    } catch (PDOException $e) {
        error_log('Error creating geographic tables: ' . $e->getMessage());
        return false;
    }
}

function initializeWilayasData()
{
    global $conn;

    try {
        // Check if data already exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM wilayas");
        $stmt->execute();
        $count = $stmt->fetchColumn();

        if ($count > 0) {
            return; // Already initialized
        }

        // All 58 Algerian wilayas with their zones
        $wilayas = [
            ['01', 'أدرار', 'Adrar', 5],
            ['02', 'الشلف', 'Chlef', 4],
            ['03', 'الأغواط', 'Laghouat', 5],
            ['04', 'أم البواقي', 'Oum El Bouaghi', 3],
            ['05', 'باتنة', 'Batna', 3],
            ['06', 'بجاية', 'Béjaïa', 2],
            ['07', 'بسكرة', 'Biskra', 3],
            ['08', 'بشار', 'Béchar', 5],
            ['09', 'البليدة', 'Blida', 1],
            ['10', 'البويرة', 'Bouira', 2],
            ['11', 'تمنراست', 'Tamanrasset', 5],
            ['12', 'تبسة', 'Tébessa', 3],
            ['13', 'تلمسان', 'Tlemcen', 4],
            ['14', 'تيارت', 'Tiaret', 4],
            ['15', 'تيزي وزو', 'Tizi Ouzou', 2],
            ['16', 'الجزائر', 'Alger', 1],
            ['17', 'الجلفة', 'Djelfa', 3],
            ['18', 'جيجل', 'Jijel', 2],
            ['19', 'سطيف', 'Sétif', 2],
            ['20', 'سعيدة', 'Saïda', 4],
            ['21', 'سكيكدة', 'Skikda', 2],
            ['22', 'سيدي بلعباس', 'Sidi Bel Abbès', 4],
            ['23', 'عنابة', 'Annaba', 2],
            ['24', 'قالمة', 'Guelma', 2],
            ['25', 'قسنطينة', 'Constantine', 2],
            ['26', 'المدية', 'Médéa', 3],
            ['27', 'مستغانم', 'Mostaganem', 4],
            ['28', 'المسيلة', 'M\'Sila', 3],
            ['29', 'معسكر', 'Mascara', 4],
            ['30', 'ورقلة', 'Ouargla', 5],
            ['31', 'وهران', 'Oran', 4],
            ['32', 'البيض', 'El Bayadh', 5],
            ['33', 'إليزي', 'Illizi', 5],
            ['34', 'برج بوعريريج', 'Bordj Bou Arréridj', 2],
            ['35', 'بومرداس', 'Boumerdès', 1],
            ['36', 'الطارف', 'El Tarf', 2],
            ['37', 'تندوف', 'Tindouf', 5],
            ['38', 'تيسمسيلت', 'Tissemsilt', 4],
            ['39', 'الوادي', 'El Oued', 5],
            ['40', 'خنشلة', 'Khenchela', 3],
            ['41', 'سوق أهراس', 'Souk Ahras', 2],
            ['42', 'تيبازة', 'Tipaza', 1],
            ['43', 'ميلة', 'Mila', 2],
            ['44', 'عين الدفلى', 'Ain Defla', 4],
            ['45', 'النعامة', 'Naâma', 5],
            ['46', 'عين تموشنت', 'Ain Témouchent', 4],
            ['47', 'غرداية', 'Ghardaïa', 5],
            ['48', 'غليزان', 'Relizane', 4],
            ['49', 'تيميمون', 'Timimoun', 5],
            ['50', 'برج باجي مختار', 'Bordj Badji Mokhtar', 5],
            ['51', 'أولاد جلال', 'Ouled Djellal', 3],
            ['52', 'بني عباس', 'Béni Abbès', 5],
            ['53', 'عين صالح', 'In Salah', 5],
            ['54', 'عين قزام', 'In Guezzam', 5],
            ['55', 'توقرت', 'Touggourt', 5],
            ['56', 'جانت', 'Djanet', 5],
            ['57', 'المغير', 'El M\'Ghair', 5],
            ['58', 'المنيعة', 'El Meniaa', 5]
        ];

        $stmt = $conn->prepare("
            INSERT INTO wilayas (wilaya_code, wilaya_name_ar, wilaya_name_fr, zone_number)
            VALUES (?, ?, ?, ?)
        ");

        foreach ($wilayas as $wilaya) {
            $stmt->execute($wilaya);
        }

        error_log('Wilayas data initialized successfully');
    } catch (PDOException $e) {
        error_log('Error initializing wilayas data: ' . $e->getMessage());
    }
}

function initializeCommunesData()
{
    global $conn;

    try {
        // Check if data already exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM communes");
        $stmt->execute();
        $count = $stmt->fetchColumn();

        if ($count > 0) {
            return; // Already initialized
        }

        // Sample communes for major wilayas (in production, you'd load all 1,541 communes)
        $communes = [
            // Alger (16)
            ['1601', 'الجزائر الوسطى', 'Alger Centre', '16'],
            ['1602', 'سيدي امحمد', 'Sidi M\'Hamed', '16'],
            ['1603', 'المدنية', 'El Madania', '16'],
            ['1604', 'حيدرة', 'Hamma El Annasser', '16'],
            ['1605', 'باب الوادي', 'Bab El Oued', '16'],

            // Blida (09)
            ['0901', 'البليدة', 'Blida', '09'],
            ['0902', 'الشريعة', 'Chréa', '09'],
            ['0903', 'بوعرفة', 'Bouarfa', '09'],
            ['0904', 'الشفة', 'Chiffa', '09'],

            // Oran (31)
            ['3101', 'وهران', 'Oran', '31'],
            ['3102', 'السانيا', 'Es Senia', '31'],
            ['3103', 'أرزيو', 'Arzew', '31'],
            ['3104', 'بطيوة', 'Bethioua', '31'],

            // Constantine (25)
            ['2501', 'قسنطينة', 'Constantine', '25'],
            ['2502', 'حامة بوزيان', 'Hamma Bouziane', '25'],
            ['2503', 'ديدوش مراد', 'Didouche Mourad', '25'],

            // Tizi Ouzou (15)
            ['1501', 'تيزي وزو', 'Tizi Ouzou', '15'],
            ['1502', 'عزازقة', 'Azazga', '15'],
            ['1503', 'تيغزرت', 'Tigzirt', '15'],

            // Add more communes as needed...
        ];

        $stmt = $conn->prepare("
            INSERT INTO communes (commune_code, commune_name_ar, commune_name_fr, wilaya_code)
            VALUES (?, ?, ?, ?)
        ");

        foreach ($communes as $commune) {
            $stmt->execute($commune);
        }

        error_log('Communes data initialized successfully');
    } catch (PDOException $e) {
        error_log('Error initializing communes data: ' . $e->getMessage());
    }
}

function handleGeographicRequest()
{
    global $conn;

    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'wilayas':
            try {
                $stmt = $conn->prepare("
                    SELECT wilaya_code, wilaya_name_ar, wilaya_name_fr, zone_number
                    FROM wilayas
                    WHERE is_active = 1
                    ORDER BY wilaya_name_ar
                ");
                $stmt->execute();
                $wilayas = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode([
                    'success' => true,
                    'wilayas' => $wilayas,
                    'total_count' => count($wilayas)
                ]);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'خطأ في جلب بيانات الولايات']);
            }
            break;

        case 'communes':
            $wilayaCode = $_GET['wilaya_code'] ?? '';

            if (empty($wilayaCode)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'كود الولاية مطلوب']);
                return;
            }

            try {
                $stmt = $conn->prepare("
                    SELECT commune_code, commune_name_ar, commune_name_fr, postal_code
                    FROM communes
                    WHERE wilaya_code = ? AND is_active = 1
                    ORDER BY commune_name_ar
                ");
                $stmt->execute([$wilayaCode]);
                $communes = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode([
                    'success' => true,
                    'communes' => $communes,
                    'wilaya_code' => $wilayaCode,
                    'total_count' => count($communes)
                ]);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'خطأ في جلب بيانات البلديات']);
            }
            break;

        case 'shipping_override':
            $locationType = $_GET['location_type'] ?? '';
            $locationCode = $_GET['location_code'] ?? '';

            try {
                $stmt = $conn->prepare("
                    SELECT shipping_cost, delivery_days
                    FROM shipping_overrides
                    WHERE location_type = ? AND location_code = ? AND is_active = 1
                ");
                $stmt->execute([$locationType, $locationCode]);
                $override = $stmt->fetch(PDO::FETCH_ASSOC);

                echo json_encode([
                    'success' => true,
                    'has_override' => !empty($override),
                    'override' => $override
                ]);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'خطأ في جلب بيانات الشحن المخصصة']);
            }
            break;

        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    }
}

// Initialize tables
createGeographicTables();

// Route requests
handleGeographicRequest();
