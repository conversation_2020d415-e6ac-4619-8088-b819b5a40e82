<?php
/**
 * OpenAI API Key Validation and Testing
 * Comprehensive verification of the OpenAI API key from .env file
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار والتحقق من مفتاح OpenAI API</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .api-key-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .test-results {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔑 اختبار والتحقق من مفتاح OpenAI API</h1>
            <p>فحص شامل لمفتاح OpenAI من ملف .env وحل مشاكل JSON parse</p>
        </div>

        <?php
        $apiKey = '';
        $keyValid = false;
        $testResults = [];

        try {
            // Test 1: Load and Validate .env File
            echo '<div class="test-section">';
            echo '<h3>📁 اختبار 1: تحميل والتحقق من ملف .env</h3>';
            
            $envFile = '../.env';
            if (file_exists($envFile)) {
                echo '<div class="result pass">✅ ملف .env موجود</div>';
                
                // Load .env file
                $envContent = file_get_contents($envFile);
                if (preg_match('/OPENAI_API_KEY=(.*)/', $envContent, $matches)) {
                    $apiKey = trim($matches[1]);
                    echo '<div class="result pass">✅ مفتاح OPENAI_API_KEY موجود في .env</div>';
                    
                    // Display masked API key
                    $maskedKey = substr($apiKey, 0, 20) . '...' . substr($apiKey, -10);
                    echo '<div class="api-key-display">مفتاح API: ' . $maskedKey . '</div>';
                    echo '<div class="result info">📏 طول المفتاح: ' . strlen($apiKey) . ' حرف</div>';
                    
                    // Validate key format
                    if (strpos($apiKey, 'sk-proj-') === 0 && strlen($apiKey) > 50) {
                        echo '<div class="result pass">✅ تنسيق مفتاح API صحيح (Project API Key)</div>';
                        $keyValid = true;
                    } elseif (strpos($apiKey, 'sk-') === 0 && strlen($apiKey) > 40) {
                        echo '<div class="result pass">✅ تنسيق مفتاح API صحيح (Legacy API Key)</div>';
                        $keyValid = true;
                    } else {
                        echo '<div class="result fail">❌ تنسيق مفتاح API غير صحيح</div>';
                    }
                } else {
                    echo '<div class="result fail">❌ مفتاح OPENAI_API_KEY غير موجود في .env</div>';
                }
            } else {
                echo '<div class="result fail">❌ ملف .env غير موجود</div>';
            }
            echo '</div>';

            // Test 2: Direct OpenAI API Test
            if ($keyValid && !empty($apiKey)) {
                echo '<div class="test-section">';
                echo '<h3>🧠 اختبار 2: اختبار مباشر لـ OpenAI API</h3>';
                
                // Test OpenAI Models endpoint
                $url = 'https://api.openai.com/v1/models';
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json',
                    'User-Agent: Mossaab-Landing-Page/1.0'
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 15);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                if ($error) {
                    echo '<div class="result fail">❌ خطأ في الاتصال: ' . $error . '</div>';
                    $testResults['direct_api'] = 'connection_error';
                } else {
                    echo '<div class="result info">📡 رمز الاستجابة HTTP: ' . $httpCode . '</div>';
                    
                    if ($httpCode === 200) {
                        $data = json_decode($response, true);
                        if ($data && isset($data['data'])) {
                            echo '<div class="result pass">✅ مفتاح API صالح وفعال!</div>';
                            echo '<div class="result info">📊 عدد النماذج المتاحة: ' . count($data['data']) . '</div>';
                            
                            // Display some available models
                            $models = array_slice($data['data'], 0, 5);
                            echo '<div class="result info">🤖 بعض النماذج المتاحة:</div>';
                            foreach ($models as $model) {
                                echo '<div class="result info">• ' . $model['id'] . '</div>';
                            }
                            
                            $testResults['direct_api'] = 'success';
                        } else {
                            echo '<div class="result fail">❌ استجابة غير صالحة من OpenAI</div>';
                            echo '<div class="json-display">' . htmlspecialchars($response) . '</div>';
                            $testResults['direct_api'] = 'invalid_response';
                        }
                    } elseif ($httpCode === 401) {
                        echo '<div class="result fail">❌ مفتاح API غير صالح أو منتهي الصلاحية</div>';
                        $testResults['direct_api'] = 'invalid_key';
                    } elseif ($httpCode === 429) {
                        echo '<div class="result warning">⚠️ تم تجاوز حد الطلبات - المفتاح صالح لكن محدود</div>';
                        $testResults['direct_api'] = 'rate_limited';
                    } else {
                        echo '<div class="result fail">❌ خطأ HTTP: ' . $httpCode . '</div>';
                        echo '<div class="json-display">' . htmlspecialchars($response) . '</div>';
                        $testResults['direct_api'] = 'http_error';
                    }
                }
                echo '</div>';
            }

            // Test 3: Test AI API Endpoint
            echo '<div class="test-section">';
            echo '<h3>🔗 اختبار 3: اختبار نقطة نهاية AI API</h3>';
            
            $aiAPIFile = '../php/api/ai.php';
            if (file_exists($aiAPIFile)) {
                echo '<div class="result pass">✅ ملف AI API موجود</div>';
                
                // Test the API endpoint
                try {
                    ob_start();
                    $_SERVER['REQUEST_METHOD'] = 'GET';
                    $_GET = ['action' => 'test_connection', 'provider' => 'openai'];
                    
                    // Capture any errors
                    $errorOccurred = false;
                    set_error_handler(function($severity, $message, $file, $line) use (&$errorOccurred) {
                        $errorOccurred = true;
                        echo '<div class="result fail">❌ PHP Error: ' . $message . '</div>';
                    });
                    
                    include $aiAPIFile;
                    restore_error_handler();
                    
                    $output = ob_get_clean();
                    
                    if (!$errorOccurred) {
                        echo '<div class="result pass">✅ AI API تم تحميله بدون أخطاء PHP</div>';
                        
                        // Try to parse JSON
                        $data = json_decode($output, true);
                        if ($data !== null) {
                            echo '<div class="result pass">✅ AI API يعطي استجابة JSON صالحة</div>';
                            echo '<div class="json-display">' . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</div>';
                            
                            if (isset($data['success']) && $data['success']) {
                                echo '<div class="result pass">✅ اختبار OpenAI نجح عبر AI API</div>';
                                $testResults['ai_api'] = 'success';
                            } else {
                                echo '<div class="result warning">⚠️ اختبار OpenAI فشل عبر AI API</div>';
                                $testResults['ai_api'] = 'test_failed';
                            }
                        } else {
                            echo '<div class="result fail">❌ AI API لا يعطي JSON صالح</div>';
                            echo '<div class="result warning">⚠️ الاستجابة الخام:</div>';
                            echo '<div class="json-display">' . htmlspecialchars($output) . '</div>';
                            $testResults['ai_api'] = 'invalid_json';
                        }
                    } else {
                        echo '<div class="result fail">❌ أخطاء PHP في AI API</div>';
                        $testResults['ai_api'] = 'php_errors';
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="result fail">❌ خطأ في AI API: ' . $e->getMessage() . '</div>';
                    $testResults['ai_api'] = 'exception';
                }
            } else {
                echo '<div class="result fail">❌ ملف AI API غير موجود</div>';
                $testResults['ai_api'] = 'missing';
            }
            echo '</div>';

            // Test 4: JavaScript Error Fix Verification
            echo '<div class="test-section">';
            echo '<h3>🌐 اختبار 4: التحقق من إصلاح أخطاء JavaScript</h3>';
            echo '<div id="jsTestResults">جاري اختبار JavaScript...</div>';
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
        }

        // Summary
        echo '<div class="test-section">';
        echo '<h3>📊 ملخص نتائج الاختبار</h3>';
        
        $successCount = 0;
        $totalTests = count($testResults);
        
        foreach ($testResults as $test => $result) {
            if ($result === 'success') {
                $successCount++;
            }
        }
        
        if ($keyValid) {
            echo '<div class="result pass">✅ مفتاح OpenAI API صالح ومُتحقق منه</div>';
        } else {
            echo '<div class="result fail">❌ مفتاح OpenAI API غير صالح</div>';
        }
        
        if ($totalTests > 0) {
            $successRate = ($successCount / $totalTests) * 100;
            echo '<div class="result info">📊 معدل نجاح الاختبارات: ' . $successCount . '/' . $totalTests . ' (' . round($successRate, 1) . '%)</div>';
        }
        
        echo '<h4>🛠️ الإجراءات التالية:</h4>';
        echo '<button onclick="testOpenAIViaJS()" class="test-button">🧪 اختبار OpenAI عبر JavaScript</button>';
        echo '<a href="ai-settings.html" class="test-button">🤖 فتح إعدادات AI</a>';
        echo '<a href="index.html" class="test-button">🏠 لوحة التحكم</a>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        // Fix for context menu selection errors
        (function() {
            // Global error handler for selection-related errors
            window.addEventListener('error', function(event) {
                if (event.error && event.error.message) {
                    const message = event.error.message;
                    if (message.includes('rangeCount') || 
                        message.includes('selection is null') || 
                        message.includes('contextmenuhlpr')) {
                        console.log('🛡️ Selection error prevented:', message);
                        event.preventDefault();
                        return true;
                    }
                }
            });

            // Safe getSelection wrapper
            const originalGetSelection = window.getSelection;
            window.getSelection = function() {
                try {
                    const selection = originalGetSelection.call(window);
                    if (!selection) {
                        return {
                            rangeCount: 0,
                            addRange: function() {},
                            removeAllRanges: function() {},
                            toString: function() { return ''; }
                        };
                    }
                    return selection;
                } catch (error) {
                    console.log('🛡️ getSelection error handled:', error.message);
                    return {
                        rangeCount: 0,
                        addRange: function() {},
                        removeAllRanges: function() {},
                        toString: function() { return ''; }
                    };
                }
            };
        })();

        // Test OpenAI via JavaScript
        async function testOpenAIViaJS() {
            const resultsDiv = document.getElementById('jsTestResults');
            resultsDiv.innerHTML = '<div class="loading"></div> جاري اختبار OpenAI عبر JavaScript...';
            
            try {
                console.log('🧪 Testing OpenAI via JavaScript...');
                
                const response = await fetch('../php/api/ai.php?action=test_connection&provider=openai');
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const text = await response.text();
                console.log('Raw response:', text);
                
                // Try to parse JSON
                let data;
                try {
                    data = JSON.parse(text);
                    console.log('Parsed JSON:', data);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    resultsDiv.innerHTML = `
                        <div class="result fail">❌ خطأ في تحليل JSON: ${parseError.message}</div>
                        <div class="result warning">⚠️ الاستجابة الخام:</div>
                        <div class="json-display">${text.substring(0, 500)}...</div>
                    `;
                    return;
                }
                
                // Display results
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="result pass">✅ اختبار OpenAI عبر JavaScript نجح!</div>
                        <div class="result info">📊 مصدر API Key: ${data.data.api_key_source}</div>
                        <div class="result info">🔑 طول API Key: ${data.data.api_key_length}</div>
                        <div class="json-display">${JSON.stringify(data, null, 2)}</div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="result fail">❌ اختبار OpenAI فشل: ${data.error || 'خطأ غير معروف'}</div>
                        <div class="json-display">${JSON.stringify(data, null, 2)}</div>
                    `;
                }
                
            } catch (error) {
                console.error('JavaScript test error:', error);
                resultsDiv.innerHTML = `
                    <div class="result fail">❌ خطأ في اختبار JavaScript: ${error.message}</div>
                    <div class="result info">🔍 تحقق من وحدة تحكم المطور للمزيد من التفاصيل</div>
                `;
            }
        }

        // Auto-run JavaScript test after page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testOpenAIViaJS, 2000);
        });
    </script>
</body>
</html>
