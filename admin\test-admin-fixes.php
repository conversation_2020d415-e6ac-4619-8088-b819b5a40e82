<?php

/**
 * Test Admin Panel Fixes
 * This script tests all the fixes applied to the admin panel
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../php/config.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات لوحة التحكم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .success {
            color: #27ae60;
        }

        .error {
            color: #e74c3c;
        }

        .warning {
            color: #f39c12;
        }

        .info {
            color: #3498db;
        }

        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }

        .test-result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .test-result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .test-result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }

        .file-test {
            margin: 5px 0;
            padding: 5px;
            background: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🧪 اختبار إصلاحات لوحة التحكم</h1>
        <p>هذا السكريبت يختبر جميع الإصلاحات المطبقة على لوحة التحكم.</p>

        <?php
        $testResults = [];
        $totalTests = 0;
        $passedTests = 0;

        // Test 1: Database Connection and Tables
        echo '<div class="test-section">';
        echo '<h3>🗄️ اختبار قاعدة البيانات</h3>';

        try {
            $pdo = getPDOConnection();
            if ($pdo) {
                echo '<div class="test-result pass">✅ الاتصال بقاعدة البيانات ناجح</div>';
                $passedTests++;
            } else {
                echo '<div class="test-result fail">❌ فشل الاتصال بقاعدة البيانات</div>';
            }
            $totalTests++;

            // Test 'actif' column in produits table
            $stmt = $pdo->query("DESCRIBE produits");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $hasActif = false;
            foreach ($columns as $column) {
                if ($column['Field'] === 'actif') {
                    $hasActif = true;
                    break;
                }
            }

            if ($hasActif) {
                echo '<div class="test-result pass">✅ عمود "actif" موجود في جدول produits</div>';
                $passedTests++;
            } else {
                echo '<div class="test-result fail">❌ عمود "actif" غير موجود في جدول produits</div>';
            }
            $totalTests++;

            // Test products query
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits WHERE actif = 1");
            $activeProducts = $stmt->fetch()['count'];
            echo '<div class="test-result pass">✅ المنتجات النشطة: ' . $activeProducts . '</div>';
            $passedTests++;
            $totalTests++;
        } catch (Exception $e) {
            echo '<div class="test-result fail">❌ خطأ في قاعدة البيانات: ' . $e->getMessage() . '</div>';
            $totalTests++;
        }
        echo '</div>';

        // Test 2: Admin HTML Files Structure
        echo '<div class="test-section">';
        echo '<h3>📄 اختبار ملفات HTML للوحة التحكم</h3>';

        $adminFiles = [
            'ai-settings.html' => '.ai-settings-content',
            'categories-management.html' => '.categories-management-content',
            'payment-settings.html' => '.payment-settings-content',
            'general-settings.html' => '.general-settings-content',
            'user-management.html' => '.user-management-content',
            'security-settings.html' => '.security-settings-content'
        ];

        foreach ($adminFiles as $file => $expectedClass) {
            $totalTests++;
            if (file_exists($file)) {
                $content = file_get_contents($file);
                if (strpos($content, $expectedClass) !== false) {
                    echo '<div class="file-test">✅ ' . $file . ' - يحتوي على ' . $expectedClass . '</div>';
                    $passedTests++;
                } else {
                    echo '<div class="file-test">❌ ' . $file . ' - لا يحتوي على ' . $expectedClass . '</div>';
                }
            } else {
                echo '<div class="file-test">❌ ' . $file . ' - الملف غير موجود</div>';
            }
        }
        echo '</div>';

        // Test 3: JavaScript Files
        echo '<div class="test-section">';
        echo '<h3>📜 اختبار ملفات JavaScript</h3>';

        $jsFiles = [
            'js/admin.js' => ['loadAISettingsContent', 'loadCategoriesManagementContent', 'loadPaymentSettingsContent'],
            'js/ai-settings.js' => ['loadAISettings', 'setupEventListeners'],
            'js/categories-management.js' => ['initializeCategoriesManagement'],
            'js/payment-settings.js' => ['initializePaymentSettings']
        ];

        foreach ($jsFiles as $file => $functions) {
            $totalTests++;
            if (file_exists($file)) {
                $content = file_get_contents($file);
                $allFunctionsFound = true;
                $missingFunctions = [];

                foreach ($functions as $function) {
                    if (strpos($content, $function) === false) {
                        $allFunctionsFound = false;
                        $missingFunctions[] = $function;
                    }
                }

                if ($allFunctionsFound) {
                    echo '<div class="file-test">✅ ' . $file . ' - جميع الوظائف موجودة</div>';
                    $passedTests++;
                } else {
                    echo '<div class="file-test">⚠️ ' . $file . ' - وظائف مفقودة: ' . implode(', ', $missingFunctions) . '</div>';
                }
            } else {
                echo '<div class="file-test">❌ ' . $file . ' - الملف غير موجود</div>';
            }
        }
        echo '</div>';

        // Test 4: API Endpoints
        echo '<div class="test-section">';
        echo '<h3>🔌 اختبار نقاط API</h3>';

        $apiFiles = [
            '../php/api/products.php',
            '../php/api/categories.php',
            '../php/admin.php'
        ];

        foreach ($apiFiles as $file) {
            $totalTests++;
            if (file_exists($file)) {
                echo '<div class="file-test">✅ ' . basename($file) . ' - موجود</div>';
                $passedTests++;
            } else {
                echo '<div class="file-test">❌ ' . basename($file) . ' - غير موجود</div>';
            }
        }
        echo '</div>';

        // Test 5: Content Loading Simulation
        echo '<div class="test-section">';
        echo '<h3>🔄 اختبار تحميل المحتوى</h3>';

        $contentTests = [
            'ai-settings.html' => 'إعدادات الذكاء الاصطناعي',
            'categories-management.html' => 'إدارة الفئات',
            'payment-settings.html' => 'إعدادات الدفع'
        ];

        foreach ($contentTests as $file => $description) {
            $totalTests++;
            if (file_exists($file)) {
                $content = file_get_contents($file);

                // Check if content can be parsed
                $dom = new DOMDocument();
                libxml_use_internal_errors(true);
                $loaded = $dom->loadHTML($content);
                libxml_clear_errors();

                if ($loaded) {
                    echo '<div class="file-test">✅ ' . $description . ' - يمكن تحليل HTML بنجاح</div>';
                    $passedTests++;
                } else {
                    echo '<div class="file-test">❌ ' . $description . ' - خطأ في تحليل HTML</div>';
                }
            } else {
                echo '<div class="file-test">❌ ' . $description . ' - الملف غير موجود</div>';
            }
        }
        echo '</div>';

        // Test 6: Database Migration Files
        echo '<div class="test-section">';
        echo '<h3>🔧 اختبار ملفات الترحيل</h3>';

        $migrationFiles = [
            '../database/migrations/fix_admin_panel_database.sql',
            '../database/migrations/add_product_active_status.sql'
        ];

        foreach ($migrationFiles as $file) {
            $totalTests++;
            if (file_exists($file)) {
                echo '<div class="file-test">✅ ' . basename($file) . ' - موجود</div>';
                $passedTests++;
            } else {
                echo '<div class="file-test">❌ ' . basename($file) . ' - غير موجود</div>';
            }
        }
        echo '</div>';

        // Summary
        echo '<div class="test-section">';
        echo '<h3>📊 ملخص النتائج</h3>';

        $successRate = ($totalTests > 0) ? round(($passedTests / $totalTests) * 100, 2) : 0;

        if ($successRate >= 90) {
            $statusClass = 'pass';
            $statusIcon = '🎉';
            $statusText = 'ممتاز';
        } elseif ($successRate >= 70) {
            $statusClass = 'warning';
            $statusIcon = '⚠️';
            $statusText = 'جيد مع تحذيرات';
        } else {
            $statusClass = 'fail';
            $statusIcon = '❌';
            $statusText = 'يحتاج إصلاح';
        }

        echo '<div class="test-result ' . $statusClass . '">';
        echo $statusIcon . ' <strong>النتيجة النهائية:</strong> ' . $passedTests . '/' . $totalTests . ' اختبار نجح (' . $successRate . '%) - ' . $statusText;
        echo '</div>';

        if ($successRate >= 90) {
            echo '<div class="test-result pass">';
            echo '✅ <strong>تم إصلاح جميع المشاكل بنجاح!</strong><br>';
            echo 'يمكنك الآن استخدام لوحة التحكم بدون مشاكل.';
            echo '</div>';
        } else {
            echo '<div class="test-result warning">';
            echo '⚠️ <strong>هناك بعض المشاكل التي تحتاج إصلاح.</strong><br>';
            echo 'يرجى مراجعة النتائج أعلاه وإصلاح المشاكل المحددة.';
            echo '</div>';
        }

        echo '<h4>🔧 أدوات الإصلاح والاختبار:</h4>';
        echo '<p><a href="index.html" style="background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 10px;">العودة إلى لوحة التحكم</a></p>';
        echo '<p><a href="fix-database.php" style="background: #e67e22; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 10px;">تشغيل إصلاح قاعدة البيانات</a></p>';
        echo '<p><a href="test-categories-api.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 10px;">اختبار API الفئات</a></p>';

        echo '</div>';
        ?>

    </div>
</body>

</html>
