/**
 * Navigation Emergency Fix CSS
 * إصلاح طارئ لمشكلة عرض جميع الأقسام في نفس الوقت
 */

/* Force hide all content sections by default */
.content-section {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1 !important;
    pointer-events: none !important;
}

/* Show only the active section */
.content-section.active {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: static !important;
    left: auto !important;
    top: auto !important;
    z-index: 1 !important;
    pointer-events: auto !important;
}

/* Ensure dashboard is visible by default */
#dashboard {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: static !important;
    left: auto !important;
    top: auto !important;
    z-index: 1 !important;
    pointer-events: auto !important;
}

/* Hide non-dashboard sections specifically */
#books:not(.active),
#orders:not(.active),
#landingPages:not(.active),
#reports:not(.active),
#settings:not(.active),
#categoriesManagement:not(.active),
#paymentSettings:not(.active),
#generalSettings:not(.active),
#storeSettings:not(.active),
#userManagement:not(.active),
#storesManagement:not(.active),
#rolesManagement:not(.active),
#subscriptionsManagement:not(.active),
#securitySettings:not(.active),
#systemTesting:not(.active) {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1 !important;
    pointer-events: none !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* Ensure proper spacing for visible sections */
.content-section.active {
    margin: 0 !important;
    padding: 20px !important;
    width: 100% !important;
    height: auto !important;
    overflow: visible !important;
}

/* Navigation active state */
.admin-nav ul li.active {
    background: rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    transform: translateX(-3px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Emergency override for any conflicting styles */
.admin-content .content-section:not(.active) {
    display: none !important;
}

.admin-content .content-section.active {
    display: block !important;
}

/* Specific fixes for dashboard content */
#dashboard.content-section.active {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: static !important;
    left: auto !important;
    top: auto !important;
    z-index: 1 !important;
    pointer-events: auto !important;
    height: auto !important;
    overflow: visible !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 20px !important;
}

/* Ensure dashboard shows only its own content */
#dashboard .stats-grid,
#dashboard .recent-orders,
#dashboard .quick-actions,
#dashboard .system-status {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Hide any accidentally visible content from other sections */
.content-section:not(#dashboard):not(.active) * {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Force clean state on page load */
body.admin-page .content-section:not(#dashboard) {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* Animation for smooth transitions */
.content-section.active {
    animation: fadeIn 0.3s ease-in-out !important;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile responsive fixes */
@media (max-width: 768px) {
    .content-section.active {
        padding: 15px !important;
    }
    
    .content-section:not(.active) {
        display: none !important;
    }
}

/* Print styles - hide non-active sections */
@media print {
    .content-section:not(.active) {
        display: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .content-section.active {
        border: 2px solid currentColor !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .content-section.active {
        animation: none !important;
        transition: none !important;
    }
}

/* Debug mode - uncomment to see section boundaries */
/*
.content-section {
    border: 2px solid red !important;
}

.content-section.active {
    border: 2px solid green !important;
}
*/
