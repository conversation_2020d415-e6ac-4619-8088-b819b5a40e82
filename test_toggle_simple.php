<?php
/**
 * Simple test for toggle functionality
 */

require_once 'php/config.php';

echo "=== Simple Toggle Test ===\n";

// Get a test product
$stmt = $conn->query("SELECT id, titre, actif FROM produits LIMIT 1");
$product = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$product) {
    echo "No products found to test\n";
    exit(1);
}

echo "Testing with product: {$product['titre']} (ID: {$product['id']})\n";
echo "Current status: " . ($product['actif'] ? 'Active' : 'Inactive') . "\n";

// Test the toggle logic directly
$productId = $product['id'];
$currentStatus = (bool)$product['actif'];
$newStatus = !$currentStatus;

echo "Toggling to: " . ($newStatus ? 'Active' : 'Inactive') . "\n";

try {
    // Execute the same logic as the API
    $stmt = $conn->prepare("UPDATE produits SET actif = ? WHERE id = ?");
    $result = $stmt->execute([$newStatus ? 1 : 0, $productId]);

    if ($result && $stmt->rowCount() > 0) {
        echo "✓ Update successful\n";
        
        // Verify the change
        $stmt = $conn->prepare("SELECT actif FROM produits WHERE id = ?");
        $stmt->execute([$productId]);
        $updatedStatus = (bool)$stmt->fetchColumn();
        
        if ($updatedStatus === $newStatus) {
            echo "✓ Status verified: " . ($updatedStatus ? 'Active' : 'Inactive') . "\n";
            echo "✅ Toggle functionality works correctly!\n";
        } else {
            echo "✗ Status verification failed\n";
        }
        
        // Restore original status
        $stmt = $conn->prepare("UPDATE produits SET actif = ? WHERE id = ?");
        $stmt->execute([$currentStatus ? 1 : 0, $productId]);
        echo "✓ Restored original status\n";
        
    } else {
        echo "✗ Update failed or no rows affected\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
