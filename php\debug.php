<?php
function initDebugLog()
{
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);

    $logFile = __DIR__ . '/debug.log';
    ini_set('error_log', $logFile);

    error_log("Debug logging initialized");
    return $logFile;
}

function debugLog($message, $context = [])
{
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? " Context: " . json_encode($context) : "";
    error_log("[$timestamp] $message$contextStr");
}
