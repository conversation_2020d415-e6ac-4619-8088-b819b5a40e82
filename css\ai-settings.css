/* AI Settings Page Styles */
:root {
    --primary-color: #4A6CF7;
    --secondary-color: #6B7280;
    --success-color: #10B981;
    --background-color: #F3F4F6;
    --card-background: #FFFFFF;
    --border-color: #E5E7EB;
    --text-primary: #1F2937;
    --text-secondary: #4B5563;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.ai-settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.ai-settings-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.ai-settings-header h1 {
    font-size: 1.875rem;
    color: var(--text-primary);
    font-weight: 600;
    margin: 0;
}

.api-section {
    background: var(--card-background);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow);
}

.api-section-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.api-section-header h2 {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
}

.api-section-header .api-icon {
    width: 2rem;
    height: 2rem;
    margin-left: 1rem;
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.inactive {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--secondary-color);
}

.ai-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.feature-card {
    background: var(--card-background);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.feature-card h3 {
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
}

.feature-card p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.5;
}

.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
}

.primary-button {
    background-color: var(--primary-color);
    color: white;
}

.primary-button:hover {
    background-color: #405CD9;
}

.secondary-button {
    background-color: var(--background-color);
    color: var(--text-primary);
}

.secondary-button:hover {
    background-color: #E5E7EB;
}

/* Loading indicator */
#loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error and success messages */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    direction: rtl;
    max-width: 80%;
    word-wrap: break-word;
}

.error-message {
    background-color: #f44336;
    border-left: 4px solid #d32f2f;
}

.success-message {
    background-color: #4CAF50;
    border-left: 4px solid #388E3C;
}

/* Improved form validation styles */
.form-input:invalid {
    border-color: #f44336;
}

.form-input:invalid + .validation-message {
    display: block;
    color: #f44336;
    font-size: 12px;
    margin-top: 4px;
}

/* Animation for message appearing/disappearing */
@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

.message.show {
    animation: slideIn 0.3s ease-out forwards;
}

.message.hide {
    animation: slideOut 0.3s ease-in forwards;
}

/* Focus styles for better accessibility */
.form-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Improved button styles */
button {
    position: relative;
    overflow: hidden;
}

button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

button:active::after {
    width: 200px;
    height: 200px;
}

/* Content Section Styles */
.content-section {
    display: none;
    padding: 20px;
    background: var(--background-color);
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 500;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* RTL Support */
[dir="rtl"] .api-section-header .api-icon {
    margin-left: 0;
    margin-right: 1rem;
}

/* Fix for RTL layout */
[dir="rtl"] .loading-overlay {
    text-align: right;
}

[dir="rtl"] .loading-spinner {
    margin-left: 0;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-settings-container {
        padding: 1rem;
    }

    .ai-features {
        grid-template-columns: 1fr;
    }

    .api-section {
        padding: 1rem;
    }

    .message {
        width: 90%;
        right: 5%;
    }

    #loading-indicator {
        width: 80%;
        max-width: 300px;
    }
}

/* Admin Panel Integration Styles */
.admin-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: var(--background-color, #f5f5f5);
}

.content-section {
    display: none;
    width: 100%;
    height: 100%;
}

.content-section.active {
    display: block;
}

.main-content {
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
}
