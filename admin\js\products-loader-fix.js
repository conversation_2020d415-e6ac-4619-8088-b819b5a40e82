/**
 * Products Loader Fix
 * Correction du chargement des produits dans l'interface admin
 */

(function() {
    'use strict';
    
    console.log('🔧 Products Loader Fix loading...');
    
    // Enhanced product loading function
    function loadProductsWithRetry() {
        console.log('📦 Loading products with retry mechanism...');
        
        const productsContainer = document.getElementById('books');
        if (!productsContainer) {
            console.error('Products container not found');
            return;
        }
        
        // Show loading state
        showProductsLoading();
        
        // Try multiple API endpoints
        const apiEndpoints = [
            '../php/api/products.php',
            '/php/api/products.php',
            'php/api/products.php'
        ];
        
        tryLoadProducts(apiEndpoints, 0);
    }
    
    // Try loading products from different endpoints
    function tryLoadProducts(endpoints, index) {
        if (index >= endpoints.length) {
            showProductsError('Impossible de charger les produits depuis tous les endpoints');
            return;
        }
        
        const endpoint = endpoints[index];
        console.log(`🔄 Trying endpoint: ${endpoint}`);
        
        fetch(endpoint, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
        .then(response => {
            console.log(`📡 Response from ${endpoint}:`, response.status);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return response.json();
        })
        .then(data => {
            console.log('📦 Products data received:', data);
            
            if (data.success && data.data) {
                displayProducts(data.data);
                console.log('✅ Products loaded successfully');
            } else {
                throw new Error(data.message || 'Invalid response format');
            }
        })
        .catch(error => {
            console.warn(`⚠️ Failed to load from ${endpoint}:`, error.message);
            
            // Try next endpoint
            setTimeout(() => {
                tryLoadProducts(endpoints, index + 1);
            }, 500);
        });
    }
    
    // Show loading state
    function showProductsLoading() {
        const productsContainer = document.getElementById('books');
        if (productsContainer) {
            productsContainer.innerHTML = `
                <div class="loading-container" style="text-align: center; padding: 40px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <h3 style="margin-top: 20px;">جاري تحميل المنتجات...</h3>
                    <p>يرجى الانتظار بينما نقوم بتحميل قائمة المنتجات</p>
                </div>
            `;
        }
    }
    
    // Show error state
    function showProductsError(message) {
        const productsContainer = document.getElementById('books');
        if (productsContainer) {
            productsContainer.innerHTML = `
                <div class="error-container" style="text-align: center; padding: 40px;">
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">❌ خطأ في تحميل المنتجات</h4>
                        <p>${message}</p>
                        <hr>
                        <button class="btn btn-outline-danger" onclick="window.productsLoader.reload()">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </div>
                </div>
            `;
        }
    }
    
    // Display products
    function displayProducts(products) {
        const productsContainer = document.getElementById('books');
        if (!productsContainer) return;
        
        if (!products || products.length === 0) {
            productsContainer.innerHTML = `
                <div class="empty-state" style="text-align: center; padding: 40px;">
                    <i class="fas fa-box-open fa-3x text-muted"></i>
                    <h3 style="margin-top: 20px;">لا توجد منتجات</h3>
                    <p>لم يتم العثور على أي منتجات في قاعدة البيانات</p>
                    <button class="btn btn-primary" onclick="showAddProductModal()">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </button>
                </div>
            `;
            return;
        }
        
        // Create products table
        let productsHTML = `
            <div class="products-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2><i class="fas fa-box"></i> إدارة المنتجات</h2>
                <button class="btn btn-primary" onclick="showAddProductModal()">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </button>
            </div>
            
            <div class="products-stats" style="margin-bottom: 20px;">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-card" style="background: #d4edda; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4>${products.length}</h4>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card" style="background: #d1ecf1; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4>${products.filter(p => p.actif == 1).length}</h4>
                            <p>منتجات نشطة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card" style="background: #fff3cd; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4>${products.filter(p => p.actif == 0).length}</h4>
                            <p>منتجات غير نشطة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card" style="background: #f8d7da; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4>${products.filter(p => p.has_landing_page).length}</h4>
                            <p>لها صفحات هبوط</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الصورة</th>
                            <th>العنوان</th>
                            <th>السعر</th>
                            <th>الحالة</th>
                            <th>صفحة الهبوط</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        products.forEach(product => {
            const statusBadge = product.actif == 1 ? 
                '<span class="badge bg-success">نشط</span>' : 
                '<span class="badge bg-secondary">غير نشط</span>';
            
            const landingPageBadge = product.has_landing_page ? 
                '<span class="badge bg-info">متوفرة</span>' : 
                '<span class="badge bg-warning">غير متوفرة</span>';
            
            const imageUrl = product.image_url || '/images/placeholder.jpg';
            
            productsHTML += `
                <tr>
                    <td>
                        <img src="${imageUrl}" alt="${product.titre}" 
                             style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;"
                             onerror="this.src='/images/placeholder.jpg'">
                    </td>
                    <td>
                        <strong>${product.titre}</strong>
                        <br><small class="text-muted">${product.description ? product.description.substring(0, 50) + '...' : ''}</small>
                    </td>
                    <td><strong>${product.prix} دج</strong></td>
                    <td>${statusBadge}</td>
                    <td>${landingPageBadge}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary" onclick="editProduct(${product.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="viewProduct(${product.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${product.has_landing_page ? 
                                `<button class="btn btn-sm btn-outline-success" onclick="viewLandingPage(${product.id})" title="صفحة الهبوط">
                                    <i class="fas fa-external-link-alt"></i>
                                </button>` : 
                                `<button class="btn btn-sm btn-outline-warning" onclick="createLandingPage(${product.id})" title="إنشاء صفحة هبوط">
                                    <i class="fas fa-plus"></i>
                                </button>`
                            }
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct(${product.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
        
        productsHTML += `
                    </tbody>
                </table>
            </div>
            
            <div class="products-footer" style="margin-top: 20px; text-align: center;">
                <button class="btn btn-outline-secondary" onclick="window.productsLoader.reload()">
                    <i class="fas fa-redo"></i> إعادة تحميل
                </button>
            </div>
        `;
        
        productsContainer.innerHTML = productsHTML;
        
        console.log(`✅ Displayed ${products.length} products`);
    }
    
    // Enhanced loadProducts function to replace the existing one
    function enhancedLoadProducts() {
        console.log('📦 Enhanced loadProducts called');
        loadProductsWithRetry();
    }
    
    // Public API
    window.productsLoader = {
        load: loadProductsWithRetry,
        reload: loadProductsWithRetry,
        enhance: enhancedLoadProducts
    };
    
    // Override existing loadProducts function if it exists
    if (typeof window.loadProducts === 'function') {
        const originalLoadProducts = window.loadProducts;
        window.loadProducts = function() {
            console.log('🔄 Overriding loadProducts with enhanced version');
            loadProductsWithRetry();
        };
    } else {
        window.loadProducts = enhancedLoadProducts;
    }
    
    // Auto-load products when this script loads
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📦 Products Loader Fix ready');
        
        // Load products if we're on the products section
        const productsSection = document.getElementById('books');
        if (productsSection && productsSection.classList.contains('active')) {
            setTimeout(loadProductsWithRetry, 1000);
        }
    });
    
    console.log('✅ Products Loader Fix loaded successfully');
    
})();
