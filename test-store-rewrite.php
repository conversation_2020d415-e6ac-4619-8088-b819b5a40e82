<?php
// Simple test file to debug store URL rewriting
echo "🔍 STORE URL REWRITE TEST\n";
echo "========================\n\n";

echo "📋 Request Information:\n";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "\n";
echo "QUERY_STRING: " . ($_SERVER['QUERY_STRING'] ?? 'Not set') . "\n";
echo "GET parameters: " . print_r($_GET, true) . "\n";

echo "\n📋 Expected vs Actual:\n";
echo "Expected: store.php?store=mossaab-store\n";
echo "Actual: " . $_SERVER['REQUEST_URI'] . "\n";

if (isset($_GET['store'])) {
    echo "\n✅ Store parameter received: " . $_GET['store'] . "\n";
    echo "✅ URL rewriting is working!\n";
} else {
    echo "\n❌ Store parameter NOT received\n";
    echo "❌ URL rewriting is NOT working\n";
}

echo "\n📋 This file should only be called if rewriting works\n";
echo "If you see this, the .htaccess rule is being processed\n";
?>
