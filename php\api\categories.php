<?php

/**
 * Categories API - Complete CRUD operations for dynamic categories management
 */

// Define security check constant BEFORE including security.php - only if not already defined
if (!defined('SECURITY_CHECK')) {
    define('SECURITY_CHECK', true);
}

require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../debug.php';
require_once __DIR__ . '/../security.php';
require_once __DIR__ . '/../SecurityHeaders.php';

// Initialize debug logging
$logFile = initDebugLog();
debugLog("Categories API initialized");

// Set security headers for XSS protection
SecurityHeaders::setSecurityHeaders();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

function handleGet()
{
    try {
        $db = Database::getInstance();
        $id = $_GET['id'] ?? null;

        if ($id) {
            // Get specific category
            $stmt = $db->prepare("SELECT * FROM categories WHERE id = ?");
            $stmt->execute([$id]);
            $category = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$category) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Category not found']);
                return;
            }

            // Get products count for this category
            $countStmt = $db->prepare("SELECT COUNT(*) as products_count FROM produits WHERE category_id = ?");
            $countStmt->execute([$id]);
            $category['products_count'] = $countStmt->fetch()['products_count'];

            echo json_encode(['success' => true, 'category' => $category]);
        } else {
            // Get all categories
            $activeOnly = $_GET['active_only'] ?? false;
            $sql = "SELECT * FROM categories";

            if ($activeOnly) {
                $sql .= " WHERE actif = 1";
            }

            $sql .= " ORDER BY ordre_affichage ASC, nom_ar ASC";

            $stmt = $db->prepare($sql);
            $stmt->execute();
            $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Add products count for each category
            foreach ($categories as &$category) {
                $countStmt = $db->prepare("SELECT COUNT(*) as products_count FROM produits WHERE category_id = ?");
                $countStmt->execute([$category['id']]);
                $category['products_count'] = $countStmt->fetch()['products_count'];
            }

            echo json_encode(['success' => true, 'categories' => $categories]);
        }
    } catch (PDOException $e) {
        error_log('Database error in categories GET: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    }
}

function handlePost()
{
    try {
        $db = Database::getInstance();
        $input = json_decode(file_get_contents('php://input'), true);

        // Validate required fields
        if (empty($input['nom_ar']) || empty($input['nom_en'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Arabic and English names are required']);
            return;
        }

        // Check for duplicate names
        $checkStmt = $db->prepare("SELECT id FROM categories WHERE nom_ar = ? OR nom_en = ?");
        $checkStmt->execute([$input['nom_ar'], $input['nom_en']]);

        if ($checkStmt->rowCount() > 0) {
            http_response_code(409);
            echo json_encode(['success' => false, 'message' => 'Category name already exists']);
            return;
        }

        $db->beginTransaction();

        // Get next order if not specified
        $ordre = $input['ordre_affichage'] ?? null;
        if ($ordre === null) {
            $orderStmt = $db->query("SELECT COALESCE(MAX(ordre_affichage), 0) + 1 as next_order FROM categories");
            $ordre = $orderStmt->fetch()['next_order'];
        }

        $stmt = $db->prepare("
            INSERT INTO categories (nom_ar, nom_en, description_ar, description_en, icone, couleur, ordre_affichage, actif)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            $input['nom_ar'],
            $input['nom_en'],
            $input['description_ar'] ?? '',
            $input['description_en'] ?? '',
            $input['icone'] ?? 'fas fa-box',
            $input['couleur'] ?? '#007bff',
            $ordre,
            $input['actif'] ?? 1
        ]);

        if ($result) {
            $categoryId = $db->lastInsertId();
            $db->commit();
            echo json_encode(['success' => true, 'message' => 'Category created successfully', 'id' => $categoryId]);
        } else {
            $db->rollBack();
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to create category']);
        }
    } catch (PDOException $e) {
        if ($db->getPDO()->inTransaction()) {
            $db->rollBack();
        }
        error_log('Database error in categories POST: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    }
}

function handlePut()
{
    try {
        $db = Database::getInstance();
        $input = json_decode(file_get_contents('php://input'), true);
        $id = $input['id'] ?? null;

        if (!$id) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Category ID is required']);
            return;
        }

        // Check if category exists
        $checkStmt = $db->prepare("SELECT id FROM categories WHERE id = ?");
        $checkStmt->execute([$id]);

        if ($checkStmt->rowCount() == 0) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Category not found']);
            return;
        }

        // Check for duplicate names (excluding current category)
        if (!empty($input['nom_ar']) || !empty($input['nom_en'])) {
            $duplicateStmt = $db->prepare("SELECT id FROM categories WHERE (nom_ar = ? OR nom_en = ?) AND id != ?");
            $duplicateStmt->execute([
                $input['nom_ar'] ?? '',
                $input['nom_en'] ?? '',
                $id
            ]);

            if ($duplicateStmt->rowCount() > 0) {
                http_response_code(409);
                echo json_encode(['success' => false, 'message' => 'Category name already exists']);
                return;
            }
        }

        $db->beginTransaction();

        // Build dynamic update query
        $updateFields = [];
        $updateValues = [];

        $allowedFields = ['nom_ar', 'nom_en', 'description_ar', 'description_en', 'icone', 'couleur', 'ordre_affichage', 'actif'];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "$field = ?";
                $updateValues[] = $input[$field];
            }
        }

        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No valid fields to update']);
            return;
        }

        $updateValues[] = $id; // Add ID for WHERE clause

        $sql = "UPDATE categories SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $db->prepare($sql);

        $result = $stmt->execute($updateValues);

        if ($result) {
            $db->commit();
            echo json_encode(['success' => true, 'message' => 'Category updated successfully']);
        } else {
            $db->rollBack();
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update category']);
        }
    } catch (PDOException $e) {
        if ($db->getPDO()->inTransaction()) {
            $db->rollBack();
        }
        error_log('Database error in categories PUT: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    }
}

function handleDelete()
{
    try {
        $db = Database::getInstance();
        $id = $_GET['id'] ?? null;

        if (!$id) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Category ID is required']);
            return;
        }

        // Check if category exists
        $checkStmt = $db->prepare("SELECT id FROM categories WHERE id = ?");
        $checkStmt->execute([$id]);

        if ($checkStmt->rowCount() == 0) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Category not found']);
            return;
        }

        // Check if category has products
        $productsStmt = $db->prepare("SELECT COUNT(*) as count FROM produits WHERE category_id = ?");
        $productsStmt->execute([$id]);
        $productsCount = $productsStmt->fetch()['count'];

        if ($productsCount > 0) {
            http_response_code(409);
            echo json_encode([
                'success' => false,
                'message' => "Cannot delete category. It has $productsCount products assigned to it.",
                'products_count' => $productsCount
            ]);
            return;
        }

        $db->beginTransaction();

        $stmt = $db->prepare("DELETE FROM categories WHERE id = ?");
        $result = $stmt->execute([$id]);

        if ($result) {
            $db->commit();
            echo json_encode(['success' => true, 'message' => 'Category deleted successfully']);
        } else {
            $db->rollBack();
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to delete category']);
        }
    } catch (PDOException $e) {
        if ($db->getPDO()->inTransaction()) {
            $db->rollBack();
        }
        error_log('Database error in categories DELETE: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    }
}

// Route requests
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        handleGet();
        break;
    case 'POST':
        handlePost();
        break;
    case 'PUT':
        handlePut();
        break;
    case 'DELETE':
        handleDelete();
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
