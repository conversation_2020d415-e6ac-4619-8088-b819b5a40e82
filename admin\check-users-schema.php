<?php
/**
 * Check Users Table Schema
 * Investigates the actual column names in the users table
 */

require_once '../php/config.php';

try {
    $pdo = getPDOConnection();
    
    echo "🔍 Investigating Users Table Schema...\n\n";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if (!$stmt->fetch()) {
        echo "❌ Users table does not exist!\n";
        exit(1);
    }
    
    echo "✅ Users table exists\n\n";
    
    // Get table structure
    echo "📋 Users Table Structure:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo sprintf("%-20s %-15s %-10s %-10s %-15s %s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Key'], 
            $column['Default'], 
            $column['Extra']
        );
    }
    
    echo "\n📊 Column Analysis:\n";
    echo "=" . str_repeat("=", 30) . "\n";
    
    $columnNames = array_column($columns, 'Field');
    
    // Check for name-related columns
    $nameColumns = array_filter($columnNames, function($col) {
        return stripos($col, 'nom') !== false || 
               stripos($col, 'name') !== false || 
               stripos($col, 'username') !== false ||
               stripos($col, 'full') !== false;
    });
    
    echo "🏷️ Name-related columns found:\n";
    foreach ($nameColumns as $col) {
        echo "   - {$col}\n";
    }
    
    // Check for email column
    $emailColumns = array_filter($columnNames, function($col) {
        return stripos($col, 'email') !== false || stripos($col, 'mail') !== false;
    });
    
    echo "\n📧 Email-related columns found:\n";
    foreach ($emailColumns as $col) {
        echo "   - {$col}\n";
    }
    
    // Check for phone columns
    $phoneColumns = array_filter($columnNames, function($col) {
        return stripos($col, 'phone') !== false || 
               stripos($col, 'telephone') !== false || 
               stripos($col, 'tel') !== false ||
               stripos($col, 'mobile') !== false;
    });
    
    echo "\n📱 Phone-related columns found:\n";
    foreach ($phoneColumns as $col) {
        echo "   - {$col}\n";
    }
    
    // Show sample data
    echo "\n📋 Sample User Data:\n";
    echo "=" . str_repeat("=", 40) . "\n";
    
    $stmt = $pdo->query("SELECT * FROM users LIMIT 3");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "⚠️ No users found in the table\n";
    } else {
        echo "Found " . count($users) . " sample users:\n\n";
        foreach ($users as $index => $user) {
            echo "User " . ($index + 1) . ":\n";
            foreach ($user as $key => $value) {
                echo "   {$key}: " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value) . "\n";
            }
            echo "\n";
        }
    }
    
    // Generate suggested API query
    echo "🔧 Suggested API Query Fix:\n";
    echo "=" . str_repeat("=", 35) . "\n";
    
    // Determine the best column mappings
    $nameCol = 'nom'; // default
    if (in_array('name', $columnNames)) {
        $nameCol = 'name';
    } elseif (in_array('username', $columnNames)) {
        $nameCol = 'username';
    } elseif (in_array('full_name', $columnNames)) {
        $nameCol = 'full_name';
    } elseif (in_array('nom', $columnNames)) {
        $nameCol = 'nom';
    }
    
    $emailCol = 'email'; // default
    if (!in_array('email', $columnNames)) {
        $emailCandidates = array_filter($columnNames, function($col) {
            return stripos($col, 'email') !== false || stripos($col, 'mail') !== false;
        });
        if (!empty($emailCandidates)) {
            $emailCol = reset($emailCandidates);
        }
    }
    
    $phoneCol = 'telephone'; // default
    if (!in_array('telephone', $columnNames)) {
        $phoneCandidates = array_filter($columnNames, function($col) {
            return stripos($col, 'phone') !== false || 
                   stripos($col, 'tel') !== false ||
                   stripos($col, 'mobile') !== false;
        });
        if (!empty($phoneCandidates)) {
            $phoneCol = reset($phoneCandidates);
        }
    }
    
    echo "Recommended column mappings:\n";
    echo "   Name column: u.{$nameCol} as owner_name\n";
    echo "   Email column: u.{$emailCol} as owner_email\n";
    echo "   Phone column: u.{$phoneCol} as owner_phone\n";
    
    echo "\nSuggested SQL query:\n";
    echo "```sql\n";
    echo "SELECT \n";
    echo "    s.*,\n";
    echo "    u.{$nameCol} as owner_name,\n";
    echo "    u.{$emailCol} as owner_email,\n";
    echo "    u.{$phoneCol} as owner_phone,\n";
    echo "    u.created_at as user_created_at\n";
    echo "FROM stores s\n";
    echo "LEFT JOIN users u ON s.user_id = u.id\n";
    echo "ORDER BY s.created_at DESC\n";
    echo "```\n";
    
    // Test the suggested query
    echo "\n🧪 Testing Suggested Query:\n";
    echo "=" . str_repeat("=", 30) . "\n";
    
    try {
        $testSql = "
            SELECT 
                s.*,
                u.{$nameCol} as owner_name,
                u.{$emailCol} as owner_email,
                u.{$phoneCol} as owner_phone,
                u.created_at as user_created_at
            FROM stores s
            LEFT JOIN users u ON s.user_id = u.id
            ORDER BY s.created_at DESC
            LIMIT 1
        ";
        
        $stmt = $pdo->prepare($testSql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            echo "✅ Query executed successfully!\n";
            echo "Sample result:\n";
            echo "   Store: {$result['store_name']}\n";
            echo "   Owner: {$result['owner_name']}\n";
            echo "   Email: {$result['owner_email']}\n";
            echo "   Phone: {$result['owner_phone']}\n";
        } else {
            echo "⚠️ Query executed but no results found\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Query test failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎉 Schema investigation complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error investigating schema: " . $e->getMessage() . "\n";
}
?>
