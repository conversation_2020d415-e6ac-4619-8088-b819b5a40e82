<?php
/**
 * Database Schema Analysis
 * Check current database structure and user relationships
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <title>Database Schema Analysis</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; white-space: pre-wrap; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔍 Database Schema Analysis</h1>";
echo "<p>Analyzing current database structure for multi-user system implementation</p>";

try {
    require_once 'php/config.php';
    $pdo = getPDOConnection();
    
    if (!$pdo) {
        throw new Exception("Database connection failed");
    }
    
    echo "<div class='success'>✅ Database connection established</div>";
    
    // Get all tables
    echo "<div class='section'>";
    echo "<h2>📋 Database Tables</h2>";
    
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='info'>Found " . count($tables) . " tables:</div>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li><strong>$table</strong></li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // Analyze key tables for user relationships
    $keyTables = ['users', 'produits', 'products', 'commandes', 'orders', 'landing_pages'];
    
    foreach ($keyTables as $tableName) {
        if (in_array($tableName, $tables)) {
            echo "<div class='section'>";
            echo "<h3>🔍 Table: $tableName</h3>";
            
            // Get table structure
            $stmt = $pdo->query("DESCRIBE $tableName");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>";
            echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            
            $hasUserId = false;
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td><strong>" . $column['Field'] . "</strong></td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . $column['Default'] . "</td>";
                echo "<td>" . $column['Extra'] . "</td>";
                echo "</tr>";
                
                if (strpos($column['Field'], 'user') !== false || strpos($column['Field'], 'utilisateur') !== false) {
                    $hasUserId = true;
                }
            }
            echo "</table>";
            
            if ($hasUserId) {
                echo "<div class='success'>✅ Table has user-related columns</div>";
            } else {
                echo "<div class='warning'>⚠️ Table missing user relationship columns</div>";
            }
            
            // Get sample data
            try {
                $stmt = $pdo->query("SELECT * FROM $tableName LIMIT 3");
                $sampleData = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($sampleData)) {
                    echo "<h4>📊 Sample Data (first 3 rows):</h4>";
                    echo "<div class='code-block'>";
                    foreach ($sampleData as $index => $row) {
                        echo "Row " . ($index + 1) . ":\n";
                        foreach ($row as $key => $value) {
                            $displayValue = is_string($value) ? substr($value, 0, 50) : $value;
                            if (strlen($value) > 50) $displayValue .= "...";
                            echo "  $key: $displayValue\n";
                        }
                        echo "\n";
                    }
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>Error getting sample data: " . $e->getMessage() . "</div>";
            }
            
            echo "</div>";
        }
    }
    
    // Check for existing user roles/permissions
    echo "<div class='section'>";
    echo "<h2>👥 User System Analysis</h2>";
    
    if (in_array('users', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $userCount = $stmt->fetch()['count'];
        echo "<div class='info'>Total users in system: $userCount</div>";
        
        // Check for role-related columns
        $stmt = $pdo->query("DESCRIBE users");
        $userColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $hasRoles = false;
        foreach ($userColumns as $column) {
            if (strpos($column['Field'], 'role') !== false || strpos($column['Field'], 'type') !== false) {
                $hasRoles = true;
                echo "<div class='success'>✅ Found role column: " . $column['Field'] . "</div>";
            }
        }
        
        if (!$hasRoles) {
            echo "<div class='warning'>⚠️ No role/permission columns found in users table</div>";
        }
        
        // Get sample users
        $stmt = $pdo->query("SELECT * FROM users LIMIT 5");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>👤 Sample Users:</h4>";
        echo "<div class='code-block'>";
        foreach ($users as $user) {
            foreach ($user as $key => $value) {
                if ($key !== 'password' && $key !== 'mot_de_passe') {
                    echo "$key: $value\n";
                }
            }
            echo "---\n";
        }
        echo "</div>";
    } else {
        echo "<div class='error'>❌ No users table found</div>";
    }
    echo "</div>";
    
    // Recommendations
    echo "<div class='section'>";
    echo "<h2>💡 Multi-User System Recommendations</h2>";
    
    $recommendations = [];
    
    // Check if products table has user_id
    if (in_array('produits', $tables)) {
        $stmt = $pdo->query("DESCRIBE produits");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        if (!in_array('user_id', $columns) && !in_array('utilisateur_id', $columns)) {
            $recommendations[] = "Add user_id column to produits table";
        }
    }
    
    // Check if landing_pages has user_id
    if (in_array('landing_pages', $tables)) {
        $stmt = $pdo->query("DESCRIBE landing_pages");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        if (!in_array('user_id', $columns) && !in_array('utilisateur_id', $columns)) {
            $recommendations[] = "Add user_id column to landing_pages table";
        }
    }
    
    // Check if orders table has user_id
    if (in_array('commandes', $tables)) {
        $stmt = $pdo->query("DESCRIBE commandes");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        if (!in_array('user_id', $columns) && !in_array('utilisateur_id', $columns)) {
            $recommendations[] = "Add user_id column to commandes table";
        }
    }
    
    if (!in_array('users', $tables)) {
        $recommendations[] = "Create users table with proper role management";
    }
    
    if (empty($recommendations)) {
        echo "<div class='success'>✅ Database schema appears ready for multi-user system</div>";
    } else {
        echo "<div class='warning'>⚠️ Required changes for multi-user system:</div>";
        echo "<ul>";
        foreach ($recommendations as $rec) {
            echo "<li>$rec</li>";
        }
        echo "</ul>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
