<?php
require_once __DIR__ . '/../php/config.php';

try {
    $pdo = getPDOConnection();
    
    // Simulate the exact store.php logic
    $storeSlug = 'mossaab-store';
    
    // Get store information
    $stmt = $pdo->prepare("
        SELECT 
            s.*,
            CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as owner_name,
            u.email as owner_email,
            u.phone as owner_phone
        FROM stores s
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.store_slug = ? AND s.status = 'active'
    ");
    $stmt->execute([$storeSlug]);
    $store = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$store) {
        echo "Store not found!";
        exit;
    }
    
    // Get store products
    $stmt = $pdo->prepare("
        SELECT p.*
        FROM produits p
        WHERE (p.store_id = ? OR p.store_id IS NULL) AND p.actif = 1
        ORDER BY p.created_at DESC
    ");
    $stmt->execute([$store['id']]);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<!DOCTYPE html>\n";
    echo "<html lang='ar' dir='rtl'>\n";
    echo "<head>\n";
    echo "<meta charset='UTF-8'>\n";
    echo "<title>Test Store Page</title>\n";
    echo "<style>body { font-family: Arial; margin: 20px; }</style>\n";
    echo "</head>\n";
    echo "<body>\n";
    
    echo "<h1>Store: " . htmlspecialchars($store['store_name']) . "</h1>\n";
    echo "<p>Store ID: " . $store['id'] . "</p>\n";
    echo "<p>Products found: " . count($products) . "</p>\n";
    
    if (!empty($products)) {
        echo "<h2>Products:</h2>\n";
        echo "<ul>\n";
        foreach ($products as $product) {
            echo "<li>";
            echo htmlspecialchars($product['titre']) . " - ";
            echo number_format($product['prix'], 0) . " DZD";
            echo " (Store ID: " . ($product['store_id'] ?? 'NULL') . ")";
            echo "</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p>No products found!</p>\n";
    }
    
    echo "</body>\n";
    echo "</html>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
