<?php
/**
 * Enhanced Landing Pages API with Better Error Handling
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set("display_errors", 1);

try {
    require_once '../config.php';
    
    header("Content-Type: application/json; charset=utf-8");
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization");
    
    if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
        exit(0);
    }
    
    $pdo = getPDOConnection();
    
    switch ($_SERVER["REQUEST_METHOD"]) {
        case "GET":
            handleGetRequest($pdo);
            break;
        case "POST":
            handlePostRequest($pdo);
            break;
        default:
            http_response_code(405);
            echo json_encode(["success" => false, "message" => "Method not allowed"]);
    }
    
} catch (Exception $e) {
    error_log("Landing Pages API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "خطأ في الخادم",
        "error" => $e->getMessage()
    ]);
}

function handleGetRequest($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT lp.*, p.titre as product_title, p.type as product_type
            FROM landing_pages lp
            LEFT JOIN produits p ON lp.produit_id = p.id
            ORDER BY lp.created_at DESC
        ");
        $stmt->execute();
        $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            "success" => true,
            "data" => $pages,
            "count" => count($pages)
        ]);
    } catch (Exception $e) {
        throw new Exception("Error fetching landing pages: " . $e->getMessage());
    }
}

function handlePostRequest($pdo) {
    try {
        $input = json_decode(file_get_contents("php://input"), true);
        
        if (!$input) {
            throw new Exception("Invalid JSON input");
        }
        
        // Basic validation
        $required_fields = ["produit_id", "titre"];
        foreach ($required_fields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url, template_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $input["produit_id"],
            $input["titre"],
            $input["contenu_droit"] ?? "",
            $input["contenu_gauche"] ?? "",
            $input["lien_url"] ?? "/landing/" . uniqid(),
            $input["template_id"] ?? "default"
        ]);
        
        $id = $pdo->lastInsertId();
        
        echo json_encode([
            "success" => true,
            "message" => "تم إنشاء صفحة الهبوط بنجاح",
            "id" => $id
        ]);
        
    } catch (Exception $e) {
        throw new Exception("Error creating landing page: " . $e->getMessage());
    }
}
?>