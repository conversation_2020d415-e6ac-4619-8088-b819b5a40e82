<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Clics Navigation</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        .test-item.success {
            border-left-color: #27ae60;
            background: #d5f4e6;
        }
        .test-item.error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        #console-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test des clics de navigation</h1>
        
        <div id="test-results"></div>
        
        <button onclick="testDirectClick()">Test clic direct</button>
        <button onclick="testAdminPageInFrame()">Charger admin dans iframe</button>
        <button onclick="clearConsole()">Effacer console</button>
        
        <div id="console-output"></div>
        
        <iframe id="admin-frame" style="display: none;"></iframe>
    </div>

    <script>
        const testResults = document.getElementById('test-results');
        const consoleOutput = document.getElementById('console-output');
        
        function logToOutput(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.innerHTML += `<div>[${timestamp}] ${type}: ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function addTestResult(title, result, success = true) {
            const testItem = document.createElement('div');
            testItem.className = `test-item ${success ? 'success' : 'error'}`;
            testItem.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 5px;">${title}</div>
                <div style="font-size: 0.9em; color: #666;">${result}</div>
            `;
            testResults.appendChild(testItem);
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
            testResults.innerHTML = '';
        }
        
        function testDirectClick() {
            testResults.innerHTML = '';
            
            // Test direct access to admin page
            fetch('/admin/index.html')
                .then(response => response.text())
                .then(html => {
                    addTestResult('Admin Page Access', 'Page accessible', true);
                    
                    // Parse HTML to check structure
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    
                    // Check navigation structure
                    const adminNav = doc.querySelector('.admin-nav');
                    if (adminNav) {
                        addTestResult('Navigation Container', 'Conteneur .admin-nav trouvé', true);
                        
                        const navUl = adminNav.querySelector('ul');
                        if (navUl) {
                            addTestResult('Navigation List', 'Liste ul trouvée', true);
                            
                            const navItems = navUl.querySelectorAll('li');
                            addTestResult('Navigation Items', `${navItems.length} éléments li trouvés`, navItems.length > 0);
                            
                            // Check each nav item
                            navItems.forEach((item, index) => {
                                const dataSection = item.getAttribute('data-section');
                                const hasIcon = item.querySelector('i');
                                const hasSpan = item.querySelector('span');
                                
                                if (dataSection) {
                                    addTestResult(`Item ${index + 1}`, `Section: ${dataSection}, Icon: ${hasIcon ? 'Oui' : 'Non'}, Text: ${hasSpan ? 'Oui' : 'Non'}`, true);
                                } else if (item.id === 'logoutBtn') {
                                    addTestResult(`Item ${index + 1}`, 'Bouton logout trouvé', true);
                                } else {
                                    addTestResult(`Item ${index + 1}`, 'Item sans data-section ni id', false);
                                }
                            });
                        } else {
                            addTestResult('Navigation List', 'Liste ul manquante', false);
                        }
                    } else {
                        addTestResult('Navigation Container', 'Conteneur .admin-nav manquant', false);
                    }
                    
                    // Check if admin.js is included
                    const scripts = doc.querySelectorAll('script[src]');
                    const adminJsScript = Array.from(scripts).find(script => script.src.includes('admin.js'));
                    if (adminJsScript) {
                        addTestResult('Admin JS Script', 'Script admin.js inclus', true);
                        logToOutput('INFO', `Script admin.js trouvé: ${adminJsScript.src}`);
                    } else {
                        addTestResult('Admin JS Script', 'Script admin.js manquant', false);
                    }
                })
                .catch(error => {
                    addTestResult('Admin Page Access', `Erreur: ${error.message}`, false);
                });
        }
        
        function testAdminPageInFrame() {
            const iframe = document.getElementById('admin-frame');
            iframe.style.display = 'block';
            iframe.src = '/admin/index.html';
            
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const navItems = iframeDoc.querySelectorAll('.admin-nav ul li');
                    
                    addTestResult('Iframe Load', 'Page admin chargée dans iframe', true);
                    addTestResult('Iframe Navigation', `${navItems.length} éléments de navigation trouvés`, navItems.length > 0);
                    
                    // Try to add click listeners directly
                    navItems.forEach((item, index) => {
                        item.addEventListener('click', function() {
                            logToOutput('CLICK', `Navigation item ${index + 1} clicked: ${item.getAttribute('data-section') || item.id}`);
                            addTestResult(`Click Test ${index + 1}`, 'Clic détecté', true);
                        });
                    });
                    
                    addTestResult('Click Listeners', 'Event listeners ajoutés manuellement', true);
                    logToOutput('INFO', 'Vous pouvez maintenant tester les clics dans l\'iframe ci-dessous');
                    
                } catch (error) {
                    addTestResult('Iframe Access', `Erreur d'accès iframe: ${error.message}`, false);
                }
            };
        }
        
        // Initial test
        document.addEventListener('DOMContentLoaded', () => {
            addTestResult('Test Page Load', 'Page de test chargée', true);
            logToOutput('INFO', 'Page de test initialisée - Cliquez sur les boutons pour tester');
        });
    </script>
</body>
</html>
