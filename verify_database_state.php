<?php
/**
 * Comprehensive database state verification and product insertion
 */

require_once 'php/config.php';

echo "<h1>🔍 Database State Verification & Product Creation</h1>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;border-radius:5px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;}</style>\n";

try {
    $pdo = getPDOConnection();
    echo "<p class='success'>✅ Database connection successful</p>\n";
    
    // Check if produits table exists and its structure
    echo "<h2>📋 Database Table Analysis</h2>\n";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'produits'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ Table 'produits' exists</p>\n";
        
        // Show table structure
        $stmt = $pdo->query("DESCRIBE produits");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<h3>Table Structure:</h3>\n";
        echo "<table><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p class='error'>❌ Table 'produits' does not exist!</p>\n";
        echo "<p>Creating table...</p>\n";
        
        $createTable = "
        CREATE TABLE produits (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(50) NOT NULL,
            titre VARCHAR(255) NOT NULL,
            description TEXT,
            prix DECIMAL(10,2) NOT NULL,
            stock INT DEFAULT 0,
            auteur VARCHAR(255),
            materiel TEXT,
            actif TINYINT(1) DEFAULT 1,
            date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createTable);
        echo "<p class='success'>✅ Table 'produits' created successfully</p>\n";
    }
    
    // Check current products count
    echo "<h2>📊 Current Database State</h2>\n";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM produits");
    $total = $stmt->fetch()['total'];
    echo "<p>Total products in database: <strong>$total</strong></p>\n";
    
    if ($total > 0) {
        echo "<h3>Existing Products:</h3>\n";
        $stmt = $pdo->query("SELECT id, type, titre, prix, stock, actif, date_creation FROM produits ORDER BY id DESC");
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>\n";
        echo "<tr style='background:#f0f0f0;'><th>ID</th><th>Type</th><th>Title</th><th>Price</th><th>Stock</th><th>Active</th><th>Created</th></tr>\n";
        foreach ($products as $product) {
            $activeStatus = $product['actif'] ? '✅' : '❌';
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>{$product['type']}</td>";
            echo "<td>" . htmlspecialchars($product['titre']) . "</td>";
            echo "<td>{$product['prix']} DZD</td>";
            echo "<td>{$product['stock']}</td>";
            echo "<td>$activeStatus</td>";
            echo "<td>{$product['date_creation']}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p class='warning'>⚠️ No products found. Creating the 5 test products...</p>\n";
    }
    
    // Force create the 5 test products
    echo "<h2>🛠️ Creating Test Products</h2>\n";
    
    $products = [
        [
            'type' => 'book',
            'titre' => 'فن اللامبالاة - كتاب تطوير الذات',
            'description' => '<p><strong>كتاب فن اللامبالاة</strong> هو دليلك الشامل لتعلم كيفية التركيز على ما يهم حقاً في الحياة.</p><p>يعلمك هذا الكتاب:</p><ul><li>كيفية اختيار معاركك بحكمة</li><li>التخلص من القلق غير المبرر</li><li>بناء الثقة بالنفس</li><li>تحقيق السعادة الحقيقية</li></ul><p>مؤلف: مارك مانسون</p>',
            'prix' => 2500.00,
            'stock' => 50,
            'auteur' => 'مارك مانسون',
            'materiel' => null,
            'actif' => 1
        ],
        [
            'type' => 'laptop',
            'titre' => 'لابتوب Dell Inspiron 15 - للطلاب والمهنيين',
            'description' => '<p><strong>لابتوب Dell Inspiron 15</strong> - الخيار المثالي للطلاب والمهنيين</p><p>المواصفات:</p><ul><li>معالج Intel Core i5 الجيل الحادي عشر</li><li>ذاكرة عشوائية 8GB DDR4</li><li>قرص صلب SSD 256GB</li><li>شاشة 15.6 بوصة Full HD</li><li>كرت رسوميات Intel Iris Xe</li><li>نظام Windows 11</li></ul><p>ضمان سنتين</p>',
            'prix' => 85000.00,
            'stock' => 15,
            'auteur' => null,
            'materiel' => 'Intel Core i5, 8GB RAM, 256GB SSD',
            'actif' => 1
        ],
        [
            'type' => 'bag',
            'titre' => 'حقيبة ظهر رياضية مقاومة للماء',
            'description' => '<p><strong>حقيبة ظهر رياضية عملية</strong> مصممة للاستخدام اليومي والرياضي</p><p>المميزات:</p><ul><li>مقاومة للماء بنسبة 100%</li><li>جيوب متعددة للتنظيم</li><li>حمالات مبطنة مريحة</li><li>جيب خاص للحاسوب المحمول</li><li>تصميم عصري وأنيق</li><li>سعة 30 لتر</li></ul><p>مثالية للمدرسة، الجامعة، والرياضة</p>',
            'prix' => 4500.00,
            'stock' => 30,
            'auteur' => null,
            'materiel' => 'نايلون مقاوم للماء، سحابات معدنية',
            'actif' => 1
        ],
        [
            'type' => 'clothing',
            'titre' => 'قميص قطني كلاسيكي للرجال',
            'description' => '<p><strong>قميص قطني فاخر</strong> مصنوع من أجود أنواع القطن</p><p>المميزات:</p><ul><li>قطن 100% عالي الجودة</li><li>تصميم كلاسيكي أنيق</li><li>مقاسات متنوعة (S-XXL)</li><li>ألوان متعددة</li><li>سهل العناية والغسيل</li><li>مناسب للعمل والمناسبات</li></ul><p>متوفر بالألوان: أبيض، أزرق، رمادي</p>',
            'prix' => 3200.00,
            'stock' => 40,
            'auteur' => null,
            'materiel' => 'قطن 100%',
            'actif' => 1
        ],
        [
            'type' => 'home',
            'titre' => 'خلاط كهربائي متعدد الاستخدامات',
            'description' => '<p><strong>خلاط كهربائي قوي</strong> لجميع احتياجات المطبخ</p><p>المميزات:</p><ul><li>قوة 1000 واط</li><li>5 سرعات مختلفة</li><li>وعاء زجاجي سعة 1.5 لتر</li><li>شفرات من الستانلس ستيل</li><li>قاعدة مانعة للانزلاق</li><li>سهل التنظيف</li></ul><p>مثالي لتحضير العصائر، الشوربات، والصلصات</p><p>ضمان سنة كاملة</p>',
            'prix' => 12000.00,
            'stock' => 25,
            'auteur' => null,
            'materiel' => 'بلاستيك عالي الجودة، شفرات ستانلس ستيل',
            'actif' => 1
        ]
    ];
    
    $createdCount = 0;
    foreach ($products as $index => $product) {
        echo "<p>Creating product " . ($index + 1) . ": " . htmlspecialchars($product['titre']) . "</p>\n";
        
        // Check if product already exists
        $checkStmt = $pdo->prepare("SELECT id FROM produits WHERE titre = ?");
        $checkStmt->execute([$product['titre']]);
        
        if ($checkStmt->rowCount() > 0) {
            echo "<p class='warning'>⚠️ Product already exists, skipping...</p>\n";
            continue;
        }
        
        $sql = "INSERT INTO produits (type, titre, description, prix, stock, auteur, materiel, actif, date_creation) 
                VALUES (:type, :titre, :description, :prix, :stock, :auteur, :materiel, :actif, NOW())";
        
        $stmt = $pdo->prepare($sql);
        
        $result = $stmt->execute([
            ':type' => $product['type'],
            ':titre' => $product['titre'],
            ':description' => $product['description'],
            ':prix' => $product['prix'],
            ':stock' => $product['stock'],
            ':auteur' => $product['auteur'],
            ':materiel' => $product['materiel'],
            ':actif' => $product['actif']
        ]);
        
        if ($result) {
            $productId = $pdo->lastInsertId();
            echo "<p class='success'>✅ Product created successfully with ID: $productId</p>\n";
            $createdCount++;
        } else {
            echo "<p class='error'>❌ Failed to create product</p>\n";
            $errorInfo = $stmt->errorInfo();
            echo "<p class='error'>Error: " . $errorInfo[2] . "</p>\n";
        }
    }
    
    echo "<h2>📊 Final Database State</h2>\n";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM produits WHERE actif = 1");
    $activeTotal = $stmt->fetch()['total'];
    echo "<p class='success'>✅ Total active products: <strong>$activeTotal</strong></p>\n";
    echo "<p class='success'>✅ Products created in this session: <strong>$createdCount</strong></p>\n";
    
    // Test the products API
    echo "<h2>🔌 Testing Products API</h2>\n";
    
    $apiUrl = 'http://localhost:8000/php/api/products.php';
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10
        ]
    ]);
    
    $apiResponse = @file_get_contents($apiUrl, false, $context);
    
    if ($apiResponse !== false) {
        echo "<p class='success'>✅ API accessible</p>\n";
        $jsonData = json_decode($apiResponse, true);
        if ($jsonData && isset($jsonData['success']) && $jsonData['success']) {
            echo "<p class='success'>✅ API returning valid data</p>\n";
            echo "<p>API returned " . count($jsonData['products']) . " products</p>\n";
        } else {
            echo "<p class='error'>❌ API not returning valid data</p>\n";
            echo "<pre>" . htmlspecialchars($apiResponse) . "</pre>\n";
        }
    } else {
        echo "<p class='error'>❌ Cannot access API</p>\n";
    }
    
    echo "<h2>🎯 Next Steps</h2>\n";
    echo "<ol>\n";
    echo "<li><a href='admin/'>Go to Admin Panel</a> - Check if products appear</li>\n";
    echo "<li><a href='php/api/products.php'>Test Products API directly</a></li>\n";
    echo "<li>Create landing pages for the products</li>\n";
    echo "</ol>\n";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>\n";
    echo "<p class='error'>Stack trace:</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<script>
console.log('🔍 Database verification completed');
</script>
