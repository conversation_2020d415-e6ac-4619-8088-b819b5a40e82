// AI Settings content loading
async function loadAISettingsContent() {
    console.log('Loading AI settings content...');
    const contentArea = document.querySelector('[data-section="aiSettings"]');

    if (!contentArea) {
        throw new Error('Could not find AI settings section container');
    }

    try {
        showLoadingOverlay();

        // Show the section
        contentArea.style.display = 'block';

        // Initialize if needed
        if (typeof window.initAISettings === 'function') {
            window.initAISettings();
        }

        // Dispatch activation event
        const event = new CustomEvent('sectionActivated', {
            detail: { section: 'aiSettings' }
        });
        document.dispatchEvent(event);

        console.log('AI settings content loaded successfully');
    } catch (error) {
        console.error('Error loading AI settings:', error);
        showError('فشل في تحميل إعدادات الذكاء الاصطناعي');
        throw error;
    } finally {
        hideLoadingOverlay();
    }
}

// Loading overlay functions
function showLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('hidden');
    }
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.add('hidden');
    }
}

// Error display
function showError(message) {
    const errorContainer = document.createElement('div');
    errorContainer.className = 'error-message';
    errorContainer.textContent = message;
    document.body.appendChild(errorContainer);

    setTimeout(() => {
        errorContainer.remove();
    }, 5000);
}

// Export functions
window.aiSettings = {
    loadContent: loadAISettingsContent,
    showLoading: showLoadingOverlay,
    hideLoading: hideLoadingOverlay,
    showError: showError
};
