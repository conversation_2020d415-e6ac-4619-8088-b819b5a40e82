<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة تحميل إدارة المتاجر</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .success {
            color: #10b981;
            background: #d1fae5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #10b981;
        }
        .error {
            color: #ef4444;
            background: #fee2e2;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ef4444;
        }
        .warning {
            color: #f59e0b;
            background: #fef3c7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #f59e0b;
        }
        .info {
            color: #3b82f6;
            background: #dbeafe;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #3b82f6;
        }
        .output {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #5a67d8;
        }
        .button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .button.success {
            background: #10b981;
        }
        .button.danger {
            background: #ef4444;
        }
        .step {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step h3 {
            margin: 0 0 15px 0;
            color: #2d3748;
        }
        .progress {
            background: #e2e8f0;
            border-radius: 10px;
            height: 8px;
            margin: 20px 0;
        }
        .progress-bar {
            background: #667eea;
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح مشكلة تحميل إدارة المتاجر</h1>
        
        <div class="info">
            <h3>📋 خطوات الإصلاح:</h3>
            <ol>
                <li>فحص قاعدة البيانات والجداول المطلوبة</li>
                <li>تشغيل ترحيل قاعدة البيانات إذا لزم الأمر</li>
                <li>اختبار API المتاجر</li>
                <li>إنشاء بيانات تجريبية</li>
                <li>اختبار تحميل إدارة المتاجر</li>
            </ol>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>

        <div class="step">
            <h3>🔍 الخطوة 1: فحص قاعدة البيانات</h3>
            <button class="button" onclick="runStep1()" id="step1Btn">بدء الفحص</button>
            <div id="step1Output"></div>
        </div>

        <div class="step">
            <h3>🚀 الخطوة 2: تشغيل ترحيل قاعدة البيانات</h3>
            <button class="button" onclick="runStep2()" id="step2Btn" disabled>تشغيل الترحيل</button>
            <div id="step2Output"></div>
        </div>

        <div class="step">
            <h3>🧪 الخطوة 3: اختبار API المتاجر</h3>
            <button class="button" onclick="runStep3()" id="step3Btn" disabled>اختبار API</button>
            <div id="step3Output"></div>
        </div>

        <div class="step">
            <h3>🌱 الخطوة 4: إنشاء بيانات تجريبية</h3>
            <button class="button" onclick="runStep4()" id="step4Btn" disabled>إنشاء البيانات</button>
            <div id="step4Output"></div>
        </div>

        <div class="step">
            <h3>✅ الخطوة 5: اختبار النظام النهائي</h3>
            <button class="button success" onclick="runStep5()" id="step5Btn" disabled>اختبار النظام</button>
            <div id="step5Output"></div>
        </div>

        <div id="finalResult"></div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 5;

        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }

        async function runStep1() {
            const btn = document.getElementById('step1Btn');
            const output = document.getElementById('step1Output');
            
            btn.disabled = true;
            btn.textContent = '⏳ جاري الفحص...';
            
            try {
                const response = await fetch('test-database-check.php');
                const text = await response.text();
                
                if (response.ok && text.includes('✅')) {
                    output.innerHTML = `<div class="success">✅ قاعدة البيانات جاهزة!</div><div class="output">${text}</div>`;
                    currentStep = 1;
                    updateProgress();
                    
                    if (text.includes('مفقود')) {
                        document.getElementById('step2Btn').disabled = false;
                    } else {
                        document.getElementById('step3Btn').disabled = false;
                        currentStep = 2;
                        updateProgress();
                    }
                } else {
                    output.innerHTML = `<div class="error">❌ مشكلة في قاعدة البيانات</div><div class="output">${text}</div>`;
                    document.getElementById('step2Btn').disabled = false;
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في الاتصال: ${error.message}</div>`;
            }
            
            btn.disabled = false;
            btn.textContent = 'إعادة الفحص';
        }

        async function runStep2() {
            const btn = document.getElementById('step2Btn');
            const output = document.getElementById('step2Output');
            
            btn.disabled = true;
            btn.textContent = '⏳ جاري التشغيل...';
            
            try {
                const response = await fetch('../php/migrations/create_stores_table.php');
                const text = await response.text();
                
                if (response.ok) {
                    output.innerHTML = `<div class="success">✅ تم تشغيل الترحيل بنجاح!</div><div class="output">${text}</div>`;
                    currentStep = 2;
                    updateProgress();
                    document.getElementById('step3Btn').disabled = false;
                } else {
                    output.innerHTML = `<div class="error">❌ فشل في تشغيل الترحيل</div><div class="output">${text}</div>`;
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في تشغيل الترحيل: ${error.message}</div>`;
            }
            
            btn.disabled = false;
            btn.textContent = 'إعادة التشغيل';
        }

        async function runStep3() {
            const btn = document.getElementById('step3Btn');
            const output = document.getElementById('step3Output');
            
            btn.disabled = true;
            btn.textContent = '⏳ جاري الاختبار...';
            
            try {
                const response = await fetch('../php/api/stores.php');
                const data = await response.json();
                
                if (data.success) {
                    output.innerHTML = `<div class="success">✅ API المتاجر يعمل بشكل صحيح!</div><div class="info">📊 عدد المتاجر: ${data.total || 0}</div>`;
                    currentStep = 3;
                    updateProgress();
                    
                    if (data.total > 0) {
                        document.getElementById('step5Btn').disabled = false;
                        currentStep = 4;
                        updateProgress();
                    } else {
                        document.getElementById('step4Btn').disabled = false;
                    }
                } else {
                    output.innerHTML = `<div class="error">❌ مشكلة في API: ${data.message}</div>`;
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في اختبار API: ${error.message}</div>`;
            }
            
            btn.disabled = false;
            btn.textContent = 'إعادة الاختبار';
        }

        async function runStep4() {
            const btn = document.getElementById('step4Btn');
            const output = document.getElementById('step4Output');
            
            btn.disabled = true;
            btn.textContent = '⏳ جاري الإنشاء...';
            
            try {
                const response = await fetch('create-sample-stores.php');
                const text = await response.text();
                
                if (response.ok) {
                    output.innerHTML = `<div class="success">✅ تم إنشاء البيانات التجريبية بنجاح!</div><div class="output">${text}</div>`;
                    currentStep = 4;
                    updateProgress();
                    document.getElementById('step5Btn').disabled = false;
                } else {
                    output.innerHTML = `<div class="error">❌ فشل في إنشاء البيانات</div><div class="output">${text}</div>`;
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في إنشاء البيانات: ${error.message}</div>`;
            }
            
            btn.disabled = false;
            btn.textContent = 'إعادة الإنشاء';
        }

        async function runStep5() {
            const btn = document.getElementById('step5Btn');
            const output = document.getElementById('step5Output');
            const finalResult = document.getElementById('finalResult');
            
            btn.disabled = true;
            btn.textContent = '⏳ جاري الاختبار...';
            
            try {
                const response = await fetch('../php/api/stores.php');
                const data = await response.json();
                
                if (data.success && data.total > 0) {
                    output.innerHTML = `<div class="success">✅ النظام يعمل بشكل مثالي!</div><div class="info">📊 عدد المتاجر: ${data.total}</div>`;
                    currentStep = 5;
                    updateProgress();
                    
                    finalResult.innerHTML = `
                        <div class="success" style="text-align: center; padding: 30px; margin: 20px 0;">
                            <h2 style="margin: 0 0 15px 0;">🎉 تم إصلاح المشكلة بنجاح!</h2>
                            <p style="margin: 0 0 20px 0;">يمكنك الآن الذهاب إلى لوحة التحكم واستخدام إدارة المتاجر</p>
                            <a href="index.html#storesManagement" class="button success" style="text-decoration: none; display: inline-block;">
                                🏪 الذهاب إلى إدارة المتاجر
                            </a>
                        </div>
                    `;
                } else {
                    output.innerHTML = `<div class="warning">⚠️ النظام يعمل لكن لا توجد بيانات</div>`;
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في الاختبار النهائي: ${error.message}</div>`;
            }
            
            btn.disabled = false;
            btn.textContent = 'إعادة الاختبار';
        }

        // Auto-start first step
        window.addEventListener('load', () => {
            setTimeout(runStep1, 1000);
        });
    </script>
</body>
</html>
