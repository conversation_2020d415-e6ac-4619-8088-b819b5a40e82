<?php
/**
 * Performance Testing Script for Mossaab Landing Page Optimizations
 * Tests image optimization, lazy loading, and caching improvements
 */

require_once 'php/config.php';
require_once 'php/ImageOptimizer.php';
require_once 'php/CacheManager.php';

echo "<h1>🚀 Performance Optimization Test Results</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
    .success { color: #28a745; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .metric { background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .performance-improvement { background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
</style>\n";

try {
    // Test 1: Database Query Performance
    echo "<div class='test-section'>\n";
    echo "<h2>📊 Test 1: Database Query Performance</h2>\n";
    
    $iterations = 5;
    $totalTimeWithoutCache = 0;
    $totalTimeWithCache = 0;
    
    // Test without cache
    echo "<h3>Without Cache:</h3>\n";
    for ($i = 0; $i < $iterations; $i++) {
        $start = microtime(true);
        
        $stmt = $conn->prepare("
            SELECT p.*,
                   CASE WHEN lp.id IS NOT NULL THEN true ELSE false END as has_landing_page,
                   lp.lien_url as landing_url
            FROM produits p
            LEFT JOIN landing_pages lp ON p.id = lp.produit_id
        ");
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $end = microtime(true);
        $time = ($end - $start) * 1000;
        $totalTimeWithoutCache += $time;
        
        echo "<p>Query " . ($i + 1) . ": " . number_format($time, 2) . " ms (" . count($products) . " products)</p>\n";
    }
    
    $avgTimeWithoutCache = $totalTimeWithoutCache / $iterations;
    echo "<p class='metric'><strong>Average without cache: " . number_format($avgTimeWithoutCache, 2) . " ms</strong></p>\n";
    
    // Test with cache
    echo "<h3>With Cache:</h3>\n";
    $cache = new CacheManager('cache/', 3600);
    
    for ($i = 0; $i < $iterations; $i++) {
        $start = microtime(true);
        
        $cacheKey = 'products_test_' . $i;
        $products = $cache->remember($cacheKey, function() use ($conn) {
            $stmt = $conn->prepare("
                SELECT p.*,
                       CASE WHEN lp.id IS NOT NULL THEN true ELSE false END as has_landing_page,
                       lp.lien_url as landing_url
                FROM produits p
                LEFT JOIN landing_pages lp ON p.id = lp.produit_id
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        });
        
        $end = microtime(true);
        $time = ($end - $start) * 1000;
        $totalTimeWithCache += $time;
        
        $cacheStatus = $cache->exists($cacheKey) ? 'HIT' : 'MISS';
        echo "<p>Query " . ($i + 1) . ": " . number_format($time, 2) . " ms ($cacheStatus) (" . count($products) . " products)</p>\n";
    }
    
    $avgTimeWithCache = $totalTimeWithCache / $iterations;
    echo "<p class='metric'><strong>Average with cache: " . number_format($avgTimeWithCache, 2) . " ms</strong></p>\n";
    
    $improvement = (($avgTimeWithoutCache - $avgTimeWithCache) / $avgTimeWithoutCache) * 100;
    echo "<div class='performance-improvement'>\n";
    echo "<h3>🎯 Database Performance Improvement: " . number_format($improvement, 1) . "%</h3>\n";
    echo "<p>Cache reduces query time by " . number_format($avgTimeWithoutCache - $avgTimeWithCache, 2) . " ms on average</p>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    // Test 2: Image Optimization
    echo "<div class='test-section'>\n";
    echo "<h2>🖼️ Test 2: Image Optimization Analysis</h2>\n";
    
    $imageOptimizer = new ImageOptimizer('uploads/products/');
    
    // Check existing images
    $uploadDir = 'uploads/products/';
    $images = glob($uploadDir . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);
    
    if (empty($images)) {
        echo "<p class='warning'>No images found in uploads directory for testing</p>\n";
    } else {
        echo "<h3>Image Optimization Statistics:</h3>\n";
        echo "<table>\n";
        echo "<tr><th>Image</th><th>Original Size</th><th>WebP Size</th><th>Savings</th><th>Status</th></tr>\n";
        
        $totalOriginalSize = 0;
        $totalWebpSize = 0;
        $optimizedCount = 0;
        
        foreach (array_slice($images, 0, 10) as $imagePath) { // Test first 10 images
            $filename = basename($imagePath);
            $originalSize = filesize($imagePath);
            $totalOriginalSize += $originalSize;
            
            $pathInfo = pathinfo($filename);
            $webpPath = $uploadDir . $pathInfo['filename'] . '.webp';
            
            if (file_exists($webpPath)) {
                $webpSize = filesize($webpPath);
                $totalWebpSize += $webpSize;
                $savings = round((($originalSize - $webpSize) / $originalSize) * 100, 1);
                $status = '<span class="success">Optimized</span>';
                $optimizedCount++;
            } else {
                $webpSize = 0;
                $savings = 0;
                $status = '<span class="warning">Not Optimized</span>';
            }
            
            echo "<tr>";
            echo "<td>$filename</td>";
            echo "<td>" . number_format($originalSize / 1024, 1) . " KB</td>";
            echo "<td>" . ($webpSize > 0 ? number_format($webpSize / 1024, 1) . " KB" : "N/A") . "</td>";
            echo "<td>" . ($savings > 0 ? $savings . "%" : "N/A") . "</td>";
            echo "<td>$status</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        if ($optimizedCount > 0) {
            $overallSavings = round((($totalOriginalSize - $totalWebpSize) / $totalOriginalSize) * 100, 1);
            echo "<div class='performance-improvement'>\n";
            echo "<h3>🎯 Image Optimization Results:</h3>\n";
            echo "<p><strong>Images optimized:</strong> $optimizedCount/" . count($images) . "</p>\n";
            echo "<p><strong>Total size reduction:</strong> $overallSavings%</p>\n";
            echo "<p><strong>Space saved:</strong> " . number_format(($totalOriginalSize - $totalWebpSize) / 1024, 1) . " KB</p>\n";
            echo "</div>\n";
        }
    }
    echo "</div>\n";
    
    // Test 3: Cache System Analysis
    echo "<div class='test-section'>\n";
    echo "<h2>💾 Test 3: Cache System Analysis</h2>\n";
    
    $cacheStats = $cache->getStats();
    
    echo "<h3>Cache Statistics:</h3>\n";
    echo "<table>\n";
    echo "<tr><th>Metric</th><th>Value</th></tr>\n";
    echo "<tr><td>Cache Enabled</td><td>" . ($cacheStats['enabled'] ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . "</td></tr>\n";
    echo "<tr><td>Total Cache Files</td><td>{$cacheStats['total_files']}</td></tr>\n";
    echo "<tr><td>Valid Files</td><td>{$cacheStats['valid_files']}</td></tr>\n";
    echo "<tr><td>Expired Files</td><td>{$cacheStats['expired_files']}</td></tr>\n";
    echo "<tr><td>Total Cache Size</td><td>{$cacheStats['total_size_mb']} MB</td></tr>\n";
    echo "<tr><td>Cache Directory</td><td>{$cacheStats['cache_dir']}</td></tr>\n";
    echo "</table>\n";
    
    // Test cache performance
    echo "<h3>Cache Performance Test:</h3>\n";
    $testData = ['test' => 'data', 'timestamp' => time(), 'products' => range(1, 100)];
    
    $start = microtime(true);
    $cache->set('performance_test', $testData);
    $setTime = (microtime(true) - $start) * 1000;
    
    $start = microtime(true);
    $retrievedData = $cache->get('performance_test');
    $getTime = (microtime(true) - $start) * 1000;
    
    echo "<p><strong>Cache Write Time:</strong> " . number_format($setTime, 3) . " ms</p>\n";
    echo "<p><strong>Cache Read Time:</strong> " . number_format($getTime, 3) . " ms</p>\n";
    echo "<p><strong>Data Integrity:</strong> " . ($testData === $retrievedData ? '<span class="success">✓ Passed</span>' : '<span class="error">✗ Failed</span>') . "</p>\n";
    
    // Clean up test cache
    $cache->delete('performance_test');
    echo "</div>\n";
    
    // Test 4: API Response Time
    echo "<div class='test-section'>\n";
    echo "<h2>🌐 Test 4: API Response Time Analysis</h2>\n";
    
    $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/php/api/products.php';
    
    echo "<h3>Testing API Endpoint: $apiUrl</h3>\n";
    
    $apiTimes = [];
    for ($i = 0; $i < 3; $i++) {
        $start = microtime(true);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10
            ]
        ]);
        
        $response = @file_get_contents($apiUrl, false, $context);
        $end = microtime(true);
        
        if ($response !== false) {
            $time = ($end - $start) * 1000;
            $apiTimes[] = $time;
            $data = json_decode($response, true);
            $productCount = isset($data['data']) ? count($data['data']) : 0;
            echo "<p>API Call " . ($i + 1) . ": " . number_format($time, 2) . " ms ($productCount products)</p>\n";
        } else {
            echo "<p class='error'>API Call " . ($i + 1) . ": Failed</p>\n";
        }
    }
    
    if (!empty($apiTimes)) {
        $avgApiTime = array_sum($apiTimes) / count($apiTimes);
        echo "<p class='metric'><strong>Average API Response Time: " . number_format($avgApiTime, 2) . " ms</strong></p>\n";
        
        if ($avgApiTime < 200) {
            echo "<p class='success'>✓ Excellent API performance (< 200ms)</p>\n";
        } elseif ($avgApiTime < 500) {
            echo "<p class='warning'>⚠ Good API performance (< 500ms)</p>\n";
        } else {
            echo "<p class='error'>✗ API performance needs improvement (> 500ms)</p>\n";
        }
    }
    echo "</div>\n";
    
    // Overall Summary
    echo "<div class='test-section' style='border-left-color: #28a745;'>\n";
    echo "<h2>📈 Overall Performance Summary</h2>\n";
    
    $overallScore = 0;
    $maxScore = 100;
    
    // Database performance score (30 points)
    if ($improvement > 50) {
        $dbScore = 30;
    } elseif ($improvement > 25) {
        $dbScore = 20;
    } elseif ($improvement > 10) {
        $dbScore = 15;
    } else {
        $dbScore = 5;
    }
    $overallScore += $dbScore;
    
    // Cache system score (25 points)
    $cacheScore = $cacheStats['enabled'] ? 25 : 0;
    $overallScore += $cacheScore;
    
    // API performance score (25 points)
    if (!empty($apiTimes)) {
        if ($avgApiTime < 200) {
            $apiScore = 25;
        } elseif ($avgApiTime < 500) {
            $apiScore = 15;
        } else {
            $apiScore = 5;
        }
        $overallScore += $apiScore;
    }
    
    // Image optimization score (20 points)
    if (isset($optimizedCount) && $optimizedCount > 0) {
        $imageScore = min(20, ($optimizedCount / count($images)) * 20);
        $overallScore += $imageScore;
    }
    
    echo "<h3>Performance Score: $overallScore/$maxScore</h3>\n";
    echo "<ul>\n";
    echo "<li>Database Optimization: $dbScore/30 points</li>\n";
    echo "<li>Cache System: $cacheScore/25 points</li>\n";
    echo "<li>API Performance: " . ($apiScore ?? 0) . "/25 points</li>\n";
    echo "<li>Image Optimization: " . ($imageScore ?? 0) . "/20 points</li>\n";
    echo "</ul>\n";
    
    if ($overallScore >= 80) {
        echo "<p class='success'>🎉 Excellent! Your optimizations are working great!</p>\n";
    } elseif ($overallScore >= 60) {
        echo "<p class='warning'>👍 Good performance improvements achieved!</p>\n";
    } else {
        echo "<p class='error'>⚠ More optimization work needed.</p>\n";
    }
    
    echo "<h3>Recommendations:</h3>\n";
    echo "<ul>\n";
    if ($improvement < 25) {
        echo "<li>Consider implementing Redis or Memcached for better caching</li>\n";
    }
    if (!empty($apiTimes) && $avgApiTime > 300) {
        echo "<li>Optimize database queries and add more indexes</li>\n";
    }
    if (!isset($optimizedCount) || $optimizedCount == 0) {
        echo "<li>Run image optimization on existing product images</li>\n";
    }
    echo "<li>Monitor performance regularly and adjust cache TTL values</li>\n";
    echo "<li>Consider implementing a CDN for static assets</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='test-section' style='border-left-color: #dc3545;'>\n";
    echo "<h2>❌ Error During Performance Testing</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}
?>

<script>
console.log('🚀 Performance testing completed');
</script>
