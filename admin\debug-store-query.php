<?php
require_once __DIR__ . '/../php/config.php';

try {
    $pdo = getPDOConnection();

    echo "🔍 DEBUGGING STORE PAGE QUERY\n";
    echo "=" . str_repeat("=", 40) . "\n\n";

    // Get store information (same as store.php)
    $storeSlug = 'mossaab-store';

    $stmt = $pdo->prepare("
        SELECT
            s.*,
            CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as owner_name,
            u.email as owner_email,
            u.phone as owner_phone
        FROM stores s
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.store_slug = ? AND s.status = 'active'
    ");
    $stmt->execute([$storeSlug]);
    $store = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$store) {
        echo "❌ Store not found!\n";
        exit;
    }

    echo "✅ Store found:\n";
    echo "   Store ID: {$store['id']}\n";
    echo "   Store Name: {$store['store_name']}\n";
    echo "   Store Slug: {$store['store_slug']}\n\n";

    // Test the exact query from store.php
    echo "📋 Testing store products query:\n";
    echo "-" . str_repeat("-", 30) . "\n";

    $stmt = $pdo->prepare("
        SELECT p.*
        FROM produits p
        WHERE (p.store_id = ? OR p.store_id IS NULL) AND p.actif = 1
        ORDER BY p.created_at DESC
    ");
    $stmt->execute([$store['id']]);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "Query executed with store_id = {$store['id']}\n";
    echo "Products found: " . count($products) . "\n\n";

    if (count($products) > 0) {
        echo "📦 Products returned by query:\n";
        foreach ($products as $index => $product) {
            echo "   " . ($index + 1) . ". {$product['titre']}\n";
            echo "      Store ID: " . ($product['store_id'] ?? 'NULL') . "\n";
            echo "      Active: {$product['actif']}\n";
            echo "      Price: {$product['prix']} DZD\n\n";
        }
    } else {
        echo "❌ No products returned by query!\n\n";

        // Debug: Check what products exist
        echo "🔍 Debugging - All products in database:\n";
        $stmt = $pdo->query("SELECT id, titre, store_id, actif FROM produits ORDER BY created_at DESC LIMIT 10");
        $allProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($allProducts as $product) {
            echo "   • {$product['titre']}\n";
            echo "     Store ID: " . ($product['store_id'] ?? 'NULL') . " | Active: {$product['actif']}\n";
        }

        echo "\n🔍 Checking specific conditions:\n";

        // Check products with store_id = 1
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM produits WHERE store_id = ?");
        $stmt->execute([$store['id']]);
        $storeSpecific = $stmt->fetchColumn();
        echo "   Products with store_id = {$store['id']}: {$storeSpecific}\n";

        // Check products with store_id = NULL
        $stmt = $pdo->query("SELECT COUNT(*) FROM produits WHERE store_id IS NULL");
        $nullStore = $stmt->fetchColumn();
        echo "   Products with store_id = NULL: {$nullStore}\n";

        // Check active products
        $stmt = $pdo->query("SELECT COUNT(*) FROM produits WHERE actif = 1");
        $activeProducts = $stmt->fetchColumn();
        echo "   Active products (actif = 1): {$activeProducts}\n";

        // Check the exact condition
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM produits WHERE (store_id = ? OR store_id IS NULL) AND actif = 1");
        $stmt->execute([$store['id']]);
        $matchingProducts = $stmt->fetchColumn();
        echo "   Products matching query condition: {$matchingProducts}\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
