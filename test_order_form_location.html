<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Order Form Location System</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            direction: rtl;
            text-align: right;
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        select { 
            width: 100%; 
            padding: 10px; 
            margin: 10px 0; 
            direction: rtl;
            text-align: right;
        }
        .loading-indicator {
            display: none;
            color: #007cba;
            font-style: italic;
        }
        .loading-indicator.show {
            display: block;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار نظام المواقع في نموذج الطلب</h1>
    
    <div class="test-section">
        <h2>اختبار 1: تحميل الولايات</h2>
        <button onclick="testWilayasAPI()">اختبار API الولايات</button>
        <div id="wilayasResults"></div>
    </div>
    
    <div class="test-section">
        <h2>اختبار 2: النظام المتسلسل للولايات والبلديات</h2>
        
        <div class="form-group">
            <label for="wilayaSelect">الولاية *</label>
            <select id="wilayaSelect" name="wilaya" required>
                <option value="">-- اختر الولاية --</option>
            </select>
            <div class="loading-indicator" id="wilayaLoading">
                <i class="fas fa-spinner fa-spin"></i> جاري تحميل الولايات...
            </div>
        </div>
        
        <div class="form-group">
            <label for="communeSelect">البلدية *</label>
            <select id="communeSelect" name="commune" required disabled>
                <option value="">-- اختر البلدية --</option>
            </select>
            <div class="loading-indicator" id="communeLoading">
                <i class="fas fa-spinner fa-spin"></i> جاري تحميل البلديات...
            </div>
        </div>
        
        <button onclick="loadWilayas()">تحميل الولايات</button>
        <div id="cascadingResults"></div>
    </div>
    
    <div class="test-section">
        <h2>اختبار 3: اختبار ولايات محددة</h2>
        <button onclick="testSpecificWilayas()">اختبار ولايات محددة</button>
        <div id="specificResults"></div>
    </div>

    <script>
        // Test functions
        async function testWilayasAPI() {
            const resultsDiv = document.getElementById('wilayasResults');
            resultsDiv.innerHTML = '<p>🔄 اختبار API الولايات...</p>';
            
            try {
                const response = await fetch('php/api/geographic-data.php?action=wilayas');
                const data = await response.json();
                
                if (data.success && data.wilayas) {
                    resultsDiv.innerHTML = `
                        <p class="success">✅ نجح اختبار API الولايات!</p>
                        <p>عدد الولايات: ${data.wilayas.length}</p>
                        <p>أول 5 ولايات:</p>
                        <ul>
                            ${data.wilayas.slice(0, 5).map(w => `<li>${w.wilaya_code}: ${w.wilaya_name_ar} (المنطقة ${w.zone_number})</li>`).join('')}
                        </ul>
                    `;
                } else {
                    resultsDiv.innerHTML = `<p class="error">❌ فشل في اختبار API الولايات</p>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p class="error">❌ خطأ: ${error.message}</p>`;
            }
        }

        async function loadWilayas() {
            const wilayaSelect = document.getElementById('wilayaSelect');
            const loading = document.getElementById('wilayaLoading');
            const resultsDiv = document.getElementById('cascadingResults');

            try {
                loading.classList.add('show');
                wilayaSelect.disabled = true;
                resultsDiv.innerHTML = '<p>🔄 تحميل الولايات...</p>';

                const response = await fetch('php/api/geographic-data.php?action=wilayas');

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success && data.wilayas) {
                    wilayaSelect.innerHTML = '<option value="">-- اختر الولاية --</option>';
                    
                    data.wilayas.forEach(wilaya => {
                        const option = document.createElement('option');
                        option.value = wilaya.wilaya_code;
                        option.textContent = `${wilaya.wilaya_name_ar} (المنطقة ${wilaya.zone_number})`;
                        wilayaSelect.appendChild(option);
                    });

                    wilayaSelect.disabled = false;
                    resultsDiv.innerHTML = `<p class="success">✅ تم تحميل ${data.wilayas.length} ولاية بنجاح!</p>`;
                } else {
                    throw new Error(data.message || 'فشل في تحميل الولايات');
                }

            } catch (error) {
                resultsDiv.innerHTML = `<p class="error">❌ خطأ في تحميل الولايات: ${error.message}</p>`;
                wilayaSelect.innerHTML = '<option value="">خطأ في تحميل الولايات</option>';
            } finally {
                loading.classList.remove('show');
            }
        }

        async function loadCommunes(wilayaCode) {
            const communeSelect = document.getElementById('communeSelect');
            const loading = document.getElementById('communeLoading');
            const resultsDiv = document.getElementById('cascadingResults');

            try {
                loading.classList.add('show');
                communeSelect.disabled = true;
                communeSelect.innerHTML = '<option value="">-- اختر البلدية --</option>';

                const response = await fetch(`php/api/geographic-data.php?action=communes&wilaya_code=${wilayaCode}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success && data.communes) {
                    data.communes.forEach(commune => {
                        const option = document.createElement('option');
                        option.value = commune.commune_code;
                        option.textContent = commune.commune_name_ar;
                        communeSelect.appendChild(option);
                    });

                    communeSelect.disabled = false;
                    resultsDiv.innerHTML += `<p class="success">✅ تم تحميل ${data.communes.length} بلدية للولاية ${wilayaCode}</p>`;
                } else {
                    throw new Error(data.message || 'فشل في تحميل البلديات');
                }

            } catch (error) {
                resultsDiv.innerHTML += `<p class="error">❌ خطأ في تحميل البلديات: ${error.message}</p>`;
                communeSelect.innerHTML = '<option value="">خطأ في تحميل البلديات</option>';
            } finally {
                loading.classList.remove('show');
            }
        }

        async function testSpecificWilayas() {
            const resultsDiv = document.getElementById('specificResults');
            resultsDiv.innerHTML = '<p>🔄 اختبار ولايات محددة...</p>';
            
            const testWilayas = [
                { code: '16', name: 'الجزائر' },
                { code: '31', name: 'وهران' },
                { code: '25', name: 'قسنطينة' },
                { code: '09', name: 'البليدة' }
            ];
            
            let results = '<h3>نتائج الاختبار:</h3>';
            
            for (const wilaya of testWilayas) {
                try {
                    const response = await fetch(`php/api/geographic-data.php?action=communes&wilaya_code=${wilaya.code}`);
                    const data = await response.json();
                    
                    if (data.success && data.communes) {
                        results += `<p class="success">✅ ${wilaya.name} (${wilaya.code}): ${data.communes.length} بلدية</p>`;
                    } else {
                        results += `<p class="error">❌ ${wilaya.name} (${wilaya.code}): فشل في التحميل</p>`;
                    }
                } catch (error) {
                    results += `<p class="error">❌ ${wilaya.name} (${wilaya.code}): خطأ - ${error.message}</p>`;
                }
            }
            
            resultsDiv.innerHTML = results;
        }

        // Event listeners
        document.getElementById('wilayaSelect').addEventListener('change', function(e) {
            const wilayaCode = e.target.value;
            if (wilayaCode) {
                loadCommunes(wilayaCode);
            } else {
                const communeSelect = document.getElementById('communeSelect');
                communeSelect.innerHTML = '<option value="">-- اختر البلدية --</option>';
                communeSelect.disabled = true;
            }
        });

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('🚀 الصفحة محملة، تشغيل الاختبارات...');
            testWilayasAPI();
        });
    </script>
</body>
</html>
