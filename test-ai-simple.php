<?php
// Test simplified AI API
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Testing Simplified AI API ===\n\n";

// Test data
$testData = [
    'action' => 'generate_product_description',
    'product' => [
        'title' => 'فن اللامبالاة - كتاب تطوير الذات',
        'category' => 'كتب',
        'price' => '2500'
    ]
];

$postData = json_encode($testData);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $postData
    ]
]);

$url = 'http://localhost:8000/php/api/ai-simple.php';
echo "Making request to: $url\n";
echo "Request data: " . $postData . "\n\n";

$response = file_get_contents($url, false, $context);

if ($response === false) {
    echo "❌ Failed to get response from AI API\n";
    $error = error_get_last();
    if ($error) {
        echo "Error: " . $error['message'] . "\n";
    }
} else {
    echo "✅ Response received\n";
    echo "Raw response: " . $response . "\n\n";
    
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ Valid JSON response\n";
        echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        
        if ($data['success']) {
            echo "Generated content available: " . (isset($data['data']) ? 'yes' : 'no') . "\n";
            if (isset($data['data'])) {
                echo "Data keys: " . implode(', ', array_keys($data['data'])) . "\n";
                if (isset($data['data']['product_description'])) {
                    echo "Generated description: " . substr($data['data']['product_description'], 0, 100) . "...\n";
                }
            }
        } else {
            echo "Error: " . ($data['error'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "❌ Invalid JSON response: " . json_last_error_msg() . "\n";
    }
}

// Test other actions
echo "\n--- Testing Landing Page Title ---\n";
$titleData = [
    'action' => 'generate_landing_page_title',
    'product' => [
        'title' => 'فن اللامبالاة'
    ]
];

$titleContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode($titleData)
    ]
]);

$titleResponse = file_get_contents($url, false, $titleContext);
if ($titleResponse !== false) {
    $titleResult = json_decode($titleResponse, true);
    if ($titleResult && $titleResult['success']) {
        echo "✅ Landing page title generated: " . $titleResult['data']['landing_page_title'] . "\n";
    }
}

echo "\n=== Test Complete ===\n";
