<?php
/**
 * Landing Pages Database State Verification and Debugging
 */

require_once 'php/config.php';

echo "<h1>🔍 Landing Pages System Verification</h1>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;border-radius:5px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;}</style>\n";

try {
    $pdo = getPDOConnection();
    echo "<p class='success'>✅ Database connection successful</p>\n";
    
    // Check if landing_pages table exists
    echo "<h2>📋 Landing Pages Table Analysis</h2>\n";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'landing_pages'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ Table 'landing_pages' exists</p>\n";
        
        // Show table structure
        $stmt = $pdo->query("DESCRIBE landing_pages");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<h3>Table Structure:</h3>\n";
        echo "<table><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // Check current landing pages count
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM landing_pages");
        $total = $stmt->fetch()['total'];
        echo "<p>Total landing pages in database: <strong>$total</strong></p>\n";
        
        if ($total > 0) {
            echo "<h3>Existing Landing Pages:</h3>\n";
            $stmt = $pdo->query("SELECT id, titre, produit_id, template_id, actif, date_creation FROM landing_pages ORDER BY id DESC LIMIT 10");
            $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>\n";
            echo "<tr style='background:#f0f0f0;'><th>ID</th><th>Title</th><th>Product ID</th><th>Template</th><th>Active</th><th>Created</th></tr>\n";
            foreach ($pages as $page) {
                $activeStatus = $page['actif'] ? '✅' : '❌';
                echo "<tr>";
                echo "<td>{$page['id']}</td>";
                echo "<td>" . htmlspecialchars($page['titre']) . "</td>";
                echo "<td>{$page['produit_id']}</td>";
                echo "<td>{$page['template_id']}</td>";
                echo "<td>$activeStatus</td>";
                echo "<td>{$page['date_creation']}</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p class='warning'>⚠️ No landing pages found in database</p>\n";
        }
        
    } else {
        echo "<p class='error'>❌ Table 'landing_pages' does not exist!</p>\n";
        echo "<p>Creating landing_pages table...</p>\n";
        
        $createTable = "
        CREATE TABLE landing_pages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            titre VARCHAR(255) NOT NULL,
            produit_id INT NOT NULL,
            template_id VARCHAR(50) NOT NULL,
            contenu_droit TEXT,
            contenu_gauche TEXT,
            image_position ENUM('left', 'right', 'center') DEFAULT 'center',
            text_position ENUM('left', 'right', 'split') DEFAULT 'split',
            meta_description TEXT,
            meta_keywords TEXT,
            actif TINYINT(1) DEFAULT 1,
            date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createTable);
        echo "<p class='success'>✅ Table 'landing_pages' created successfully</p>\n";
    }
    
    // Check landing_page_images table
    echo "<h2>🖼️ Landing Page Images Table</h2>\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'landing_page_images'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ Table 'landing_page_images' exists</p>\n";
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM landing_page_images");
        $imageTotal = $stmt->fetch()['total'];
        echo "<p>Total images: <strong>$imageTotal</strong></p>\n";
    } else {
        echo "<p class='error'>❌ Table 'landing_page_images' does not exist!</p>\n";
        echo "<p>Creating landing_page_images table...</p>\n";
        
        $createImagesTable = "
        CREATE TABLE landing_page_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            landing_page_id INT NOT NULL,
            nom_fichier VARCHAR(255) NOT NULL,
            chemin_fichier VARCHAR(500) NOT NULL,
            ordre_affichage INT DEFAULT 0,
            date_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (landing_page_id) REFERENCES landing_pages(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createImagesTable);
        echo "<p class='success'>✅ Table 'landing_page_images' created successfully</p>\n";
    }
    
    // Test landing pages API
    echo "<h2>🔌 Testing Landing Pages API</h2>\n";
    
    $apiUrl = 'http://localhost:8000/php/api/landing_pages.php';
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10
        ]
    ]);
    
    $apiResponse = @file_get_contents($apiUrl, false, $context);
    
    if ($apiResponse !== false) {
        echo "<p class='success'>✅ Landing Pages API accessible</p>\n";
        $jsonData = json_decode($apiResponse, true);
        if ($jsonData && isset($jsonData['success'])) {
            echo "<p class='success'>✅ API returning valid JSON</p>\n";
            if (isset($jsonData['landing_pages'])) {
                echo "<p>API returned " . count($jsonData['landing_pages']) . " landing pages</p>\n";
            }
        } else {
            echo "<p class='error'>❌ API not returning valid JSON</p>\n";
            echo "<pre>" . htmlspecialchars($apiResponse) . "</pre>\n";
        }
    } else {
        echo "<p class='error'>❌ Cannot access Landing Pages API</p>\n";
    }
    
    // Check admin panel landing pages file
    echo "<h2>📁 Admin Panel Files Check</h2>\n";
    
    $adminLandingFile = 'admin/landing_pages.php';
    if (file_exists($adminLandingFile)) {
        echo "<p class='success'>✅ Admin landing pages file exists</p>\n";
        
        // Check if file has any obvious errors
        $fileContent = file_get_contents($adminLandingFile);
        if (strpos($fileContent, 'landing_pages') !== false) {
            echo "<p class='success'>✅ File contains landing pages references</p>\n";
        } else {
            echo "<p class='warning'>⚠️ File may not be properly configured</p>\n";
        }
    } else {
        echo "<p class='error'>❌ Admin landing pages file missing</p>\n";
    }
    
    // Check create landing page file
    $createLandingFile = 'admin/create_landing_page.php';
    if (file_exists($createLandingFile)) {
        echo "<p class='success'>✅ Create landing page file exists</p>\n";
    } else {
        echo "<p class='error'>❌ Create landing page file missing</p>\n";
    }
    
    // Test creating a sample landing page
    echo "<h2>🧪 Testing Landing Page Creation</h2>\n";
    
    // First check if we have products
    $stmt = $pdo->query("SELECT id, titre FROM produits WHERE actif = 1 LIMIT 1");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($product) {
        echo "<p class='success'>✅ Found product for testing: " . htmlspecialchars($product['titre']) . "</p>\n";
        
        // Try to create a test landing page
        $testTitle = "صفحة هبوط تجريبية - " . $product['titre'];
        
        // Check if test page already exists
        $checkStmt = $pdo->prepare("SELECT id FROM landing_pages WHERE titre = ?");
        $checkStmt->execute([$testTitle]);
        
        if ($checkStmt->rowCount() == 0) {
            echo "<p>Creating test landing page...</p>\n";
            
            $insertStmt = $pdo->prepare("
                INSERT INTO landing_pages (titre, produit_id, template_id, contenu_droit, contenu_gauche, actif) 
                VALUES (?, ?, ?, ?, ?, 1)
            ");
            
            $result = $insertStmt->execute([
                $testTitle,
                $product['id'],
                'custom',
                '<h3>محتوى تجريبي</h3><p>هذا محتوى تجريبي للاختبار</p>',
                '<h3>معلومات إضافية</h3><p>معلومات تجريبية</p>'
            ]);
            
            if ($result) {
                $landingPageId = $pdo->lastInsertId();
                echo "<p class='success'>✅ Test landing page created successfully with ID: $landingPageId</p>\n";
            } else {
                echo "<p class='error'>❌ Failed to create test landing page</p>\n";
                $errorInfo = $insertStmt->errorInfo();
                echo "<p class='error'>Error: " . $errorInfo[2] . "</p>\n";
            }
        } else {
            echo "<p class='warning'>⚠️ Test landing page already exists</p>\n";
        }
    } else {
        echo "<p class='error'>❌ No active products found for testing</p>\n";
    }
    
    // Final count
    echo "<h2>📊 Final Landing Pages Count</h2>\n";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM landing_pages");
    $finalTotal = $stmt->fetch()['total'];
    echo "<p class='success'>✅ Total landing pages: <strong>$finalTotal</strong></p>\n";
    
    echo "<h2>🎯 Debugging Steps</h2>\n";
    echo "<ol>\n";
    echo "<li><a href='admin/landing_pages.php'>Check Admin Landing Pages</a></li>\n";
    echo "<li><a href='php/api/landing_pages.php'>Test Landing Pages API</a></li>\n";
    echo "<li><a href='admin/create_landing_page.php'>Try Creating Landing Page</a></li>\n";
    echo "<li>Check browser console for JavaScript errors</li>\n";
    echo "</ol>\n";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>\n";
    echo "<p class='error'>Stack trace:</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<script>
console.log('🔍 Landing pages verification completed');
</script>
