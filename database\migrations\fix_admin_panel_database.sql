-- Fix Admin Panel Database Issues
-- This script ensures all required columns exist for the admin panel to function properly
-- ===== STEP 1: CHECK AND CREATE CORE TABLES =====
-- Ensure produits table exists with required structure
CREATE TABLE IF NOT EXISTS `produits` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `type` VARCHAR(50) NOT NULL,
    `titre` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `prix` DECIMAL(10, 2) NOT NULL,
    `stock` INT DEFAULT 0,
    `auteur` VARCHAR(255),
    `materiel` TEXT,
    `image_url` VARCHAR(500),
    `actif` TINYINT(1) DEFAULT 1,
    `category_id` INT DEFAULT NULL,
    `has_landing_page` TINYINT(1) DEFAULT 0,
    `landing_page_enabled` TINYINT(1) DEFAULT 0,
    `slug` VARCHAR(255) DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Ensure categories table exists with required structure
CREATE TABLE IF NOT EXISTS `categories` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_ar` VARCHAR(255) NOT NULL,
    `nom_fr` VARCHAR(255),
    `description` TEXT,
    `actif` TINYINT(1) DEFAULT 1,
    `parent_id` INT DEFAULT NULL,
    `ordre` INT DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- ===== STEP 2: ADD MISSING COLUMNS TO EXISTING TABLES =====
-- Add missing columns to produits table if they don't exist
ALTER TABLE `produits`
ADD COLUMN IF NOT EXISTS `actif` TINYINT(1) DEFAULT 1,
    ADD COLUMN IF NOT EXISTS `category_id` INT DEFAULT NULL,
    ADD COLUMN IF NOT EXISTS `has_landing_page` TINYINT(1) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS `landing_page_enabled` TINYINT(1) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS `slug` VARCHAR(255) DEFAULT NULL,
    ADD COLUMN IF NOT EXISTS `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ADD COLUMN IF NOT EXISTS `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
-- Add missing columns to categories table if they don't exist
ALTER TABLE `categories`
ADD COLUMN IF NOT EXISTS `actif` TINYINT(1) DEFAULT 1,
    ADD COLUMN IF NOT EXISTS `parent_id` INT DEFAULT NULL,
    ADD COLUMN IF NOT EXISTS `ordre` INT DEFAULT 0,
    ADD COLUMN IF NOT EXISTS `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ADD COLUMN IF NOT EXISTS `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
-- ===== STEP 3: CREATE LANDING PAGES TABLE =====
-- Ensure landing_pages table exists with required structure
CREATE TABLE IF NOT EXISTS `landing_pages` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `produit_id` INT NOT NULL,
    `titre` VARCHAR(255) NOT NULL,
    `contenu_droit` TEXT,
    `contenu_gauche` TEXT,
    `lien_url` VARCHAR(255) NOT NULL,
    `template_id` VARCHAR(50) DEFAULT 'custom',
    `status` VARCHAR(20) DEFAULT 'active',
    `actif` TINYINT(1) DEFAULT 1,
    `meta_title` VARCHAR(255),
    `meta_description` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Add missing columns to landing_pages table if they don't exist
ALTER TABLE `landing_pages`
ADD COLUMN IF NOT EXISTS `template_id` VARCHAR(50) DEFAULT 'custom',
    ADD COLUMN IF NOT EXISTS `status` VARCHAR(20) DEFAULT 'active',
    ADD COLUMN IF NOT EXISTS `actif` TINYINT(1) DEFAULT 1,
    ADD COLUMN IF NOT EXISTS `meta_title` VARCHAR(255),
    ADD COLUMN IF NOT EXISTS `meta_description` TEXT,
    ADD COLUMN IF NOT EXISTS `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ADD COLUMN IF NOT EXISTS `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
-- ===== ADMIN SETTINGS TABLE =====
-- Create admin_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS `admin_settings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `setting_key` VARCHAR(100) NOT NULL UNIQUE,
    `setting_value` TEXT,
    `setting_type` VARCHAR(50) DEFAULT 'string',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Create payment_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS `payment_settings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `setting_key` VARCHAR(100) NOT NULL UNIQUE,
    `setting_value` TEXT,
    `setting_type` ENUM('string', 'integer', 'float', 'boolean', 'json') DEFAULT 'string',
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_active (is_active)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Insert default admin settings
INSERT IGNORE INTO `admin_settings` (`setting_key`, `setting_value`, `setting_type`)
VALUES ('ai_openai_enabled', '0', 'boolean'),
    ('ai_anthropic_enabled', '0', 'boolean'),
    ('ai_google_enabled', '0', 'boolean'),
    ('payment_stripe_enabled', '0', 'boolean'),
    ('payment_paypal_enabled', '0', 'boolean'),
    ('payment_bank_transfer_enabled', '1', 'boolean'),
    ('default_currency', 'DZD', 'string'),
    ('currency_symbol', 'د.ج', 'string'),
    ('currency_position', 'after', 'string');
-- ===== USER MANAGEMENT TABLE =====
-- Create users table if it doesn't exist (separate from admins)
CREATE TABLE IF NOT EXISTS `users` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `email` VARCHAR(255) NOT NULL UNIQUE,
    `password` VARCHAR(255) NOT NULL,
    `role` ENUM('admin', 'editor', 'customer') DEFAULT 'customer',
    `status` ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    `avatar` VARCHAR(255) DEFAULT NULL,
    `phone` VARCHAR(20) DEFAULT NULL,
    `address` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Add indexes for users table
CREATE INDEX IF NOT EXISTS `idx_users_email` ON `users`(`email`);
CREATE INDEX IF NOT EXISTS `idx_users_role` ON `users`(`role`);
CREATE INDEX IF NOT EXISTS `idx_users_status` ON `users`(`status`);
-- ===== SECURITY SETTINGS TABLE =====
-- Create security_logs table for tracking security events
CREATE TABLE IF NOT EXISTS `security_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `event_type` VARCHAR(50) NOT NULL,
    `user_id` INT DEFAULT NULL,
    `ip_address` VARCHAR(45) NOT NULL,
    `user_agent` TEXT DEFAULT NULL,
    `details` JSON DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Add indexes for security logs
CREATE INDEX IF NOT EXISTS `idx_security_logs_event_type` ON `security_logs`(`event_type`);
CREATE INDEX IF NOT EXISTS `idx_security_logs_user_id` ON `security_logs`(`user_id`);
CREATE INDEX IF NOT EXISTS `idx_security_logs_created_at` ON `security_logs`(`created_at`);
-- ===== STEP 5: CREATE ADDITIONAL INDEXES FOR PERFORMANCE =====
-- Add indexes for produits table
CREATE INDEX IF NOT EXISTS `idx_produits_actif` ON `produits`(`actif`);
CREATE INDEX IF NOT EXISTS `idx_produits_type_actif` ON `produits`(`type`, `actif`);
CREATE INDEX IF NOT EXISTS `idx_produits_category` ON `produits`(`category_id`);
CREATE INDEX IF NOT EXISTS `idx_produits_slug` ON `produits`(`slug`);
CREATE INDEX IF NOT EXISTS `idx_produits_landing_page` ON `produits`(`has_landing_page`);
-- Add indexes for categories table
CREATE INDEX IF NOT EXISTS `idx_categories_actif` ON `categories`(`actif`);
CREATE INDEX IF NOT EXISTS `idx_categories_parent` ON `categories`(`parent_id`);
CREATE INDEX IF NOT EXISTS `idx_categories_ordre` ON `categories`(`ordre`);
-- Add indexes for landing_pages table
CREATE INDEX IF NOT EXISTS `idx_landing_pages_produit` ON `landing_pages`(`produit_id`);
CREATE INDEX IF NOT EXISTS `idx_landing_pages_status` ON `landing_pages`(`status`);
CREATE INDEX IF NOT EXISTS `idx_landing_pages_actif` ON `landing_pages`(`actif`);
-- ===== STEP 6: INSERT DEFAULT DATA =====
-- Insert default admin settings
INSERT IGNORE INTO `admin_settings` (`setting_key`, `setting_value`, `setting_type`)
VALUES ('ai_openai_enabled', '0', 'boolean'),
    ('ai_anthropic_enabled', '0', 'boolean'),
    ('ai_google_enabled', '0', 'boolean'),
    ('payment_stripe_enabled', '0', 'boolean'),
    ('payment_paypal_enabled', '0', 'boolean'),
    ('payment_bank_transfer_enabled', '1', 'boolean'),
    ('default_currency', 'DZD', 'string'),
    ('currency_symbol', 'د.ج', 'string'),
    ('currency_position', 'after', 'string');
-- ===== STEP 7: UPDATE EXISTING DATA =====
-- Ensure all existing products have actif = 1 if NULL
UPDATE `produits`
SET `actif` = 1
WHERE `actif` IS NULL;
-- Ensure all existing categories have actif = 1 if NULL
UPDATE `categories`
SET `actif` = 1
WHERE `actif` IS NULL;
-- Update products to have proper landing page associations
UPDATE `produits` p
SET `has_landing_page` = 1,
    `landing_page_enabled` = 1
WHERE EXISTS (
        SELECT 1
        FROM `landing_pages` lp
        WHERE lp.product_id = p.id
    );
-- ===== STEP 8: VERIFY TABLES =====
-- Show table structures for verification
-- (These are comments for manual verification)
-- DESCRIBE produits;
-- DESCRIBE categories;
-- DESCRIBE admin_settings;
-- DESCRIBE users;
-- DESCRIBE security_logs;
