<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Management</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .user { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Test User Management API</h1>
    
    <div id="status">Loading...</div>
    
    <h2>Users from API:</h2>
    <div id="users-container"></div>
    
    <script>
        async function testUserAPI() {
            const statusDiv = document.getElementById('status');
            const usersContainer = document.getElementById('users-container');
            
            try {
                statusDiv.innerHTML = 'Testing API connection...';
                
                // Test the users API
                const response = await fetch('php/api/users.php?action=list');
                
                statusDiv.innerHTML = `API Response Status: ${response.status}`;
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.innerHTML = `<span class="success">✅ API Success! Found ${data.users.length} users</span>`;
                    
                    // Display users
                    usersContainer.innerHTML = data.users.map(user => `
                        <div class="user">
                            <h3>${user.name}</h3>
                            <p>Email: ${user.email}</p>
                            <p>Role: ${user.role}</p>
                            <p>Status: ${user.status}</p>
                            <p>Registered: ${user.registeredAt}</p>
                            <p>Last Login: ${user.lastLogin || 'Never'}</p>
                        </div>
                    `).join('');
                } else {
                    statusDiv.innerHTML = `<span class="error">❌ API Error: ${data.message}</span>`;
                }
                
            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
                console.error('Error:', error);
            }
        }
        
        // Run test when page loads
        document.addEventListener('DOMContentLoaded', testUserAPI);
    </script>
</body>
</html>
