<?php

/**
 * Simplified AI API Endpoint for Mossaab Landing Page
 * Handles AI text generation requests without complex dependencies
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-Token');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Prevent any output before JSON
ob_start();

try {
    // Simple includes to avoid circular dependencies
    require_once __DIR__ . '/../../config/config.php';
    require_once __DIR__ . '/../../config/database.php';

    // Initialize Config
    Config::init();

    $db = Database::getInstance();
    $pdo = $db->getPDO();

    // Get action from request
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? $_GET['action'] ?? $_POST['action'] ?? '';

    // Check if any AI provider is enabled
    $enabledProviders = [];
    $providers = ['openai', 'anthropic', 'gemini'];

    foreach ($providers as $provider) {
        $stmt = $pdo->prepare("SELECT enabled, api_key FROM ai_settings WHERE provider = ?");
        $stmt->execute([$provider]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && $result['enabled'] && !empty($result['api_key'])) {
            $enabledProviders[] = $provider;
        }
    }

    if (empty($enabledProviders)) {
        throw new Exception('لم يتم تكوين أي مزود ذكاء اصطناعي', 400);
    }

    switch ($action) {
        case 'generate_product_description':
            handleGenerateProductDescription($pdo, $enabledProviders, $input);
            break;

        case 'generate_landing_page_title':
            handleGenerateLandingPageTitle($pdo, $enabledProviders, $input);
            break;

        case 'generate_landing_page_content':
            handleGenerateLandingPageContent($pdo, $enabledProviders, $input);
            break;

        case 'generate_meta_description':
            handleGenerateMetaDescription($pdo, $enabledProviders, $input);
            break;

        default:
            throw new Exception('إجراء غير صالح: ' . $action, 400);
    }
} catch (Exception $e) {
    // Clear any output buffer
    ob_clean();

    error_log("AI API Error: " . $e->getMessage());
    http_response_code($e->getCode() ?: 500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Handle product description generation
 */
function handleGenerateProductDescription($pdo, $enabledProviders, $input)
{
    try {
        $product = $input['product'] ?? [];

        // Try to get title from various possible fields
        $title = $product['title'] ?? $product['name'] ?? $product['nom'] ?? '';

        if (empty($title)) {
            // If no title provided, generate a generic description
            $title = 'منتج جديد';
        }

        // Ensure title is in the product array
        $product['title'] = $title;

        // Generate mock content for now (replace with actual AI call later)
        $generatedContent = generateMockProductDescription($product);

        // Clear output buffer and return response
        ob_clean();
        echo json_encode([
            'success' => true,
            'data' => [
                'product_description' => $generatedContent,
                'provider' => $enabledProviders[0] ?? 'mock'
            ]
        ]);
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * Handle landing page title generation
 */
function handleGenerateLandingPageTitle($pdo, $enabledProviders, $input)
{
    try {
        $product = $input['product'] ?? [];

        // Ensure we have a title for generation
        $title = $product['title'] ?? $product['name'] ?? $product['nom'] ?? 'منتج جديد';
        $product['title'] = $title;

        // Generate mock content
        $generatedContent = generateMockLandingPageTitle($product);

        ob_clean();
        echo json_encode([
            'success' => true,
            'data' => [
                'landing_page_title' => $generatedContent,
                'provider' => $enabledProviders[0] ?? 'mock'
            ]
        ]);
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * Handle landing page content generation
 */
function handleGenerateLandingPageContent($pdo, $enabledProviders, $input)
{
    try {
        $product = $input['product'] ?? [];

        // Ensure we have a title for generation
        $title = $product['title'] ?? $product['name'] ?? $product['nom'] ?? 'منتج جديد';
        $product['title'] = $title;

        // Generate mock content
        $generatedContent = generateMockLandingPageContent($product);

        ob_clean();
        echo json_encode([
            'success' => true,
            'data' => [
                'landing_page_content' => $generatedContent,
                'provider' => $enabledProviders[0] ?? 'mock'
            ]
        ]);
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * Handle meta description generation
 */
function handleGenerateMetaDescription($pdo, $enabledProviders, $input)
{
    try {
        $product = $input['product'] ?? [];

        // Ensure we have a title for generation
        $title = $product['title'] ?? $product['name'] ?? $product['nom'] ?? 'منتج جديد';
        $product['title'] = $title;

        // Generate mock content
        $generatedContent = generateMockMetaDescription($product);

        ob_clean();
        echo json_encode([
            'success' => true,
            'data' => [
                'meta_description' => $generatedContent,
                'provider' => $enabledProviders[0] ?? 'mock'
            ]
        ]);
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * Generate mock product description
 */
function generateMockProductDescription($product)
{
    $title = $product['title'] ?? 'منتج رائع';
    $category = $product['category'] ?? 'منتجات عامة';
    $price = $product['price'] ?? '0';

    return "🌟 اكتشف {$title} - منتج استثنائي في فئة {$category}

📋 **المواصفات الرئيسية:**
• منتج عالي الجودة ومصمم بعناية فائقة
• يلبي احتياجاتك اليومية بكفاءة عالية
• مناسب لجميع الأعمار والاستخدامات
• ضمان الجودة والمتانة

💰 **السعر:** {$price} دج

🚚 **التوصيل:** متوفر لجميع أنحاء الجزائر
📞 **الدعم:** خدمة عملاء متاحة 24/7

⭐ احصل على {$title} الآن واستمتع بتجربة فريدة ومميزة!";
}

/**
 * Generate mock landing page title
 */
function generateMockLandingPageTitle($product)
{
    $title = $product['title'] ?? 'منتج مميز';

    $templates = [
        "🌟 {$title} - الخيار الأمثل لك",
        "✨ اكتشف {$title} الآن",
        "🎯 {$title} - جودة استثنائية",
        "💎 {$title} - منتج فريد ومميز"
    ];

    return $templates[array_rand($templates)];
}

/**
 * Generate mock landing page content
 */
function generateMockLandingPageContent($product)
{
    $title = $product['title'] ?? 'منتج رائع';

    return "🎯 **لماذا تختار {$title}؟**

✅ **جودة عالية:** منتج مصنوع بأفضل المواد والتقنيات الحديثة
✅ **سهولة الاستخدام:** تصميم بسيط وعملي يناسب الجميع
✅ **ضمان الجودة:** نضمن لك الرضا التام أو استرداد المال
✅ **خدمة ممتازة:** فريق دعم متخصص لمساعدتك

🚀 **احصل على {$title} الآن وغيّر حياتك للأفضل!**

📞 اتصل بنا الآن أو اطلب عبر الموقع";
}

/**
 * Generate mock meta description
 */
function generateMockMetaDescription($product)
{
    $title = $product['title'] ?? 'منتج مميز';

    return "اكتشف {$title} - منتج عالي الجودة بأفضل الأسعار. توصيل مجاني لجميع أنحاء الجزائر. اطلب الآن!";
}
