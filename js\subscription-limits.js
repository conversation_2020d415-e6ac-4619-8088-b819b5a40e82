/**
 * Subscription Limits Management
 * Handles frontend validation and UI updates for subscription limits
 */

class SubscriptionLimits {
    constructor() {
        this.limits = null;
        this.usage = null;
        this.initialized = false;
    }

    /**
     * Initialize subscription limits
     */
    async init() {
        try {
            await this.loadLimitsAndUsage();
            this.initialized = true;
            this.updateUI();
        } catch (error) {
            console.error('Failed to initialize subscription limits:', error);
        }
    }

    /**
     * Load user limits and usage from API
     */
    async loadLimitsAndUsage() {
        try {
            const response = await fetch('php/api/subscription-limits.php?action=limits');
            const data = await response.json();

            if (data.success) {
                this.limits = data.data.limits;
                this.usage = data.data.usage;
                this.remaining = data.data.remaining;
                this.subscription = data.data.subscription;
            } else {
                throw new Error(data.message || 'Failed to load subscription limits');
            }
        } catch (error) {
            console.error('Error loading subscription limits:', error);
            throw error;
        }
    }

    /**
     * Check if user can perform a specific action
     */
    async canPerformAction(actionType, additionalData = {}) {
        try {
            const response = await fetch('php/api/subscription-limits.php?action=validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: actionType,
                    data: additionalData
                })
            });

            const result = await response.json();
            return result;
        } catch (error) {
            console.error('Error checking action permission:', error);
            return { success: false, allowed: false, message: 'خطأ في التحقق من الصلاحيات' };
        }
    }

    /**
     * Show subscription limit warning
     */
    showLimitWarning(type, current, limit) {
        const messages = {
            products: `لقد وصلت إلى الحد الأقصى لعدد المنتجات (${current}/${limit})`,
            landing_pages: `لقد وصلت إلى الحد الأقصى لعدد صفحات الهبوط (${current}/${limit})`,
            categories: `لقد وصلت إلى الحد الأقصى لعدد الفئات (${current}/${limit})`,
            storage: `لقد وصلت إلى الحد الأقصى لمساحة التخزين`
        };

        const message = messages[type] || 'لقد وصلت إلى حد اشتراكك';
        
        this.showNotification(message, 'warning');
    }

    /**
     * Show upgrade subscription prompt
     */
    showUpgradePrompt(type) {
        const modal = document.createElement('div');
        modal.className = 'subscription-upgrade-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>ترقية الاشتراك مطلوبة</h3>
                        <button class="close-btn" onclick="this.closest('.subscription-upgrade-modal').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <p>لقد وصلت إلى حدود اشتراكك الحالي. يرجى ترقية اشتراكك للاستمرار.</p>
                        <div class="current-limits">
                            <h4>حدودك الحالية:</h4>
                            <ul>
                                <li>المنتجات: ${this.usage.products}/${this.limits.products}</li>
                                <li>صفحات الهبوط: ${this.usage.landing_pages}/${this.limits.landing_pages}</li>
                                <li>الفئات: ${this.usage.categories}/${this.limits.categories}</li>
                                <li>التخزين: ${this.usage.storage_mb}/${this.limits.storage_mb} ميجابايت</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.subscription-upgrade-modal').remove()">إلغاء</button>
                        <button class="btn btn-primary" onclick="this.upgradeSubscription()">ترقية الاشتراك</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * Update UI elements with subscription information
     */
    updateUI() {
        if (!this.initialized) return;

        // Update subscription info in header/sidebar
        this.updateSubscriptionInfo();
        
        // Update progress bars
        this.updateProgressBars();
        
        // Update action buttons
        this.updateActionButtons();
    }

    /**
     * Update subscription information display
     */
    updateSubscriptionInfo() {
        const subscriptionInfo = document.querySelector('.subscription-info');
        if (subscriptionInfo && this.subscription) {
            subscriptionInfo.innerHTML = `
                <div class="subscription-plan">
                    <span class="plan-name">${this.subscription.display_name}</span>
                </div>
            `;
        }
    }

    /**
     * Update progress bars for limits
     */
    updateProgressBars() {
        const progressBars = {
            products: document.querySelector('.products-progress'),
            landing_pages: document.querySelector('.landing-pages-progress'),
            categories: document.querySelector('.categories-progress'),
            storage: document.querySelector('.storage-progress')
        };

        Object.keys(progressBars).forEach(type => {
            const progressBar = progressBars[type];
            if (progressBar && this.usage && this.limits) {
                const current = this.usage[type] || 0;
                const limit = this.limits[type] || 1;
                const percentage = Math.min((current / limit) * 100, 100);
                
                progressBar.innerHTML = `
                    <div class="progress-info">
                        <span>${current}/${limit}</span>
                        <span>${percentage.toFixed(0)}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${percentage}%"></div>
                    </div>
                `;
                
                // Add warning class if near limit
                if (percentage >= 90) {
                    progressBar.classList.add('near-limit');
                } else if (percentage >= 100) {
                    progressBar.classList.add('at-limit');
                }
            }
        });
    }

    /**
     * Update action buttons based on limits
     */
    updateActionButtons() {
        const buttons = {
            'add-product-btn': 'products',
            'add-landing-page-btn': 'landing_pages',
            'add-category-btn': 'categories'
        };

        Object.keys(buttons).forEach(buttonId => {
            const button = document.getElementById(buttonId);
            const type = buttons[buttonId];
            
            if (button && this.remaining) {
                if (this.remaining[type] <= 0) {
                    button.disabled = true;
                    button.title = 'لقد وصلت إلى الحد الأقصى لاشتراكك';
                    button.classList.add('limit-reached');
                } else {
                    button.disabled = false;
                    button.title = `يمكنك إضافة ${this.remaining[type]} عنصر آخر`;
                    button.classList.remove('limit-reached');
                }
            }
        });
    }

    /**
     * Validate before performing an action
     */
    async validateBeforeAction(actionType, additionalData = {}) {
        const result = await this.canPerformAction(actionType, additionalData);
        
        if (!result.allowed) {
            this.showLimitWarning(actionType, result.details?.current, result.details?.limit);
            this.showUpgradePrompt(actionType);
            return false;
        }
        
        return true;
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * Redirect to subscription upgrade page
     */
    upgradeSubscription() {
        window.location.href = '/admin/subscription-plans.html';
    }

    /**
     * Refresh limits and usage data
     */
    async refresh() {
        await this.loadLimitsAndUsage();
        this.updateUI();
    }
}

// Global instance
window.subscriptionLimits = new SubscriptionLimits();

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (typeof currentUser !== 'undefined' && currentUser) {
        window.subscriptionLimits.init();
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SubscriptionLimits;
}
