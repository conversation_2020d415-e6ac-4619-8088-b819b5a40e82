<?php
/**
 * Test Admin Sidebar Menu Functionality
 * Comprehensive testing of all navigation sections and their API endpoints
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قائمة الشريط الجانبي للإدارة</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 12px;
        }
        .menu-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .menu-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .menu-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .menu-icon {
            margin-left: 10px;
            font-size: 24px;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        .status-working {
            background: #d4edda;
            color: #155724;
        }
        .status-partial {
            background: #fff3cd;
            color: #856404;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        .api-details {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .score-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎛️ اختبار قائمة الشريط الجانبي للإدارة</h1>
            <p>اختبار شامل لجميع أقسام التنقل ونقاط نهاية API الخاصة بها</p>
        </div>

        <?php
        $menuSections = [
            'الرئيسية' => [
                'icon' => '🏠',
                'api' => '../php/api/dashboard-stats.php',
                'description' => 'لوحة التحكم الرئيسية وإحصائيات النظام',
                'features' => ['إحصائيات المنتجات', 'إحصائيات الطلبات', 'إحصائيات المبيعات']
            ],
            'إدارة المنتجات' => [
                'icon' => '📚',
                'api' => '../php/api/products.php',
                'description' => 'إدارة الكتب والمنتجات',
                'features' => ['عرض المنتجات', 'إضافة منتج جديد', 'تعديل المنتجات', 'حذف المنتجات']
            ],
            'الطلبات' => [
                'icon' => '🛒',
                'api' => '../php/api/orders.php',
                'description' => 'إدارة طلبات العملاء',
                'features' => ['عرض الطلبات', 'تحديث حالة الطلب', 'تفاصيل الطلب']
            ],
            'صفحات الهبوط' => [
                'icon' => '🚀',
                'api' => '../php/api/landing-pages.php',
                'description' => 'إدارة صفحات الهبوط للمنتجات',
                'features' => ['إنشاء صفحة هبوط', 'تعديل المحتوى', 'إعدادات SEO', 'معاينة الصفحة']
            ],
            'إعدادات الذكاء الاصطناعي' => [
                'icon' => '🤖',
                'api' => '../php/api/ai.php',
                'description' => 'إعدادات وإدارة الذكاء الاصطناعي',
                'features' => ['إعدادات OpenAI', 'إعدادات Anthropic', 'إعدادات Gemini', 'اختبار الاتصال']
            ],
            'الإعدادات' => [
                'icon' => '⚙️',
                'api' => '../php/api/store-settings.php',
                'description' => 'إعدادات المتجر العامة',
                'features' => ['معلومات المتجر', 'إعدادات الدفع', 'إعدادات الشحن', 'إعدادات الأمان']
            ]
        ];

        $testResults = [];
        $totalSections = count($menuSections);
        $workingSections = 0;

        try {
            require_once '../php/config.php';
            echo '<div class="result pass">✅ تم تحميل نظام التكوين بنجاح</div>';
        } catch (Exception $e) {
            echo '<div class="result fail">❌ فشل في تحميل نظام التكوين: ' . $e->getMessage() . '</div>';
        }

        echo '<div class="menu-grid">';
        
        foreach ($menuSections as $sectionName => $sectionData) {
            echo '<div class="menu-item">';
            echo '<div class="menu-title">';
            echo '<span class="menu-icon">' . $sectionData['icon'] . '</span>';
            echo $sectionName;
            echo '</div>';
            
            echo '<p style="color: #6c757d; margin-bottom: 15px;">' . $sectionData['description'] . '</p>';
            
            // Test API endpoint
            $apiFile = $sectionData['api'];
            $status = 'failed';
            $statusText = 'فاشل';
            $statusClass = 'status-failed';
            $details = '';
            
            if (file_exists($apiFile)) {
                echo '<div class="result pass">✅ ملف API موجود</div>';
                
                try {
                    // Test API endpoint
                    ob_start();
                    $errorOccurred = false;
                    
                    // Set up environment for API test
                    $_SERVER['REQUEST_METHOD'] = 'GET';
                    $_GET = [];
                    
                    // Capture errors
                    set_error_handler(function($severity, $message, $file, $line) use (&$errorOccurred, &$details) {
                        $errorOccurred = true;
                        $details .= "PHP Error: $message in " . basename($file) . ":$line\n";
                    });
                    
                    include $apiFile;
                    restore_error_handler();
                    
                    $output = ob_get_clean();
                    
                    if (!$errorOccurred) {
                        // Check if output is valid JSON or empty (which is also OK)
                        if (empty($output) || json_decode($output, true) !== null) {
                            $status = 'working';
                            $statusText = 'يعمل';
                            $statusClass = 'status-working';
                            $workingSections++;
                            echo '<div class="result pass">✅ API يعمل بنجاح</div>';
                        } else {
                            $status = 'partial';
                            $statusText = 'جزئي';
                            $statusClass = 'status-partial';
                            echo '<div class="result warning">⚠️ API يعمل مع تحذيرات</div>';
                            $details = "Response: " . substr($output, 0, 100) . "...";
                        }
                    } else {
                        echo '<div class="result fail">❌ أخطاء في API</div>';
                        $details = "Errors occurred during execution";
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="result fail">❌ خطأ في API: ' . $e->getMessage() . '</div>';
                    $details = "Exception: " . $e->getMessage();
                }
                
            } else {
                echo '<div class="result fail">❌ ملف API غير موجود</div>';
                $details = "File not found: " . $apiFile;
            }
            
            // Display status badge
            echo '<div style="margin: 15px 0;">';
            echo '<span class="status-badge ' . $statusClass . '">' . $statusText . '</span>';
            echo '</div>';
            
            // Display features
            echo '<h5>الميزات المتاحة:</h5>';
            echo '<ul style="margin: 10px 0; padding-right: 20px;">';
            foreach ($sectionData['features'] as $feature) {
                echo '<li>' . $feature . '</li>';
            }
            echo '</ul>';
            
            // Display API details if there are issues
            if ($status !== 'working' && !empty($details)) {
                echo '<div class="api-details">';
                echo '<strong>تفاصيل المشكلة:</strong><br>';
                echo htmlspecialchars($details);
                echo '</div>';
            }
            
            $testResults[$sectionName] = $status;
            echo '</div>';
        }
        
        echo '</div>';

        // Calculate overall score
        $successRate = ($workingSections / $totalSections) * 100;
        $scoreClass = 'score-card';
        $scoreIcon = '🎉';
        $scoreText = 'ممتاز';

        if ($successRate < 70) {
            $scoreIcon = '⚠️';
            $scoreText = 'يحتاج تحسين';
        } elseif ($successRate < 90) {
            $scoreIcon = '✅';
            $scoreText = 'جيد';
        }

        echo '<div class="' . $scoreClass . '">';
        echo $scoreIcon . ' نتيجة اختبار قائمة الشريط الجانبي: ' . round($successRate, 1) . '% (' . $scoreText . ')';
        echo '<br>أقسام تعمل: ' . $workingSections . '/' . $totalSections;
        echo '</div>';

        // Detailed results
        echo '<div class="menu-section">';
        echo '<h3>📊 تفاصيل النتائج</h3>';
        
        foreach ($testResults as $section => $status) {
            $statusIcon = $status === 'working' ? '✅' : ($status === 'partial' ? '⚠️' : '❌');
            $statusText = $status === 'working' ? 'يعمل بنجاح' : ($status === 'partial' ? 'يعمل مع تحذيرات' : 'لا يعمل');
            echo '<div class="result ' . ($status === 'working' ? 'pass' : ($status === 'partial' ? 'warning' : 'fail')) . '">';
            echo $statusIcon . ' ' . $section . ': ' . $statusText;
            echo '</div>';
        }
        echo '</div>';

        // Recommendations
        echo '<div class="menu-section">';
        echo '<h3>💡 التوصيات</h3>';
        
        if ($successRate >= 90) {
            echo '<div class="result pass">🎉 ممتاز! جميع أقسام لوحة التحكم تعمل بشكل صحيح</div>';
            echo '<div class="result info">💡 يمكنك الآن استخدام جميع ميزات لوحة التحكم بثقة</div>';
        } elseif ($successRate >= 70) {
            echo '<div class="result warning">⚠️ معظم الأقسام تعمل، لكن بعضها يحتاج إصلاحات</div>';
            echo '<div class="result info">💡 قم بتشغيل سكريبت إصلاح API endpoints لحل المشاكل المتبقية</div>';
        } else {
            echo '<div class="result fail">❌ عدة أقسام تحتاج إصلاحات</div>';
            echo '<div class="result info">💡 قم بتشغيل سكريبت الإصلاح الشامل أولاً</div>';
        }
        
        echo '<h4>🛠️ أدوات الإصلاح:</h4>';
        echo '<a href="fix-api-endpoints-500-errors.php" class="test-button">🔧 إصلاح أخطاء API</a>';
        echo '<a href="master-fix-all-errors.php" class="test-button">🔧 إصلاح شامل</a>';
        echo '<a href="setup-admin-user.php" class="test-button">👤 إعداد مستخدم الإدارة</a>';
        
        echo '<h4>🧪 اختبار الوظائف:</h4>';
        echo '<a href="index.html" class="test-button">🏠 فتح لوحة التحكم</a>';
        echo '<a href="landing-pages-management.html" class="test-button">🚀 اختبار صفحات الهبوط</a>';
        echo '<a href="ai-settings.html" class="test-button">🤖 اختبار إعدادات AI</a>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        // Test menu sections via JavaScript
        async function testMenuSectionsJS() {
            const sections = [
                { name: 'الرئيسية', url: '../php/api/dashboard-stats.php' },
                { name: 'إدارة المنتجات', url: '../php/api/products.php' },
                { name: 'الطلبات', url: '../php/api/orders.php' },
                { name: 'صفحات الهبوط', url: '../php/api/landing-pages.php' },
                { name: 'إعدادات الذكاء الاصطناعي', url: '../php/api/ai.php' },
                { name: 'الإعدادات', url: '../php/api/store-settings.php' }
            ];
            
            console.log('🧪 اختبار أقسام القائمة عبر JavaScript...');
            
            let jsWorkingSections = 0;
            
            for (const section of sections) {
                try {
                    const response = await fetch(section.url);
                    console.log(`${section.name}: Status ${response.status}`);
                    
                    if (response.status < 500) {
                        console.log(`✅ ${section.name}: لا يوجد خطأ 500`);
                        jsWorkingSections++;
                    } else {
                        console.log(`❌ ${section.name}: خطأ 500`);
                    }
                } catch (error) {
                    console.log(`❌ ${section.name}: خطأ في الشبكة - ${error.message}`);
                }
            }
            
            const jsSuccessRate = (jsWorkingSections / sections.length) * 100;
            console.log(`📊 معدل نجاح JavaScript: ${jsSuccessRate.toFixed(1)}%`);
            
            // Display JS results in page
            const jsResultDiv = document.createElement('div');
            jsResultDiv.className = 'menu-section';
            jsResultDiv.innerHTML = `
                <h3>🌐 نتائج اختبار JavaScript</h3>
                <div class="result info">
                    📊 معدل النجاح عبر JavaScript: ${jsWorkingSections}/${sections.length} (${jsSuccessRate.toFixed(1)}%)
                </div>
                <div class="result ${jsSuccessRate >= 80 ? 'pass' : jsSuccessRate >= 60 ? 'warning' : 'fail'}">
                    ${jsSuccessRate >= 80 ? '✅' : jsSuccessRate >= 60 ? '⚠️' : '❌'} 
                    اختبار JavaScript: ${jsSuccessRate >= 80 ? 'ممتاز' : jsSuccessRate >= 60 ? 'جيد' : 'يحتاج تحسين'}
                </div>
            `;
            document.querySelector('.container').appendChild(jsResultDiv);
        }
        
        // Run tests after page loads
        document.addEventListener('DOMContentLoaded', testMenuSectionsJS);
    </script>
</body>
</html>
