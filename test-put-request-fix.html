<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PUT Request Fix</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test PUT Request Fix</h1>
        <p>اختبار إصلاح طلبات PUT في API صفحات الهبوط</p>

        <div class="test-section">
            <h2>اختبارات API</h2>
            <button class="test-button" onclick="testGetLandingPages()">
                1. اختبار GET Landing Pages
            </button>
            <button class="test-button" onclick="testPutRequest()">
                2. اختبار PUT Request (محاكاة)
            </button>
            <button class="test-button" onclick="testFormDataHandling()">
                3. اختبار معالجة بيانات النموذج
            </button>
        </div>

        <div class="test-section">
            <h2>اختبار شامل</h2>
            <button class="test-button" onclick="runAllTests()">
                🚀 تشغيل جميع الاختبارات
            </button>
            <button class="test-button" onclick="clearResults()">
                🧹 مسح النتائج
            </button>
        </div>

        <div id="test-results"></div>
        <div id="console-output"></div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function logToConsole(message) {
            const output = document.getElementById('console-output');
            output.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        async function testGetLandingPages() {
            addTestResult('🧪 اختبار GET Landing Pages...', 'info');
            logToConsole('Testing GET landing pages...');
            
            try {
                const response = await fetch('php/api/landing-pages.php');
                logToConsole(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const text = await response.text();
                logToConsole(`Response length: ${text.length} characters`);
                
                const data = JSON.parse(text);
                logToConsole(`Parsed data success: ${data.success}`);
                
                if (data.success) {
                    addTestResult('✅ GET Landing Pages يعمل بشكل صحيح', 'success');
                    logToConsole(`Found ${data.data ? data.data.length : 0} landing pages`);
                } else {
                    addTestResult('❌ GET Landing Pages فشل: ' + (data.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في GET Landing Pages: ' + error.message, 'error');
                logToConsole(`GET test error: ${error.message}`);
            }
        }

        async function testPutRequest() {
            addTestResult('🧪 اختبار PUT Request (محاكاة)...', 'info');
            logToConsole('Testing PUT request simulation...');
            
            // First, get existing landing pages to get a valid ID
            try {
                const getResponse = await fetch('php/api/landing-pages.php');
                const getData = await getResponse.json();
                
                if (getData.success && getData.data && getData.data.length > 0) {
                    const firstLandingPage = getData.data[0];
                    logToConsole(`Found landing page ID: ${firstLandingPage.id}`);
                    
                    // Prepare test data
                    const testData = new URLSearchParams({
                        id: firstLandingPage.id,
                        productSelect: firstLandingPage.produit_id || '1',
                        landingPageTitle: 'Test Update - ' + new Date().toLocaleTimeString(),
                        rightContent: 'Test right content updated',
                        leftContent: 'Test left content updated',
                        template_id: 'custom'
                    });
                    
                    logToConsole('Sending PUT request...');
                    
                    // Send PUT request
                    const putResponse = await fetch('php/api/landing-pages.php', {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: testData.toString()
                    });
                    
                    logToConsole(`PUT Response status: ${putResponse.status}`);
                    
                    const putText = await putResponse.text();
                    logToConsole(`PUT Response: ${putText.substring(0, 200)}...`);
                    
                    if (putResponse.ok) {
                        const putData = JSON.parse(putText);
                        if (putData.success) {
                            addTestResult('✅ PUT Request يعمل بشكل صحيح', 'success');
                            logToConsole('PUT request successful');
                        } else {
                            addTestResult('❌ PUT Request فشل: ' + (putData.message || 'Unknown error'), 'error');
                        }
                    } else {
                        addTestResult('❌ PUT Request HTTP error: ' + putResponse.status, 'error');
                    }
                    
                } else {
                    addTestResult('⚠️ لا توجد صفحات هبوط للاختبار', 'info');
                }
                
            } catch (error) {
                addTestResult('❌ خطأ في PUT Request: ' + error.message, 'error');
                logToConsole(`PUT test error: ${error.message}`);
            }
        }

        function testFormDataHandling() {
            addTestResult('🧪 اختبار معالجة بيانات النموذج...', 'info');
            logToConsole('Testing form data handling...');
            
            try {
                // Test URLSearchParams creation
                const testData = new URLSearchParams({
                    id: '1',
                    productSelect: '2',
                    landingPageTitle: 'Test Title',
                    rightContent: 'Right content',
                    leftContent: 'Left content',
                    template_id: 'custom'
                });
                
                logToConsole(`Form data string: ${testData.toString()}`);
                
                // Test parsing
                const parsedData = {};
                for (const [key, value] of testData) {
                    parsedData[key] = value;
                }
                
                logToConsole(`Parsed data: ${JSON.stringify(parsedData, null, 2)}`);
                
                // Validate required fields
                const requiredFields = ['id', 'productSelect', 'landingPageTitle'];
                const missingFields = requiredFields.filter(field => !parsedData[field]);
                
                if (missingFields.length === 0) {
                    addTestResult('✅ معالجة بيانات النموذج تعمل بشكل صحيح', 'success');
                    logToConsole('Form data handling test passed');
                } else {
                    addTestResult('❌ حقول مفقودة: ' + missingFields.join(', '), 'error');
                }
                
            } catch (error) {
                addTestResult('❌ خطأ في معالجة بيانات النموذج: ' + error.message, 'error');
                logToConsole(`Form data test error: ${error.message}`);
            }
        }

        async function runAllTests() {
            clearResults();
            addTestResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            logToConsole('Starting all PUT request tests...');
            
            await testGetLandingPages();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testFormDataHandling();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPutRequest();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            addTestResult('🎉 انتهاء جميع الاختبارات', 'info');
            logToConsole('All tests completed');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('console-output').textContent = '';
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('📋 تشغيل اختبار أساسي تلقائياً...', 'info');
                testGetLandingPages();
            }, 1000);
        });
    </script>
</body>
</html>
