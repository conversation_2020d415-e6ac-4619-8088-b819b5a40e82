<?php

/**
 * API Endpoints Testing Tool
 * Tests all 8 system settings API endpoints
 */

// Start session (authentication check disabled for testing)
session_start();

/**
 * Test an API endpoint
 */
function testAPIEndpoint($endpoint, $description)
{
    $url = "http://localhost:8000/php/api/$endpoint";

    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    $result = [
        'endpoint' => $endpoint,
        'description' => $description,
        'url' => $url,
        'http_code' => $httpCode,
        'success' => false,
        'message' => '',
        'data' => null,
        'error' => $error
    ];

    if ($error) {
        $result['message'] = "خطأ في الاتصال: $error";
        return $result;
    }

    if ($httpCode === 200) {
        $jsonData = json_decode($response, true);
        if ($jsonData !== null) {
            $result['success'] = true;
            $result['message'] = 'نجح الاتصال وتم إرجاع بيانات صحيحة';
            $result['data'] = $jsonData;
        } else {
            $result['message'] = 'نجح الاتصال لكن البيانات المرجعة غير صحيحة';
        }
    } else {
        $result['message'] = "فشل الاتصال - كود HTTP: $httpCode";
    }

    return $result;
}

/**
 * Test file existence
 */
function testFileExists($filepath, $description)
{
    $fullPath = "../php/api/$filepath";
    return [
        'file' => $filepath,
        'description' => $description,
        'exists' => file_exists($fullPath),
        'readable' => file_exists($fullPath) && is_readable($fullPath),
        'size' => file_exists($fullPath) ? filesize($fullPath) : 0
    ];
}

// Run tests if requested
$apiTests = [];
$fileTests = [];

if (isset($_GET['test']) && $_GET['test'] === 'run') {
    // Test API endpoints
    $endpoints = [
        'categories.php' => 'إدارة الفئات',
        'payment-settings.php' => 'إعدادات الدفع',
        'general-settings.php' => 'الإعدادات العامة',
        'users.php' => 'إدارة المستخدمين',
        'roles.php' => 'إدارة الأدوار',
        'subscriptions.php' => 'إدارة الاشتراكات',
        'security-settings.php' => 'إعدادات الأمان',
        'dashboard-stats.php' => 'إحصائيات لوحة المعلومات'
    ];

    foreach ($endpoints as $endpoint => $description) {
        $apiTests[] = testAPIEndpoint($endpoint, $description);
        $fileTests[] = testFileExists($endpoint, $description);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نقاط API - مصعب لاندينغ بيج</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .test-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .test-result {
            display: flex;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            gap: 15px;
            border: 1px solid #ddd;
        }

        .test-result.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .test-result.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .test-result.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .test-details {
            flex: 1;
        }

        .test-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .test-message {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .test-url {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
            font-family: monospace;
        }

        .run-test-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .run-test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(40, 167, 69, 0.3);
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-network-wired"></i> اختبار نقاط API</h1>
            <p>فحص جميع نقاط API الخاصة بإعدادات النظام</p>
        </div>

        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
        </a>

        <div class="test-section">
            <h2><i class="fas fa-play-circle"></i> تشغيل الاختبارات</h2>
            <p>اضغط على الزر أدناه لاختبار جميع نقاط API</p>
            <a href="?test=run" class="run-test-btn">
                <i class="fas fa-rocket"></i> تشغيل اختبار API
            </a>
        </div>

        <?php if (!empty($apiTests)): ?>

            <?php
            // Calculate statistics
            $totalTests = count($apiTests);
            $successfulTests = count(array_filter($apiTests, function ($test) {
                return $test['success'];
            }));
            $failedTests = $totalTests - $successfulTests;
            $successRate = $totalTests > 0 ? round(($successfulTests / $totalTests) * 100) : 0;

            $totalFiles = count($fileTests);
            $existingFiles = count(array_filter($fileTests, function ($test) {
                return $test['exists'];
            }));
            ?>

            <!-- Summary Statistics -->
            <div class="summary-stats">
                <div class="stat-card">
                    <div class="stat-value" style="color: #28a745;"><?php echo $successfulTests; ?></div>
                    <div class="stat-label">اختبارات ناجحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #dc3545;"><?php echo $failedTests; ?></div>
                    <div class="stat-label">اختبارات فاشلة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #007bff;"><?php echo $successRate; ?>%</div>
                    <div class="stat-label">معدل النجاح</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #6f42c1;"><?php echo $existingFiles; ?>/<?php echo $totalFiles; ?></div>
                    <div class="stat-label">ملفات موجودة</div>
                </div>
            </div>

            <!-- File Existence Tests -->
            <div class="test-section">
                <h2><i class="fas fa-file-code"></i> فحص وجود الملفات</h2>
                <?php foreach ($fileTests as $test): ?>
                    <div class="test-result <?php echo $test['exists'] ? 'success' : 'error'; ?>">
                        <i class="fas <?php echo $test['exists'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        <div class="test-details">
                            <div class="test-name"><?php echo $test['description']; ?></div>
                            <div class="test-message">
                                <?php if ($test['exists']): ?>
                                    الملف موجود (<?php echo number_format($test['size']); ?> بايت)
                                <?php else: ?>
                                    الملف غير موجود
                                <?php endif; ?>
                            </div>
                            <div class="test-url"><?php echo $test['file']; ?></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- API Response Tests -->
            <div class="test-section">
                <h2><i class="fas fa-exchange-alt"></i> اختبار استجابة API</h2>
                <?php foreach ($apiTests as $test): ?>
                    <div class="test-result <?php echo $test['success'] ? 'success' : 'error'; ?>">
                        <i class="fas <?php echo $test['success'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        <div class="test-details">
                            <div class="test-name"><?php echo $test['description']; ?></div>
                            <div class="test-message"><?php echo $test['message']; ?></div>
                            <div class="test-url"><?php echo $test['url']; ?> (HTTP: <?php echo $test['http_code']; ?>)</div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

        <?php endif; ?>
    </div>
</body>

</html>
