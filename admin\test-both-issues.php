<?php
require_once __DIR__ . '/../php/config.php';

echo "🔧 TESTING BOTH CRITICAL ISSUES\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    $pdo = getPDOConnection();
    
    // Issue 1: Admin Panel Store Management
    echo "🏪 ISSUE 1: Admin Panel Store Management\n";
    echo "-" . str_repeat("-", 40) . "\n";
    
    // Test stores API directly
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/stores.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $apiResponse = curl_exec($ch);
    $apiHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "📊 Stores API Status: HTTP {$apiHttpCode}\n";
    
    if ($apiHttpCode === 200) {
        $apiData = json_decode($apiResponse, true);
        if ($apiData && $apiData['success']) {
            echo "✅ Stores API: WORKING\n";
            echo "   Total stores: {$apiData['total']}\n";
            echo "   Message: {$apiData['message']}\n";
        } else {
            echo "❌ Stores API: Invalid response\n";
            echo "   Response: " . substr($apiResponse, 0, 200) . "...\n";
        }
    } else {
        echo "❌ Stores API: Not accessible\n";
    }
    
    // Test admin panel access
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/admin/');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $adminResponse = curl_exec($ch);
    $adminHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "📊 Admin Panel Status: HTTP {$adminHttpCode}\n";
    
    if ($adminHttpCode === 200) {
        if (strpos($adminResponse, 'storesManagement') !== false) {
            echo "✅ Admin Panel: Contains stores management section\n";
        } else {
            echo "❌ Admin Panel: Missing stores management section\n";
        }
        
        if (strpos($adminResponse, 'js/stores-management.js') !== false) {
            echo "✅ Admin Panel: References stores management JS\n";
        } else {
            echo "❌ Admin Panel: Missing stores management JS reference\n";
        }
    } else {
        echo "❌ Admin Panel: Not accessible\n";
    }
    echo "\n";
    
    // Issue 2: Store Page Routing
    echo "🏬 ISSUE 2: Store Page Routing\n";
    echo "-" . str_repeat("-", 30) . "\n";
    
    // Test store page directly
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/store/mossaab-store');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    
    $storeResponse = curl_exec($ch);
    $storeHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "📊 Store Page Status: HTTP {$storeHttpCode}\n";
    
    if ($storeHttpCode === 200) {
        // Check what content is being served
        if (strpos($storeResponse, 'متجر مصعب') !== false) {
            echo "✅ Store Page: CORRECT (shows Mossaab Store)\n";
            
            // Check for products
            $productChecks = [
                'كتاب الطبخ الجزائري' => 'Algerian Cookbook',
                'Samsung Galaxy' => 'Samsung Phone',
                'حقيبة ظهر' => 'Backpack'
            ];
            
            $productsFound = 0;
            foreach ($productChecks as $productText => $description) {
                if (strpos($storeResponse, $productText) !== false) {
                    echo "   ✅ Product found: {$description}\n";
                    $productsFound++;
                } else {
                    echo "   ❌ Product missing: {$description}\n";
                }
            }
            
            if ($productsFound > 0) {
                echo "✅ Store products: {$productsFound}/3 sample products found\n";
            } else {
                echo "❌ Store products: No products displayed\n";
            }
            
        } elseif (strpos($storeResponse, 'متجر الكتب') !== false) {
            echo "❌ Store Page: WRONG CONTENT (showing main landing page)\n";
            echo "   Expected: متجر مصعب (Mossaab Store)\n";
            echo "   Actual: متجر الكتب (Book Store)\n";
        } else {
            echo "❌ Store Page: UNKNOWN CONTENT\n";
            echo "   Response preview: " . substr(strip_tags($storeResponse), 0, 150) . "...\n";
        }
    } else {
        echo "❌ Store Page: Not accessible (HTTP {$storeHttpCode})\n";
    }
    echo "\n";
    
    // Test database state
    echo "🗄️ DATABASE STATE\n";
    echo "-" . str_repeat("-", 15) . "\n";
    
    $stmt = $pdo->prepare("SELECT store_name, store_slug, status FROM stores WHERE store_slug = 'mossaab-store'");
    $stmt->execute();
    $store = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($store) {
        echo "✅ Demo store in database: {$store['store_name']} ({$store['status']})\n";
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM produits WHERE store_id = 1 AND actif = 1");
        $stmt->execute();
        $storeProducts = $stmt->fetchColumn();
        
        echo "✅ Store products: {$storeProducts} items\n";
    } else {
        echo "❌ Demo store missing from database\n";
    }
    echo "\n";
    
    // FINAL DIAGNOSIS
    echo "🎯 FINAL DIAGNOSIS\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $issue1Fixed = ($apiHttpCode === 200 && $adminHttpCode === 200);
    $issue2Fixed = ($storeHttpCode === 200 && strpos($storeResponse, 'متجر مصعب') !== false);
    
    if ($issue1Fixed && $issue2Fixed) {
        echo "🎉 BOTH ISSUES RESOLVED!\n\n";
        echo "✅ Issue 1 - Admin Panel: FIXED\n";
        echo "   • Stores API working (HTTP {$apiHttpCode})\n";
        echo "   • Admin panel accessible (HTTP {$adminHttpCode})\n\n";
        echo "✅ Issue 2 - Store Routing: FIXED\n";
        echo "   • Store page accessible (HTTP {$storeHttpCode})\n";
        echo "   • Correct content displayed\n\n";
        echo "🔗 Ready to test:\n";
        echo "   1. Admin Panel: http://localhost:8000/admin/\n";
        echo "   2. Store Page: http://localhost:8000/store/mossaab-store\n";
    } else {
        echo "⚠️ ISSUES STILL PRESENT:\n\n";
        
        if (!$issue1Fixed) {
            echo "❌ Issue 1 - Admin Panel: NOT FIXED\n";
            if ($apiHttpCode !== 200) {
                echo "   • Stores API not working (HTTP {$apiHttpCode})\n";
            }
            if ($adminHttpCode !== 200) {
                echo "   • Admin panel not accessible (HTTP {$adminHttpCode})\n";
            }
            echo "\n";
        }
        
        if (!$issue2Fixed) {
            echo "❌ Issue 2 - Store Routing: NOT FIXED\n";
            if ($storeHttpCode !== 200) {
                echo "   • Store page not accessible (HTTP {$storeHttpCode})\n";
            } elseif (strpos($storeResponse, 'متجر مصعب') === false) {
                echo "   • Wrong content being served\n";
            }
            echo "\n";
        }
        
        echo "📋 NEXT STEPS:\n";
        if (!$issue1Fixed) {
            echo "   • Check web server is running\n";
            echo "   • Verify stores API endpoint\n";
            echo "   • Check JavaScript console for errors\n";
        }
        if (!$issue2Fixed) {
            echo "   • Check .htaccess rewrite rules\n";
            echo "   • Verify store.php file exists\n";
            echo "   • Check Apache mod_rewrite module\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Critical Error: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Test completed at " . date('Y-m-d H:i:s') . "\n";
?>
