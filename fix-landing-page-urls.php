<?php
/**
 * Fix Landing Page URLs Migration Script
 * Updates existing landing pages to use the correct URL format
 */

// Define security check constant BEFORE including security.php
define('SECURITY_CHECK', true);

require_once 'php/config.php';

echo "<h1>🔧 Fix Landing Page URLs</h1>";

try {
    // Find landing pages with old URL format
    $stmt = $conn->prepare("
        SELECT id, titre, lien_url, produit_id 
        FROM landing_pages 
        WHERE lien_url LIKE '/landing/product-%'
        ORDER BY id
    ");
    $stmt->execute();
    $oldFormatPages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Landing Pages with Old URL Format</h2>";
    
    if (count($oldFormatPages) > 0) {
        echo "<p>Found " . count($oldFormatPages) . " landing pages with old URL format that need to be updated:</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>ID</th><th>Title</th><th>Product ID</th><th>Old URL</th><th>New URL</th><th>Status</th>";
        echo "</tr>";
        
        $conn->beginTransaction();
        $updatedCount = 0;
        
        foreach ($oldFormatPages as $page) {
            $newUrl = '/landing-page-template.php?id=' . $page['id'];
            
            echo "<tr>";
            echo "<td>{$page['id']}</td>";
            echo "<td>" . htmlspecialchars($page['titre']) . "</td>";
            echo "<td>{$page['produit_id']}</td>";
            echo "<td>" . htmlspecialchars($page['lien_url']) . "</td>";
            echo "<td>" . htmlspecialchars($newUrl) . "</td>";
            
            try {
                // Update the URL
                $updateStmt = $conn->prepare("UPDATE landing_pages SET lien_url = ? WHERE id = ?");
                $updateStmt->execute([$newUrl, $page['id']]);
                
                echo "<td style='color: green;'>✅ Updated</td>";
                $updatedCount++;
            } catch (Exception $e) {
                echo "<td style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</td>";
            }
            
            echo "</tr>";
        }
        
        echo "</table>";
        
        $conn->commit();
        echo "<p style='color: green; font-weight: bold;'>✅ Successfully updated $updatedCount landing page URLs!</p>";
        
    } else {
        echo "<p style='color: green;'>✅ No landing pages found with old URL format. All URLs are already correct!</p>";
    }
    
    // Show current status of all landing pages
    echo "<h2>Current Landing Pages Status</h2>";
    
    $stmt = $conn->prepare("
        SELECT id, titre, lien_url, produit_id,
               CASE 
                   WHEN lien_url LIKE '/landing-page-template.php?id=%' THEN 'Correct Format'
                   WHEN lien_url LIKE '/landing/product-%' THEN 'Old Format'
                   ELSE 'Unknown Format'
               END as url_status
        FROM landing_pages 
        ORDER BY id DESC
        LIMIT 20
    ");
    $stmt->execute();
    $allPages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($allPages) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>ID</th><th>Title</th><th>URL</th><th>Status</th><th>Test Link</th>";
        echo "</tr>";
        
        foreach ($allPages as $page) {
            echo "<tr>";
            echo "<td>{$page['id']}</td>";
            echo "<td>" . htmlspecialchars($page['titre']) . "</td>";
            echo "<td>" . htmlspecialchars($page['lien_url']) . "</td>";
            
            $statusColor = $page['url_status'] === 'Correct Format' ? 'green' : 'red';
            echo "<td style='color: $statusColor;'>{$page['url_status']}</td>";
            
            // Create test link
            if ($page['url_status'] === 'Correct Format') {
                echo "<td><a href='{$page['lien_url']}' target='_blank'>Test Page</a></td>";
            } else {
                echo "<td><a href='landing-page-template.php?id={$page['id']}' target='_blank'>Test Direct</a></td>";
            }
            
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No landing pages found.</p>";
    }
    
    // Test URL routing
    echo "<h2>URL Routing Test</h2>";
    echo "<p>Test these URLs to verify routing is working:</p>";
    
    if (count($allPages) > 0) {
        $testPage = $allPages[0];
        echo "<ul>";
        echo "<li><a href='landing-page-template.php?id={$testPage['id']}' target='_blank'>Direct ID Access</a></li>";
        echo "<li><a href='{$testPage['lien_url']}' target='_blank'>Template URL Access</a></li>";
        echo "</ul>";
    }
    
    echo "<h2>Admin Panel Integration</h2>";
    echo "<p>The admin panel should now:</p>";
    echo "<ul>";
    echo "<li>✅ Create landing pages with correct URL format</li>";
    echo "<li>✅ Display working links in the landing pages list</li>";
    echo "<li>✅ Allow editing and cloning with proper URLs</li>";
    echo "</ul>";
    
    echo "<p><a href='admin/index.html' target='_blank'>Test Admin Panel</a></p>";
    
} catch (Exception $e) {
    if ($conn && $conn->inTransaction()) {
        $conn->rollBack();
    }
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2 { color: #333; }
h1 { border-bottom: 3px solid #007cba; padding-bottom: 10px; }
table { margin: 10px 0; }
th, td { padding: 8px 12px; text-align: left; border: 1px solid #ddd; }
th { background: #f5f5f5; font-weight: bold; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
</style>
