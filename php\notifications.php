<?php

/**
 * Notifications API
 * Handles notification management for admin panel
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config.php';

// Security check
if (!defined('SECURITY_CHECK')) {
    define('SECURITY_CHECK', true);
}

class Notifications
{
    private $pdo;

    public function __construct($pdo)
    {
        $this->pdo = $pdo;
    }

    // Créer une nouvelle notification
    public function create($type, $message, $referenceId = null)
    {
        try {
            $stmt = $this->pdo->prepare(
                'INSERT INTO notifications (type, message, reference_id) VALUES (?, ?, ?)'
            );
            $stmt->execute([$type, $message, $referenceId]);
            return ['success' => true, 'id' => $this->pdo->lastInsertId()];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Récupérer les notifications non lues
    public function getUnread()
    {
        try {
            $stmt = $this->pdo->query(
                'SELECT * FROM notifications WHERE is_read = FALSE ORDER BY created_at DESC'
            );
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Marquer une notification comme lue
    public function markAsRead($id)
    {
        try {
            $stmt = $this->pdo->prepare('UPDATE notifications SET is_read = TRUE WHERE id = ?');
            $stmt->execute([$id]);
            return ['success' => true];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Marquer toutes les notifications comme lues
    public function markAllAsRead()
    {
        try {
            $stmt = $this->pdo->query('UPDATE notifications SET is_read = TRUE WHERE is_read = FALSE');
            return ['success' => true, 'rows_affected' => $stmt->rowCount()];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Supprimer les anciennes notifications (plus de 30 jours)
    public function cleanOldNotifications()
    {
        try {
            $stmt = $this->pdo->query(
                'DELETE FROM notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)'
            );
            return ['success' => true, 'rows_affected' => $stmt->rowCount()];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }
}

try {
    // Instancier la classe Notifications
    $pdo = getPDOConnection();
    $notifications = new Notifications($pdo);

    // Traitement des requêtes API
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Check authentication
        if (!isAdminLoggedIn()) {
            http_response_code(401);
            echo json_encode([
                'success' => false,
                'message' => 'غير مصرح بالوصول'
            ]);
            exit();
        }

        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'unread':
                    $result = $notifications->getUnread();
                    if (isset($result['error'])) {
                        http_response_code(500);
                        echo json_encode([
                            'success' => false,
                            'message' => 'خطأ في تحميل الإشعارات',
                            'error' => $result['error']
                        ]);
                    } else {
                        echo json_encode([
                            'success' => true,
                            'data' => $result,
                            'count' => count($result)
                        ]);
                    }
                    break;

                case 'mark_read':
                    if (isset($_GET['id'])) {
                        $result = $notifications->markAsRead($_GET['id']);
                        if (isset($result['error'])) {
                            http_response_code(500);
                            echo json_encode([
                                'success' => false,
                                'message' => 'خطأ في تحديث الإشعار',
                                'error' => $result['error']
                            ]);
                        } else {
                            echo json_encode([
                                'success' => true,
                                'message' => 'تم تحديث الإشعار بنجاح'
                            ]);
                        }
                    } else {
                        http_response_code(400);
                        echo json_encode([
                            'success' => false,
                            'message' => 'معرف الإشعار مطلوب'
                        ]);
                    }
                    break;

                case 'mark_all_read':
                    $result = $notifications->markAllAsRead();
                    if (isset($result['error'])) {
                        http_response_code(500);
                        echo json_encode([
                            'success' => false,
                            'message' => 'خطأ في تحديث الإشعارات',
                            'error' => $result['error']
                        ]);
                    } else {
                        echo json_encode([
                            'success' => true,
                            'message' => 'تم تحديث جميع الإشعارات بنجاح',
                            'rows_affected' => $result['rows_affected']
                        ]);
                    }
                    break;

                default:
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'message' => 'إجراء غير مدعوم'
                    ]);
                    break;
            }
        } else {
            // Return empty notifications if no action specified
            echo json_encode([
                'success' => true,
                'data' => [],
                'count' => 0,
                'message' => 'لا توجد إشعارات جديدة'
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'طريقة الطلب غير مدعومة'
        ]);
    }
} catch (Exception $e) {
    error_log("Notifications API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error' => $e->getMessage()
    ]);
}
