/* Styles spécifiques à la page du panier */
.cart-page {
    padding: 120px 0 60px;
}

.cart-page h2 {
    text-align: center;
    margin-bottom: 40px;
    color: #2c3e50;
}

.cart-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

/* Styles des éléments du panier */
.cart-items {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.cart-item {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 20px;
    padding: 20px 0;
    border-bottom: 1px solid #eee;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item img {
    width: 100px;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
}

.item-details {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item-details h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 5px;
}

.item-price {
    font-weight: 700;
    color: #2c3e50;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.quantity-controls button {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.quantity-controls button:hover {
    background: #e9ecef;
}

.quantity-controls span {
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.remove-item {
    color: #e74c3c;
    cursor: pointer;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.remove-item:hover {
    color: #c0392b;
}

/* Styles du résumé du panier */
.cart-summary {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    height: fit-content;
}

.cart-summary h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #eee;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    color: #666;
}

.summary-item.total {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.2rem;
    padding-top: 15px;
    border-top: 2px solid #eee;
    margin-top: 15px;
}

.checkout-button {
    width: 100%;
    padding: 15px;
    background: #2ecc71;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.checkout-button:hover {
    background: #27ae60;
}

/* Styles du panier vide */
.empty-cart {
    text-align: center;
    padding: 60px 20px;
}

.empty-cart i {
    font-size: 4rem;
    color: #bdc3c7;
    margin-bottom: 20px;
}

.empty-cart h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.empty-cart p {
    color: #7f8c8d;
    margin-bottom: 30px;
}

.continue-shopping {
    display: inline-block;
    padding: 12px 25px;
    background: #3498db;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.continue-shopping:hover {
    background: #2980b9;
}

/* Enhanced Responsive Design for Shopping Cart */

/* Mobile Small (320px and up) */
@media (max-width: 480px) {
    .cart-container {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }

    .cart-item {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 20px 15px;
        gap: 15px;
    }

    .cart-item img {
        margin: 0 auto;
        width: 120px;
        height: 120px;
        object-fit: cover;
    }

    .cart-item h3 {
        font-size: 1.1rem;
        margin: 10px 0;
        text-align: center;
    }

    .cart-item p {
        font-size: 0.9rem;
        text-align: center;
        margin: 5px 0;
    }

    .quantity-controls {
        justify-content: center;
        gap: 15px;
        margin: 15px 0;
    }

    .quantity-controls button {
        min-height: 44px;
        min-width: 44px;
        font-size: 1.2rem;
        border-radius: 8px;
    }

    .quantity-controls span {
        font-size: 1.1rem;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .remove-item {
        text-align: center;
        margin-top: 15px;
    }

    .remove-item button {
        min-height: 44px;
        padding: 12px 20px;
        font-size: 1rem;
        width: 100%;
        max-width: 200px;
    }

    .cart-summary {
        padding: 20px 15px;
        margin-top: 20px;
    }

    .cart-summary h3 {
        font-size: 1.3rem;
        text-align: center;
        margin-bottom: 15px;
    }

    .cart-total {
        font-size: 1.4rem;
        text-align: center;
        margin: 20px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .checkout-btn, .continue-shopping {
        width: 100%;
        min-height: 48px;
        font-size: 1.1rem;
        margin: 10px 0;
        padding: 14px 20px;
        border-radius: 8px;
    }
}

/* Mobile Medium (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .cart-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cart-item {
        grid-template-columns: 150px 1fr auto;
        text-align: right;
        align-items: center;
        gap: 20px;
        padding: 20px;
    }

    .cart-item img {
        width: 150px;
        height: 150px;
        object-fit: cover;
    }

    .cart-item-info {
        text-align: right;
    }

    .cart-item h3 {
        font-size: 1.2rem;
        margin-bottom: 8px;
    }

    .quantity-controls {
        justify-content: center;
        gap: 12px;
        flex-direction: row;
    }

    .quantity-controls button {
        min-height: 44px;
        min-width: 44px;
    }

    .remove-item {
        text-align: center;
        margin-top: 10px;
    }

    .remove-item button {
        min-height: 44px;
        padding: 10px 16px;
    }

    .checkout-btn, .continue-shopping {
        min-height: 48px;
        padding: 12px 24px;
    }
}

/* Tablet and larger mobile devices */
@media (min-width: 769px) and (max-width: 1024px) {
    .cart-container {
        grid-template-columns: 2fr 1fr;
        gap: 30px;
    }

    .cart-item {
        grid-template-columns: 120px 1fr auto auto;
        gap: 20px;
        align-items: center;
    }
}

/* Touch-friendly improvements for all mobile devices */
@media (max-width: 1024px) {
    /* Ensure all cart buttons are touch-friendly */
    .quantity-controls button,
    .remove-item button,
    .checkout-btn,
    .continue-shopping {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    /* Better visual feedback for touch */
    .cart-item {
        transition: box-shadow 0.2s ease;
    }

    .cart-item:active {
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    /* Improve button states for touch */
    .quantity-controls button:active,
    .remove-item button:active,
    .checkout-btn:active,
    .continue-shopping:active {
        transform: scale(0.98);
    }

    /* Better spacing for Arabic RTL layout */
    .cart-item-info {
        direction: rtl;
        text-align: right;
    }

    .cart-item h3,
    .cart-item p {
        direction: rtl;
        text-align: right;
    }

    /* Improve price display on mobile */
    .cart-item .price {
        font-weight: bold;
        font-size: 1.1rem;
        color: #2c3e50;
    }
}
