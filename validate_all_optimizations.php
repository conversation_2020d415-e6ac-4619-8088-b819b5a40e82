<?php
/**
 * Comprehensive Validation Script for All Performance Optimizations
 * Tests API fixes, image optimization, lazy loading, and caching
 */

require_once 'php/config.php';
require_once 'php/ImageOptimizer.php';
require_once 'php/CacheManager.php';

echo "<h1>🔍 Complete Performance Optimization Validation</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
    .success { color: #28a745; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .metric { background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .summary-card { background: #fff; border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; }
    .score-excellent { background: #d4edda; border-color: #c3e6cb; }
    .score-good { background: #fff3cd; border-color: #ffeaa7; }
    .score-poor { background: #f8d7da; border-color: #f5c6cb; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
    .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
</style>\n";

$overallScore = 0;
$maxScore = 400; // Total possible points
$testResults = [];

try {
    // Test 1: API Response Format Fix
    echo "<div class='test-section'>\n";
    echo "<h2>🔧 Test 1: API Response Format Validation</h2>\n";
    
    $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/php/api/products.php';
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents($apiUrl, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        
        if ($data && isset($data['success']) && isset($data['data'])) {
            echo "<p class='success'>✅ API returns correct standardized format</p>\n";
            echo "<p><strong>Response structure:</strong> {success: " . ($data['success'] ? 'true' : 'false') . ", data: array(" . count($data['data']) . " items)}</p>\n";
            $testResults['api_format'] = 100;
            $overallScore += 100;
        } else {
            echo "<p class='error'>❌ API response format incorrect</p>\n";
            echo "<p>Expected: {success: boolean, data: array}</p>\n";
            echo "<p>Got: " . json_encode(array_keys($data ?? []), JSON_UNESCAPED_UNICODE) . "</p>\n";
            $testResults['api_format'] = 0;
        }
    } else {
        echo "<p class='error'>❌ Failed to fetch API response</p>\n";
        $testResults['api_format'] = 0;
    }
    
    echo "</div>\n";
    
    // Test 2: Image Optimization System
    echo "<div class='test-section'>\n";
    echo "<h2>🖼️ Test 2: Image Optimization System</h2>\n";
    
    $imageOptimizer = new ImageOptimizer('uploads/products/');
    $imageScore = 0;
    
    // Check GD extension and WebP support
    if (extension_loaded('gd')) {
        $gdInfo = gd_info();
        echo "<p class='success'>✅ GD Extension loaded</p>\n";
        $imageScore += 25;
        
        if ($gdInfo['WebP Support']) {
            echo "<p class='success'>✅ WebP support available</p>\n";
            $imageScore += 25;
        } else {
            echo "<p class='warning'>⚠️ WebP support not available</p>\n";
        }
    } else {
        echo "<p class='error'>❌ GD Extension not loaded</p>\n";
    }
    
    // Check directory structure
    $requiredDirs = ['uploads/products/', 'uploads/products/thumbnail/', 'uploads/products/medium/', 'uploads/products/large/'];
    $dirsExist = 0;
    
    foreach ($requiredDirs as $dir) {
        if (is_dir($dir)) {
            $dirsExist++;
        }
    }
    
    if ($dirsExist === count($requiredDirs)) {
        echo "<p class='success'>✅ All required directories exist</p>\n";
        $imageScore += 25;
    } else {
        echo "<p class='warning'>⚠️ Some directories missing ($dirsExist/" . count($requiredDirs) . ")</p>\n";
        $imageScore += ($dirsExist / count($requiredDirs)) * 25;
    }
    
    // Check for existing optimized images
    $originalImages = glob('uploads/products/*.{jpg,jpeg,png,gif}', GLOB_BRACE);
    $optimizedCount = 0;
    
    foreach ($originalImages as $imagePath) {
        $filename = basename($imagePath);
        $pathInfo = pathinfo($filename);
        $webpPath = 'uploads/products/' . $pathInfo['filename'] . '.webp';
        
        if (file_exists($webpPath)) {
            $optimizedCount++;
        }
    }
    
    if (count($originalImages) > 0) {
        $optimizationRate = ($optimizedCount / count($originalImages)) * 100;
        echo "<p><strong>Image optimization rate:</strong> " . number_format($optimizationRate, 1) . "% ($optimizedCount/" . count($originalImages) . ")</p>\n";
        $imageScore += ($optimizationRate / 100) * 25;
    } else {
        echo "<p class='warning'>⚠️ No images found to test optimization</p>\n";
    }
    
    $testResults['image_optimization'] = $imageScore;
    $overallScore += $imageScore;
    
    echo "<p class='metric'><strong>Image Optimization Score: " . number_format($imageScore, 1) . "/100</strong></p>\n";
    echo "</div>\n";
    
    // Test 3: Lazy Loading Implementation
    echo "<div class='test-section'>\n";
    echo "<h2>⚡ Test 3: Lazy Loading Implementation</h2>\n";
    
    $lazyScore = 0;
    
    // Check if utils.js exists and contains LazyLoadManager
    if (file_exists('js/utils.js')) {
        $utilsContent = file_get_contents('js/utils.js');
        
        if (strpos($utilsContent, 'LazyLoadManager') !== false) {
            echo "<p class='success'>✅ LazyLoadManager found in utils.js</p>\n";
            $lazyScore += 30;
            
            if (strpos($utilsContent, 'IntersectionObserver') !== false) {
                echo "<p class='success'>✅ Intersection Observer implementation found</p>\n";
                $lazyScore += 30;
            }
            
            if (strpos($utilsContent, 'data-src') !== false) {
                echo "<p class='success'>✅ Lazy loading attributes supported</p>\n";
                $lazyScore += 20;
            }
        } else {
            echo "<p class='error'>❌ LazyLoadManager not found</p>\n";
        }
    } else {
        echo "<p class='error'>❌ utils.js file not found</p>\n";
    }
    
    // Check if main.js uses lazy loading
    if (file_exists('js/main.js')) {
        $mainContent = file_get_contents('js/main.js');
        
        if (strpos($mainContent, 'data-src') !== false && strpos($mainContent, 'LazyLoadManager') !== false) {
            echo "<p class='success'>✅ main.js implements lazy loading</p>\n";
            $lazyScore += 20;
        } else {
            echo "<p class='warning'>⚠️ main.js may not fully implement lazy loading</p>\n";
        }
    }
    
    $testResults['lazy_loading'] = $lazyScore;
    $overallScore += $lazyScore;
    
    echo "<p class='metric'><strong>Lazy Loading Score: " . number_format($lazyScore, 1) . "/100</strong></p>\n";
    echo "</div>\n";
    
    // Test 4: Caching System Performance
    echo "<div class='test-section'>\n";
    echo "<h2>💾 Test 4: Caching System Performance</h2>\n";
    
    $cache = new CacheManager('cache/', 3600);
    $cacheScore = 0;
    
    // Test basic cache operations
    $testData = ['test' => 'performance', 'timestamp' => time()];
    $setResult = $cache->set('perf_test', $testData, 60);
    $getResult = $cache->get('perf_test');
    $deleteResult = $cache->delete('perf_test');
    
    if ($setResult && $getResult === $testData && $deleteResult) {
        echo "<p class='success'>✅ Basic cache operations working</p>\n";
        $cacheScore += 25;
    } else {
        echo "<p class='error'>❌ Basic cache operations failed</p>\n";
    }
    
    // Test performance improvement
    $iterations = 3;
    $timesWithoutCache = [];
    $timesWithCache = [];
    
    // Without cache
    for ($i = 0; $i < $iterations; $i++) {
        $start = microtime(true);
        $stmt = $conn->prepare("SELECT * FROM produits LIMIT 10");
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $end = microtime(true);
        $timesWithoutCache[] = ($end - $start) * 1000;
    }
    
    // With cache
    for ($i = 0; $i < $iterations; $i++) {
        $start = microtime(true);
        $products = $cache->remember("perf_test_$i", function() use ($conn) {
            $stmt = $conn->prepare("SELECT * FROM produits LIMIT 10");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }, 300);
        $end = microtime(true);
        $timesWithCache[] = ($end - $start) * 1000;
    }
    
    $avgWithoutCache = array_sum($timesWithoutCache) / count($timesWithoutCache);
    $avgWithCache = array_sum($timesWithCache) / count($timesWithCache);
    $improvement = (($avgWithoutCache - $avgWithCache) / $avgWithoutCache) * 100;
    
    echo "<p><strong>Performance improvement:</strong> " . number_format($improvement, 1) . "%</p>\n";
    echo "<p><strong>Average without cache:</strong> " . number_format($avgWithoutCache, 2) . " ms</p>\n";
    echo "<p><strong>Average with cache:</strong> " . number_format($avgWithCache, 2) . " ms</p>\n";
    
    if ($improvement > 50) {
        $cacheScore += 50;
        echo "<p class='success'>✅ Excellent cache performance improvement</p>\n";
    } elseif ($improvement > 25) {
        $cacheScore += 35;
        echo "<p class='success'>✅ Good cache performance improvement</p>\n";
    } elseif ($improvement > 10) {
        $cacheScore += 20;
        echo "<p class='warning'>⚠️ Moderate cache performance improvement</p>\n";
    } else {
        echo "<p class='error'>❌ Poor cache performance improvement</p>\n";
    }
    
    // Test cache statistics
    $stats = $cache->getStats();
    if ($stats['enabled']) {
        echo "<p class='success'>✅ Cache system enabled and functional</p>\n";
        $cacheScore += 25;
    }
    
    // Clean up test cache
    for ($i = 0; $i < $iterations; $i++) {
        $cache->delete("perf_test_$i");
    }
    
    $testResults['caching'] = $cacheScore;
    $overallScore += $cacheScore;
    
    echo "<p class='metric'><strong>Caching Score: " . number_format($cacheScore, 1) . "/100</strong></p>\n";
    echo "</div>\n";
    
    // Overall Summary
    echo "<div class='test-section' style='border-left-color: #28a745;'>\n";
    echo "<h2>📊 Overall Performance Optimization Summary</h2>\n";
    
    $overallPercentage = ($overallScore / $maxScore) * 100;
    
    echo "<div class='summary-card " . 
         ($overallPercentage >= 80 ? 'score-excellent' : 
          ($overallPercentage >= 60 ? 'score-good' : 'score-poor')) . "'>\n";
    
    echo "<h3>Overall Score: " . number_format($overallScore, 1) . "/$maxScore (" . number_format($overallPercentage, 1) . "%)</h3>\n";
    
    echo "<div class='progress-bar'>\n";
    echo "<div class='progress-fill' style='width: " . $overallPercentage . "%;'></div>\n";
    echo "</div>\n";
    
    echo "<table>\n";
    echo "<tr><th>Component</th><th>Score</th><th>Status</th></tr>\n";
    
    foreach ($testResults as $component => $score) {
        $status = $score >= 80 ? '<span class="success">Excellent</span>' : 
                 ($score >= 60 ? '<span class="warning">Good</span>' : '<span class="error">Needs Work</span>');
        $componentName = ucwords(str_replace('_', ' ', $component));
        echo "<tr><td>$componentName</td><td>" . number_format($score, 1) . "/100</td><td>$status</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "</div>\n";
    
    // Recommendations
    echo "<h3>🎯 Recommendations:</h3>\n";
    echo "<ul>\n";
    
    if ($testResults['api_format'] < 100) {
        echo "<li class='error'>Fix API response format to use standardized structure</li>\n";
    }
    
    if ($testResults['image_optimization'] < 80) {
        echo "<li class='warning'>Optimize existing product images and ensure WebP support</li>\n";
    }
    
    if ($testResults['lazy_loading'] < 80) {
        echo "<li class='warning'>Complete lazy loading implementation in frontend</li>\n";
    }
    
    if ($testResults['caching'] < 80) {
        echo "<li class='warning'>Improve caching system performance and coverage</li>\n";
    }
    
    if ($overallPercentage >= 80) {
        echo "<li class='success'>🎉 All optimizations are working excellently!</li>\n";
        echo "<li>Monitor performance regularly and consider advanced optimizations</li>\n";
        echo "<li>Implement CDN for static assets</li>\n";
        echo "<li>Consider Redis for high-traffic scenarios</li>\n";
    } elseif ($overallPercentage >= 60) {
        echo "<li>Focus on the lowest-scoring components first</li>\n";
        echo "<li>Test optimizations with real user traffic</li>\n";
    } else {
        echo "<li class='error'>Significant optimization work needed</li>\n";
        echo "<li>Review implementation of each component</li>\n";
        echo "<li>Consider professional performance audit</li>\n";
    }
    
    echo "</ul>\n";
    
    // Next Steps
    echo "<h3>🚀 Next Steps:</h3>\n";
    echo "<ol>\n";
    echo "<li>Test the main website and admin panel functionality</li>\n";
    echo "<li>Upload a new product image to test the optimization pipeline</li>\n";
    echo "<li>Monitor cache hit rates and performance metrics</li>\n";
    echo "<li>Run performance tests with real user scenarios</li>\n";
    echo "<li>Consider implementing additional optimizations based on usage patterns</li>\n";
    echo "</ol>\n";
    
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='test-section' style='border-left-color: #dc3545;'>\n";
    echo "<h2>❌ Error During Validation</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<p class='error'>Stack trace:</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    echo "</div>\n";
}
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Performance optimization validation completed');
    console.log('Overall Score: <?php echo number_format($overallPercentage ?? 0, 1); ?>%');
    
    // Animate progress bar
    setTimeout(() => {
        const progressBar = document.querySelector('.progress-fill');
        if (progressBar) {
            progressBar.style.width = '<?php echo $overallPercentage ?? 0; ?>%';
        }
    }, 500);
});
</script>
