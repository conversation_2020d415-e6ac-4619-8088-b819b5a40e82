<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الذكاء الاصطناعي - لوحة التحكم</title>
    <link href="css/admin.css" rel="stylesheet">
    <link href="../css/ai-settings.css" rel="stylesheet">
    <style>
        body {
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
            background: #d4edda;
            color: #155724;
        }

        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }

        .api-key-input {
            font-family: 'Courier New', monospace;
            direction: ltr;
            text-align: left;
        }

        .test-connection-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .test-connection-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .ai-features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">لوحة التحكم</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white" href="index.html">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white active" href="ai-settings.html">
                                <i class="fas fa-robot"></i> إعدادات الذكاء الاصطناعي
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🤖 إعدادات الذكاء الاصطناعي</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                    </div>
                </div>

                <!-- AI Status Overview -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-info" id="aiStatusAlert">
                            <i class="fas fa-info-circle"></i>
                            <span id="aiStatusText">جاري تحميل حالة الذكاء الاصطناعي...</span>
                        </div>
                    </div>
                </div>

                <!-- AI Providers Configuration -->
                <div class="row">
                    <div class="col-12">
                        <h3>🔧 إعداد مزودي الذكاء الاصطناعي</h3>
                        <p class="text-muted">قم بتكوين مفاتيح API للمزودين المختلفين لتفعيل ميزات الذكاء الاصطناعي</p>

                        <!-- OpenAI Configuration -->
                        <div class="ai-provider-card" id="openaiCard">
                            <div class="provider-header">
                                <div class="d-flex align-items-center">
                                    <div class="provider-logo openai-logo">🤖</div>
                                    <div>
                                        <h5 class="mb-0">OpenAI GPT</h5>
                                        <small class="text-muted">GPT-4, GPT-3.5 Turbo</small>
                                    </div>
                                </div>
                                <span class="status-badge" id="openaiStatus">غير مفعل</span>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="openaiEnabled">
                                            <label class="form-check-label" for="openaiEnabled">
                                                تفعيل OpenAI
                                            </label>
                                        </div>
                                        <div class="mb-3">
                                            <label for="openaiApiKey" class="form-label">مفتاح API</label>
                                            <input type="password" class="form-control api-key-input" id="openaiApiKey"
                                                   placeholder="sk-...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="openaiModel" class="form-label">النموذج</label>
                                            <select class="form-select" id="openaiModel">
                                                <option value="gpt-4">GPT-4</option>
                                                <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                                <option value="gpt-4o">GPT-4o</option>
                                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                                <option value="gpt-3.5-turbo-16k">GPT-3.5 Turbo 16k</option>
                                            </select>
                                        </div>
                                        <button type="button" class="test-connection-btn" onclick="testConnection('openai')">
                                            <i class="fas fa-plug"></i> اختبار الاتصال
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Anthropic Configuration -->
                        <div class="ai-provider-card" id="anthropicCard">
                            <div class="provider-header">
                                <div class="d-flex align-items-center">
                                    <div class="provider-logo anthropic-logo">🧠</div>
                                    <div>
                                        <h5 class="mb-0">Anthropic Claude</h5>
                                        <small class="text-muted">Claude 3 Sonnet, Claude 3 Haiku</small>
                                    </div>
                                </div>
                                <span class="status-badge" id="anthropicStatus">غير مفعل</span>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="anthropicEnabled">
                                            <label class="form-check-label" for="anthropicEnabled">
                                                تفعيل Anthropic
                                            </label>
                                        </div>
                                        <div class="mb-3">
                                            <label for="anthropicApiKey" class="form-label">مفتاح API</label>
                                            <input type="password" class="form-control api-key-input" id="anthropicApiKey"
                                                   placeholder="sk-ant-...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="anthropicModel" class="form-label">النموذج</label>
                                            <select class="form-select" id="anthropicModel">
                                                <option value="claude-3-sonnet-20240229">Claude 3 Sonnet</option>
                                                <option value="claude-3-opus-20240229">Claude 3 Opus</option>
                                                <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</option>
                                                <option value="claude-3-haiku-20240307">Claude 3 Haiku</option>
                                            </select>
                                        </div>
                                        <button type="button" class="test-connection-btn" onclick="testConnection('anthropic')">
                                            <i class="fas fa-plug"></i> اختبار الاتصال
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Google Gemini Configuration -->
                        <div class="ai-provider-card" id="geminiCard">
                            <div class="provider-header">
                                <div class="d-flex align-items-center">
                                    <div class="provider-logo gemini-logo">💎</div>
                                    <div>
                                        <h5 class="mb-0">Google Gemini</h5>
                                        <small class="text-muted">Gemini Pro, Gemini Pro Vision</small>
                                    </div>
                                </div>
                                <span class="status-badge" id="geminiStatus">غير مفعل</span>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="geminiEnabled">
                                            <label class="form-check-label" for="geminiEnabled">
                                                تفعيل Google Gemini
                                            </label>
                                        </div>
                                        <div class="mb-3">
                                            <label for="geminiApiKey" class="form-label">مفتاح API</label>
                                            <input type="password" class="form-control api-key-input" id="geminiApiKey"
                                                   placeholder="AIza...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="geminiModel" class="form-label">النموذج</label>
                                            <select class="form-select" id="geminiModel">
                                                <option value="gemini-pro">Gemini Pro</option>
                                                <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                                                <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                                                <option value="gemini-pro-vision">Gemini Pro Vision</option>
                                            </select>
                                        </div>
                                        <button type="button" class="test-connection-btn" onclick="testConnection('gemini')">
                                            <i class="fas fa-plug"></i> اختبار الاتصال
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Features Overview -->
                <div class="row mt-5">
                    <div class="col-12">
                        <h3>✨ ميزات الذكاء الاصطناعي المتاحة</h3>
                        <div class="ai-features-grid">
                            <div class="feature-card">
                                <div class="feature-icon">📝</div>
                                <h5>إنشاء أوصاف المنتجات</h5>
                                <p class="text-muted">إنشاء أوصاف تسويقية جذابة للمنتجات تلقائياً</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">🎯</div>
                                <h5>عناوين صفحات الهبوط</h5>
                                <p class="text-muted">اقتراح عناوين مقنعة لصفحات الهبوط</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">📄</div>
                                <h5>محتوى تسويقي</h5>
                                <p class="text-muted">كتابة محتوى تسويقي كامل لصفحات الهبوط</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">🔍</div>
                                <h5>أوصاف SEO</h5>
                                <p class="text-muted">إنشاء أوصاف ميتا محسنة لمحركات البحث</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/utils.js"></script>
    <script src="js/ai-settings.js"></script>
</body>
</html>
