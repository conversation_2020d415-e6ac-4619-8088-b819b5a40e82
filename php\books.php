<?php
require_once 'config.php';

class Books
{
    private $pdo;

    public function __construct($pdo)
    {
        $this->pdo = $pdo;
    }

    // Récupérer tous les livres
    public function getAllBooks()
    {
        try {
            $stmt = $this->pdo->query('SELECT * FROM produits WHERE type = "book" ORDER BY created_at DESC');
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Récupérer un livre par son ID
    public function getBookById($id)
    {
        try {
            $stmt = $this->pdo->prepare('SELECT * FROM produits WHERE id = ? AND type = "book"');
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Ajouter un nouveau livre
    public function addBook($data)
    {
        try {
            // Generate slug from title
            require_once 'ProductLanding.php';
            $productLanding = new ProductLanding();
            $slug = $productLanding->generateSlug(sanitize($data['titre']));

            $stmt = $this->pdo->prepare(
                'INSERT INTO produits (type, titre, auteur, description, prix, image_url, stock, slug, has_landing_page, landing_page_enabled)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)'
            );

            $stmt->execute([
                'book', // type
                sanitize($data['titre']),
                sanitize($data['auteur']),
                sanitize($data['description']),
                floatval($data['prix']),
                sanitize($data['image_url']),
                intval($data['stock']),
                $slug,
                isset($data['has_landing_page']) ? (bool)$data['has_landing_page'] : false,
                isset($data['landing_page_enabled']) ? (bool)$data['landing_page_enabled'] : false
            ]);

            $productId = $this->pdo->lastInsertId();

            // Handle content blocks if present
            if (isset($data['content_blocks']) && is_array($data['content_blocks'])) {
                foreach ($data['content_blocks'] as $block) {
                    $productLanding->saveContentBlock(
                        $productId,
                        sanitize($block['title']),
                        sanitize($block['content']),
                        intval($block['sort_order'])
                    );
                }
            }

            // Handle gallery images if present
            if (isset($data['gallery_images']) && is_array($data['gallery_images'])) {
                foreach ($data['gallery_images'] as $imageUrl) {
                    $productLanding->addProductImage($productId, sanitize($imageUrl));
                }
            }

            return ['success' => true, 'id' => $productId];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Mettre à jour un livre
    public function updateBook($id, $data)
    {
        try {
            // Generate slug from title if title is being updated
            require_once 'ProductLanding.php';
            $productLanding = new ProductLanding();
            $slug = $productLanding->generateSlug(sanitize($data['titre']), $id);

            $stmt = $this->pdo->prepare(
                'UPDATE produits
                SET titre = ?, auteur = ?, description = ?, prix = ?, image_url = ?, stock = ?,
                    slug = ?, has_landing_page = ?, landing_page_enabled = ?
                WHERE id = ? AND type = "book"'
            );

            $stmt->execute([
                sanitize($data['titre']),
                sanitize($data['auteur']),
                sanitize($data['description']),
                floatval($data['prix']),
                sanitize($data['image_url']),
                intval($data['stock']),
                $slug,
                isset($data['has_landing_page']) ? (bool)$data['has_landing_page'] : false,
                isset($data['landing_page_enabled']) ? (bool)$data['landing_page_enabled'] : false,
                $id
            ]);

            // Update content blocks if present
            if (isset($data['content_blocks']) && is_array($data['content_blocks'])) {
                // Remove existing content blocks
                $stmt = $this->pdo->prepare('DELETE FROM product_content_blocks WHERE product_id = ?');
                $stmt->execute([$id]);

                // Add new content blocks
                foreach ($data['content_blocks'] as $block) {
                    $productLanding->saveContentBlock(
                        $id,
                        sanitize($block['title']),
                        sanitize($block['content']),
                        intval($block['sort_order'])
                    );
                }
            }

            // Update gallery images if present
            if (isset($data['gallery_images']) && is_array($data['gallery_images'])) {
                foreach ($data['gallery_images'] as $imageUrl) {
                    $productLanding->addProductImage($id, sanitize($imageUrl));
                }
            }

            return ['success' => true, 'rows_affected' => $stmt->rowCount()];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Supprimer un livre
    public function deleteBook($id)
    {
        try {
            $stmt = $this->pdo->prepare('DELETE FROM produits WHERE id = ? AND type = "book"');
            $stmt->execute([$id]);
            return ['success' => true, 'rows_affected' => $stmt->rowCount()];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Vérifier le stock d'un livre
    public function checkStock($id)
    {
        try {
            $stmt = $this->pdo->prepare('SELECT stock FROM produits WHERE id = ? AND type = "book"');
            $stmt->execute([$id]);
            $result = $stmt->fetch();
            return $result ? $result['stock'] : 0;
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Mettre à jour le stock
    public function updateStock($id, $quantity)
    {
        try {
            $stmt = $this->pdo->prepare(
                'UPDATE produits SET stock = stock - ? WHERE id = ? AND stock >= ? AND type = "book"'
            );
            $stmt->execute([$quantity, $id, $quantity]);
            return ['success' => true, 'rows_affected' => $stmt->rowCount()];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Rechercher des livres
    public function searchBooks($query)
    {
        try {
            $stmt = $this->pdo->prepare(
                'SELECT * FROM produits
                WHERE (titre LIKE ? OR auteur LIKE ? OR description LIKE ?) AND type = "book"
                ORDER BY created_at DESC'
            );

            $searchTerm = "%{$query}%";
            $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);

            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Obtenir les statistiques pour le tableau de bord
    public function getStats()
    {
        try {
            // Total des livres
            $totalBooks = $this->pdo->query('SELECT COUNT(*) as total FROM produits WHERE type = "book"')->fetch()['total'];

            // Total des ventes (à partir de la table commandes)
            $totalSales = $this->pdo->query('SELECT COALESCE(SUM(montant_total), 0) as total FROM commandes WHERE statut = "payé"')->fetch()['total'];

            // Nouvelles commandes (en attente)
            $newOrders = $this->pdo->query('SELECT COUNT(*) as total FROM commandes WHERE statut = "en_attente"')->fetch()['total'];

            // Commandes récentes
            $recentOrders = $this->pdo->query(
                'SELECT c.*, GROUP_CONCAT(p.titre) as items
                FROM commandes c
                LEFT JOIN commande_items ci ON c.id = ci.commande_id
                LEFT JOIN produits p ON ci.livre_id = p.id
                GROUP BY c.id
                ORDER BY c.date_commande DESC
                LIMIT 5'
            )->fetchAll();

            return [
                'success' => true,
                'stats' => [
                    'total_books' => $totalBooks,
                    'total_sales' => $totalSales,
                    'new_orders' => $newOrders,
                    'recent_orders' => $recentOrders
                ]
            ];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }
}

// Instancier la classe Books avec une nouvelle connexion PDO
$pdo = getPDOConnection();
$books = new Books($pdo);

// Traitement des requêtes API
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (isset($_GET['id'])) {
        // Récupérer un livre spécifique
        echo json_encode($books->getBookById($_GET['id']));
    } elseif (isset($_GET['search'])) {
        // Rechercher des livres
        echo json_encode($books->searchBooks($_GET['search']));
    } elseif (isset($_GET['stats'])) {
        // Récupérer les statistiques
        echo json_encode($books->getStats());
    } else {
        // Récupérer tous les livres
        echo json_encode($books->getAllBooks());
    }
}

// Vérifier si l'utilisateur est admin pour les opérations de modification
if (isAdminLoggedIn()) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Ajouter un nouveau livre
        echo json_encode($books->addBook($_POST));
    }

    if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // Mettre à jour un livre
        parse_str(file_get_contents("php://input"), $_PUT);
        if (isset($_GET['id'])) {
            echo json_encode($books->updateBook($_GET['id'], $_PUT));
        }
    }

    if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        // Supprimer un livre
        if (isset($_GET['id'])) {
            echo json_encode($books->deleteBook($_GET['id']));
        }
    }
} else {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        http_response_code(403);
        echo json_encode(['error' => 'Accès non autorisé']);
    }
}
