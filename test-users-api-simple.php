<?php
/**
 * Simple Users API Test
 */

require_once 'php/config.php';

header('Content-Type: application/json');

try {
    $pdo = getPDOConnection();
    
    // Test the exact query from users API
    $stmt = $pdo->prepare("
        SELECT
            u.*,
            ur.display_name_ar as role_name,
            ur.level as role_level,
            sp.display_name_ar as subscription_name,
            sp.max_products,
            sp.max_landing_pages,
            sp.max_storage_mb,
            us.store_name,
            us.store_slug
        FROM users u
        LEFT JOIN user_roles ur ON u.role_id = ur.id
        LEFT JOIN subscription_plans sp ON u.subscription_id = sp.id
        LEFT JOIN user_stores us ON u.store_id = us.id
        ORDER BY u.created_at DESC
    ");
    
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    // Format users data
    $formattedUsers = array_map(function ($user) {
        return [
            'id' => (int)$user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'name' => trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')),
            'phone' => $user['phone'],
            'role' => $user['role_name'] ?? 'عميل',
            'role_level' => (int)($user['role_level'] ?? 20),
            'subscription' => $user['subscription_name'] ?? 'مجاني',
            'status' => $user['status'],
            'avatar' => $user['avatar'] ?? 'https://via.placeholder.com/40',
            'registeredAt' => date('Y-m-d', strtotime($user['created_at'])),
            'lastLogin' => $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : null,
            'store' => [
                'name' => $user['store_name'],
                'slug' => $user['store_slug']
            ]
        ];
    }, $users);
    
    echo json_encode([
        'success' => true,
        'users' => $formattedUsers,
        'total' => count($formattedUsers)
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
