-- Création de la table produits
CREATE TABLE IF NOT EXISTS `produits` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `type` ENUM('book', 'bag', 'laptop') NOT NULL,
    `titre` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `prix` DECIMAL(10,2) NOT NULL,
    `stock` INT NOT NULL DEFAULT 0,
    `image_url` VARCHAR(255),
    -- Champs spécifiques pour les livres
    `auteur` VARCHAR(255),
    -- Champs spécifiques pour les sacs
    `materiel` VARCHAR(100),
    `capacite` VARCHAR(50),
    -- Champs spécifiques pour les laptops
    `processeur` VARCHAR(100),
    `ram` VARCHAR(50),
    `stockage` VARCHAR(100),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Création de la table landing_pages
CREATE TABLE IF NOT EXISTS `landing_pages` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `produit_id` INT NOT NULL,
    `titre` VARCHAR(255) NOT NULL,
    `contenu_droit` TEXT,
    `contenu_gauche` TEXT,
    `lien_url` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Création de la table landing_page_images
CREATE TABLE IF NOT EXISTS `landing_page_images` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `landing_page_id` INT NOT NULL,
    `image_url` VARCHAR(255) NOT NULL,
    `ordre` INT NOT NULL DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`landing_page_id`) REFERENCES `landing_pages`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Ajout d'index pour améliorer les performances
CREATE INDEX idx_produits_type ON produits(type);
CREATE INDEX idx_landing_pages_produit ON landing_pages(produit_id);
CREATE INDEX idx_landing_page_images_ordre ON landing_page_images(landing_page_id, ordre);