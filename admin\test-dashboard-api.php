<?php
/**
 * Dashboard API Test Script
 * Tests the dashboard API connectivity and data retrieval
 */

// Set headers for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

echo "<h1>Dashboard API Test</h1>";
echo "<h2>Testing Database Connection and API Response</h2>";

try {
    // Test 1: Include the API file and check response
    echo "<h3>Test 1: Direct API Call</h3>";
    
    // Capture the output from the API
    ob_start();
    include '../php/api/dashboard-stats.php';
    $apiOutput = ob_get_clean();
    
    echo "<pre>";
    echo "API Response:\n";
    echo htmlspecialchars($apiOutput);
    echo "</pre>";
    
    // Test 2: Parse JSON response
    echo "<h3>Test 2: JSON Parsing</h3>";
    $data = json_decode($apiOutput, true);
    
    if ($data) {
        echo "<p style='color: green;'>✅ JSON parsing successful</p>";
        echo "<pre>";
        print_r($data);
        echo "</pre>";
        
        if (isset($data['success']) && $data['success']) {
            echo "<p style='color: green;'>✅ API returned success status</p>";
            
            if (isset($data['data'])) {
                echo "<h4>Dashboard Statistics:</h4>";
                echo "<ul>";
                if (isset($data['data']['products'])) {
                    echo "<li>Total Products: " . $data['data']['products']['total'] . "</li>";
                }
                if (isset($data['data']['orders'])) {
                    echo "<li>Total Orders: " . $data['data']['orders']['total'] . "</li>";
                }
                if (isset($data['data']['sales'])) {
                    echo "<li>Total Sales: " . $data['data']['sales']['formatted_total'] . "</li>";
                }
                if (isset($data['data']['landing_pages'])) {
                    echo "<li>Landing Pages: " . $data['data']['landing_pages']['total'] . "</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>❌ API returned error: " . ($data['message'] ?? 'Unknown error') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to parse JSON response</p>";
        echo "<p>Raw output: " . htmlspecialchars($apiOutput) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception occurred: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 3: Database connection test
echo "<h3>Test 3: Direct Database Connection</h3>";

try {
    require_once '../config/database.php';
    
    $db = Database::getInstance();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Test basic query
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM produits");
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo "<p>Products count from direct query: " . $result['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 4: Check if tables exist
echo "<h3>Test 4: Database Tables Check</h3>";

try {
    $tables = ['produits', 'commandes', 'landing_pages', 'details_commande'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $result = $stmt->fetch();
            echo "<p style='color: green;'>✅ Table '$table' exists with " . $result['count'] . " records</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Table '$table' issue: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking tables: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>Test Complete</h3>";
echo "<p><a href='index.html'>Back to Admin</a></p>";
?>
