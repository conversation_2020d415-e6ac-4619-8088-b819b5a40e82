<?php

/**
 * Subscription Limits API
 * Handles subscription limit validation and enforcement
 */

require_once __DIR__ . '/../SubscriptionLimits.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';
    
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('يجب تسجيل الدخول أولاً');
    }
    
    $userId = $_SESSION['user_id'];
    $limitsManager = new SubscriptionLimits();
    
    switch ($method) {
        case 'GET':
            handleGetRequest($limitsManager, $userId, $action);
            break;
            
        case 'POST':
            handlePostRequest($limitsManager, $userId, $action);
            break;
            
        default:
            throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    error_log("Subscription Limits API Error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Handle GET requests
 */
function handleGetRequest($limitsManager, $userId, $action)
{
    switch ($action) {
        case 'limits':
            getLimitsAndUsage($limitsManager, $userId);
            break;
            
        case 'check':
            checkSpecificLimit($limitsManager, $userId);
            break;
            
        case 'usage':
            getUsageOnly($limitsManager, $userId);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
}

/**
 * Handle POST requests
 */
function handlePostRequest($limitsManager, $userId, $action)
{
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'validate':
            validateAction($limitsManager, $userId, $input);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
}

/**
 * Get user limits and usage
 */
function getLimitsAndUsage($limitsManager, $userId)
{
    try {
        $data = $limitsManager->getUserLimitsAndUsage($userId);
        
        echo json_encode([
            'success' => true,
            'data' => $data
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في جلب حدود الاشتراك: ' . $e->getMessage());
    }
}

/**
 * Check specific limit
 */
function checkSpecificLimit($limitsManager, $userId)
{
    $type = $_GET['type'] ?? '';
    
    if (empty($type)) {
        throw new Exception('نوع الحد مطلوب');
    }
    
    try {
        $result = null;
        
        switch ($type) {
            case 'products':
                $result = $limitsManager->canAddProduct($userId);
                break;
                
            case 'landing_pages':
                $result = $limitsManager->canAddLandingPage($userId);
                break;
                
            case 'categories':
                $result = $limitsManager->canAddCategory($userId);
                break;
                
            default:
                throw new Exception('نوع حد غير صالح');
        }
        
        echo json_encode([
            'success' => true,
            'can_add' => $result['allowed'],
            'current' => $result['current'],
            'limit' => $result['limit'],
            'remaining' => $result['remaining']
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في فحص الحد: ' . $e->getMessage());
    }
}

/**
 * Get usage statistics only
 */
function getUsageOnly($limitsManager, $userId)
{
    try {
        $usage = $limitsManager->getUserUsage($userId);
        
        echo json_encode([
            'success' => true,
            'usage' => $usage
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في جلب إحصائيات الاستخدام: ' . $e->getMessage());
    }
}

/**
 * Validate an action against subscription limits
 */
function validateAction($limitsManager, $userId, $input)
{
    $actionType = $input['action'] ?? '';
    $additionalData = $input['data'] ?? [];
    
    if (empty($actionType)) {
        throw new Exception('نوع العملية مطلوب');
    }
    
    try {
        $result = $limitsManager->validateAction($userId, $actionType, $additionalData);
        
        if (!$result['allowed']) {
            $message = '';
            switch ($actionType) {
                case 'add_product':
                    $message = 'لقد وصلت إلى الحد الأقصى لعدد المنتجات في اشتراكك';
                    break;
                case 'add_landing_page':
                    $message = 'لقد وصلت إلى الحد الأقصى لعدد صفحات الهبوط في اشتراكك';
                    break;
                case 'add_category':
                    $message = 'لقد وصلت إلى الحد الأقصى لعدد الفئات في اشتراكك';
                    break;
                case 'upload_file':
                    $message = 'لقد وصلت إلى الحد الأقصى لمساحة التخزين في اشتراكك';
                    break;
                default:
                    $message = 'لقد وصلت إلى حد اشتراكك';
            }
            
            echo json_encode([
                'success' => false,
                'allowed' => false,
                'message' => $message,
                'details' => $result
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'allowed' => true,
                'message' => 'العملية مسموحة',
                'details' => $result
            ]);
        }
    } catch (Exception $e) {
        throw new Exception('فشل في التحقق من صحة العملية: ' . $e->getMessage());
    }
}
