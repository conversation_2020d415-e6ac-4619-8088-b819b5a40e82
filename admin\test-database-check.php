<?php

/**
 * Database Check Script
 * Checks if all required tables exist for the store management system
 */

require_once '../php/config.php';

try {
    $pdo = getPDOConnection();

    echo "🔍 فحص قاعدة البيانات...\n\n";

    // Check if stores table exists
    echo "📋 فحص الجداول المطلوبة:\n";

    $tables = [
        'stores' => 'جدول المتاجر الرئيسي',
        'store_categories' => 'جدول فئات المتاجر',
        'store_products' => 'جدول منتجات المتاجر',
        'store_orders' => 'جدول طلبات المتاجر',
        'users' => 'جدول المستخدمين',
        'produits' => 'جدول المنتجات',
        'landing_pages' => 'جدول صفحات الهبوط'
    ];

    $existingTables = [];
    $missingTables = [];

    foreach ($tables as $table => $description) {
        // Use INFORMATION_SCHEMA for better MariaDB compatibility
        $stmt = $pdo->prepare("
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = ?
        ");
        $stmt->execute([$table]);
        $exists = $stmt->fetch();

        if ($exists) {
            echo "✅ {$table} - {$description}\n";
            $existingTables[] = $table;

            // Get table info
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$table}");
            $stmt->execute();
            $count = $stmt->fetchColumn();
            echo "   📊 عدد السجلات: {$count}\n";
        } else {
            echo "❌ {$table} - {$description} (مفقود)\n";
            $missingTables[] = $table;
        }
    }

    echo "\n📊 ملخص الفحص:\n";
    echo "✅ الجداول الموجودة: " . count($existingTables) . "\n";
    echo "❌ الجداول المفقودة: " . count($missingTables) . "\n";

    if (!empty($missingTables)) {
        echo "\n⚠️ الجداول المفقودة:\n";
        foreach ($missingTables as $table) {
            echo "   - {$table}\n";
        }
        echo "\n💡 يرجى تشغيل ترحيل قاعدة البيانات لإنشاء الجداول المفقودة.\n";
    }

    // Check if stores table has the correct structure
    if (in_array('stores', $existingTables)) {
        echo "\n🔍 فحص هيكل جدول المتاجر:\n";
        $stmt = $pdo->prepare("DESCRIBE stores");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $requiredColumns = [
            'id',
            'user_id',
            'store_name',
            'store_slug',
            'description',
            'logo_url',
            'status',
            'domain',
            'theme',
            'settings',
            'total_products',
            'total_orders',
            'total_revenue',
            'created_at',
            'updated_at'
        ];

        $existingColumns = array_column($columns, 'Field');
        $missingColumns = array_diff($requiredColumns, $existingColumns);

        if (empty($missingColumns)) {
            echo "✅ هيكل جدول المتاجر صحيح\n";
        } else {
            echo "⚠️ أعمدة مفقودة في جدول المتاجر:\n";
            foreach ($missingColumns as $column) {
                echo "   - {$column}\n";
            }
        }

        // Show column details
        echo "\n📋 أعمدة جدول المتاجر:\n";
        foreach ($columns as $column) {
            echo "   {$column['Field']} ({$column['Type']})\n";
        }
    }

    // Test database connection
    echo "\n🔗 اختبار الاتصال بقاعدة البيانات:\n";
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetchColumn();
    echo "✅ إصدار MySQL: {$version}\n";

    // Check if we can create a test table
    try {
        $pdo->exec("CREATE TEMPORARY TABLE test_connection (id INT)");
        $pdo->exec("DROP TEMPORARY TABLE test_connection");
        echo "✅ صلاحيات إنشاء الجداول: متاحة\n";
    } catch (Exception $e) {
        echo "❌ صلاحيات إنشاء الجداول: غير متاحة - " . $e->getMessage() . "\n";
    }

    echo "\n🎉 انتهى فحص قاعدة البيانات!\n";

    if (empty($missingTables)) {
        echo "\n✅ قاعدة البيانات جاهزة لنظام إدارة المتاجر!\n";
    } else {
        echo "\n⚠️ يرجى تشغيل ترحيل قاعدة البيانات أولاً.\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص قاعدة البيانات: " . $e->getMessage() . "\n";
    echo "\n💡 تأكد من:\n";
    echo "   - إعدادات قاعدة البيانات في ملف .env\n";
    echo "   - تشغيل خادم MySQL\n";
    echo "   - صحة بيانات الاتصال\n";
}
