<?php
require_once __DIR__ . '/../config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    $pdo = getPDOConnection();

    // Function to check if table exists
    function tableExists($pdo, $tableName)
    {
        try {
            $stmt = $pdo->prepare("SELECT 1 FROM $tableName LIMIT 1");
            $stmt->execute();
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }

    // Determine table names (French or English)
    $productsTable = tableExists($pdo, 'produits') ? 'produits' : 'products';
    $booksTable = tableExists($pdo, 'livres') ? 'livres' : 'books';
    $ordersTable = tableExists($pdo, 'commandes') ? 'commandes' : 'orders';
    $landingPagesTable = 'landing_pages';

    // Initialize stats
    $totalProducts = 0;
    $activeProducts = 0;

    // Statistiques des produits/livres
    try {
        if (tableExists($pdo, $productsTable)) {
            $stmt = $pdo->prepare("SELECT COUNT(*) as total_products FROM $productsTable");
            $stmt->execute();
            $totalProducts = $stmt->fetch()['total_products'];

            // Try different column names for active status
            $activeColumn = tableExists($pdo, 'produits') ? 'actif' : 'status';
            $activeValue = tableExists($pdo, 'produits') ? '1' : "'active'";

            $stmt = $pdo->prepare("SELECT COUNT(*) as active_products FROM $productsTable WHERE $activeColumn = $activeValue");
            $stmt->execute();
            $activeProducts = $stmt->fetch()['active_products'];
        }
    } catch (PDOException $e) {
        error_log("Error getting products stats: " . $e->getMessage());
    }

    // Try books table if products table is empty or doesn't exist
    if ($totalProducts == 0 && tableExists($pdo, $booksTable)) {
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) as total_books FROM $booksTable");
            $stmt->execute();
            $totalProducts = $stmt->fetch()['total_books'];

            $activeColumn = tableExists($pdo, 'livres') ? 'actif' : 'status';
            $activeValue = tableExists($pdo, 'livres') ? '1' : "'active'";

            $stmt = $pdo->prepare("SELECT COUNT(*) as active_books FROM $booksTable WHERE $activeColumn = $activeValue");
            $stmt->execute();
            $activeProducts = $stmt->fetch()['active_books'];
        } catch (PDOException $e) {
            error_log("Error getting books stats: " . $e->getMessage());
        }
    }

    // Statistiques des commandes
    $totalOrders = 0;
    $pendingOrders = 0;
    $paidOrders = 0;
    $shippedOrders = 0;
    $totalSales = 0;
    $pendingSales = 0;

    try {
        if (tableExists($pdo, $ordersTable)) {
            $stmt = $pdo->prepare("SELECT COUNT(*) as total_orders FROM $ordersTable");
            $stmt->execute();
            $totalOrders = $stmt->fetch()['total_orders'];

            // Determine status column and values
            $statusColumn = tableExists($pdo, 'commandes') ? 'statut' : 'status';
            $amountColumn = tableExists($pdo, 'commandes') ? 'montant_total' : 'total_amount';

            // Status values mapping
            $pendingStatus = tableExists($pdo, 'commandes') ? 'en_attente' : 'pending';
            $paidStatus = tableExists($pdo, 'commandes') ? 'payé' : 'paid';
            $shippedStatus = tableExists($pdo, 'commandes') ? 'expédié' : 'shipped';
            $completedStatus = tableExists($pdo, 'commandes') ? 'payé' : 'completed';

            $stmt = $pdo->prepare("SELECT COUNT(*) as pending_orders FROM $ordersTable WHERE $statusColumn = '$pendingStatus'");
            $stmt->execute();
            $pendingOrders = $stmt->fetch()['pending_orders'];

            $stmt = $pdo->prepare("SELECT COUNT(*) as paid_orders FROM $ordersTable WHERE $statusColumn IN ('$paidStatus', '$completedStatus')");
            $stmt->execute();
            $paidOrders = $stmt->fetch()['paid_orders'];

            $stmt = $pdo->prepare("SELECT COUNT(*) as shipped_orders FROM $ordersTable WHERE $statusColumn = '$shippedStatus'");
            $stmt->execute();
            $shippedOrders = $stmt->fetch()['shipped_orders'];

            // Statistiques des ventes
            $stmt = $pdo->prepare("SELECT COALESCE(SUM($amountColumn), 0) as total_sales FROM $ordersTable WHERE $statusColumn IN ('$paidStatus', '$shippedStatus', '$completedStatus')");
            $stmt->execute();
            $totalSales = $stmt->fetch()['total_sales'];

            $stmt = $pdo->prepare("SELECT COALESCE(SUM($amountColumn), 0) as pending_sales FROM $ordersTable WHERE $statusColumn = '$pendingStatus'");
            $stmt->execute();
            $pendingSales = $stmt->fetch()['pending_sales'];
        }
    } catch (PDOException $e) {
        error_log("Error getting orders stats: " . $e->getMessage());
    }

    // Statistiques des landing pages
    $totalLandingPages = 0;
    try {
        if (tableExists($pdo, $landingPagesTable)) {
            $stmt = $pdo->prepare("SELECT COUNT(*) as total_landing_pages FROM $landingPagesTable");
            $stmt->execute();
            $totalLandingPages = $stmt->fetch()['total_landing_pages'];
        }
    } catch (PDOException $e) {
        error_log("Error getting landing pages stats: " . $e->getMessage());
    }

    // Statistiques des utilisateurs
    $totalUsers = 0;
    try {
        if (tableExists($pdo, 'users')) {
            $stmt = $pdo->prepare("SELECT COUNT(*) as total_users FROM users");
            $stmt->execute();
            $totalUsers = $stmt->fetch()['total_users'];
        }
    } catch (PDOException $e) {
        error_log("Error getting users stats: " . $e->getMessage());
    }

    // Get recent orders for dashboard
    $recentOrders = [];
    try {
        if (tableExists($pdo, $ordersTable)) {
            $dateColumn = tableExists($pdo, 'commandes') ? 'date_commande' : 'created_at';
            $customerColumn = tableExists($pdo, 'commandes') ? 'nom_client' : 'customer_name';
            $amountColumn = tableExists($pdo, 'commandes') ? 'montant_total' : 'total_amount';
            $statusColumn = tableExists($pdo, 'commandes') ? 'statut' : 'status';

            $stmt = $pdo->prepare("
                SELECT
                    id,
                    $customerColumn as customer_name,
                    $amountColumn as total_amount,
                    $statusColumn as status,
                    $dateColumn as created_at
                FROM $ordersTable
                ORDER BY $dateColumn DESC
                LIMIT 5
            ");
            $stmt->execute();
            $recentOrders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (PDOException $e) {
        error_log("Error getting recent orders: " . $e->getMessage());
        // Provide sample data
        $recentOrders = [
            [
                'id' => 1,
                'customer_name' => 'أحمد محمد',
                'total_amount' => 150.00,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
            ]
        ];
    }



    // Réponse JSON compatible avec le dashboard
    echo json_encode([
        'success' => true,
        'data' => [
            'totalBooks' => (int)$totalProducts,
            'newOrders' => (int)$pendingOrders,
            'totalSales' => (float)$totalSales,
            'totalLandingPages' => (int)$totalLandingPages,
            'recentOrders' => $recentOrders,
            'activeUsers' => (int)$totalUsers,
            'pendingOrders' => (int)$pendingOrders,
            'lastUpdated' => date('Y-m-d H:i:s'),
            'currency' => 'DZD',
            // Additional detailed stats
            'detailed' => [
                'products' => [
                    'total' => (int)$totalProducts,
                    'active' => (int)$activeProducts,
                    'inactive' => (int)($totalProducts - $activeProducts)
                ],
                'orders' => [
                    'total' => (int)$totalOrders,
                    'pending' => (int)$pendingOrders,
                    'paid' => (int)$paidOrders,
                    'shipped' => (int)$shippedOrders
                ],
                'sales' => [
                    'total' => (float)$totalSales,
                    'pending' => (float)$pendingSales,
                    'formatted_total' => number_format($totalSales, 2) . ' دج',
                    'formatted_pending' => number_format($pendingSales, 2) . ' دج'
                ]
            ]
        ],
        'message' => 'Dashboard statistics loaded successfully',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    error_log("Database error in dashboard-stats.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erreur de base de données',
        'error' => $e->getMessage()
    ]);
} catch (Exception $e) {
    error_log("General error in dashboard-stats.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erreur serveur',
        'error' => $e->getMessage()
    ]);
}
