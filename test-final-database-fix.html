<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final - Connectivité Base de Données</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .admin-frame {
            width: 100%;
            height: 800px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .api-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid #6c757d;
        }
        .api-status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .api-status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .api-status.loading {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .stat-card.success {
            background: #d4edda;
            border-color: #28a745;
        }
        .stat-card h3 {
            margin: 0;
            font-size: 2rem;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-database"></i> Test Final - Connectivité Base de Données</h1>
        <p class="lead">Vérification complète que les problèmes de connectivité sont résolus</p>
        
        <div class="alert alert-info">
            <h4><i class="fas fa-info-circle"></i> Corrections Appliquées</h4>
            <ul>
                <li>✅ API Dashboard Statistics corrigée et adaptée aux tables existantes</li>
                <li>✅ Détection automatique des noms de tables (français/anglais)</li>
                <li>✅ Script de chargement des produits amélioré</li>
                <li>✅ Gestion d'erreurs et retry automatique</li>
                <li>✅ Interface utilisateur améliorée pour l'affichage des produits</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-play"></i> Tests de Connectivité</h2>
            
            <button class="btn btn-primary" onclick="testDashboardStats()">
                <i class="fas fa-chart-bar"></i> Tester API Dashboard
            </button>
            
            <button class="btn btn-success" onclick="testProductsAPI()">
                <i class="fas fa-box"></i> Tester API Produits
            </button>
            
            <button class="btn btn-info" onclick="testAllAPIs()">
                <i class="fas fa-network-wired"></i> Tester Toutes les APIs
            </button>
            
            <button class="btn btn-warning" onclick="clearResults()">
                <i class="fas fa-eraser"></i> Effacer
            </button>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-chart-pie"></i> Statistiques en Temps Réel</h2>
            <div id="statsGrid" class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalProducts">-</h3>
                    <p>Total Produits</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalOrders">-</h3>
                    <p>Total Commandes</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalSales">-</h3>
                    <p>Total Ventes (DZD)</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalLandingPages">-</h3>
                    <p>Landing Pages</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-list"></i> État des APIs</h2>
            <div id="apiStatus">
                <div class="api-status loading" data-api="dashboard">
                    <span><i class="fas fa-chart-bar"></i> API Dashboard Statistics</span>
                    <span>En attente...</span>
                </div>
                <div class="api-status loading" data-api="products">
                    <span><i class="fas fa-box"></i> API Products</span>
                    <span>En attente...</span>
                </div>
                <div class="api-status loading" data-api="orders">
                    <span><i class="fas fa-shopping-cart"></i> API Orders</span>
                    <span>En attente...</span>
                </div>
                <div class="api-status loading" data-api="landing-pages">
                    <span><i class="fas fa-bullhorn"></i> API Landing Pages</span>
                    <span>En attente...</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-terminal"></i> Résultats des Tests</h2>
            <div id="testResults" class="test-results">
                <div style="color: #28a745;">🚀 Prêt à tester la connectivité base de données...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-browser"></i> Interface Admin</h2>
            <iframe id="adminFrame" class="admin-frame" src="/admin/"></iframe>
            <div class="mt-3">
                <button class="btn btn-secondary" onclick="reloadAdminFrame()">
                    <i class="fas fa-redo"></i> Recharger
                </button>
                <button class="btn btn-info" onclick="testAdminInterface()">
                    <i class="fas fa-search"></i> Tester Interface
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Test results logging
        function logResult(message, type = "info") {
            const resultsDiv = document.getElementById("testResults");
            const timestamp = new Date().toLocaleTimeString("fr-FR");
            const icon = type === "error" ? "❌" : type === "success" ? "✅" : type === "warning" ? "⚠️" : "ℹ️";
            const color = type === "error" ? "#dc3545" : type === "success" ? "#28a745" : type === "warning" ? "#ffc107" : "#6c757d";
            
            const logEntry = document.createElement("div");
            logEntry.style.color = color;
            logEntry.style.marginBottom = "5px";
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Update API status
        function updateAPIStatus(apiName, status, message) {
            const statusElement = document.querySelector(`[data-api="${apiName}"]`);
            if (statusElement) {
                statusElement.className = `api-status ${status}`;
                statusElement.querySelector("span:last-child").textContent = message;
            }
        }
        
        // Update statistics
        function updateStats(stats) {
            document.getElementById('totalProducts').textContent = stats.totalBooks || 0;
            document.getElementById('totalOrders').textContent = stats.newOrders || 0;
            document.getElementById('totalSales').textContent = stats.totalSales || 0;
            document.getElementById('totalLandingPages').textContent = stats.totalLandingPages || 0;
            
            // Add success class to stat cards
            document.querySelectorAll('.stat-card').forEach(card => {
                card.classList.add('success');
            });
        }
        
        // Test Dashboard Stats API
        function testDashboardStats() {
            logResult("🔍 Test de l'API Dashboard Statistics...");
            updateAPIStatus('dashboard', 'loading', 'Test en cours...');
            
            fetch('/php/api/dashboard-stats.php')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        logResult("✅ API Dashboard Statistics fonctionne parfaitement", "success");
                        updateAPIStatus('dashboard', 'success', 'Fonctionnel ✅');
                        updateStats(data.data);
                        
                        logResult(`📊 Données récupérées: ${data.data.totalBooks} produits, ${data.data.totalLandingPages} landing pages`, "info");
                    } else {
                        throw new Error(data.message || 'Réponse invalide');
                    }
                })
                .catch(error => {
                    logResult(`❌ Erreur API Dashboard: ${error.message}`, "error");
                    updateAPIStatus('dashboard', 'error', 'Erreur ❌');
                });
        }
        
        // Test Products API
        function testProductsAPI() {
            logResult("🔍 Test de l'API Products...");
            updateAPIStatus('products', 'loading', 'Test en cours...');
            
            fetch('/php/api/products.php')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        logResult(`✅ API Products fonctionne: ${data.data.length} produits trouvés`, "success");
                        updateAPIStatus('products', 'success', `${data.data.length} produits ✅`);
                    } else {
                        throw new Error(data.message || 'Réponse invalide');
                    }
                })
                .catch(error => {
                    logResult(`❌ Erreur API Products: ${error.message}`, "error");
                    updateAPIStatus('products', 'error', 'Erreur ❌');
                });
        }
        
        // Test Orders API
        function testOrdersAPI() {
            logResult("🔍 Test de l'API Orders...");
            updateAPIStatus('orders', 'loading', 'Test en cours...');
            
            fetch('/php/api/orders.php')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        logResult(`✅ API Orders fonctionne: ${data.data.length} commandes`, "success");
                        updateAPIStatus('orders', 'success', `${data.data.length} commandes ✅`);
                    } else {
                        throw new Error(data.message || 'Réponse invalide');
                    }
                })
                .catch(error => {
                    logResult(`❌ Erreur API Orders: ${error.message}`, "error");
                    updateAPIStatus('orders', 'error', 'Erreur ❌');
                });
        }
        
        // Test Landing Pages API
        function testLandingPagesAPI() {
            logResult("🔍 Test de l'API Landing Pages...");
            updateAPIStatus('landing-pages', 'loading', 'Test en cours...');
            
            fetch('/php/api/landing-pages.php')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        logResult(`✅ API Landing Pages fonctionne: ${data.data.length} pages`, "success");
                        updateAPIStatus('landing-pages', 'success', `${data.data.length} pages ✅`);
                    } else {
                        throw new Error(data.message || 'Réponse invalide');
                    }
                })
                .catch(error => {
                    logResult(`❌ Erreur API Landing Pages: ${error.message}`, "error");
                    updateAPIStatus('landing-pages', 'error', 'Erreur ❌');
                });
        }
        
        // Test all APIs
        function testAllAPIs() {
            logResult("🚀 Test de toutes les APIs...");
            
            testDashboardStats();
            setTimeout(() => testProductsAPI(), 1000);
            setTimeout(() => testOrdersAPI(), 2000);
            setTimeout(() => testLandingPagesAPI(), 3000);
        }
        
        // Test admin interface
        function testAdminInterface() {
            logResult("🔍 Test de l'interface admin...");
            
            const frame = document.getElementById('adminFrame');
            try {
                const frameWindow = frame.contentWindow;
                const frameDoc = frame.contentDocument || frameWindow.document;
                
                // Check if dashboard shows real stats
                const dashboardSection = frameDoc.getElementById('dashboard');
                if (dashboardSection && dashboardSection.classList.contains('active')) {
                    logResult("✅ Dashboard est actif dans l'interface", "success");
                    
                    // Check for stats elements
                    const statsElements = frameDoc.querySelectorAll('#totalBooks, #newOrders, #totalSales, #totalLandingPages');
                    if (statsElements.length > 0) {
                        logResult(`✅ ${statsElements.length} éléments de statistiques trouvés`, "success");
                    } else {
                        logResult("⚠️ Éléments de statistiques non trouvés", "warning");
                    }
                } else {
                    logResult("⚠️ Dashboard non actif dans l'interface", "warning");
                }
                
                // Check if products section exists
                const productsSection = frameDoc.getElementById('books');
                if (productsSection) {
                    logResult("✅ Section produits trouvée", "success");
                } else {
                    logResult("❌ Section produits non trouvée", "error");
                }
                
            } catch (error) {
                logResult(`❌ Erreur test interface: ${error.message}`, "error");
            }
        }
        
        // Reload admin frame
        function reloadAdminFrame() {
            logResult("🔄 Rechargement de l'interface admin...");
            const frame = document.getElementById('adminFrame');
            frame.src = frame.src;
        }
        
        // Clear results
        function clearResults() {
            document.getElementById("testResults").innerHTML = "<div style='color: #28a745;'>🧹 Résultats effacés</div>";
            
            // Reset API statuses
            document.querySelectorAll(".api-status").forEach(element => {
                element.className = "api-status loading";
                element.querySelector("span:last-child").textContent = "En attente...";
            });
            
            // Reset stats
            document.querySelectorAll('.stat-card h3').forEach(h3 => {
                h3.textContent = '-';
            });
            document.querySelectorAll('.stat-card').forEach(card => {
                card.classList.remove('success');
            });
        }
        
        // Auto-run tests when page loads
        document.addEventListener("DOMContentLoaded", function() {
            logResult("Interface de test chargée", "success");
            
            // Auto-test after 3 seconds
            setTimeout(() => {
                logResult("Démarrage des tests automatiques...");
                testAllAPIs();
            }, 3000);
        });
    </script>
</body>
</html>
