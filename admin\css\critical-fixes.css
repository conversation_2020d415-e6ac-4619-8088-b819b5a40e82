/* Enhanced UI Visibility Fixes */

/* Force visibility for landing pages elements */
.landing-pages-section,
#landingPagesContent,
[data-section="landing-pages"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure add button is always visible */
#addLandingPageBtn,
.add-landing-page-btn,
[data-action="add-landing-page"] {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
    min-width: 120px !important;
    min-height: 36px !important;
    padding: 8px 16px !important;
    margin: 5px !important;
}

/* Fix for hidden content sections */
.content-section {
    min-height: 1px !important;
}

.content-section.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Modal fixes */
.modal {
    z-index: 1050 !important;
}

.modal.show {
    display: block !important;
    opacity: 1 !important;
}

.modal-backdrop {
    z-index: 1040 !important;
}

/* Button styling improvements */
.btn {
    cursor: pointer !important;
    user-select: none !important;
    border: 1px solid transparent !important;
    border-radius: 4px !important;
    text-align: center !important;
    vertical-align: middle !important;
    white-space: nowrap !important;
}

.btn:hover {
    opacity: 0.9 !important;
}

.btn-primary {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: #fff !important;
}

/* Landing pages table improvements */
.landing-pages-table {
    width: 100% !important;
    margin-top: 20px !important;
    display: table !important;
}

/* Form improvements */
.form-control {
    width: 100% !important;
    padding: 8px 12px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    display: block !important;
}

/* Arabic RTL improvements */
[dir="rtl"] .modal-header .close {
    margin-left: auto !important;
    margin-right: -1rem !important;
}

[dir="rtl"] .form-group label {
    text-align: right !important;
}

/* Debug styles for development */
.debug-visible {
    border: 2px solid red !important;
    background-color: rgba(255, 0, 0, 0.1) !important;
}

/* Force visibility for troubleshooting */
.force-visible {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    width: auto !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 10px !important;
        max-width: calc(100% - 20px) !important;
    }

    #addLandingPageBtn {
        width: 100% !important;
        margin: 10px 0 !important;
    }
}

/* Ensure sidebar and navigation are visible */
.sidebar,
.admin-sidebar {
    display: block !important;
    visibility: visible !important;
}

.main-content {
    display: block !important;
    visibility: visible !important;
}

/* Fix for any hidden admin sections */
.admin-section {
    display: block !important;
}

.admin-section.active {
    display: block !important;
    visibility: visible !important;
}