<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف أقسام لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error-log {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success-log {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning-log {
            background: #fff3cd;
            color: #856404;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .section-test {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .section-test.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .section-test.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .section-test.warning {
            border-color: #ffc107;
            background: #fff3cd;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-loading { 
            background: #6c757d; 
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .admin-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-check-double"></i> اختبار وظائف أقسام لوحة التحكم</h1>
        <p>فحص شامل لجميع أقسام الشريط الجانبي في لوحة التحكم للتأكد من أنها قابلة للنقر وتعمل بشكل صحيح</p>
        
        <div class="test-section">
            <h2><i class="fas fa-play"></i> اختبارات الأقسام</h2>
            
            <button class="btn btn-primary" onclick="testAllSections()">
                <i class="fas fa-test-tube"></i> اختبار جميع الأقسام
            </button>
            
            <button class="btn btn-success" onclick="openAdminInNewTab()">
                <i class="fas fa-external-link-alt"></i> فتح لوحة التحكم في نافذة جديدة
            </button>
            
            <button class="btn btn-info" onclick="testIndividualSections()">
                <i class="fas fa-mouse-pointer"></i> اختبار الأقسام فردياً
            </button>
            
            <button class="btn btn-warning" onclick="clearResults()">
                <i class="fas fa-eraser"></i> مسح النتائج
            </button>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-list-check"></i> حالة الأقسام</h2>
            <div id="sectionsStatus">
                <!-- سيتم ملء هذا القسم بواسطة JavaScript -->
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-terminal"></i> سجل الاختبارات</h2>
            <div id="consoleOutput" class="console-output">
                <div class="success-log">🚀 جاهز لاختبار أقسام لوحة التحكم...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-browser"></i> معاينة لوحة التحكم</h2>
            <iframe id="adminFrame" class="admin-frame" src="/admin/"></iframe>
            <div class="mt-3">
                <button class="btn btn-secondary" onclick="reloadAdminFrame()">
                    <i class="fas fa-redo"></i> إعادة تحميل الإطار
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // قائمة الأقسام المتوقعة في لوحة التحكم
        const adminSections = [
            {
                id: 'dashboard',
                name: 'الرئيسية',
                icon: 'fas fa-home',
                selector: '[data-section="dashboard"], .nav-item[data-target="dashboardContent"], .sidebar-item[onclick*="dashboard"]',
                contentSelector: '#dashboardContent, .dashboard-section'
            },
            {
                id: 'products',
                name: 'إدارة المنتجات',
                icon: 'fas fa-box',
                selector: '[data-section="products"], .nav-item[data-target="productsContent"], .sidebar-item[onclick*="products"]',
                contentSelector: '#productsContent, .products-section'
            },
            {
                id: 'orders',
                name: 'الطلبات',
                icon: 'fas fa-shopping-cart',
                selector: '[data-section="orders"], .nav-item[data-target="ordersContent"], .sidebar-item[onclick*="orders"]',
                contentSelector: '#ordersContent, .orders-section'
            },
            {
                id: 'landing-pages',
                name: 'صفحات هبوط',
                icon: 'fas fa-bullhorn',
                selector: '[data-section="landing-pages"], .nav-item[data-target="landingPagesContent"], .sidebar-item[onclick*="landing"]',
                contentSelector: '#landingPagesContent, .landing-pages-section'
            },
            {
                id: 'reports',
                name: 'التقارير والإحصائيات',
                icon: 'fas fa-chart-bar',
                selector: '[data-section="reports"], .nav-item[data-target="reportsContent"], .sidebar-item[onclick*="reports"]',
                contentSelector: '#reportsContent, .reports-section'
            },
            {
                id: 'settings',
                name: 'إعدادات النظام',
                icon: 'fas fa-cog',
                selector: '[data-section="settings"], .nav-item[data-target="settingsContent"], .sidebar-item[onclick*="settings"]',
                contentSelector: '#settingsContent, .settings-section'
            },
            {
                id: 'logout',
                name: 'تسجيل الخروج',
                icon: 'fas fa-sign-out-alt',
                selector: '[data-action="logout"], .logout-btn, .sidebar-item[onclick*="logout"]',
                contentSelector: null // لا يحتاج محتوى
            }
        ];
        
        // Console logging function
        function logToConsole(message, type = "info") {
            const console = document.getElementById("consoleOutput");
            const timestamp = new Date().toLocaleTimeString("ar-DZ");
            const icon = type === "error" ? "❌" : type === "success" ? "✅" : type === "warning" ? "⚠️" : "ℹ️";
            
            const logEntry = document.createElement("div");
            logEntry.className = type === "error" ? "error-log" : type === "success" ? "success-log" : type === "warning" ? "warning-log" : "";
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            console.appendChild(logEntry);
            console.scrollTop = console.scrollHeight;
        }
        
        // Update section status
        function updateSectionStatus() {
            const statusDiv = document.getElementById("sectionsStatus");
            let statusHTML = "";
            
            adminSections.forEach(section => {
                statusHTML += `
                    <div class="section-test" id="status-${section.id}">
                        <div>
                            <i class="${section.icon}"></i>
                            <strong>${section.name}</strong>
                        </div>
                        <div>
                            <span class="status-indicator status-loading"></span>
                            <span>جاري الفحص...</span>
                        </div>
                    </div>
                `;
            });
            
            statusDiv.innerHTML = statusHTML;
        }
        
        // Test all sections
        function testAllSections() {
            logToConsole("بدء اختبار جميع أقسام لوحة التحكم...");
            updateSectionStatus();
            
            const frame = document.getElementById('adminFrame');
            
            // Wait for frame to load
            setTimeout(() => {
                try {
                    const frameDoc = frame.contentDocument || frame.contentWindow.document;
                    
                    adminSections.forEach((section, index) => {
                        setTimeout(() => {
                            testSection(section, frameDoc);
                        }, index * 500); // Test each section with 500ms delay
                    });
                    
                } catch (error) {
                    logToConsole(`❌ خطأ في الوصول إلى إطار لوحة التحكم: ${error.message}`, "error");
                }
            }, 2000);
        }
        
        // Test individual section
        function testSection(section, frameDoc) {
            logToConsole(`🔍 اختبار قسم: ${section.name}`);
            
            const statusElement = document.getElementById(`status-${section.id}`);
            
            try {
                // Find the section element
                const sectionElement = frameDoc.querySelector(section.selector);
                
                if (sectionElement) {
                    // Check if element is clickable
                    const isClickable = sectionElement.onclick || 
                                      sectionElement.getAttribute('onclick') || 
                                      sectionElement.href ||
                                      sectionElement.getAttribute('data-section') ||
                                      sectionElement.getAttribute('data-target');
                    
                    if (isClickable) {
                        // Test click functionality
                        try {
                            // Simulate click
                            sectionElement.click();
                            
                            // Check if content appears (for non-logout sections)
                            if (section.contentSelector) {
                                setTimeout(() => {
                                    const contentElement = frameDoc.querySelector(section.contentSelector);
                                    if (contentElement && contentElement.style.display !== 'none') {
                                        updateSectionResult(section.id, 'success', 'يعمل بشكل صحيح');
                                        logToConsole(`✅ ${section.name}: يعمل بشكل صحيح`, "success");
                                    } else {
                                        updateSectionResult(section.id, 'warning', 'قابل للنقر لكن المحتوى لا يظهر');
                                        logToConsole(`⚠️ ${section.name}: قابل للنقر لكن المحتوى لا يظهر`, "warning");
                                    }
                                }, 1000);
                            } else {
                                updateSectionResult(section.id, 'success', 'قابل للنقر');
                                logToConsole(`✅ ${section.name}: قابل للنقر`, "success");
                            }
                            
                        } catch (clickError) {
                            updateSectionResult(section.id, 'warning', 'موجود لكن النقر لا يعمل');
                            logToConsole(`⚠️ ${section.name}: موجود لكن النقر لا يعمل`, "warning");
                        }
                    } else {
                        updateSectionResult(section.id, 'warning', 'موجود لكن غير قابل للنقر');
                        logToConsole(`⚠️ ${section.name}: موجود لكن غير قابل للنقر`, "warning");
                    }
                } else {
                    updateSectionResult(section.id, 'error', 'غير موجود');
                    logToConsole(`❌ ${section.name}: غير موجود`, "error");
                }
                
            } catch (error) {
                updateSectionResult(section.id, 'error', `خطأ: ${error.message}`);
                logToConsole(`❌ ${section.name}: خطأ في الاختبار - ${error.message}`, "error");
            }
        }
        
        // Update section result
        function updateSectionResult(sectionId, status, message) {
            const statusElement = document.getElementById(`status-${sectionId}`);
            if (statusElement) {
                statusElement.className = `section-test ${status}`;
                
                const statusIndicator = statusElement.querySelector('.status-indicator');
                const statusText = statusElement.querySelector('span:last-child');
                
                statusIndicator.className = `status-indicator status-${status}`;
                statusText.textContent = message;
            }
        }
        
        // Test individual sections manually
        function testIndividualSections() {
            logToConsole("اختبار الأقسام فردياً...");
            
            adminSections.forEach((section, index) => {
                const button = document.createElement('button');
                button.className = 'btn btn-outline-primary btn-sm me-2 mb-2';
                button.innerHTML = `<i class="${section.icon}"></i> ${section.name}`;
                button.onclick = () => {
                    const frame = document.getElementById('adminFrame');
                    try {
                        const frameDoc = frame.contentDocument || frame.contentWindow.document;
                        testSection(section, frameDoc);
                    } catch (error) {
                        logToConsole(`❌ خطأ في اختبار ${section.name}: ${error.message}`, "error");
                    }
                };
                
                document.querySelector('.test-section').appendChild(button);
            });
        }
        
        // Open admin in new tab
        function openAdminInNewTab() {
            logToConsole("فتح لوحة التحكم في نافذة جديدة...");
            window.open('/admin/', '_blank');
        }
        
        // Reload admin frame
        function reloadAdminFrame() {
            logToConsole("إعادة تحميل إطار لوحة التحكم...");
            const frame = document.getElementById('adminFrame');
            frame.src = frame.src;
        }
        
        // Clear results
        function clearResults() {
            document.getElementById("consoleOutput").innerHTML = "<div class='success-log'>🧹 تم مسح النتائج</div>";
            document.getElementById("sectionsStatus").innerHTML = "";
        }
        
        // Initialize when page loads
        document.addEventListener("DOMContentLoaded", function() {
            logToConsole("تم تحميل صفحة اختبار أقسام لوحة التحكم", "success");
            
            // Auto-test after 3 seconds
            setTimeout(() => {
                logToConsole("بدء الاختبار التلقائي...", "info");
                testAllSections();
            }, 3000);
        });
    </script>
</body>
</html>
