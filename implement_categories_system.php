<?php
/**
 * ISSUE 3: Database Schema Enhancement - Categories System Implementation
 * This script creates a dynamic categories system to replace hardcoded product types
 */

require_once 'php/config.php';

echo "<h1>🗂️ Categories System Implementation</h1>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} .step{background:#f0f8ff;padding:15px;margin:10px 0;border-radius:8px;border-left:4px solid #007bff;}</style>\n";

try {
    $pdo = getPDOConnection();
    echo "<p class='success'>✅ Database connection successful</p>\n";
    
    echo "<div class='step'>\n";
    echo "<h2>🗂️ Step 1: Creating Categories Table</h2>\n";
    
    // Check if categories table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Creating categories table...</p>\n";
        
        $createCategories = "
        CREATE TABLE categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom_ar VARCHAR(100) NOT NULL,
            nom_en VARCHAR(100) NOT NULL,
            description_ar TEXT,
            description_en TEXT,
            icone VARCHAR(50) DEFAULT 'fas fa-box',
            couleur VARCHAR(7) DEFAULT '#007bff',
            ordre_affichage INT DEFAULT 0,
            actif TINYINT(1) DEFAULT 1,
            date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_nom_ar (nom_ar),
            UNIQUE KEY unique_nom_en (nom_en)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createCategories);
        echo "<p class='success'>✅ Categories table created successfully</p>\n";
    } else {
        echo "<p class='success'>✅ Categories table already exists</p>\n";
    }
    
    echo "</div>\n";
    
    echo "<div class='step'>\n";
    echo "<h2>📝 Step 2: Populating Categories with Default Data</h2>\n";
    
    // Check if categories are already populated
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM categories");
    $categoriesCount = $stmt->fetch()['total'];
    
    if ($categoriesCount == 0) {
        echo "<p class='warning'>⚠️ Populating categories with default data...</p>\n";
        
        $defaultCategories = [
            [
                'nom_ar' => 'كتب',
                'nom_en' => 'book',
                'description_ar' => 'كتب تطوير الذات والثقافة العامة',
                'description_en' => 'Self-development and general culture books',
                'icone' => 'fas fa-book',
                'couleur' => '#28a745',
                'ordre_affichage' => 1
            ],
            [
                'nom_ar' => 'حاسوب محمول',
                'nom_en' => 'laptop',
                'description_ar' => 'أجهزة حاسوب محمولة للطلاب والمهنيين',
                'description_en' => 'Laptops for students and professionals',
                'icone' => 'fas fa-laptop',
                'couleur' => '#007bff',
                'ordre_affichage' => 2
            ],
            [
                'nom_ar' => 'حقائب',
                'nom_en' => 'bag',
                'description_ar' => 'حقائب ظهر وحقائب يد عملية وأنيقة',
                'description_en' => 'Practical and elegant backpacks and handbags',
                'icone' => 'fas fa-shopping-bag',
                'couleur' => '#fd7e14',
                'ordre_affichage' => 3
            ],
            [
                'nom_ar' => 'ملابس',
                'nom_en' => 'clothing',
                'description_ar' => 'ملابس رجالية ونسائية عالية الجودة',
                'description_en' => 'High-quality men\'s and women\'s clothing',
                'icone' => 'fas fa-tshirt',
                'couleur' => '#e83e8c',
                'ordre_affichage' => 4
            ],
            [
                'nom_ar' => 'أجهزة منزلية',
                'nom_en' => 'home',
                'description_ar' => 'أجهزة كهربائية ومنزلية متنوعة',
                'description_en' => 'Various electrical and home appliances',
                'icone' => 'fas fa-home',
                'couleur' => '#6f42c1',
                'ordre_affichage' => 5
            ]
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO categories (nom_ar, nom_en, description_ar, description_en, icone, couleur, ordre_affichage, actif) 
            VALUES (?, ?, ?, ?, ?, ?, ?, 1)
        ");
        
        $createdCategories = 0;
        foreach ($defaultCategories as $category) {
            $result = $insertStmt->execute([
                $category['nom_ar'],
                $category['nom_en'],
                $category['description_ar'],
                $category['description_en'],
                $category['icone'],
                $category['couleur'],
                $category['ordre_affichage']
            ]);
            
            if ($result) {
                $categoryId = $pdo->lastInsertId();
                echo "<p class='success'>✅ Category created: {$category['nom_ar']} (ID: $categoryId)</p>\n";
                $createdCategories++;
            } else {
                echo "<p class='error'>❌ Failed to create category: {$category['nom_ar']}</p>\n";
            }
        }
        
        echo "<p class='success'>✅ Created $createdCategories categories</p>\n";
    } else {
        echo "<p class='success'>✅ Categories already populated ($categoriesCount categories)</p>\n";
    }
    
    echo "</div>\n";
    
    echo "<div class='step'>\n";
    echo "<h2>🔗 Step 3: Updating Products Table Structure</h2>\n";
    
    // Check if category_id column exists in produits table
    $stmt = $pdo->query("SHOW COLUMNS FROM produits LIKE 'category_id'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Adding category_id column to produits table...</p>\n";
        
        // Add category_id column
        $pdo->exec("ALTER TABLE produits ADD COLUMN category_id INT NULL AFTER id");
        echo "<p class='success'>✅ category_id column added</p>\n";
        
        // Add foreign key constraint
        $pdo->exec("ALTER TABLE produits ADD CONSTRAINT fk_produits_category FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL");
        echo "<p class='success'>✅ Foreign key constraint added</p>\n";
    } else {
        echo "<p class='success'>✅ category_id column already exists</p>\n";
    }
    
    echo "</div>\n";
    
    echo "<div class='step'>\n";
    echo "<h2>🔄 Step 4: Migrating Existing Products to Categories</h2>\n";
    
    // Update existing products to use category_id instead of type
    $stmt = $pdo->query("SELECT id, type FROM produits WHERE category_id IS NULL");
    $productsToUpdate = $stmt->fetchAll();
    
    if (count($productsToUpdate) > 0) {
        echo "<p class='warning'>⚠️ Updating " . count($productsToUpdate) . " products to use categories...</p>\n";
        
        $updateStmt = $pdo->prepare("UPDATE produits SET category_id = (SELECT id FROM categories WHERE nom_en = ? LIMIT 1) WHERE id = ?");
        
        $updatedCount = 0;
        foreach ($productsToUpdate as $product) {
            $result = $updateStmt->execute([$product['type'], $product['id']]);
            if ($result) {
                echo "<p class='success'>✅ Updated product ID {$product['id']} (type: {$product['type']})</p>\n";
                $updatedCount++;
            } else {
                echo "<p class='error'>❌ Failed to update product ID {$product['id']}</p>\n";
            }
        }
        
        echo "<p class='success'>✅ Updated $updatedCount products</p>\n";
    } else {
        echo "<p class='success'>✅ All products already have categories assigned</p>\n";
    }
    
    echo "</div>\n";
    
    echo "<div class='step'>\n";
    echo "<h2>📊 Step 5: Verification and Summary</h2>\n";
    
    // Show categories summary
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY ordre_affichage");
    $categories = $stmt->fetchAll();
    
    echo "<h3>📋 Categories Created:</h3>\n";
    echo "<table>\n";
    echo "<tr style='background:#f0f0f0;'><th>ID</th><th>Arabic Name</th><th>English Name</th><th>Icon</th><th>Color</th><th>Products Count</th></tr>\n";
    
    foreach ($categories as $category) {
        // Count products in this category
        $countStmt = $pdo->prepare("SELECT COUNT(*) as count FROM produits WHERE category_id = ?");
        $countStmt->execute([$category['id']]);
        $productCount = $countStmt->fetch()['count'];
        
        echo "<tr>";
        echo "<td>{$category['id']}</td>";
        echo "<td>{$category['nom_ar']}</td>";
        echo "<td>{$category['nom_en']}</td>";
        echo "<td><i class='{$category['icone']}' style='color:{$category['couleur']}'></i> {$category['icone']}</td>";
        echo "<td><span style='background:{$category['couleur']};color:white;padding:2px 8px;border-radius:3px;'>{$category['couleur']}</span></td>";
        echo "<td>$productCount</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Show products with categories
    $stmt = $pdo->query("
        SELECT p.id, p.titre, p.type, c.nom_ar as category_name, c.nom_en as category_en 
        FROM produits p 
        LEFT JOIN categories c ON p.category_id = c.id 
        ORDER BY p.id DESC LIMIT 10
    ");
    $products = $stmt->fetchAll();
    
    echo "<h3>📦 Products with Categories (Latest 10):</h3>\n";
    echo "<table>\n";
    echo "<tr style='background:#f0f0f0;'><th>ID</th><th>Product Title</th><th>Old Type</th><th>New Category</th></tr>\n";
    
    foreach ($products as $product) {
        echo "<tr>";
        echo "<td>{$product['id']}</td>";
        echo "<td>" . htmlspecialchars($product['titre']) . "</td>";
        echo "<td>{$product['type']}</td>";
        echo "<td>{$product['category_name']} ({$product['category_en']})</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "</div>\n";
    
    echo "<h2>🎉 Categories System Implementation Complete!</h2>\n";
    echo "<div class='step'>\n";
    echo "<h3>✅ What was accomplished:</h3>\n";
    echo "<ol>\n";
    echo "<li><strong>Categories Table:</strong> Created with Arabic/English names, icons, colors</li>\n";
    echo "<li><strong>Default Categories:</strong> 5 categories populated (books, laptops, bags, clothing, home)</li>\n";
    echo "<li><strong>Products Migration:</strong> Existing products linked to appropriate categories</li>\n";
    echo "<li><strong>Database Relationships:</strong> Foreign key constraints established</li>\n";
    echo "<li><strong>Backward Compatibility:</strong> Old 'type' field preserved during transition</li>\n";
    echo "</ol>\n";
    
    echo "<h3>🚀 Next Steps for Admin Panel Integration:</h3>\n";
    echo "<ol>\n";
    echo "<li>Create Categories Management API (php/api/categories.php)</li>\n";
    echo "<li>Add Categories section to admin panel</li>\n";
    echo "<li>Update product forms to use category dropdown</li>\n";
    echo "<li>Update templates system to work with dynamic categories</li>\n";
    echo "<li>Test the complete workflow</li>\n";
    echo "</ol>\n";
    
    echo "<h3>🔗 Quick Links:</h3>\n";
    echo "<p>\n";
    echo "<a href='admin/' target='_blank' style='background:#007bff;color:white;padding:8px 15px;text-decoration:none;border-radius:4px;margin:5px;'>Admin Panel</a>\n";
    echo "<a href='php/api/products.php' target='_blank' style='background:#28a745;color:white;padding:8px 15px;text-decoration:none;border-radius:4px;margin:5px;'>Products API</a>\n";
    echo "<a href='create_categories_api.php' target='_blank' style='background:#fd7e14;color:white;padding:8px 15px;text-decoration:none;border-radius:4px;margin:5px;'>Create Categories API</a>\n";
    echo "</p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>\n";
    echo "<p class='error'>Stack trace:</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<script>
console.log('🗂️ Categories system implementation completed');
</script>
