<?php
/**
 * Database Health Fix - Address Critical Issues Found in phpMyAdmin Analysis
 * This script fixes data population, engine inconsistencies, and collation mismatches
 */

require_once 'php/config.php';

echo "<h1>🔧 Database Health Fix & Verification</h1>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} .step{background:#f0f8ff;padding:15px;margin:10px 0;border-radius:8px;border-left:4px solid #007bff;}</style>\n";

try {
    $pdo = getPDOConnection();
    echo "<p class='success'>✅ Database connection successful</p>\n";
    
    // STEP 1: Fix Engine Inconsistencies
    echo "<div class='step'>\n";
    echo "<h2>🔧 Step 1: Fixing Database Engine Inconsistencies</h2>\n";
    
    $engineFixes = [
        'commande_items' => 'InnoDB',
        'store_settings' => 'InnoDB'
    ];
    
    foreach ($engineFixes as $table => $targetEngine) {
        try {
            echo "<p>Converting $table to $targetEngine...</p>\n";
            $pdo->exec("ALTER TABLE $table ENGINE = $targetEngine");
            echo "<p class='success'>✅ $table converted to $targetEngine</p>\n";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Failed to convert $table: " . $e->getMessage() . "</p>\n";
        }
    }
    echo "</div>\n";
    
    // STEP 2: Fix Collation Inconsistencies
    echo "<div class='step'>\n";
    echo "<h2>🔧 Step 2: Standardizing Collations to utf8mb4_unicode_ci</h2>\n";
    
    $collationFixes = [
        'commande_items',
        'store_settings',
        'admins',
        'categories'
    ];
    
    foreach ($collationFixes as $table) {
        try {
            echo "<p>Updating collation for $table...</p>\n";
            $pdo->exec("ALTER TABLE $table CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<p class='success'>✅ $table collation updated</p>\n";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Failed to update collation for $table: " . $e->getMessage() . "</p>\n";
        }
    }
    echo "</div>\n";
    
    // STEP 3: Fix Missing Products Data
    echo "<div class='step'>\n";
    echo "<h2>📦 Step 3: Ensuring All 5 Products Are Present</h2>\n";
    
    // Check current products
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM produits WHERE actif = 1");
    $currentCount = $stmt->fetch()['total'];
    echo "<p class='info'>Current active products: $currentCount</p>\n";
    
    // Define all 5 required products
    $requiredProducts = [
        [
            'type' => 'book',
            'titre' => 'فن اللامبالاة - كتاب تطوير الذات',
            'description' => '<p><strong>كتاب فن اللامبالاة</strong> هو دليلك الشامل لتعلم كيفية التركيز على ما يهم حقاً في الحياة.</p><p>يعلمك هذا الكتاب:</p><ul><li>كيفية اختيار معاركك بحكمة</li><li>التخلص من القلق غير المبرر</li><li>بناء الثقة بالنفس</li><li>تحقيق السعادة الحقيقية</li></ul><p>مؤلف: مارك مانسون</p>',
            'prix' => 2500.00,
            'stock' => 50,
            'auteur' => 'مارك مانسون',
            'materiel' => null,
            'actif' => 1
        ],
        [
            'type' => 'laptop',
            'titre' => 'لابتوب Dell Inspiron 15 - للطلاب والمهنيين',
            'description' => '<p><strong>لابتوب Dell Inspiron 15</strong> - الخيار المثالي للطلاب والمهنيين</p><p>المواصفات:</p><ul><li>معالج Intel Core i5 الجيل الحادي عشر</li><li>ذاكرة عشوائية 8GB DDR4</li><li>قرص صلب SSD 256GB</li><li>شاشة 15.6 بوصة Full HD</li><li>كرت رسوميات Intel Iris Xe</li><li>نظام Windows 11</li></ul><p>ضمان سنتين</p>',
            'prix' => 85000.00,
            'stock' => 15,
            'auteur' => null,
            'materiel' => 'Intel Core i5, 8GB RAM, 256GB SSD',
            'actif' => 1
        ],
        [
            'type' => 'bag',
            'titre' => 'حقيبة ظهر رياضية مقاومة للماء',
            'description' => '<p><strong>حقيبة ظهر رياضية عملية</strong> مصممة للاستخدام اليومي والرياضي</p><p>المميزات:</p><ul><li>مقاومة للماء بنسبة 100%</li><li>جيوب متعددة للتنظيم</li><li>حمالات مبطنة مريحة</li><li>جيب خاص للحاسوب المحمول</li><li>تصميم عصري وأنيق</li><li>سعة 30 لتر</li></ul><p>مثالية للمدرسة، الجامعة، والرياضة</p>',
            'prix' => 4500.00,
            'stock' => 30,
            'auteur' => null,
            'materiel' => 'نايلون مقاوم للماء، سحابات معدنية',
            'actif' => 1
        ],
        [
            'type' => 'clothing',
            'titre' => 'قميص قطني كلاسيكي للرجال',
            'description' => '<p><strong>قميص قطني فاخر</strong> مصنوع من أجود أنواع القطن</p><p>المميزات:</p><ul><li>قطن 100% عالي الجودة</li><li>تصميم كلاسيكي أنيق</li><li>مقاسات متنوعة (S-XXL)</li><li>ألوان متعددة</li><li>سهل العناية والغسيل</li><li>مناسب للعمل والمناسبات</li></ul><p>متوفر بالألوان: أبيض، أزرق، رمادي</p>',
            'prix' => 3200.00,
            'stock' => 40,
            'auteur' => null,
            'materiel' => 'قطن 100%',
            'actif' => 1
        ],
        [
            'type' => 'home',
            'titre' => 'خلاط كهربائي متعدد الاستخدامات',
            'description' => '<p><strong>خلاط كهربائي قوي</strong> لجميع احتياجات المطبخ</p><p>المميزات:</p><ul><li>قوة 1000 واط</li><li>5 سرعات مختلفة</li><li>وعاء زجاجي سعة 1.5 لتر</li><li>شفرات من الستانلس ستيل</li><li>قاعدة مانعة للانزلاق</li><li>سهل التنظيف</li></ul><p>مثالي لتحضير العصائر، الشوربات، والصلصات</p><p>ضمان سنة كاملة</p>',
            'prix' => 12000.00,
            'stock' => 25,
            'auteur' => null,
            'materiel' => 'بلاستيك عالي الجودة، شفرات ستانلس ستيل',
            'actif' => 1
        ]
    ];
    
    $createdCount = 0;
    foreach ($requiredProducts as $index => $product) {
        // Check if product already exists
        $checkStmt = $pdo->prepare("SELECT id FROM produits WHERE titre = ?");
        $checkStmt->execute([$product['titre']]);
        
        if ($checkStmt->rowCount() > 0) {
            echo "<p class='info'>ℹ️ Product already exists: " . htmlspecialchars($product['titre']) . "</p>\n";
            continue;
        }
        
        echo "<p>Creating product " . ($index + 1) . ": " . htmlspecialchars($product['titre']) . "</p>\n";
        
        $sql = "INSERT INTO produits (type, titre, description, prix, stock, auteur, materiel, actif, date_creation) 
                VALUES (:type, :titre, :description, :prix, :stock, :auteur, :materiel, :actif, NOW())";
        
        $stmt = $pdo->prepare($sql);
        
        $result = $stmt->execute([
            ':type' => $product['type'],
            ':titre' => $product['titre'],
            ':description' => $product['description'],
            ':prix' => $product['prix'],
            ':stock' => $product['stock'],
            ':auteur' => $product['auteur'],
            ':materiel' => $product['materiel'],
            ':actif' => $product['actif']
        ]);
        
        if ($result) {
            $productId = $pdo->lastInsertId();
            echo "<p class='success'>✅ Product created successfully with ID: $productId</p>\n";
            $createdCount++;
        } else {
            echo "<p class='error'>❌ Failed to create product</p>\n";
        }
    }
    
    // Update categories for products
    echo "<h3>🔗 Linking Products to Categories</h3>\n";
    $categoryMappings = [
        'book' => 'book',
        'laptop' => 'laptop', 
        'bag' => 'bag',
        'clothing' => 'clothing',
        'home' => 'home'
    ];
    
    foreach ($categoryMappings as $productType => $categoryEn) {
        $updateStmt = $pdo->prepare("
            UPDATE produits 
            SET category_id = (SELECT id FROM categories WHERE nom_en = ? LIMIT 1) 
            WHERE type = ? AND category_id IS NULL
        ");
        $updateStmt->execute([$categoryEn, $productType]);
        echo "<p class='success'>✅ Linked {$productType} products to {$categoryEn} category</p>\n";
    }
    
    // Final count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM produits WHERE actif = 1");
    $finalCount = $stmt->fetch()['total'];
    echo "<p class='success'>✅ Total active products now: $finalCount</p>\n";
    echo "</div>\n";
    
    // STEP 4: Create Test Landing Page
    echo "<div class='step'>\n";
    echo "<h2>🎨 Step 4: Creating Test Landing Page</h2>\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM landing_pages");
    $landingPagesCount = $stmt->fetch()['total'];
    
    if ($landingPagesCount == 0) {
        echo "<p class='warning'>⚠️ No landing pages exist, creating test landing page...</p>\n";
        
        // Get first active product
        $stmt = $pdo->query("SELECT id, titre FROM produits WHERE actif = 1 LIMIT 1");
        $product = $stmt->fetch();
        
        if ($product) {
            $testTitle = "صفحة هبوط تجريبية - " . $product['titre'];
            $testUrl = "/landing-page-template.php?id=1";
            
            $insertStmt = $pdo->prepare("
                INSERT INTO landing_pages (titre, produit_id, template_id, contenu_droit, contenu_gauche, lien_url, actif) 
                VALUES (?, ?, ?, ?, ?, ?, 1)
            ");
            
            $result = $insertStmt->execute([
                $testTitle,
                $product['id'],
                'custom',
                '<h3>🎯 مميزات المنتج الرئيسية</h3><p>هذا محتوى تجريبي يعرض أهم مميزات المنتج</p><ul><li>جودة عالية ومضمونة</li><li>سعر مناسب ومنافس</li><li>خدمة عملاء ممتازة</li><li>ضمان شامل</li></ul><h3>💡 لماذا تختار هذا المنتج؟</h3><p>نحن نقدم أفضل المنتجات بأعلى معايير الجودة</p>',
                '<h3>📞 معلومات الاتصال والطلب</h3><p>للطلب والاستفسار تواصل معنا الآن</p><p>📱 الهاتف: 0123456789</p><p>📧 البريد الإلكتروني: <EMAIL></p><p>🌐 الموقع: www.example.com</p><h3>🎁 عروض خاصة</h3><p>احصل على خصم 20% عند الطلب اليوم!</p><p>توصيل مجاني لجميع أنحاء الجزائر</p>'
            ]);
            
            if ($result) {
                $landingPageId = $pdo->lastInsertId();
                
                // Update the URL with actual ID
                $updateStmt = $pdo->prepare("UPDATE landing_pages SET lien_url = ? WHERE id = ?");
                $updateStmt->execute(["/landing-page-template.php?id=$landingPageId", $landingPageId]);
                
                echo "<p class='success'>✅ Test landing page created with ID: $landingPageId</p>\n";
            } else {
                echo "<p class='error'>❌ Failed to create test landing page</p>\n";
            }
        } else {
            echo "<p class='error'>❌ No products available to create test landing page</p>\n";
        }
    } else {
        echo "<p class='success'>✅ Landing pages already exist: $landingPagesCount</p>\n";
    }
    echo "</div>\n";
    
    // STEP 5: Database Health Summary
    echo "<div class='step'>\n";
    echo "<h2>📊 Step 5: Database Health Summary</h2>\n";
    
    // Check final state
    $healthChecks = [
        'Products' => "SELECT COUNT(*) as count FROM produits WHERE actif = 1",
        'Categories' => "SELECT COUNT(*) as count FROM categories WHERE actif = 1", 
        'Landing Pages' => "SELECT COUNT(*) as count FROM landing_pages",
        'Landing Page Images' => "SELECT COUNT(*) as count FROM landing_page_images"
    ];
    
    echo "<table>\n";
    echo "<tr style='background:#f0f0f0;'><th>Component</th><th>Count</th><th>Status</th></tr>\n";
    
    foreach ($healthChecks as $component => $query) {
        $stmt = $pdo->query($query);
        $count = $stmt->fetch()['count'];
        
        $status = '✅ Good';
        if ($component === 'Products' && $count < 5) $status = '⚠️ Incomplete';
        if ($component === 'Categories' && $count < 5) $status = '⚠️ Incomplete';
        
        echo "<tr>";
        echo "<td>$component</td>";
        echo "<td>$count</td>";
        echo "<td>$status</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<h3>🎯 Recommendations:</h3>\n";
    echo "<ul>\n";
    echo "<li>✅ Database engines standardized to InnoDB</li>\n";
    echo "<li>✅ Collations standardized to utf8mb4_unicode_ci</li>\n";
    echo "<li>✅ All required products should now be present</li>\n";
    echo "<li>✅ Test landing page created for testing</li>\n";
    echo "<li>🔄 Run the comprehensive test suite to verify all fixes</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<h2>🎉 Database Health Fix Complete!</h2>\n";
    echo "<p><a href='test_all_issues_resolved.html' class='btn' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>🧪 Run Complete Test Suite</a></p>\n";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Critical Error: " . $e->getMessage() . "</p>\n";
    echo "<p class='error'>Stack trace:</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<script>
console.log('🔧 Database health fix completed');
</script>
