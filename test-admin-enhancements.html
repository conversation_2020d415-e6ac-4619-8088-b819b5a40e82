<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Panel Enhancements</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test Admin Panel Enhancements</h1>
        <p>اختبار التحسينات الجديدة في لوحة التحكم</p>

        <div class="test-section">
            <h3>🎯 PRIORITY 1: Product Management Database Fix</h3>
            <button class="test-button" onclick="testProductsAPI()">
                1. اختبار API المنتجات
            </button>
            <button class="test-button" onclick="testTinyMCEValidation()">
                2. اختبار TinyMCE Validation
            </button>
            <button class="test-button" onclick="testLandingPagesUpdate()">
                3. اختبار تحديث صفحات الهبوط
            </button>
        </div>

        <div class="test-section">
            <h3>📊 ENHANCEMENT 1: Dashboard Statistics</h3>
            <button class="test-button" onclick="testDashboardStats()">
                1. اختبار إحصائيات لوحة المعلومات
            </button>
            <button class="test-button" onclick="testLandingPagesCount()">
                2. اختبار عدد صفحات الهبوط
            </button>
        </div>

        <div class="test-section">
            <h3>🗑️ ENHANCEMENT 2: Bulk Product Deletion</h3>
            <button class="test-button" onclick="testProductsBulkAPI()">
                1. اختبار API الحذف المجمع للمنتجات
            </button>
            <button class="test-button" onclick="testProductsUIElements()">
                2. اختبار عناصر واجهة المنتجات
            </button>
        </div>

        <div class="test-section">
            <h3>📄 ENHANCEMENT 3: Bulk Landing Pages Deletion</h3>
            <button class="test-button" onclick="testLandingPagesBulkAPI()">
                1. اختبار API الحذف المجمع لصفحات الهبوط
            </button>
            <button class="test-button" onclick="testLandingPagesUIElements()">
                2. اختبار عناصر واجهة صفحات الهبوط
            </button>
        </div>

        <div class="test-section">
            <h3>🚀 Comprehensive Tests</h3>
            <button class="test-button" onclick="runAllTests()">
                تشغيل جميع الاختبارات
            </button>
            <button class="test-button" onclick="clearResults()">
                مسح النتائج
            </button>
        </div>

        <div id="test-results"></div>
        <div id="console-output"></div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function logToConsole(message) {
            const output = document.getElementById('console-output');
            output.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        // PRIORITY 1 TESTS
        async function testProductsAPI() {
            addTestResult('🧪 اختبار API المنتجات...', 'info');
            logToConsole('Testing Products API...');
            
            try {
                const response = await fetch('php/api/products.php');
                logToConsole(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const text = await response.text();
                const data = JSON.parse(text);
                
                if (data.success !== false && (Array.isArray(data) || data.products)) {
                    addTestResult('✅ Products API يعمل بشكل صحيح', 'success');
                    logToConsole(`Found products data successfully`);
                } else {
                    addTestResult('⚠️ Products API يعمل لكن لا توجد منتجات', 'warning');
                }
            } catch (error) {
                addTestResult('❌ خطأ في Products API: ' + error.message, 'error');
                logToConsole(`Products API error: ${error.message}`);
            }
        }

        async function testTinyMCEValidation() {
            addTestResult('🧪 اختبار TinyMCE Validation...', 'info');
            logToConsole('Testing TinyMCE validation fix...');
            
            // This test checks if the required attribute was removed
            const hasRequiredAttribute = document.querySelector('#productDescription[required]');
            const hasDataRequired = document.querySelector('#productDescription[data-required]');
            
            if (!hasRequiredAttribute && hasDataRequired) {
                addTestResult('✅ TinyMCE validation fix تم تطبيقه بنجاح', 'success');
                logToConsole('TinyMCE validation fix applied correctly');
            } else if (!hasRequiredAttribute && !hasDataRequired) {
                addTestResult('⚠️ لم يتم العثور على textarea المنتج', 'warning');
                logToConsole('Product description textarea not found in current page');
            } else {
                addTestResult('❌ TinyMCE validation fix لم يتم تطبيقه', 'error');
                logToConsole('TinyMCE validation fix not applied');
            }
        }

        async function testLandingPagesUpdate() {
            addTestResult('🧪 اختبار تحديث صفحات الهبوط...', 'info');
            logToConsole('Testing landing pages update fix...');
            
            try {
                // First get landing pages to test with
                const getResponse = await fetch('php/api/landing-pages.php');
                const getData = await getResponse.json();
                
                if (getData.success && getData.data && getData.data.length > 0) {
                    addTestResult('✅ Landing pages API يعمل بشكل صحيح', 'success');
                    logToConsole(`Found ${getData.data.length} landing pages`);
                } else {
                    addTestResult('⚠️ لا توجد صفحات هبوط للاختبار', 'warning');
                    logToConsole('No landing pages found for testing');
                }
            } catch (error) {
                addTestResult('❌ خطأ في landing pages API: ' + error.message, 'error');
                logToConsole(`Landing pages API error: ${error.message}`);
            }
        }

        // ENHANCEMENT 1 TESTS
        async function testDashboardStats() {
            addTestResult('🧪 اختبار إحصائيات لوحة المعلومات...', 'info');
            logToConsole('Testing dashboard statistics...');
            
            try {
                const response = await fetch('php/api/dashboard-stats.php');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                logToConsole(`Dashboard stats response: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success && result.data) {
                    const hasLandingPagesStats = result.data.landing_pages && 
                                               typeof result.data.landing_pages.total === 'number';
                    
                    if (hasLandingPagesStats) {
                        addTestResult('✅ Dashboard statistics تتضمن صفحات الهبوط', 'success');
                        logToConsole(`Landing pages count: ${result.data.landing_pages.total}`);
                    } else {
                        addTestResult('❌ Dashboard statistics لا تتضمن صفحات الهبوط', 'error');
                    }
                } else {
                    addTestResult('❌ Dashboard statistics API فشل', 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في dashboard stats: ' + error.message, 'error');
                logToConsole(`Dashboard stats error: ${error.message}`);
            }
        }

        async function testLandingPagesCount() {
            addTestResult('🧪 اختبار عدد صفحات الهبوط...', 'info');
            logToConsole('Testing landing pages count in dashboard...');
            
            // Check if the HTML element exists
            const totalLandingPagesElement = document.getElementById('totalLandingPages');
            
            if (totalLandingPagesElement) {
                addTestResult('✅ عنصر عدد صفحات الهبوط موجود في HTML', 'success');
                logToConsole('totalLandingPages element found in HTML');
            } else {
                addTestResult('❌ عنصر عدد صفحات الهبوط غير موجود', 'error');
                logToConsole('totalLandingPages element not found');
            }
        }

        // ENHANCEMENT 2 TESTS
        async function testProductsBulkAPI() {
            addTestResult('🧪 اختبار API الحذف المجمع للمنتجات...', 'info');
            logToConsole('Testing products bulk deletion API...');
            
            try {
                // Test with empty array (should return error)
                const response = await fetch('php/api/products.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'bulk_delete',
                        ids: []
                    })
                });
                
                const result = await response.json();
                logToConsole(`Bulk delete API response: ${JSON.stringify(result)}`);
                
                if (!result.success && result.message.includes('No valid product IDs')) {
                    addTestResult('✅ Products bulk delete API يتعامل مع الأخطاء بشكل صحيح', 'success');
                    logToConsole('Products bulk delete API handles errors correctly');
                } else {
                    addTestResult('⚠️ Products bulk delete API response غير متوقع', 'warning');
                }
            } catch (error) {
                addTestResult('❌ خطأ في products bulk delete API: ' + error.message, 'error');
                logToConsole(`Products bulk delete API error: ${error.message}`);
            }
        }

        function testProductsUIElements() {
            addTestResult('🧪 اختبار عناصر واجهة المنتجات...', 'info');
            logToConsole('Testing products UI elements...');
            
            const selectAllProducts = document.getElementById('selectAllProducts');
            const deleteSelectedProducts = document.getElementById('deleteSelectedProductsBtn');
            const selectedCount = document.getElementById('selectedProductsCount');
            
            let foundElements = 0;
            if (selectAllProducts) foundElements++;
            if (deleteSelectedProducts) foundElements++;
            if (selectedCount) foundElements++;
            
            if (foundElements === 3) {
                addTestResult('✅ جميع عناصر واجهة المنتجات موجودة', 'success');
                logToConsole('All products UI elements found');
            } else {
                addTestResult(`⚠️ تم العثور على ${foundElements}/3 عناصر واجهة المنتجات`, 'warning');
                logToConsole(`Found ${foundElements}/3 products UI elements`);
            }
        }

        // ENHANCEMENT 3 TESTS
        async function testLandingPagesBulkAPI() {
            addTestResult('🧪 اختبار API الحذف المجمع لصفحات الهبوط...', 'info');
            logToConsole('Testing landing pages bulk deletion API...');
            
            try {
                // Test with empty array (should return error)
                const response = await fetch('php/api/landing-pages.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'bulk_delete',
                        ids: []
                    })
                });
                
                const result = await response.json();
                logToConsole(`Landing pages bulk delete API response: ${JSON.stringify(result)}`);
                
                if (!result.success && result.message.includes('No valid landing page IDs')) {
                    addTestResult('✅ Landing pages bulk delete API يتعامل مع الأخطاء بشكل صحيح', 'success');
                    logToConsole('Landing pages bulk delete API handles errors correctly');
                } else {
                    addTestResult('⚠️ Landing pages bulk delete API response غير متوقع', 'warning');
                }
            } catch (error) {
                addTestResult('❌ خطأ في landing pages bulk delete API: ' + error.message, 'error');
                logToConsole(`Landing pages bulk delete API error: ${error.message}`);
            }
        }

        function testLandingPagesUIElements() {
            addTestResult('🧪 اختبار عناصر واجهة صفحات الهبوط...', 'info');
            logToConsole('Testing landing pages UI elements...');
            
            const selectAllLandingPages = document.getElementById('selectAllLandingPages');
            const deleteSelectedLandingPages = document.getElementById('deleteSelectedLandingPagesBtn');
            const selectedCount = document.getElementById('selectedLandingPagesCount');
            const bulkControls = document.getElementById('landingPagesBulkControls');
            
            let foundElements = 0;
            if (selectAllLandingPages) foundElements++;
            if (deleteSelectedLandingPages) foundElements++;
            if (selectedCount) foundElements++;
            if (bulkControls) foundElements++;
            
            if (foundElements === 4) {
                addTestResult('✅ جميع عناصر واجهة صفحات الهبوط موجودة', 'success');
                logToConsole('All landing pages UI elements found');
            } else {
                addTestResult(`⚠️ تم العثور على ${foundElements}/4 عناصر واجهة صفحات الهبوط`, 'warning');
                logToConsole(`Found ${foundElements}/4 landing pages UI elements`);
            }
        }

        async function runAllTests() {
            clearResults();
            addTestResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            logToConsole('Starting comprehensive admin enhancements tests...');
            
            // Priority 1 tests
            await testProductsAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            testTinyMCEValidation();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testLandingPagesUpdate();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Enhancement 1 tests
            await testDashboardStats();
            await new Promise(resolve => setTimeout(resolve, 500));
            testLandingPagesCount();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Enhancement 2 tests
            await testProductsBulkAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            testProductsUIElements();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Enhancement 3 tests
            await testLandingPagesBulkAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            testLandingPagesUIElements();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            addTestResult('🎉 انتهاء جميع الاختبارات', 'info');
            logToConsole('All admin enhancements tests completed');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('console-output').textContent = '';
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('📋 تشغيل اختبار أساسي تلقائياً...', 'info');
                testProductsAPI();
            }, 1000);
        });
    </script>
</body>
</html>
