<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Landing Pages Modal Fix</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Landing Pages Modal Fixes</h1>
        <p>اختبار إصلاحات مشكلة "editors is not iterable" و "failed to load image data"</p>

        <div class="test-section">
            <h2>اختبارات TinyMCE</h2>
            <button class="test-button" onclick="testTinyMCEEditors()">
                1. اختبار TinyMCE Editors Object
            </button>
            <button class="test-button" onclick="testTinyMCEIteration()">
                2. اختبار TinyMCE Iteration
            </button>
            <button class="test-button" onclick="testTinyMCECleanup()">
                3. اختبار TinyMCE Cleanup
            </button>
        </div>

        <div class="test-section">
            <h2>اختبارات الصور</h2>
            <button class="test-button" onclick="testImageLoading()">
                4. اختبار تحميل الصور
            </button>
            <button class="test-button" onclick="testImageErrorHandling()">
                5. اختبار معالجة أخطاء الصور
            </button>
        </div>

        <div class="test-section">
            <h2>اختبار شامل</h2>
            <button class="test-button" onclick="runAllTests()">
                🚀 تشغيل جميع الاختبارات
            </button>
            <button class="test-button" onclick="clearResults()">
                🧹 مسح النتائج
            </button>
        </div>

        <div id="test-results"></div>
        <div id="console-output"></div>
    </div>

    <!-- Include TinyMCE for testing -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function logToConsole(message) {
            const output = document.getElementById('console-output');
            output.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        function testTinyMCEEditors() {
            addTestResult('🧪 اختبار TinyMCE Editors Object...', 'info');
            logToConsole('Testing TinyMCE editors object...');
            
            if (typeof tinymce !== 'undefined') {
                logToConsole('TinyMCE is available');
                
                if (tinymce.editors) {
                    logToConsole(`TinyMCE editors object: ${typeof tinymce.editors}`);
                    logToConsole(`TinyMCE editors keys: ${Object.keys(tinymce.editors)}`);
                    logToConsole(`TinyMCE editors is array: ${Array.isArray(tinymce.editors)}`);
                    
                    addTestResult('✅ TinyMCE editors object متاح', 'success');
                } else {
                    addTestResult('❌ TinyMCE editors object غير متاح', 'error');
                }
            } else {
                addTestResult('❌ TinyMCE غير متاح', 'error');
                logToConsole('TinyMCE is not available');
            }
        }

        function testTinyMCEIteration() {
            addTestResult('🧪 اختبار TinyMCE Iteration...', 'info');
            logToConsole('Testing TinyMCE iteration...');
            
            if (typeof tinymce !== 'undefined' && tinymce.editors) {
                try {
                    // Test the old way (should fail)
                    logToConsole('Testing old iteration method...');
                    try {
                        for (const editor of tinymce.editors) {
                            logToConsole('This should not work');
                        }
                        addTestResult('❌ Old iteration method worked (unexpected)', 'error');
                    } catch (error) {
                        logToConsole(`Old method failed as expected: ${error.message}`);
                        addTestResult('✅ Old iteration method failed as expected', 'success');
                    }

                    // Test the new way (should work)
                    logToConsole('Testing new iteration method...');
                    const editorInstances = Object.values(tinymce.editors);
                    logToConsole(`Found ${editorInstances.length} editor instances`);
                    
                    for (const editor of editorInstances) {
                        logToConsole(`Editor: ${editor ? editor.id || 'unnamed' : 'null'}`);
                    }
                    
                    addTestResult('✅ New iteration method works correctly', 'success');
                } catch (error) {
                    addTestResult('❌ Error in iteration test: ' + error.message, 'error');
                    logToConsole(`Iteration test error: ${error.message}`);
                }
            } else {
                addTestResult('⚠️ TinyMCE not available for iteration test', 'info');
            }
        }

        function testTinyMCECleanup() {
            addTestResult('🧪 اختبار TinyMCE Cleanup...', 'info');
            logToConsole('Testing TinyMCE cleanup simulation...');
            
            if (typeof tinymce !== 'undefined' && tinymce.editors) {
                try {
                    // Simulate the cleanup logic
                    const editors = tinymce.editors;
                    if (!editors || Object.keys(editors).length === 0) {
                        logToConsole('No TinyMCE editors found to clean up');
                        addTestResult('✅ Cleanup logic handles empty editors correctly', 'success');
                        return;
                    }

                    const editorsToRemove = Object.values(editors);
                    logToConsole(`Found ${editorsToRemove.length} editors to clean up`);
                    
                    for (const editor of editorsToRemove) {
                        if (editor) {
                            logToConsole(`Would clean up editor: ${editor.id || 'unnamed'}`);
                        }
                    }
                    
                    addTestResult('✅ Cleanup logic works correctly', 'success');
                } catch (error) {
                    addTestResult('❌ Error in cleanup test: ' + error.message, 'error');
                    logToConsole(`Cleanup test error: ${error.message}`);
                }
            } else {
                addTestResult('⚠️ TinyMCE not available for cleanup test', 'info');
            }
        }

        function testImageLoading() {
            addTestResult('🧪 اختبار تحميل الصور...', 'info');
            logToConsole('Testing image loading...');
            
            // Test with valid image
            const validImageUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwN2JmZiIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk9LPC90ZXh0Pjwvc3ZnPg==';
            
            const testImg = new Image();
            testImg.onload = () => {
                logToConsole('✅ Valid image loaded successfully');
                addTestResult('✅ Valid image loading works', 'success');
            };
            testImg.onerror = () => {
                logToConsole('❌ Valid image failed to load');
                addTestResult('❌ Valid image loading failed', 'error');
            };
            testImg.src = validImageUrl;
        }

        function testImageErrorHandling() {
            addTestResult('🧪 اختبار معالجة أخطاء الصور...', 'info');
            logToConsole('Testing image error handling...');
            
            // Test with invalid image
            const invalidImageUrl = 'https://invalid-url-that-does-not-exist.com/image.jpg';
            
            const testImg = new Image();
            testImg.onload = () => {
                logToConsole('❌ Invalid image loaded (unexpected)');
                addTestResult('❌ Invalid image loaded unexpectedly', 'error');
            };
            testImg.onerror = () => {
                logToConsole('✅ Invalid image failed to load as expected');
                addTestResult('✅ Image error handling works correctly', 'success');
            };
            testImg.src = invalidImageUrl;
        }

        async function runAllTests() {
            clearResults();
            addTestResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            logToConsole('Starting all tests...');
            
            testTinyMCEEditors();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testTinyMCEIteration();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testTinyMCECleanup();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testImageLoading();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testImageErrorHandling();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            addTestResult('🎉 انتهاء جميع الاختبارات', 'info');
            logToConsole('All tests completed');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('console-output').textContent = '';
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('📋 تشغيل اختبار أساسي تلقائياً...', 'info');
                testTinyMCEEditors();
            }, 1000);
        });
    </script>
</body>
</html>
