/* Styles de la page de connexion */
body {
    background: linear-gradient(135deg, #3498db, #2c3e50);
    min-height: 100vh;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-box {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    padding: 40px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    color: #2c3e50;
    font-size: 2rem;
    margin: 0 0 10px 0;
}

.login-header p {
    color: #7f8c8d;
    margin: 0;
}

.login-form {
    margin-top: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #2c3e50;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.form-group input:focus {
    outline: none;
    border-color: #3498db;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-bottom: 15px;
    text-align: center;
    min-height: 20px;
}

.login-button {
    width: 100%;
    padding: 12px;
    background: #3498db;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.login-button:hover {
    background: #2980b9;
}

.back-to-site {
    text-align: center;
    margin-top: 20px;
}

.back-to-site a {
    color: #3498db;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.back-to-site a:hover {
    color: #2980b9;
}

/* Animation de chargement */
.login-button.loading {
    position: relative;
    color: transparent;
}

.login-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #fff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: 10px;
    }

    .login-box {
        padding: 30px 20px;
    }

    .login-header h1 {
        font-size: 1.8rem;
    }
}