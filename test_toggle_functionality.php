<?php
/**
 * Test the toggle-active functionality
 */

require_once 'php/config.php';

echo "=== Testing Toggle Active Functionality ===\n";

// Simulate POST request to toggle-active endpoint
$_SERVER['REQUEST_METHOD'] = 'POST';
$_GET['action'] = 'toggle-active';

// Get a test product
$stmt = $conn->query("SELECT id, titre, actif FROM produits LIMIT 1");
$product = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$product) {
    echo "No products found to test\n";
    exit(1);
}

echo "Testing with product: {$product['titre']} (ID: {$product['id']})\n";
echo "Current status: " . ($product['actif'] ? 'Active' : 'Inactive') . "\n";

// Test 1: Toggle to opposite status
$newStatus = !$product['actif'];
$_POST['productId'] = $product['id'];
$_POST['active'] = $newStatus ? '1' : '0';

echo "\nTest 1: Toggling to " . ($newStatus ? 'Active' : 'Inactive') . "\n";

// Capture output
ob_start();

try {
    // Include the API file to test the function
    include 'php/api/products.php';
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$output = ob_get_clean();
echo "API Response: $output\n";

// Verify the change in database
$stmt = $conn->prepare("SELECT actif FROM produits WHERE id = ?");
$stmt->execute([$product['id']]);
$currentStatus = (bool)$stmt->fetchColumn();

echo "Database status after toggle: " . ($currentStatus ? 'Active' : 'Inactive') . "\n";

if ($currentStatus === $newStatus) {
    echo "✓ Toggle test PASSED\n";
} else {
    echo "✗ Toggle test FAILED\n";
}

// Restore original status
$stmt = $conn->prepare("UPDATE produits SET actif = ? WHERE id = ?");
$stmt->execute([$product['actif'], $product['id']]);
echo "✓ Restored original status\n";

echo "\n=== Test Complete ===\n";
?>
