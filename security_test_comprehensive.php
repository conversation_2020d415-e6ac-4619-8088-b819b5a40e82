<?php
/**
 * Comprehensive Security Testing Suite
 * Tests all implemented security measures for the Mossaab Landing Page
 */

require_once 'php/config.php';
require_once 'php/security.php';
require_once 'php/SecurityHeaders.php';

// Define security check constant
define('SECURITY_CHECK', true);

echo "<h1>🔒 Comprehensive Security Test Suite</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
    .test-section { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
    .success { color: #28a745; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .critical { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .secure { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .test-result { background: #fff; border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f2f2f2; }
    .score-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
    .score-fill { height: 100%; background: linear-gradient(90deg, #dc3545, #ffc107, #28a745); transition: width 0.3s ease; }
</style>\n";

$totalScore = 0;
$maxScore = 1000;
$testResults = [];
$securityIssues = [];

try {
    // Test 1: Input Validation Tests
    echo "<div class='test-section'>\n";
    echo "<h2>🛡️ اختبار 1: التحقق من صحة المدخلات</h2>\n";
    
    $inputScore = 0;
    
    // Test Arabic text validation
    echo "<h3>اختبار التحقق من النصوص العربية:</h3>\n";
    
    $testCases = [
        ['text' => 'مرحبا بكم في موقعنا', 'expected' => true, 'description' => 'نص عربي صحيح'],
        ['text' => 'Hello مرحبا', 'expected' => true, 'description' => 'نص مختلط عربي-إنجليزي'],
        ['text' => '<script>alert("xss")</script>', 'expected' => false, 'description' => 'محاولة XSS'],
        ['text' => '', 'expected' => false, 'description' => 'نص فارغ'],
        ['text' => str_repeat('أ', 1001), 'expected' => false, 'description' => 'نص طويل جداً']
    ];
    
    foreach ($testCases as $test) {
        $result = validateArabicText($test['text']);
        $passed = ($result['valid'] === $test['expected']);
        
        echo "<div class='test-result'>\n";
        echo "<strong>{$test['description']}:</strong> ";
        echo $passed ? '<span class="success">✅ نجح</span>' : '<span class="error">❌ فشل</span>';
        if (!$passed) {
            echo " - متوقع: " . ($test['expected'] ? 'صحيح' : 'خطأ') . ", الحصول على: " . ($result['valid'] ? 'صحيح' : 'خطأ');
        }
        echo "</div>\n";
        
        if ($passed) $inputScore += 10;
    }
    
    // Test email validation
    echo "<h3>اختبار التحقق من البريد الإلكتروني:</h3>\n";
    
    $emailTests = [
        ['email' => '<EMAIL>', 'expected' => true, 'description' => 'بريد إلكتروني صحيح'],
        ['email' => 'مستخدم@موقع.كوم', 'expected' => true, 'description' => 'بريد إلكتروني بالعربية'],
        ['email' => 'invalid-email', 'expected' => false, 'description' => 'بريد إلكتروني غير صحيح'],
        ['email' => '', 'expected' => false, 'description' => 'بريد إلكتروني فارغ']
    ];
    
    foreach ($emailTests as $test) {
        $result = validateEmail($test['email']);
        $passed = ($result['valid'] === $test['expected']);
        
        echo "<div class='test-result'>\n";
        echo "<strong>{$test['description']}:</strong> ";
        echo $passed ? '<span class="success">✅ نجح</span>' : '<span class="error">❌ فشل</span>';
        echo "</div>\n";
        
        if ($passed) $inputScore += 10;
    }
    
    // Test price validation
    echo "<h3>اختبار التحقق من الأسعار:</h3>\n";
    
    $priceTests = [
        ['price' => '100.50', 'expected' => true, 'description' => 'سعر صحيح'],
        ['price' => '0', 'expected' => false, 'description' => 'سعر صفر'],
        ['price' => '-50', 'expected' => false, 'description' => 'سعر سالب'],
        ['price' => 'abc', 'expected' => false, 'description' => 'سعر غير رقمي']
    ];
    
    foreach ($priceTests as $test) {
        $result = validatePrice($test['price']);
        $passed = ($result['valid'] === $test['expected']);
        
        echo "<div class='test-result'>\n";
        echo "<strong>{$test['description']}:</strong> ";
        echo $passed ? '<span class="success">✅ نجح</span>' : '<span class="error">❌ فشل</span>';
        echo "</div>\n";
        
        if ($passed) $inputScore += 10;
    }
    
    $testResults['input_validation'] = $inputScore;
    $totalScore += $inputScore;
    
    echo "<p class='secure'><strong>نتيجة التحقق من المدخلات: {$inputScore}/120</strong></p>\n";
    echo "</div>\n";
    
    // Test 2: File Upload Security
    echo "<div class='test-section'>\n";
    echo "<h2>📁 اختبار 2: أمان رفع الملفات</h2>\n";
    
    $fileScore = 0;
    
    // Test file validation function exists
    if (function_exists('validateFileUpload')) {
        echo "<div class='secure'>✅ دالة التحقق من الملفات موجودة</div>\n";
        $fileScore += 50;
        
        // Test malicious file detection
        $testFile = [
            'tmp_name' => __FILE__, // Use current file for testing
            'name' => 'test.php',
            'size' => filesize(__FILE__),
            'error' => UPLOAD_ERR_OK,
            'type' => 'text/plain'
        ];
        
        // This should fail because it's a PHP file
        $result = validateFileUpload($testFile, ['image/jpeg', 'image/png']);
        if (!$result['valid']) {
            echo "<div class='secure'>✅ تم اكتشاف ملف PHP ضار بنجاح</div>\n";
            $fileScore += 50;
        } else {
            echo "<div class='critical'>❌ لم يتم اكتشاف ملف PHP ضار</div>\n";
            $securityIssues[] = 'فشل في اكتشاف الملفات الضارة';
        }
    } else {
        echo "<div class='critical'>❌ دالة التحقق من الملفات غير موجودة</div>\n";
        $securityIssues[] = 'دالة التحقق من الملفات مفقودة';
    }
    
    $testResults['file_upload'] = $fileScore;
    $totalScore += $fileScore;
    
    echo "<p class='secure'><strong>نتيجة أمان رفع الملفات: {$fileScore}/100</strong></p>\n";
    echo "</div>\n";
    
    // Test 3: XSS Protection
    echo "<div class='test-section'>\n";
    echo "<h2>🛡️ اختبار 3: الحماية من XSS</h2>\n";
    
    $xssScore = 0;
    
    // Test input sanitization
    $xssTests = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert(1)',
        '<iframe src="javascript:alert(1)"></iframe>',
        '"><script>alert(1)</script>'
    ];
    
    echo "<h3>اختبار تنظيف المدخلات:</h3>\n";
    
    foreach ($xssTests as $maliciousInput) {
        $sanitized = sanitizeInput($maliciousInput);
        $safe = (strpos($sanitized, '<script') === false && 
                strpos($sanitized, 'javascript:') === false && 
                strpos($sanitized, 'onerror') === false);
        
        echo "<div class='test-result'>\n";
        echo "<strong>المدخل:</strong> " . htmlspecialchars($maliciousInput) . "<br>\n";
        echo "<strong>بعد التنظيف:</strong> " . htmlspecialchars($sanitized) . "<br>\n";
        echo "<strong>النتيجة:</strong> " . ($safe ? '<span class="success">✅ آمن</span>' : '<span class="error">❌ غير آمن</span>');
        echo "</div>\n";
        
        if ($safe) $xssScore += 20;
    }
    
    // Test security headers
    echo "<h3>اختبار رؤوس الأمان:</h3>\n";
    
    if (class_exists('SecurityHeaders')) {
        echo "<div class='secure'>✅ فئة رؤوس الأمان موجودة</div>\n";
        $xssScore += 50;
        
        // Test JSON sanitization
        $testData = ['message' => '<script>alert("xss")</script>', 'name' => 'اختبار'];
        $sanitized = SecurityHeaders::sanitizeJsonOutput($testData);
        
        if (strpos($sanitized['message'], '<script') === false) {
            echo "<div class='secure'>✅ تنظيف JSON يعمل بشكل صحيح</div>\n";
            $xssScore += 50;
        } else {
            echo "<div class='critical'>❌ تنظيف JSON لا يعمل</div>\n";
            $securityIssues[] = 'تنظيف JSON غير فعال';
        }
    } else {
        echo "<div class='critical'>❌ فئة رؤوس الأمان غير موجودة</div>\n";
        $securityIssues[] = 'فئة رؤوس الأمان مفقودة';
    }
    
    $testResults['xss_protection'] = $xssScore;
    $totalScore += $xssScore;
    
    echo "<p class='secure'><strong>نتيجة الحماية من XSS: {$xssScore}/200</strong></p>\n";
    echo "</div>\n";
    
    // Test 4: SQL Injection Protection
    echo "<div class='test-section'>\n";
    echo "<h2>🗄️ اختبار 4: الحماية من SQL Injection</h2>\n";
    
    $sqlScore = 0;
    
    // Test database connection uses PDO
    if ($conn instanceof PDO) {
        echo "<div class='secure'>✅ يتم استخدام PDO للاتصال بقاعدة البيانات</div>\n";
        $sqlScore += 100;
        
        // Test prepared statements
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM produits WHERE id = ?");
            $stmt->execute([1]);
            echo "<div class='secure'>✅ البيانات المحضرة تعمل بشكل صحيح</div>\n";
            $sqlScore += 100;
        } catch (Exception $e) {
            echo "<div class='warning'>⚠️ خطأ في اختبار البيانات المحضرة: " . $e->getMessage() . "</div>\n";
        }
        
        // Check PDO options
        $options = $conn->getAttribute(PDO::ATTR_EMULATE_PREPARES);
        if ($options === false) {
            echo "<div class='secure'>✅ تم تعطيل محاكاة البيانات المحضرة</div>\n";
            $sqlScore += 50;
        } else {
            echo "<div class='warning'>⚠️ محاكاة البيانات المحضرة مفعلة</div>\n";
        }
        
    } else {
        echo "<div class='critical'>❌ لا يتم استخدام PDO</div>\n";
        $securityIssues[] = 'عدم استخدام PDO للاتصال بقاعدة البيانات';
    }
    
    $testResults['sql_injection'] = $sqlScore;
    $totalScore += $sqlScore;
    
    echo "<p class='secure'><strong>نتيجة الحماية من SQL Injection: {$sqlScore}/250</strong></p>\n";
    echo "</div>\n";
    
    // Test 5: Session Security
    echo "<div class='test-section'>\n";
    echo "<h2>🔐 اختبار 5: أمان الجلسات</h2>\n";
    
    $sessionScore = 0;
    
    // Test session configuration
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    $sessionConfig = [
        'cookie_httponly' => ini_get('session.cookie_httponly'),
        'use_only_cookies' => ini_get('session.use_only_cookies'),
        'cookie_secure' => ini_get('session.cookie_secure')
    ];
    
    echo "<table>\n";
    echo "<tr><th>إعداد الجلسة</th><th>القيمة</th><th>الحالة</th></tr>\n";
    
    foreach ($sessionConfig as $setting => $value) {
        $isSecure = ($value == 1);
        $status = $isSecure ? '<span class="success">آمن</span>' : '<span class="warning">غير آمن</span>';
        
        echo "<tr><td>$setting</td><td>$value</td><td>$status</td></tr>\n";
        
        if ($isSecure) $sessionScore += 50;
    }
    echo "</table>\n";
    
    // Test CSRF protection
    if (function_exists('generateCSRFToken')) {
        echo "<div class='secure'>✅ حماية CSRF متوفرة</div>\n";
        $sessionScore += 100;
        
        $token1 = generateCSRFToken();
        $token2 = generateCSRFToken();
        
        if ($token1 === $token2) {
            echo "<div class='secure'>✅ رموز CSRF متسقة</div>\n";
            $sessionScore += 50;
        }
    } else {
        echo "<div class='critical'>❌ حماية CSRF غير متوفرة</div>\n";
        $securityIssues[] = 'حماية CSRF مفقودة';
    }
    
    $testResults['session_security'] = $sessionScore;
    $totalScore += $sessionScore;
    
    echo "<p class='secure'><strong>نتيجة أمان الجلسات: {$sessionScore}/250</strong></p>\n";
    echo "</div>\n";
    
    // Test 6: Rate Limiting
    echo "<div class='test-section'>\n";
    echo "<h2>⏱️ اختبار 6: تحديد المعدل</h2>\n";
    
    $rateScore = 0;
    
    if (function_exists('checkRateLimit')) {
        echo "<div class='secure'>✅ دالة تحديد المعدل متوفرة</div>\n";
        $rateScore += 40;
        
        // Test rate limiting
        $allowed1 = checkRateLimit('test_action', 2, 60);
        $allowed2 = checkRateLimit('test_action', 2, 60);
        $allowed3 = checkRateLimit('test_action', 2, 60);
        
        if ($allowed1 && $allowed2 && !$allowed3) {
            echo "<div class='secure'>✅ تحديد المعدل يعمل بشكل صحيح</div>\n";
            $rateScore += 40;
        } else {
            echo "<div class='warning'>⚠️ تحديد المعدل قد لا يعمل بشكل صحيح</div>\n";
        }
    } else {
        echo "<div class='critical'>❌ دالة تحديد المعدل غير متوفرة</div>\n";
        $securityIssues[] = 'تحديد المعدل غير مطبق';
    }
    
    $testResults['rate_limiting'] = $rateScore;
    $totalScore += $rateScore;
    
    echo "<p class='secure'><strong>نتيجة تحديد المعدل: {$rateScore}/80</strong></p>\n";
    echo "</div>\n";
    
    // Overall Security Assessment
    echo "<div class='test-section' style='border-left-color: #28a745;'>\n";
    echo "<h2>📊 التقييم الأمني الشامل</h2>\n";
    
    $securityPercentage = ($totalScore / $maxScore) * 100;
    
    echo "<h3>النتيجة الأمنية: {$totalScore}/{$maxScore} (" . number_format($securityPercentage, 1) . "%)</h3>\n";
    
    echo "<div class='score-bar'>\n";
    echo "<div class='score-fill' style='width: {$securityPercentage}%;'></div>\n";
    echo "</div>\n";
    
    if ($securityPercentage >= 90) {
        echo "<div class='secure'>🎉 ممتاز! الموقع محمي بشكل ممتاز</div>\n";
    } elseif ($securityPercentage >= 75) {
        echo "<div class='secure'>👍 جيد! الأمان في مستوى جيد مع بعض التحسينات المطلوبة</div>\n";
    } elseif ($securityPercentage >= 50) {
        echo "<div class='warning'>⚠️ متوسط! يحتاج تحسينات أمنية مهمة</div>\n";
    } else {
        echo "<div class='critical'>🚨 ضعيف! يحتاج تحسينات أمنية عاجلة!</div>\n";
    }
    
    // Security Issues Summary
    if (!empty($securityIssues)) {
        echo "<h3>🚨 المشاكل الأمنية المكتشفة:</h3>\n";
        echo "<ul>\n";
        foreach ($securityIssues as $issue) {
            echo "<li class='error'>$issue</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<h3>✅ لم يتم اكتشاف مشاكل أمنية حرجة</h3>\n";
    }
    
    // Detailed Results
    echo "<h3>النتائج التفصيلية:</h3>\n";
    echo "<table>\n";
    echo "<tr><th>المكون</th><th>النتيجة</th><th>الحالة</th></tr>\n";
    
    foreach ($testResults as $component => $score) {
        $componentName = [
            'input_validation' => 'التحقق من المدخلات',
            'file_upload' => 'أمان رفع الملفات',
            'xss_protection' => 'الحماية من XSS',
            'sql_injection' => 'الحماية من SQL Injection',
            'session_security' => 'أمان الجلسات',
            'rate_limiting' => 'تحديد المعدل'
        ][$component] ?? $component;
        
        $maxScores = [
            'input_validation' => 120,
            'file_upload' => 100,
            'xss_protection' => 200,
            'sql_injection' => 250,
            'session_security' => 250,
            'rate_limiting' => 80
        ];
        
        $maxScore = $maxScores[$component] ?? 100;
        $percentage = ($score / $maxScore) * 100;
        
        $status = $percentage >= 80 ? '<span class="success">ممتاز</span>' : 
                 ($percentage >= 60 ? '<span class="warning">جيد</span>' : '<span class="error">يحتاج تحسين</span>');
        
        echo "<tr><td>$componentName</td><td>$score/$maxScore (" . number_format($percentage, 1) . "%)</td><td>$status</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='test-section' style='border-left-color: #dc3545;'>\n";
    echo "<h2>❌ خطأ أثناء اختبار الأمان</h2>\n";
    echo "<p class='error'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔒 اكتمل اختبار الأمان الشامل');
    console.log('النتيجة الأمنية: <?php echo number_format($securityPercentage ?? 0, 1); ?>%');
});
</script>
