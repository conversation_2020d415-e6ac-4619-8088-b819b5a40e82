<?php

/**
 * Stores Management API
 * Handles all store-related operations for admin panel
 */

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    require_once __DIR__ . '/../config.php';
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Configuration error: ' . $e->getMessage(),
        'debug' => 'Failed to load config.php'
    ]);
    exit();
}

// Security check
if (!defined('SECURITY_CHECK')) {
    define('SECURITY_CHECK', true);
}

try {
    $pdo = getPDOConnection();

    // Test database connection
    $pdo->query("SELECT 1");
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection error: ' . $e->getMessage(),
        'debug' => 'Failed to connect to database'
    ]);
    exit();
}

try {
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            handleGetStores($pdo);
            break;
        case 'POST':
            handleCreateStore($pdo);
            break;
        case 'PUT':
            handleUpdateStore($pdo);
            break;
        case 'DELETE':
            handleDeleteStore($pdo);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    error_log("Stores API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error' => $e->getMessage()
    ]);
}

/**
 * Get all stores with owner information
 */
function handleGetStores($pdo)
{
    try {
        // First, check if stores table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'stores'");
        if (!$stmt->fetch()) {
            echo json_encode([
                'success' => false,
                'message' => 'جدول المتاجر غير موجود. يرجى تشغيل ترحيل قاعدة البيانات.',
                'debug' => 'stores table does not exist',
                'stores' => [],
                'total' => 0
            ]);
            return;
        }

        // Check if users table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if (!$stmt->fetch()) {
            echo json_encode([
                'success' => false,
                'message' => 'جدول المستخدمين غير موجود.',
                'debug' => 'users table does not exist',
                'stores' => [],
                'total' => 0
            ]);
            return;
        }

        $sql = "
            SELECT
                s.*,
                CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as owner_name,
                u.email as owner_email,
                u.phone as owner_phone,
                u.created_at as user_created_at
            FROM stores s
            LEFT JOIN users u ON s.user_id = u.id
            ORDER BY s.created_at DESC
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process stores data
        foreach ($stores as &$store) {
            // Parse settings JSON safely
            if ($store['settings']) {
                $settings = json_decode($store['settings'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $store['settings'] = $settings;
                } else {
                    $store['settings'] = [];
                    error_log("JSON decode error for store {$store['id']}: " . json_last_error_msg());
                }
            } else {
                $store['settings'] = [];
            }

            // Format dates safely
            $store['created_at_formatted'] = $store['created_at'] ? date('Y-m-d H:i', strtotime($store['created_at'])) : '';
            $store['updated_at_formatted'] = $store['updated_at'] ? date('Y-m-d H:i', strtotime($store['updated_at'])) : '';

            // Ensure numeric values
            $store['total_products'] = (int)($store['total_products'] ?? 0);
            $store['total_orders'] = (int)($store['total_orders'] ?? 0);
            $store['total_revenue'] = (float)($store['total_revenue'] ?? 0);

            // Ensure required fields
            $store['owner_name'] = $store['owner_name'] ?? 'غير محدد';
            $store['owner_email'] = $store['owner_email'] ?? '';
            $store['status'] = $store['status'] ?? 'pending';
        }

        echo json_encode([
            'success' => true,
            'stores' => $stores,
            'total' => count($stores),
            'message' => 'تم تحميل المتاجر بنجاح',
            'debug' => 'Query executed successfully'
        ]);
    } catch (Exception $e) {
        error_log("handleGetStores error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في تحميل المتاجر: ' . $e->getMessage(),
            'debug' => $e->getTraceAsString(),
            'stores' => [],
            'total' => 0
        ]);
    }
}

/**
 * Create new store
 */
function handleCreateStore($pdo)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // Validate required fields
        $requiredFields = ['user_id', 'store_name', 'store_slug'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                throw new Exception("الحقل {$field} مطلوب");
            }
        }

        // Check if slug is unique
        $stmt = $pdo->prepare("SELECT id FROM stores WHERE store_slug = ?");
        $stmt->execute([$input['store_slug']]);
        if ($stmt->fetch()) {
            throw new Exception('رابط المتجر مستخدم بالفعل');
        }

        // Check if user exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
        $stmt->execute([$input['user_id']]);
        if (!$stmt->fetch()) {
            throw new Exception('المستخدم غير موجود');
        }

        // Insert new store
        $sql = "
            INSERT INTO stores (
                user_id, store_name, store_slug, description, logo_url,
                status, domain, theme, settings, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ";

        $settings = isset($input['settings']) ? json_encode($input['settings']) : json_encode([
            'currency' => 'DZD',
            'language' => 'ar',
            'timezone' => 'Africa/Algiers'
        ]);

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $input['user_id'],
            $input['store_name'],
            $input['store_slug'],
            $input['description'] ?? '',
            $input['logo_url'] ?? null,
            $input['status'] ?? 'pending',
            $input['domain'] ?? null,
            $input['theme'] ?? 'default',
            $settings
        ]);

        $storeId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'store_id' => $storeId,
            'message' => 'تم إنشاء المتجر بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('خطأ في إنشاء المتجر: ' . $e->getMessage());
    }
}

/**
 * Update store information
 */
function handleUpdateStore($pdo)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (empty($input['id'])) {
            throw new Exception('معرف المتجر مطلوب');
        }

        // Check if store exists
        $stmt = $pdo->prepare("SELECT id FROM stores WHERE id = ?");
        $stmt->execute([$input['id']]);
        if (!$stmt->fetch()) {
            throw new Exception('المتجر غير موجود');
        }

        // Build update query dynamically
        $updateFields = [];
        $params = [];

        $allowedFields = [
            'store_name',
            'store_slug',
            'description',
            'logo_url',
            'status',
            'domain',
            'theme',
            'settings'
        ];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "{$field} = ?";
                if ($field === 'settings' && is_array($input[$field])) {
                    $params[] = json_encode($input[$field]);
                } else {
                    $params[] = $input[$field];
                }
            }
        }

        if (empty($updateFields)) {
            throw new Exception('لا توجد حقول للتحديث');
        }

        // Add updated_at
        $updateFields[] = "updated_at = NOW()";
        $params[] = $input['id'];

        $sql = "UPDATE stores SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث المتجر بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('خطأ في تحديث المتجر: ' . $e->getMessage());
    }
}

/**
 * Delete store
 */
function handleDeleteStore($pdo)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (empty($input['id'])) {
            throw new Exception('معرف المتجر مطلوب');
        }

        // Check if store exists
        $stmt = $pdo->prepare("SELECT store_name FROM stores WHERE id = ?");
        $stmt->execute([$input['id']]);
        $store = $stmt->fetch();

        if (!$store) {
            throw new Exception('المتجر غير موجود');
        }

        // Delete store
        $stmt = $pdo->prepare("DELETE FROM stores WHERE id = ?");
        $stmt->execute([$input['id']]);

        echo json_encode([
            'success' => true,
            'message' => "تم حذف متجر '{$store['store_name']}' بنجاح"
        ]);
    } catch (Exception $e) {
        throw new Exception('خطأ في حذف المتجر: ' . $e->getMessage());
    }
}

/**
 * Get store statistics
 */
function getStoreStatistics($pdo, $storeId)
{
    try {
        // This would typically query products, orders, etc. tables
        // For now, return the stored statistics
        $stmt = $pdo->prepare("
            SELECT total_products, total_orders, total_revenue
            FROM stores
            WHERE id = ?
        ");
        $stmt->execute([$storeId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [
            'total_products' => 0,
            'total_orders' => 0,
            'total_revenue' => 0.00
        ];
    }
}

/**
 * Update store statistics
 */
function updateStoreStatistics($pdo, $storeId)
{
    try {
        // This would typically calculate from related tables
        // For now, just update the timestamp
        $stmt = $pdo->prepare("UPDATE stores SET updated_at = NOW() WHERE id = ?");
        $stmt->execute([$storeId]);

        return true;
    } catch (Exception $e) {
        return false;
    }
}
