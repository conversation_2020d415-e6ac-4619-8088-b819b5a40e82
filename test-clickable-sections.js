/**
 * Test Clickable Sections - Script de test pour vérifier que toutes les sections sont cliquables
 * À exécuter dans la console du navigateur sur la page admin
 */

(function() {
    'use strict';
    
    console.log('🧪 Démarrage du test des sections cliquables...');
    
    // Définition des sections à tester
    const sectionsToTest = [
        {
            name: 'الرئيسية (Dashboard)',
            selectors: [
                '[data-section="dashboard"]',
                '.nav-item[onclick*="dashboard"]',
                '.sidebar-item[onclick*="dashboard"]',
                'a[href*="dashboard"]',
                '[data-target="dashboardContent"]'
            ],
            contentSelector: '#dashboardContent'
        },
        {
            name: 'إدارة المنتجات (Products)',
            selectors: [
                '[data-section="products"]',
                '.nav-item[onclick*="products"]',
                '.sidebar-item[onclick*="products"]',
                'a[href*="products"]',
                '[data-target="productsContent"]'
            ],
            contentSelector: '#productsContent'
        },
        {
            name: 'الطلبات (Orders)',
            selectors: [
                '[data-section="orders"]',
                '.nav-item[onclick*="orders"]',
                '.sidebar-item[onclick*="orders"]',
                'a[href*="orders"]',
                '[data-target="ordersContent"]'
            ],
            contentSelector: '#ordersContent'
        },
        {
            name: 'صفحات هبوط (Landing Pages)',
            selectors: [
                '[data-section="landing-pages"]',
                '.nav-item[onclick*="landing"]',
                '.sidebar-item[onclick*="landing"]',
                'a[href*="landing"]',
                '[data-target="landingPagesContent"]'
            ],
            contentSelector: '#landingPagesContent'
        },
        {
            name: 'التقارير والإحصائيات (Reports)',
            selectors: [
                '[data-section="reports"]',
                '.nav-item[onclick*="reports"]',
                '.sidebar-item[onclick*="reports"]',
                'a[href*="reports"]',
                '[data-target="reportsContent"]'
            ],
            contentSelector: '#reportsContent'
        },
        {
            name: 'إعدادات النظام (Settings)',
            selectors: [
                '[data-section="settings"]',
                '.nav-item[onclick*="settings"]',
                '.sidebar-item[onclick*="settings"]',
                'a[href*="settings"]',
                '[data-target="settingsContent"]'
            ],
            contentSelector: '#settingsContent'
        },
        {
            name: 'تسجيل الخروج (Logout)',
            selectors: [
                '[data-action="logout"]',
                '.logout-btn',
                '.nav-item[onclick*="logout"]',
                '.sidebar-item[onclick*="logout"]',
                'a[href*="logout"]'
            ],
            contentSelector: null // Pas de contenu à vérifier pour logout
        }
    ];
    
    let testResults = [];
    let totalTests = 0;
    let passedTests = 0;
    
    // Fonction pour tester une section
    function testSection(section) {
        console.log(`\n🔍 Test de la section: ${section.name}`);
        
        let sectionElement = null;
        let foundSelector = null;
        
        // Chercher l'élément de la section
        for (let selector of section.selectors) {
            sectionElement = document.querySelector(selector);
            if (sectionElement) {
                foundSelector = selector;
                break;
            }
        }
        
        totalTests++;
        
        if (!sectionElement) {
            console.log(`❌ ${section.name}: Élément non trouvé`);
            testResults.push({
                section: section.name,
                status: 'not-found',
                message: 'Élément non trouvé avec les sélecteurs fournis'
            });
            return;
        }
        
        console.log(`✅ ${section.name}: Élément trouvé avec le sélecteur "${foundSelector}"`);
        
        // Vérifier si l'élément est cliquable
        const isClickable = sectionElement.onclick || 
                           sectionElement.getAttribute('onclick') || 
                           sectionElement.href ||
                           sectionElement.getAttribute('data-section') ||
                           sectionElement.getAttribute('data-target') ||
                           sectionElement.tagName.toLowerCase() === 'button' ||
                           sectionElement.tagName.toLowerCase() === 'a';
        
        if (!isClickable) {
            console.log(`⚠️ ${section.name}: Élément trouvé mais ne semble pas cliquable`);
            testResults.push({
                section: section.name,
                status: 'not-clickable',
                message: 'Élément trouvé mais ne semble pas cliquable'
            });
            return;
        }
        
        // Tester le clic
        try {
            console.log(`🖱️ ${section.name}: Test du clic...`);
            
            // Simuler le clic
            sectionElement.click();
            
            // Attendre un peu pour que le contenu se charge
            setTimeout(() => {
                if (section.contentSelector) {
                    const contentElement = document.querySelector(section.contentSelector);
                    if (contentElement && window.getComputedStyle(contentElement).display !== 'none') {
                        console.log(`✅ ${section.name}: Clic réussi et contenu affiché`);
                        testResults.push({
                            section: section.name,
                            status: 'success',
                            message: 'Clic réussi et contenu affiché'
                        });
                        passedTests++;
                    } else {
                        console.log(`⚠️ ${section.name}: Clic réussi mais contenu non affiché`);
                        testResults.push({
                            section: section.name,
                            status: 'partial',
                            message: 'Clic réussi mais contenu non affiché'
                        });
                    }
                } else {
                    console.log(`✅ ${section.name}: Clic réussi (pas de contenu à vérifier)`);
                    testResults.push({
                        section: section.name,
                        status: 'success',
                        message: 'Clic réussi (pas de contenu à vérifier)'
                    });
                    passedTests++;
                }
            }, 1000);
            
        } catch (error) {
            console.log(`❌ ${section.name}: Erreur lors du clic - ${error.message}`);
            testResults.push({
                section: section.name,
                status: 'error',
                message: `Erreur lors du clic: ${error.message}`
            });
        }
    }
    
    // Fonction pour afficher les résultats finaux
    function showFinalResults() {
        console.log('\n📊 RÉSULTATS FINAUX DU TEST DES SECTIONS');
        console.log('==========================================');
        
        const successRate = (passedTests / totalTests) * 100;
        console.log(`Taux de réussite: ${successRate.toFixed(1)}% (${passedTests}/${totalTests})`);
        
        console.log('\n📋 Détail des résultats:');
        testResults.forEach(result => {
            const icon = result.status === 'success' ? '✅' : 
                        result.status === 'partial' ? '⚠️' : 
                        result.status === 'not-clickable' ? '🔒' : 
                        result.status === 'not-found' ? '❌' : '💥';
            
            console.log(`${icon} ${result.section}: ${result.message}`);
        });
        
        if (successRate >= 85) {
            console.log('\n🎉 EXCELLENT! La plupart des sections fonctionnent correctement.');
        } else if (successRate >= 60) {
            console.log('\n⚠️ BIEN! Quelques sections nécessitent des améliorations.');
        } else {
            console.log('\n❌ ATTENTION! Plusieurs sections ont des problèmes.');
        }
        
        console.log('\n💡 Pour tester manuellement:');
        console.log('1. Cliquez sur chaque section dans la barre latérale');
        console.log('2. Vérifiez que le contenu approprié s\'affiche');
        console.log('3. Vérifiez qu\'il n\'y a pas d\'erreurs dans la console');
    }
    
    // Exécuter les tests
    console.log('🚀 Début des tests des sections...\n');
    
    sectionsToTest.forEach((section, index) => {
        setTimeout(() => {
            testSection(section);
            
            // Afficher les résultats finaux après le dernier test
            if (index === sectionsToTest.length - 1) {
                setTimeout(showFinalResults, 2000);
            }
        }, index * 1500); // Délai de 1.5 secondes entre chaque test
    });
    
    // Fonction utilitaire pour tester une section spécifique
    window.testSpecificSection = function(sectionName) {
        const section = sectionsToTest.find(s => s.name.includes(sectionName));
        if (section) {
            testSection(section);
        } else {
            console.log(`❌ Section "${sectionName}" non trouvée dans la liste des tests`);
        }
    };
    
    // Fonction utilitaire pour relancer tous les tests
    window.retestAllSections = function() {
        testResults = [];
        totalTests = 0;
        passedTests = 0;
        
        sectionsToTest.forEach((section, index) => {
            setTimeout(() => {
                testSection(section);
                if (index === sectionsToTest.length - 1) {
                    setTimeout(showFinalResults, 2000);
                }
            }, index * 1500);
        });
    };
    
    console.log('\n💡 Fonctions utilitaires disponibles:');
    console.log('- testSpecificSection("nom_section") : Tester une section spécifique');
    console.log('- retestAllSections() : Relancer tous les tests');
    
})();
