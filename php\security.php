<?php

/**
 * Enhanced Security System for Mossaab Landing Page
 */

// Config is already loaded by database.php

class Security
{
    private static $instance = null;
    private $csrfToken;
    private $rateLimits = [];

    private function __construct()
    {
        if (!defined('SECURITY_CHECK')) {
            define('SECURITY_CHECK', true);
        }

        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $this->initializeCsrf();
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize security system
     * This method provides compatibility with API files that call Security::init()
     */
    public static function init()
    {
        // Initialize the Security instance
        self::getInstance();

        // Also initialize SecurityHeaders if available
        if (class_exists('SecurityHeaders')) {
            SecurityHeaders::init();
        }

        return true;
    }

    /**
     * Validate incoming request
     * This method provides compatibility with API files that call Security::validateRequest()
     */
    public static function validateRequest()
    {
        $instance = self::getInstance();

        // Basic request validation
        $method = $_SERVER['REQUEST_METHOD'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // Check for suspicious patterns
        $suspiciousPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scanner/i'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                // Log suspicious activity but don't block (could be legitimate)
                error_log("Suspicious user agent detected: " . $userAgent);
                break;
            }
        }

        // Rate limiting check
        try {
            $instance->checkRateLimit($_SERVER['REMOTE_ADDR'] ?? 'unknown');
        } catch (Exception $e) {
            http_response_code(429);
            echo json_encode(['success' => false, 'message' => 'Rate limit exceeded']);
            exit;
        }

        return true;
    }

    /**
     * Input Sanitization with Arabic Support
     */
    public function sanitizeInput($data, $allowHtml = false)
    {
        if (is_array($data)) {
            return array_map(function ($item) use ($allowHtml) {
                return $this->sanitizeInput($item, $allowHtml);
            }, $data);
        }

        if (!is_string($data)) {
            return $data;
        }

        // Trim whitespace and normalize Unicode
        $data = trim($data);
        $data = mb_convert_encoding($data, 'UTF-8', 'UTF-8');

        if (!$allowHtml) {
            $data = strip_tags($data);
        }

        return $data;
    }

    // API Key Validation
    public function validateApiKey($key, $provider)
    {
        if (empty($key)) {
            throw new Exception($this->getErrorMessage('API_KEY_EMPTY', 'ar'));
        }

        switch (strtolower($provider)) {
            case 'openai':
                if (!preg_match('/^sk-[a-zA-Z0-9]{32,}$/', $key)) {
                    throw new Exception($this->getErrorMessage('INVALID_OPENAI_KEY', 'ar'));
                }
                break;
            case 'anthropic':
                if (!preg_match('/^sk-ant-[a-zA-Z0-9]{32,}$/', $key)) {
                    throw new Exception($this->getErrorMessage('INVALID_ANTHROPIC_KEY', 'ar'));
                }
                break;
            case 'gemini':
                if (!preg_match('/^AIza[a-zA-Z0-9_-]{35,}$/', $key)) {
                    throw new Exception($this->getErrorMessage('INVALID_GEMINI_KEY', 'ar'));
                }
                break;
            default:
                throw new Exception($this->getErrorMessage('INVALID_PROVIDER', 'ar'));
        }

        return true;
    }

    // Provider Connection Testing
    public function testProviderConnection($provider, $apiKey)
    {
        try {
            switch (strtolower($provider)) {
                case 'openai':
                    return $this->testOpenAIConnection($apiKey);
                case 'anthropic':
                    return $this->testAnthropicConnection($apiKey);
                case 'gemini':
                    return $this->testGeminiConnection($apiKey);
                default:
                    throw new Exception($this->getErrorMessage('INVALID_PROVIDER', 'ar'));
            }
        } catch (Exception $e) {
            error_log("Provider connection test failed: {$provider} - " . $e->getMessage());
            throw new Exception($this->getErrorMessage('CONNECTION_TEST_FAILED', 'ar'));
        }
    }

    // CSRF Protection
    private function initializeCsrf()
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        $this->csrfToken = $_SESSION['csrf_token'];
    }

    public function getCsrfToken()
    {
        return $this->csrfToken;
    }

    public function validateCsrfToken($token)
    {
        if (!Config::getBool('CSRF_ENABLED', true)) {
            return true;
        }

        if (!hash_equals($this->csrfToken, $token)) {
            throw new Exception($this->getErrorMessage('INVALID_CSRF', 'ar'));
        }
        return true;
    }

    // Rate Limiting
    public function checkRateLimit($identifier, $limit = null, $period = null)
    {
        if (!Config::getBool('RATE_LIMIT_ENABLED', true)) {
            return true;
        }

        $aiConfig = Config::getInstance()->getAIConfig();
        $limit = $limit ?? $aiConfig['rate_limit'];
        $period = $period ?? $aiConfig['rate_period'];

        $currentTime = time();
        if (!isset($this->rateLimits[$identifier])) {
            $this->rateLimits[$identifier] = ['count' => 0, 'timestamp' => $currentTime];
        }

        if ($currentTime - $this->rateLimits[$identifier]['timestamp'] > $period) {
            $this->rateLimits[$identifier] = ['count' => 0, 'timestamp' => $currentTime];
        }

        if ($this->rateLimits[$identifier]['count'] >= $limit) {
            throw new Exception($this->getErrorMessage('RATE_LIMIT_EXCEEDED', 'ar'));
        }

        $this->rateLimits[$identifier]['count']++;
        return true;
    }

    // Data Encryption
    public function encrypt($data)
    {
        $key = Config::get('APP_KEY');
        if (empty($key)) {
            throw new Exception($this->getErrorMessage('ENCRYPTION_KEY_MISSING', 'ar'));
        }

        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }

    public function decrypt($data)
    {
        $key = Config::get('APP_KEY');
        if (empty($key)) {
            throw new Exception($this->getErrorMessage('ENCRYPTION_KEY_MISSING', 'ar'));
        }

        $data = base64_decode($data);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }

    // Error Messages with RTL Support
    private $errorMessages = [
        'API_KEY_EMPTY' => [
            'en' => 'API key cannot be empty',
            'ar' => 'لا يمكن أن يكون مفتاح API فارغًا'
        ],
        'INVALID_OPENAI_KEY' => [
            'en' => 'Invalid OpenAI API key format',
            'ar' => 'تنسيق مفتاح OpenAI API غير صالح'
        ],
        'INVALID_ANTHROPIC_KEY' => [
            'en' => 'Invalid Anthropic API key format',
            'ar' => 'تنسيق مفتاح Anthropic API غير صالح'
        ],
        'INVALID_GEMINI_KEY' => [
            'en' => 'Invalid Gemini API key format',
            'ar' => 'تنسيق مفتاح Gemini API غير صالح'
        ],
        'INVALID_PROVIDER' => [
            'en' => 'Invalid AI provider specified',
            'ar' => 'مزود الذكاء الاصطناعي المحدد غير صالح'
        ],
        'CONNECTION_TEST_FAILED' => [
            'en' => 'Provider connection test failed',
            'ar' => 'فشل اختبار الاتصال بالمزود'
        ],
        'INVALID_CSRF' => [
            'en' => 'Invalid CSRF token',
            'ar' => 'رمز CSRF غير صالح'
        ],
        'RATE_LIMIT_EXCEEDED' => [
            'en' => 'Rate limit exceeded. Please try again later',
            'ar' => 'تم تجاوز حد معدل الطلبات. يرجى المحاولة لاحقًا'
        ],
        'ENCRYPTION_KEY_MISSING' => [
            'en' => 'Encryption key is missing',
            'ar' => 'مفتاح التشفير مفقود'
        ]
    ];

    private function getErrorMessage($code, $lang = 'en')
    {
        return $this->errorMessages[$code][$lang] ?? $this->errorMessages[$code]['en'];
    }

    // Private provider test methods
    private function testOpenAIConnection($apiKey)
    {
        $ch = curl_init('https://api.openai.com/v1/models');
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $apiKey,
                'Content-Type: application/json'
            ]
        ]);
        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $statusCode === 200;
    }

    private function testAnthropicConnection($apiKey)
    {
        $ch = curl_init('https://api.anthropic.com/v1/messages');
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'x-api-key: ' . $apiKey,
                'anthropic-version: 2023-06-01',
                'Content-Type: application/json'
            ]
        ]);
        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $statusCode !== 401;
    }

    private function testGeminiConnection($apiKey)
    {
        $ch = curl_init('https://generativelanguage.googleapis.com/v1beta/models?key=' . $apiKey);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => ['Content-Type: application/json']
        ]);
        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $statusCode === 200;
    }
}
