<?php

/**
 * Admin Dashboard API
 * Comprehensive admin view with all users' data and system overview
 */

header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID, X-User-Role");

// Handle preflight requests
if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit();
}

try {
    require_once __DIR__ . '/../config.php';
    $pdo = getPDOConnection();

    if (!$pdo) {
        throw new Exception("Database connection failed");
    }

    // Get user information
    $currentUserId = $_SERVER['HTTP_X_USER_ID'] ?? $_GET['user_id'] ?? 1;
    $currentUserRole = $_SERVER['HTTP_X_USER_ROLE'] ?? $_GET['user_role'] ?? 'admin';

    // Verify admin access
    $isAdmin = ($currentUserRole === 'admin' || $currentUserId == 1);

    if (!$isAdmin) {
        throw new Exception("Access denied: Admin privileges required");
    }

    $action = $_GET['action'] ?? 'overview';

    switch ($action) {
        case 'overview':
            getAdminOverview($pdo);
            break;
        case 'users':
            getAllUsers($pdo);
            break;
        case 'products':
            getAllProductsWithOwners($pdo);
            break;
        case 'landing_pages':
            getAllLandingPagesWithOwners($pdo);
            break;
        case 'orders':
            getAllOrdersWithOwners($pdo);
            break;
        case 'customers':
            getAllCustomers($pdo);
            break;
        default:
            getAdminOverview($pdo);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'error' => true
    ], JSON_UNESCAPED_UNICODE);
}

function getAdminOverview($pdo)
{
    try {
        $overview = [];

        // System-wide statistics
        $overview['system_stats'] = [
            'total_users' => 0,
            'total_sellers' => 0,
            'total_products' => 0,
            'total_landing_pages' => 0,
            'total_orders' => 0,
            'total_customers' => 0,
            'total_sales' => 0
        ];

        // Get user statistics
        $stmt = $pdo->query("
            SELECT
                COUNT(*) as total_users,
                SUM(CASE WHEN role_id = 2 THEN 1 ELSE 0 END) as total_sellers,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users
            FROM users
        ");
        $userStats = $stmt->fetch(PDO::FETCH_ASSOC);
        $overview['system_stats']['total_users'] = $userStats['total_users'];
        $overview['system_stats']['total_sellers'] = $userStats['total_sellers'];
        $overview['system_stats']['active_users'] = $userStats['active_users'];

        // Get product statistics
        $stmt = $pdo->query("
            SELECT
                COUNT(*) as total_products,
                SUM(CASE WHEN actif = 1 THEN 1 ELSE 0 END) as active_products,
                COUNT(DISTINCT user_id) as sellers_with_products
            FROM produits
            WHERE user_id IS NOT NULL
        ");
        $productStats = $stmt->fetch(PDO::FETCH_ASSOC);
        $overview['system_stats']['total_products'] = $productStats['total_products'];
        $overview['system_stats']['active_products'] = $productStats['active_products'];
        $overview['system_stats']['sellers_with_products'] = $productStats['sellers_with_products'];

        // Get landing page statistics
        $stmt = $pdo->query("
            SELECT
                COUNT(*) as total_landing_pages,
                COUNT(DISTINCT user_id) as sellers_with_landing_pages
            FROM landing_pages
            WHERE user_id IS NOT NULL
        ");
        $landingStats = $stmt->fetch(PDO::FETCH_ASSOC);
        $overview['system_stats']['total_landing_pages'] = $landingStats['total_landing_pages'];
        $overview['system_stats']['sellers_with_landing_pages'] = $landingStats['sellers_with_landing_pages'];

        // Get order statistics
        $stmt = $pdo->query("
            SELECT
                COUNT(*) as total_orders,
                COUNT(DISTINCT nom_client) as unique_customers,
                COALESCE(SUM(montant_total), 0) as total_sales,
                SUM(CASE WHEN statut = 'en_attente' THEN 1 ELSE 0 END) as pending_orders,
                SUM(CASE WHEN statut = 'payé' THEN 1 ELSE 0 END) as paid_orders,
                SUM(CASE WHEN statut = 'expédié' THEN 1 ELSE 0 END) as shipped_orders
            FROM commandes
        ");
        $orderStats = $stmt->fetch(PDO::FETCH_ASSOC);
        $overview['system_stats']['total_orders'] = $orderStats['total_orders'];
        $overview['system_stats']['total_customers'] = $orderStats['unique_customers'];
        $overview['system_stats']['total_sales'] = $orderStats['total_sales'];
        $overview['system_stats']['pending_orders'] = $orderStats['pending_orders'];
        $overview['system_stats']['paid_orders'] = $orderStats['paid_orders'];
        $overview['system_stats']['shipped_orders'] = $orderStats['shipped_orders'];

        // Top performing sellers
        $stmt = $pdo->query("
            SELECT
                u.id,
                u.username,
                u.first_name,
                u.last_name,
                u.email,
                COUNT(DISTINCT p.id) as product_count,
                COUNT(DISTINCT lp.id) as landing_page_count
            FROM users u
            LEFT JOIN produits p ON u.id = p.user_id AND p.actif = 1
            LEFT JOIN landing_pages lp ON u.id = lp.user_id
            WHERE u.role_id = 2
            GROUP BY u.id
            ORDER BY product_count DESC, landing_page_count DESC
            LIMIT 10
        ");
        $overview['top_sellers'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Recent activity
        $stmt = $pdo->query("
            SELECT
                'product' as type,
                p.titre as title,
                u.username as user,
                p.created_at as date
            FROM produits p
            JOIN users u ON p.user_id = u.id
            WHERE p.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

            UNION ALL

            SELECT
                'landing_page' as type,
                lp.titre as title,
                u.username as user,
                lp.created_at as date
            FROM landing_pages lp
            JOIN users u ON lp.user_id = u.id
            WHERE lp.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

            UNION ALL

            SELECT
                'order' as type,
                CONCAT('Order #', c.id, ' - ', c.nom_client) as title,
                'Customer' as user,
                c.date_commande as date
            FROM commandes c
            WHERE c.date_commande >= DATE_SUB(NOW(), INTERVAL 7 DAY)

            ORDER BY date DESC
            LIMIT 20
        ");
        $overview['recent_activity'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $overview,
            'message' => 'Admin overview loaded successfully'
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        throw new Exception("Error loading admin overview: " . $e->getMessage());
    }
}

function getAllUsers($pdo)
{
    try {
        $stmt = $pdo->query("
            SELECT
                u.*,
                ur.name as role_name,
                COUNT(DISTINCT p.id) as product_count,
                COUNT(DISTINCT lp.id) as landing_page_count
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.id
            LEFT JOIN produits p ON u.id = p.user_id AND p.actif = 1
            LEFT JOIN landing_pages lp ON u.id = lp.user_id
            GROUP BY u.id
            ORDER BY u.created_at DESC
        ");

        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Remove sensitive information
        foreach ($users as &$user) {
            unset($user['password']);
            $user['full_name'] = trim($user['first_name'] . ' ' . $user['last_name']);
        }

        echo json_encode([
            'success' => true,
            'data' => $users,
            'message' => 'All users loaded successfully',
            'count' => count($users)
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        throw new Exception("Error loading users: " . $e->getMessage());
    }
}

function getAllProductsWithOwners($pdo)
{
    try {
        $stmt = $pdo->query("
            SELECT
                p.*,
                u.username as owner_username,
                u.first_name as owner_first_name,
                u.last_name as owner_last_name,
                u.email as owner_email,
                CASE WHEN lp.id IS NOT NULL THEN 1 ELSE 0 END as has_landing_page
            FROM produits p
            LEFT JOIN users u ON p.user_id = u.id
            LEFT JOIN landing_pages lp ON p.id = lp.produit_id
            ORDER BY p.created_at DESC
        ");

        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($products as &$product) {
            $product['owner_full_name'] = trim($product['owner_first_name'] . ' ' . $product['owner_last_name']);
            $product['formatted_price'] = number_format($product['prix'], 2) . ' دج';
        }

        echo json_encode([
            'success' => true,
            'data' => $products,
            'message' => 'All products with owners loaded successfully',
            'count' => count($products)
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        throw new Exception("Error loading products: " . $e->getMessage());
    }
}

function getAllLandingPagesWithOwners($pdo)
{
    try {
        $stmt = $pdo->query("
            SELECT
                lp.*,
                u.username as owner_username,
                u.first_name as owner_first_name,
                u.last_name as owner_last_name,
                u.email as owner_email,
                p.titre as product_title,
                p.prix as product_price
            FROM landing_pages lp
            LEFT JOIN users u ON lp.user_id = u.id
            LEFT JOIN produits p ON lp.produit_id = p.id
            ORDER BY lp.created_at DESC
        ");

        $landingPages = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($landingPages as &$page) {
            $page['owner_full_name'] = trim($page['owner_first_name'] . ' ' . $page['owner_last_name']);
        }

        echo json_encode([
            'success' => true,
            'data' => $landingPages,
            'message' => 'All landing pages with owners loaded successfully',
            'count' => count($landingPages)
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        throw new Exception("Error loading landing pages: " . $e->getMessage());
    }
}

function getAllOrdersWithOwners($pdo)
{
    try {
        $stmt = $pdo->query("
            SELECT
                c.*,
                u.username as seller_username,
                u.first_name as seller_first_name,
                u.last_name as seller_last_name,
                u.email as seller_email
            FROM commandes c
            LEFT JOIN users u ON c.user_id = u.id
            ORDER BY c.date_commande DESC
        ");

        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($orders as &$order) {
            $order['seller_full_name'] = trim($order['seller_first_name'] . ' ' . $order['seller_last_name']);
            $order['formatted_amount'] = number_format($order['montant_total'], 2) . ' دج';
        }

        echo json_encode([
            'success' => true,
            'data' => $orders,
            'message' => 'All orders with sellers loaded successfully',
            'count' => count($orders)
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        throw new Exception("Error loading orders: " . $e->getMessage());
    }
}

function getAllCustomers($pdo)
{
    try {
        $stmt = $pdo->query("
            SELECT
                nom_client as customer_name,
                telephone as phone,
                adresse_livraison as address,
                COUNT(*) as order_count,
                SUM(montant_total) as total_spent,
                MAX(date_commande) as last_order_date,
                MIN(date_commande) as first_order_date
            FROM commandes
            WHERE nom_client IS NOT NULL AND nom_client != ''
            GROUP BY nom_client, telephone
            ORDER BY total_spent DESC, order_count DESC
        ");

        $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($customers as &$customer) {
            $customer['formatted_total_spent'] = number_format($customer['total_spent'], 2) . ' دج';
        }

        echo json_encode([
            'success' => true,
            'data' => $customers,
            'message' => 'All customers loaded successfully',
            'count' => count($customers)
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        throw new Exception("Error loading customers: " . $e->getMessage());
    }
}
