// API Error Handling and Settings Management
document.addEventListener('DOMContentLoaded', function() {
    // Add styles for messages
    const style = document.createElement('style');
    style.textContent = `
        .error-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #f44336;
            color: white;
            padding: 15px;
            border-radius: 4px;
            z-index: 1000;
            display: none;
            direction: rtl;
        }

        .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 4px;
            z-index: 1000;
            direction: rtl;
        }
    `;
    document.head.appendChild(style);

    // API Key toggle visibility
    document.querySelectorAll('.show-key').forEach(button => {
        button.addEventListener('click', function() {
            const input = this.previousElementSibling;
            const icon = this.querySelector('i');
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.replace('fa-eye', 'fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.replace('fa-eye-slash', 'fa-eye');
            }
        });
    });

    // API Error handling with retry mechanism
    async function makeAPICall(url, options, retries = 3) {
        for (let i = 0; i < retries; i++) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        ...options?.headers
                    }
                });

                if (!response.ok) {
                    throw new Error(`خطأ في الاتصال! الحالة: ${response.status}`);
                }

                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                console.error(`محاولة ${i + 1}/${retries} فشلت:`, error);
                if (i === retries - 1) {
                    showError(error.message);
                    return { success: false, error: error.message };
                }
                // Wait before retrying
                await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
            }
        }
    }

    // Error display with auto-dismiss
    function showError(message) {
        const errorContainer = document.getElementById('error-container') || createErrorContainer();
        errorContainer.textContent = `خطأ: ${message}`;
        errorContainer.style.display = 'block';

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            errorContainer.style.opacity = '0';
            setTimeout(() => {
                errorContainer.style.display = 'none';
                errorContainer.style.opacity = '1';
            }, 300);
        }, 5000);
    }

    function createErrorContainer() {
        const container = document.createElement('div');
        container.id = 'error-container';
        container.className = 'error-message';
        document.body.appendChild(container);
        return container;
    }

    // Save settings with validation
    async function saveSettings(event) {
        event.preventDefault();

        // Validate required fields
        const requiredFields = ['openaiKey', 'anthropicKey', 'geminiKey'];
        const emptyFields = requiredFields.filter(field => !document.getElementById(field).value);

        if (emptyFields.length > 0) {
            showError('يرجى ملء جميع حقول المفاتيح المطلوبة');
            return;
        }

        const settings = {
            openai: {
                key: document.getElementById('openaiKey').value,
                model: document.querySelector('input[name="openai-model"]:checked')?.value || 'gpt-3.5-turbo'
            },
            anthropic: {
                key: document.getElementById('anthropicKey').value,
                model: document.querySelector('input[name="anthropic-model"]:checked')?.value || 'claude-2'
            },
            gemini: {
                key: document.getElementById('geminiKey').value,
                model: document.querySelector('input[name="gemini-model"]:checked')?.value || 'gemini-pro'
            }
        };

        const result = await makeAPICall('/api/save-ai-settings.php', {
            method: 'POST',
            body: JSON.stringify(settings)
        });

        if (result.success) {
            showSuccess('تم حفظ الإعدادات بنجاح');
        }
    }

    // Success message with fade effect
    function showSuccess(message) {
        const successContainer = document.createElement('div');
        successContainer.className = 'success-message';
        successContainer.textContent = message;
        successContainer.style.opacity = '0';
        document.body.appendChild(successContainer);

        // Fade in
        requestAnimationFrame(() => {
            successContainer.style.transition = 'opacity 0.3s ease-in';
            successContainer.style.opacity = '1';
        });

        // Fade out and remove
        setTimeout(() => {
            successContainer.style.opacity = '0';
            setTimeout(() => successContainer.remove(), 300);
        }, 3000);
    }

    // Event listeners
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', saveSettings);
    }

    // Load saved settings
    loadSavedSettings();
});

// Load saved settings from backend
async function loadSavedSettings() {
    const result = await makeAPICall('/api/get-ai-settings.php', {
        method: 'GET'
    });

    if (result.success && result.data) {
        Object.entries(result.data).forEach(([provider, settings]) => {
            // Set API keys
            const keyInput = document.getElementById(`${provider}Key`);
            if (keyInput && settings.key) {
                keyInput.value = '••••••••••••••••';
            }

            // Set selected models
            if (settings.model) {
                const modelInput = document.querySelector(`input[name="${provider}-model"][value="${settings.model}"]`);
                if (modelInput) {
                    modelInput.checked = true;
                }
            }
        });
    }
}
