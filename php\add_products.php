<?php
require_once 'config.php';

try {
    // Ajouter un livre
    $stmt = $conn->prepare(
        'INSERT INTO produits (titre, prix, stock, type, auteur, image_url) 
        VALUES (?, ?, ?, ?, ?, ?)'
    );
    $stmt->execute([
        'كتاب البرمجة للمبتدئين',
        299.99,
        25,
        'book',
        'محمد أحمد',
        '/images/default-book.jpg'
    ]);

    // Ajouter un PC portable
    $stmt = $conn->prepare(
        'INSERT INTO produits (titre, prix, stock, type, processeur, ram, stockage, image_url) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)'
    );
    $stmt->execute([
        'حاسوب محمول للمطورين',
        8999.99,
        10,
        'laptop',
        'Intel Core i7',
        '16GB',
        '512GB SSD',
        '/images/default-laptop.jpg'
    ]);

    // Ajouter un sac à dos
    $stmt = $conn->prepare(
        'INSERT INTO produits (titre, prix, stock, type, materiel, capacite, image_url) 
        VALUES (?, ?, ?, ?, ?, ?, ?)'
    );
    $stmt->execute([
        'حقيبة ظهر للحاسوب',
        199.99,
        50,
        'bag',
        'نايلون مقاوم للماء',
        '30L',
        '/images/default-bag.jpg'
    ]);

    echo "Produits ajoutés avec succès!";

} catch(PDOException $e) {
    echo "Erreur: " . $e->getMessage();
}