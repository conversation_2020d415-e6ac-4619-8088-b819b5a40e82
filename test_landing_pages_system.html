<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام صفحات الهبوط</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
        }
        .test-section.success {
            border-color: #28a745;
            background: #f8fff9;
        }
        .test-section.warning {
            border-color: #ffc107;
            background: #fffdf5;
        }
        .test-section.error {
            border-color: #dc3545;
            background: #fff5f5;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        .product-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .product-type {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .type-book { background: #6f42c1; }
        .type-laptop { background: #007bff; }
        .type-bag { background: #28a745; }
        .type-clothing { background: #fd7e14; }
        .type-home { background: #dc3545; }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "☐ ";
            color: #6c757d;
            font-weight: bold;
        }
        .checklist li.completed:before {
            content: "✅ ";
            color: #28a745;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        .status-pending { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار شامل لنظام صفحات الهبوط</h1>
        <p>هذه الصفحة تساعدك في اختبار جميع ميزات نظام صفحات الهبوط المحدث بشكل منهجي.</p>

        <!-- Phase 1: Product Verification -->
        <div class="test-section" id="phase1">
            <h2>📦 المرحلة 1: التحقق من المنتجات</h2>
            <p>تأكد من وجود 5 منتجات متنوعة وأنها مفعلة:</p>
            
            <div class="test-grid">
                <div class="product-card">
                    <h4>📚 كتاب تطوير الذات</h4>
                    <span class="product-type type-book">كتاب</span>
                    <p>فن اللامبالاة - كتاب تطوير الذات</p>
                    <span class="status-indicator status-pending" id="book-status"></span>
                </div>
                
                <div class="product-card">
                    <h4>💻 لابتوب للطلاب</h4>
                    <span class="product-type type-laptop">حاسوب محمول</span>
                    <p>Dell Inspiron 15 - للطلاب والمهنيين</p>
                    <span class="status-indicator status-pending" id="laptop-status"></span>
                </div>
                
                <div class="product-card">
                    <h4>🎒 حقيبة رياضية</h4>
                    <span class="product-type type-bag">حقيبة</span>
                    <p>حقيبة ظهر رياضية مقاومة للماء</p>
                    <span class="status-indicator status-pending" id="bag-status"></span>
                </div>
                
                <div class="product-card">
                    <h4>👔 قميص كلاسيكي</h4>
                    <span class="product-type type-clothing">ملابس</span>
                    <p>قميص قطني كلاسيكي للرجال</p>
                    <span class="status-indicator status-pending" id="clothing-status"></span>
                </div>
                
                <div class="product-card">
                    <h4>🏠 جهاز منزلي</h4>
                    <span class="product-type type-home">أجهزة منزلية</span>
                    <p>خلاط كهربائي متعدد الاستخدامات</p>
                    <span class="status-indicator status-pending" id="home-status"></span>
                </div>
            </div>
            
            <button class="btn" onclick="checkProducts()">🔍 فحص المنتجات</button>
            <a href="admin/" class="btn btn-success">🚀 الذهاب إلى لوحة التحكم</a>
        </div>

        <!-- Phase 2: Landing Page Creation Testing -->
        <div class="test-section" id="phase2">
            <h2>🎨 المرحلة 2: اختبار إنشاء صفحات الهبوط</h2>
            <p>اختبر إنشاء صفحة هبوط لكل منتج باستخدام النظام المحدث:</p>
            
            <ul class="checklist">
                <li id="test-template-selection">اختبار نظام اختيار القوالب</li>
                <li id="test-multiple-images">اختبار رفع صور متعددة</li>
                <li id="test-image-accumulation">اختبار تجميع الصور (عدم الحذف عند إضافة جديدة)</li>
                <li id="test-individual-removal">اختبار حذف صور فردية</li>
                <li id="test-clear-all">اختبار زر "مسح الكل"</li>
                <li id="test-tinymce-editors">اختبار محررات النصوص TinyMCE</li>
                <li id="test-product-selection">اختبار اختيار المنتج وتحديث العنوان</li>
                <li id="test-save-functionality">اختبار حفظ صفحة الهبوط</li>
                <li id="test-edit-functionality">اختبار تعديل صفحة هبوط موجودة</li>
                <li id="test-responsive-design">اختبار التصميم المتجاوب</li>
            </ul>
            
            <div style="margin: 20px 0;">
                <h3>🎯 خطوات الاختبار المفصلة:</h3>
                <ol>
                    <li><strong>افتح لوحة التحكم</strong> واذهب إلى قسم "صفحات هبوط"</li>
                    <li><strong>انقر على "إضافة صفحة هبوط جديدة"</strong></li>
                    <li><strong>اختر قالب</strong> من القوالب المتاحة</li>
                    <li><strong>انقر "التالي"</strong> للانتقال إلى خطوة المحتوى</li>
                    <li><strong>اختر منتج</strong> من القائمة المنسدلة</li>
                    <li><strong>ارفع صور متعددة</strong> (3-5 صور) واختبر:</li>
                    <ul>
                        <li>تجميع الصور بدلاً من استبدالها</li>
                        <li>حذف صور فردية</li>
                        <li>زر "مسح الكل"</li>
                    </ul>
                    <li><strong>اكتب محتوى</strong> في محررات النصوص</li>
                    <li><strong>احفظ الصفحة</strong> وتأكد من عدم وجود أخطاء</li>
                    <li><strong>اختبر التعديل</strong> بفتح الصفحة مرة أخرى</li>
                </ol>
            </div>
        </div>

        <!-- Phase 3: Feature Validation -->
        <div class="test-section" id="phase3">
            <h2>✅ المرحلة 3: التحقق من الميزات</h2>
            <p>تأكد من أن جميع الميزات الجديدة تعمل بشكل صحيح:</p>
            
            <div class="test-grid">
                <div class="product-card">
                    <h4>🎨 نظام القوالب</h4>
                    <ul>
                        <li>عرض قوالب متعددة</li>
                        <li>اختيار قالب وتطبيقه</li>
                        <li>تحديث المحتوى تلقائياً</li>
                    </ul>
                </div>
                
                <div class="product-card">
                    <h4>📸 إدارة الصور</h4>
                    <ul>
                        <li>رفع صور متعددة</li>
                        <li>معاينة فورية</li>
                        <li>حذف فردي ومسح شامل</li>
                    </ul>
                </div>
                
                <div class="product-card">
                    <h4>📝 محررات النصوص</h4>
                    <ul>
                        <li>TinyMCE يعمل بشكل صحيح</li>
                        <li>دعم النصوص العربية</li>
                        <li>حفظ المحتوى بنجاح</li>
                    </ul>
                </div>
                
                <div class="product-card">
                    <h4>🔄 إدارة البيانات</h4>
                    <ul>
                        <li>حفظ بدون أخطاء JSON</li>
                        <li>تحديث فوري للقوائم</li>
                        <li>معالجة أخطاء PHP</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Results Summary -->
        <div class="test-section" id="results">
            <h2>📊 ملخص النتائج</h2>
            <div id="test-results">
                <p>ابدأ الاختبار لرؤية النتائج هنا...</p>
            </div>
        </div>
    </div>

    <script>
        // Product checking functionality
        async function checkProducts() {
            console.log('🔍 Checking products...');
            
            try {
                const response = await fetch('/php/api/products.php');
                const data = await response.json();
                
                if (data.success && data.products) {
                    const products = data.products;
                    const activeProducts = products.filter(p => p.actif == 1);
                    
                    console.log(`Found ${activeProducts.length} active products out of ${products.length} total`);
                    
                    // Update status indicators
                    const types = ['book', 'laptop', 'bag', 'clothing', 'home'];
                    types.forEach(type => {
                        const hasType = activeProducts.some(p => p.type === type);
                        const indicator = document.getElementById(`${type}-status`);
                        if (indicator) {
                            indicator.className = `status-indicator ${hasType ? 'status-success' : 'status-error'}`;
                        }
                    });
                    
                    // Update phase 1 status
                    const phase1 = document.getElementById('phase1');
                    if (activeProducts.length >= 5) {
                        phase1.className = 'test-section success';
                        updateResults('✅ تم العثور على جميع المنتجات المطلوبة');
                    } else {
                        phase1.className = 'test-section warning';
                        updateResults(`⚠️ تم العثور على ${activeProducts.length} منتجات فقط من أصل 5 مطلوبة`);
                    }
                } else {
                    throw new Error('Failed to load products');
                }
            } catch (error) {
                console.error('Error checking products:', error);
                document.getElementById('phase1').className = 'test-section error';
                updateResults('❌ خطأ في تحميل المنتجات: ' + error.message);
            }
        }
        
        function updateResults(message) {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            results.innerHTML += `<p>[${timestamp}] ${message}</p>`;
        }
        
        // Mark checklist items as completed
        function markCompleted(itemId) {
            const item = document.getElementById(itemId);
            if (item) {
                item.classList.add('completed');
            }
        }
        
        // Auto-check products on page load
        window.addEventListener('load', () => {
            setTimeout(checkProducts, 1000);
        });
        
        console.log('🧪 Landing Pages Testing System Loaded');
        console.log('Use markCompleted("test-item-id") to mark test items as completed');
    </script>
</body>
</html>
