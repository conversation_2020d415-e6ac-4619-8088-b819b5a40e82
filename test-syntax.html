<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Syntax JavaScript</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        .test-item.success {
            border-left-color: #27ae60;
            background: #d5f4e6;
        }
        .test-item.error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .test-result {
            font-size: 0.9em;
            color: #666;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        #console-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test de syntaxe JavaScript - Admin</h1>
        
        <div id="test-results"></div>
        
        <button onclick="testJavaScriptSyntax()">Tester la syntaxe JS</button>
        <button onclick="testAdminFunctions()">Tester les fonctions admin</button>
        <button onclick="clearConsole()">Effacer console</button>
        
        <div id="console-output"></div>
    </div>

    <script>
        const testResults = document.getElementById('test-results');
        const consoleOutput = document.getElementById('console-output');
        
        // Capture console errors
        const originalConsoleError = console.error;
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        
        function logToOutput(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.innerHTML += `<div>[${timestamp}] ${type}: ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.error = function(...args) {
            logToOutput('ERROR', args.join(' '));
            originalConsoleError.apply(console, args);
        };
        
        console.log = function(...args) {
            logToOutput('LOG', args.join(' '));
            originalConsoleLog.apply(console, args);
        };
        
        console.warn = function(...args) {
            logToOutput('WARN', args.join(' '));
            originalConsoleWarn.apply(console, args);
        };
        
        function addTestResult(title, result, success = true) {
            const testItem = document.createElement('div');
            testItem.className = `test-item ${success ? 'success' : 'error'}`;
            testItem.innerHTML = `
                <div class="test-title">${title}</div>
                <div class="test-result">${result}</div>
            `;
            testResults.appendChild(testItem);
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
            testResults.innerHTML = '';
        }
        
        function testJavaScriptSyntax() {
            testResults.innerHTML = '';
            
            // Test loading admin.js
            const script = document.createElement('script');
            script.src = '/admin/js/admin.js';
            script.onload = function() {
                addTestResult('Admin.js Loading', 'Fichier chargé sans erreur de syntaxe', true);
                testAdminFunctions();
            };
            script.onerror = function() {
                addTestResult('Admin.js Loading', 'Erreur lors du chargement du fichier', false);
            };
            
            // Remove existing script if any
            const existingScript = document.querySelector('script[src="/admin/js/admin.js"]');
            if (existingScript) {
                existingScript.remove();
            }
            
            document.head.appendChild(script);
        }
        
        function testAdminFunctions() {
            // Test if key functions exist
            const functions = [
                'initNavigation',
                'initFormHandlers', 
                'initModalHandlers',
                'initSettingsHandlers',
                'loadDashboard',
                'loadBooks',
                'loadOrders',
                'loadSettings',
                'loadStoreSettings'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addTestResult(`Function ${funcName}`, 'Fonction définie correctement', true);
                } else {
                    addTestResult(`Function ${funcName}`, 'Fonction manquante ou non définie', false);
                }
            });
            
            // Test if navigation elements would work
            testNavigationStructure();
        }
        
        function testNavigationStructure() {
            // Simulate navigation test
            fetch('/admin/index.html')
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    
                    const navItems = doc.querySelectorAll('.admin-nav li[data-section]');
                    if (navItems.length > 0) {
                        addTestResult('Navigation Structure', `${navItems.length} éléments de navigation trouvés`, true);
                        
                        // Check each navigation item
                        navItems.forEach((item, index) => {
                            const section = item.getAttribute('data-section');
                            const sectionElement = doc.getElementById(section);
                            if (sectionElement) {
                                addTestResult(`Section ${section}`, 'Section correspondante existe', true);
                            } else {
                                addTestResult(`Section ${section}`, 'Section correspondante manquante', false);
                            }
                        });
                    } else {
                        addTestResult('Navigation Structure', 'Aucun élément de navigation trouvé', false);
                    }
                })
                .catch(error => {
                    addTestResult('Navigation Structure', `Erreur: ${error.message}`, false);
                });
        }
        
        // Capture any JavaScript errors on page load
        window.addEventListener('error', function(e) {
            logToOutput('GLOBAL ERROR', `${e.message} at ${e.filename}:${e.lineno}`);
        });
        
        // Initial test
        document.addEventListener('DOMContentLoaded', () => {
            addTestResult('Test Page Load', 'Page de test chargée', true);
            logToOutput('INFO', 'Page de test initialisée');
        });
    </script>
</body>
</html>
