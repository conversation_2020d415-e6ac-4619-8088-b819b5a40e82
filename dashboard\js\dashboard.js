// Dashboard JavaScript

// Global variables
let currentUser = null;
let currentStore = null;
let products = [];

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    loadStoreData();
    loadProducts();
});

// Initialize dashboard
function initializeDashboard() {
    // Check authentication (simplified for demo)
    currentUser = {
        id: 1,
        name: 'مصعب التجريبي',
        email: '<EMAIL>'
    };
    
    currentStore = {
        id: 1,
        name: 'متجر مصعب',
        slug: 'mossaab-store'
    };
    
    // Update user name in header
    document.getElementById('userName').textContent = currentUser.name;
    
    // Show dashboard section by default
    showSection('dashboard');
}

// Navigation functions
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.classList.remove('active'));
    
    // Remove active class from all nav items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => item.classList.remove('active'));
    
    // Show selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // Add active class to corresponding nav item
    const targetNav = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
    if (targetNav) {
        targetNav.closest('.nav-item').classList.add('active');
    }
    
    // Load section-specific data
    switch(sectionName) {
        case 'products':
            loadProducts();
            break;
        case 'orders':
            loadOrders();
            break;
        case 'analytics':
            loadAnalytics();
            break;
    }
}

// User menu functions
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
}

function viewProfile() {
    alert('عرض الملف الشخصي - قيد التطوير');
    toggleUserMenu();
}

function viewStore() {
    window.open(`/store/${currentStore.slug}`, '_blank');
    toggleUserMenu();
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        window.location.href = '/login.html';
    }
    toggleUserMenu();
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (!userMenu.contains(event.target)) {
        dropdown.classList.remove('show');
    }
});

// Load store data and statistics
async function loadStoreData() {
    try {
        // Simulate API call to get store statistics
        const stats = {
            totalProducts: 10,
            totalOrders: 0,
            totalRevenue: 0,
            storeViews: 0
        };
        
        // Update stats in dashboard
        document.getElementById('totalProducts').textContent = stats.totalProducts;
        document.getElementById('totalOrders').textContent = stats.totalOrders;
        document.getElementById('totalRevenue').textContent = stats.totalRevenue.toLocaleString() + ' DZD';
        document.getElementById('storeViews').textContent = stats.storeViews;
        
    } catch (error) {
        console.error('Error loading store data:', error);
        showNotification('خطأ في تحميل بيانات المتجر', 'error');
    }
}

// Load products
async function loadProducts() {
    try {
        // Simulate API call to get store products
        const response = await fetch(`/php/api/store-products.php?store_id=${currentStore.id}`);
        
        if (!response.ok) {
            throw new Error('Failed to load products');
        }
        
        const data = await response.json();
        
        if (data.success) {
            products = data.products || [];
            displayProducts();
        } else {
            throw new Error(data.message || 'Failed to load products');
        }
        
    } catch (error) {
        console.error('Error loading products:', error);
        
        // Fallback: Use demo data
        products = [
            {
                id: 54,
                titre: 'كتاب الطبخ الجزائري الأصيل',
                type: 'book',
                prix: 2500,
                stock: 50,
                actif: 1
            },
            {
                id: 55,
                titre: 'تاريخ الجزائر المعاصر',
                type: 'book',
                prix: 3200,
                stock: 30,
                actif: 1
            },
            {
                id: 56,
                titre: 'هاتف Samsung Galaxy A54 5G',
                type: 'smartphone',
                prix: 45000,
                stock: 15,
                actif: 1
            },
            {
                id: 57,
                titre: 'لابتوب HP Pavilion 15 للطلاب',
                type: 'laptop',
                prix: 75000,
                stock: 8,
                actif: 1
            },
            {
                id: 58,
                titre: 'حقيبة ظهر جلدية فاخرة للرجال',
                type: 'bag',
                prix: 8500,
                stock: 25,
                actif: 1
            }
        ];
        
        displayProducts();
    }
}

// Display products in table
function displayProducts() {
    const tbody = document.getElementById('productsTableBody');
    
    if (!tbody) return;
    
    if (products.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; padding: 2rem; color: #6b7280;">
                    <i class="fas fa-box" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                    لا توجد منتجات حتى الآن
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = products.map(product => `
        <tr>
            <td>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <div style="width: 40px; height: 40px; background: #f3f4f6; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-box" style="color: #6b7280;"></i>
                    </div>
                    <div>
                        <div style="font-weight: 500;">${product.titre}</div>
                        <div style="font-size: 0.8rem; color: #6b7280;">ID: ${product.id}</div>
                    </div>
                </div>
            </td>
            <td>
                <span class="product-type-badge type-${product.type}">${getProductTypeLabel(product.type)}</span>
            </td>
            <td style="font-weight: 500;">${product.prix.toLocaleString()} DZD</td>
            <td>
                <span class="stock-badge ${product.stock > 10 ? 'stock-good' : product.stock > 0 ? 'stock-low' : 'stock-out'}">
                    ${product.stock}
                </span>
            </td>
            <td>
                <span class="status-badge ${product.actif ? 'status-active' : 'status-inactive'}">
                    ${product.actif ? 'نشط' : 'غير نشط'}
                </span>
            </td>
            <td>
                <div style="display: flex; gap: 5px;">
                    <button class="btn btn-sm btn-secondary" onclick="editProduct(${product.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="deleteProduct(${product.id})" title="حذف" style="background: #ef4444;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Get product type label in Arabic
function getProductTypeLabel(type) {
    const labels = {
        'book': 'كتاب',
        'laptop': 'لابتوب',
        'smartphone': 'هاتف ذكي',
        'bag': 'حقيبة',
        'clothing': 'ملابس',
        'home': 'منزلية',
        'accessory': 'إكسسوار',
        'sports': 'رياضية',
        'beauty': 'تجميل',
        'game': 'ألعاب'
    };
    return labels[type] || type;
}

// Filter products
function filterProducts() {
    const searchTerm = document.getElementById('productSearch').value.toLowerCase();
    const typeFilter = document.getElementById('productTypeFilter').value;
    
    let filteredProducts = products;
    
    if (searchTerm) {
        filteredProducts = filteredProducts.filter(product => 
            product.titre.toLowerCase().includes(searchTerm)
        );
    }
    
    if (typeFilter) {
        filteredProducts = filteredProducts.filter(product => 
            product.type === typeFilter
        );
    }
    
    // Temporarily update products array for display
    const originalProducts = [...products];
    products = filteredProducts;
    displayProducts();
    products = originalProducts;
}

// Product management functions
function showAddProductModal() {
    document.getElementById('addProductModal').classList.add('show');
}

function closeAddProductModal() {
    document.getElementById('addProductModal').classList.remove('show');
    document.getElementById('addProductForm').reset();
}

async function addProduct() {
    const form = document.getElementById('addProductForm');
    const formData = new FormData(form);
    
    const productData = {
        titre: document.getElementById('newProductName').value,
        type: document.getElementById('newProductType').value,
        prix: parseFloat(document.getElementById('newProductPrice').value),
        stock: parseInt(document.getElementById('newProductStock').value),
        description: document.getElementById('newProductDescription').value,
        store_id: currentStore.id
    };
    
    // Validate required fields
    if (!productData.titre || !productData.type || !productData.prix || productData.stock < 0) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    try {
        // Simulate API call
        const newProduct = {
            id: Date.now(), // Temporary ID
            ...productData,
            actif: 1
        };
        
        products.push(newProduct);
        displayProducts();
        closeAddProductModal();
        showNotification('تم إضافة المنتج بنجاح', 'success');
        
        // Update stats
        document.getElementById('totalProducts').textContent = products.length;
        
    } catch (error) {
        console.error('Error adding product:', error);
        showNotification('خطأ في إضافة المنتج', 'error');
    }
}

function editProduct(productId) {
    const product = products.find(p => p.id === productId);
    if (product) {
        alert(`تعديل المنتج: ${product.titre}\nقيد التطوير`);
    }
}

function deleteProduct(productId) {
    const product = products.find(p => p.id === productId);
    if (product && confirm(`هل أنت متأكد من حذف المنتج: ${product.titre}؟`)) {
        products = products.filter(p => p.id !== productId);
        displayProducts();
        showNotification('تم حذف المنتج بنجاح', 'success');
        
        // Update stats
        document.getElementById('totalProducts').textContent = products.length;
    }
}

// Load orders (placeholder)
function loadOrders() {
    console.log('Loading orders...');
}

// Load analytics (placeholder)
function loadAnalytics() {
    console.log('Loading analytics...');
}

// Store settings functions
function saveStoreSettings() {
    const settings = {
        name: document.getElementById('storeName').value,
        description: document.getElementById('storeDescription').value,
        color: document.getElementById('storeColor').value,
        currency: document.getElementById('storeCurrency').value
    };
    
    // Simulate API call
    console.log('Saving store settings:', settings);
    showNotification('تم حفظ الإعدادات بنجاح', 'success');
}

// Utility functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 3000;
        display: flex;
        align-items: center;
        gap: 10px;
        font-weight: 500;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add CSS for badges and status indicators
const style = document.createElement('style');
style.textContent = `
    .product-type-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
        background: #f3f4f6;
        color: #374151;
    }
    
    .type-book { background: #dbeafe; color: #1e40af; }
    .type-laptop { background: #f3e8ff; color: #7c3aed; }
    .type-smartphone { background: #ecfdf5; color: #059669; }
    .type-bag { background: #fef3c7; color: #d97706; }
    .type-clothing { background: #fce7f3; color: #be185d; }
    .type-home { background: #e0f2fe; color: #0369a1; }
    
    .stock-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .stock-good { background: #dcfce7; color: #166534; }
    .stock-low { background: #fef3c7; color: #92400e; }
    .stock-out { background: #fee2e2; color: #dc2626; }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-active { background: #dcfce7; color: #166534; }
    .status-inactive { background: #fee2e2; color: #dc2626; }
`;
document.head.appendChild(style);
