<?php
/**
 * API Utilities Class for Mossaab Landing Page
 * Consolidates duplicate code across PHP API files
 */

class ApiUtils
{
    /**
     * Send standardized JSON response
     * Consolidates response patterns from all API files
     */
    public static function sendResponse($success, $data = null, $message = '', $httpCode = 200, $errorCode = '')
    {
        http_response_code($httpCode);
        
        $response = [
            'success' => $success,
            'timestamp' => date('c'),
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN'
        ];

        if ($success) {
            $response['data'] = $data;
            if ($message) {
                $response['message'] = $message;
            }
        } else {
            $response['error'] = [
                'message' => $message,
                'code' => $errorCode,
                'http_code' => $httpCode
            ];
            // Log error for debugging
            error_log("API Error: $errorCode - $message");
        }

        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
        exit;
    }

    /**
     * Send success response
     */
    public static function sendSuccess($data = null, $message = '')
    {
        self::sendResponse(true, $data, $message, 200);
    }

    /**
     * Send error response with appropriate HTTP status code
     */
    public static function sendError($message, $httpCode = 500, $errorCode = '')
    {
        // Determine HTTP code based on error type if not specified
        if ($httpCode === 500) {
            if (strpos($message, 'validation') !== false || 
                strpos($message, 'required') !== false ||
                strpos($message, 'invalid') !== false) {
                $httpCode = 400;
            }
        }
        
        self::sendResponse(false, null, $message, $httpCode, $errorCode);
    }

    /**
     * Validate database connection
     * Consolidates connection checking from multiple API files
     */
    public static function validateConnection($conn)
    {
        if (!$conn) {
            self::sendError('Database connection error', 500, 'DB_CONNECTION_FAILED');
            return false;
        }
        return true;
    }

    /**
     * Safe database transaction wrapper
     * Consolidates transaction handling patterns
     */
    public static function executeTransaction($conn, $callback)
    {
        try {
            $conn->beginTransaction();
            $result = $callback($conn);
            $conn->commit();
            return $result;
        } catch (Exception $e) {
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            throw $e;
        }
    }

    /**
     * Validate required fields
     * Consolidates validation patterns from API files
     */
    public static function validateRequired($data, $requiredFields)
    {
        $missing = [];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || trim($data[$field]) === '') {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            $missingStr = implode(', ', $missing);
            self::sendError("الحقول التالية مطلوبة: $missingStr", 400, 'VALIDATION_FAILED');
            return false;
        }
        return true;
    }

    /**
     * Sanitize input data
     * Enhanced version of existing sanitization
     */
    public static function sanitizeInput($data)
    {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        
        if (!is_string($data)) {
            return $data;
        }
        
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        return $data;
    }

    /**
     * Validate and sanitize file upload
     * Consolidates file upload patterns
     */
    public static function validateFileUpload($file, $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'], $maxSize = 5242880)
    {
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return ['valid' => false, 'error' => 'ملف غير صحيح'];
        }

        // Check file size
        if ($file['size'] > $maxSize) {
            return ['valid' => false, 'error' => 'حجم الملف كبير جداً'];
        }

        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $allowedTypes)) {
            return ['valid' => false, 'error' => 'نوع الملف غير مسموح'];
        }

        // Check for malicious content
        $content = file_get_contents($file['tmp_name']);
        if (strpos($content, '<?php') !== false || strpos($content, '<script') !== false) {
            return ['valid' => false, 'error' => 'محتوى الملف غير آمن'];
        }

        return ['valid' => true];
    }

    /**
     * Generate secure filename
     */
    public static function generateSecureFilename($originalName)
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = bin2hex(random_bytes(16)) . '.' . $extension;
        return $filename;
    }

    /**
     * Handle pagination parameters
     * Consolidates pagination logic
     */
    public static function getPaginationParams($defaultLimit = 20, $maxLimit = 100)
    {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min($maxLimit, max(1, intval($_GET['limit'] ?? $defaultLimit)));
        $offset = ($page - 1) * $limit;
        
        return [
            'page' => $page,
            'limit' => $limit,
            'offset' => $offset
        ];
    }

    /**
     * Build pagination response
     */
    public static function buildPaginatedResponse($data, $total, $page, $limit)
    {
        $totalPages = ceil($total / $limit);
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_items' => $total,
                'items_per_page' => $limit,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ]
        ];
    }

    /**
     * Log API activity
     * Standardized logging for all API calls
     */
    public static function logActivity($action, $details = [], $userId = null)
    {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'action' => $action,
            'user_id' => $userId,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'details' => $details
        ];
        
        error_log('API Activity: ' . json_encode($logEntry, JSON_UNESCAPED_UNICODE));
    }

    /**
     * Rate limiting check
     * Basic rate limiting implementation
     */
    public static function checkRateLimit($action, $limit = 60, $window = 3600)
    {
        $key = $action . '_' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown');
        
        if (!isset($_SESSION['rate_limit'])) {
            $_SESSION['rate_limit'] = [];
        }
        
        $now = time();
        
        // Clean old entries
        $_SESSION['rate_limit'] = array_filter($_SESSION['rate_limit'], function($timestamp) use ($now, $window) {
            return ($now - $timestamp) < $window;
        });
        
        // Count requests for this action
        $requests = $_SESSION['rate_limit'][$key] ?? [];
        $count = count($requests);
        
        if ($count >= $limit) {
            self::sendError('تم تجاوز الحد المسموح من الطلبات', 429, 'RATE_LIMIT_EXCEEDED');
            return false;
        }
        
        // Add current request
        if (!isset($_SESSION['rate_limit'][$key])) {
            $_SESSION['rate_limit'][$key] = [];
        }
        $_SESSION['rate_limit'][$key][] = $now;
        
        return true;
    }

    /**
     * Validate JSON input
     * Consolidates JSON validation patterns
     */
    public static function getJsonInput()
    {
        $input = file_get_contents('php://input');
        if (empty($input)) {
            return $_POST; // Fallback to POST data
        }
        
        $data = json_decode($input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            self::sendError('بيانات JSON غير صالحة', 400, 'INVALID_JSON');
            return false;
        }
        
        return $data;
    }

    /**
     * Handle CORS headers
     * Standardized CORS handling
     */
    public static function handleCors()
    {
        // Allow from any origin
        if (isset($_SERVER['HTTP_ORIGIN'])) {
            header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
            header('Access-Control-Allow-Credentials: true');
            header('Access-Control-Max-Age: 86400');
        }

        // Access-Control headers are received during OPTIONS requests
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
                header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
            }
            if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
                header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
            }
            exit(0);
        }
    }

    /**
     * Database query helper with error handling
     * Consolidates query execution patterns
     */
    public static function executeQuery($conn, $sql, $params = [])
    {
        try {
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query error: " . $e->getMessage());
            error_log("SQL: " . $sql);
            error_log("Params: " . json_encode($params));
            throw new Exception('خطأ في قاعدة البيانات');
        }
    }

    /**
     * Get single record by ID
     * Common pattern across APIs
     */
    public static function getRecordById($conn, $table, $id, $idColumn = 'id')
    {
        $sql = "SELECT * FROM `$table` WHERE `$idColumn` = ?";
        $stmt = self::executeQuery($conn, $sql, [$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Check if record exists
     */
    public static function recordExists($conn, $table, $id, $idColumn = 'id')
    {
        $sql = "SELECT COUNT(*) FROM `$table` WHERE `$idColumn` = ?";
        $stmt = self::executeQuery($conn, $sql, [$id]);
        return $stmt->fetchColumn() > 0;
    }
}
?>
