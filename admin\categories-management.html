<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفئات - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Categories Management Content -->
    <div class="categories-management-content">
        <!-- Header Section -->
        <div class="categories-management-header">
            <div class="section-title-wrapper">
                <div class="section-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="section-title-content">
                    <h3 class="section-title">إدارة الفئات</h3>
                    <p class="section-subtitle">تنظيم وإدارة فئات المنتجات والخدمات</p>
                </div>
            </div>
            <div class="settings-summary">
                <div class="summary-item">
                    <span class="summary-label">إجمالي الفئات:</span>
                    <span class="summary-value" id="totalCategories">--</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">الفئات النشطة:</span>
                    <span class="summary-value" id="activeCategories">--</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">آخر تحديث:</span>
                    <span class="summary-value" id="lastUpdated">--</span>
                </div>
            </div>
        </div>

        <!-- Categories Management Tools -->
        <div class="settings-section">
            <div class="section-header">
                <h4 class="section-title">
                    <i class="fas fa-tools"></i>
                    أدوات إدارة الفئات
                </h4>
                <div class="tools-actions">
                    <button type="button" class="action-button" onclick="showAddCategoryModal()">
                        <i class="fas fa-plus"></i>
                        إضافة فئة جديدة
                    </button>
                    <button type="button" class="action-button" style="background: #28a745;" onclick="exportCategories()">
                        <i class="fas fa-download"></i>
                        تصدير الفئات
                    </button>
                    <button type="button" class="action-button" style="background: #17a2b8;" onclick="importCategories()">
                        <i class="fas fa-upload"></i>
                        استيراد الفئات
                    </button>
                </div>
            </div>
        </div>

        <!-- Categories Tree View -->
        <div class="settings-section">
            <div class="section-header">
                <h4 class="section-title">
                    <i class="fas fa-sitemap"></i>
                    هيكل الفئات
                </h4>
                <p class="section-description">عرض وتنظيم الفئات في شكل هرمي</p>
            </div>

            <div class="categories-tree-container">
                <div class="tree-controls">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="expandAllCategories()">
                        <i class="fas fa-expand-arrows-alt"></i>
                        توسيع الكل
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAllCategories()">
                        <i class="fas fa-compress-arrows-alt"></i>
                        طي الكل
                    </button>
                    <div class="search-categories">
                        <input type="text" class="form-control" id="categorySearch" placeholder="البحث في الفئات...">
                        <i class="fas fa-search"></i>
                    </div>
                </div>

                <div class="categories-tree" id="categoriesTree">
                    <!-- Categories will be loaded here dynamically -->
                    <div class="tree-loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        جاري تحميل الفئات...
                    </div>
                </div>
            </div>
        </div>

        <!-- Categories Table -->
        <div class="settings-section">
            <div class="section-header">
                <h4 class="section-title">
                    <i class="fas fa-table"></i>
                    قائمة الفئات
                </h4>
                <div class="table-controls">
                    <div class="entries-per-page">
                        <label>عرض</label>
                        <select id="entriesPerPage" onchange="changeEntriesPerPage()">
                            <option value="10">10</option>
                            <option value="25" selected>25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <label>فئة</label>
                    </div>
                    <div class="table-search">
                        <input type="text" id="tableSearch" placeholder="البحث..." onkeyup="searchCategories()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>الاسم</th>
                            <th>الفئة الأب</th>
                            <th>عدد المنتجات</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="categoriesTableBody">
                        <!-- Categories will be loaded here -->
                        <tr>
                            <td colspan="7" class="text-center">
                                <div class="loading-spinner">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    جاري تحميل البيانات...
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="table-pagination">
                <div class="pagination-info">
                    <span id="paginationInfo">عرض 0 من 0</span>
                </div>
                <div class="pagination-controls">
                    <button type="button" class="btn btn-sm" id="prevPage" onclick="changePage(-1)">
                        <i class="fas fa-chevron-right"></i>
                        السابق
                    </button>
                    <span class="page-numbers" id="pageNumbers"></span>
                    <button type="button" class="btn btn-sm" id="nextPage" onclick="changePage(1)">
                        التالي
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="bulk-actions" id="bulkActions" style="display: none;">
                <div class="bulk-actions-content">
                    <span class="bulk-count">تم تحديد <span id="selectedCount">0</span> فئة</span>
                    <div class="bulk-buttons">
                        <button type="button" class="btn btn-success btn-sm" onclick="bulkActivateCategories()">
                            <i class="fas fa-check"></i>
                            تفعيل
                        </button>
                        <button type="button" class="btn btn-warning btn-sm" onclick="bulkDeactivateCategories()">
                            <i class="fas fa-pause"></i>
                            إلغاء تفعيل
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="bulkDeleteCategories()">
                            <i class="fas fa-trash"></i>
                            حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Actions -->
        <div class="save-actions">
            <button type="button" class="enhanced-save-btn" onclick="saveCategories()">
                <i class="fas fa-save"></i>
                حفظ التغييرات
            </button>
            <button type="button" class="enhanced-reset-btn" onclick="resetCategories()">
                <i class="fas fa-undo"></i>
                إعادة تعيين
            </button>
        </div>
    </div>

    <!-- Add/Edit Category Modal -->
    <div class="enhanced-modal" id="categoryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة فئة جديدة</h3>
                <button type="button" class="modal-close" onclick="closeCategoryModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <div class="form-group">
                        <label for="categoryName" class="enhanced-label">
                            <i class="fas fa-tag"></i>
                            اسم الفئة
                        </label>
                        <input type="text" id="categoryName" class="enhanced-input" required>
                    </div>
                    <div class="form-group">
                        <label for="categorySlug" class="enhanced-label">
                            <i class="fas fa-link"></i>
                            الرابط المختصر (Slug)
                        </label>
                        <input type="text" id="categorySlug" class="enhanced-input" dir="ltr">
                    </div>
                    <div class="form-group">
                        <label for="parentCategory" class="enhanced-label">
                            <i class="fas fa-sitemap"></i>
                            الفئة الأب
                        </label>
                        <select id="parentCategory" class="enhanced-select">
                            <option value="">-- فئة رئيسية --</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="categoryDescription" class="enhanced-label">
                            <i class="fas fa-align-right"></i>
                            الوصف
                        </label>
                        <textarea id="categoryDescription" class="enhanced-input" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="categoryImage" class="enhanced-label">
                            <i class="fas fa-image"></i>
                            صورة الفئة
                        </label>
                        <input type="file" id="categoryImage" class="enhanced-input" accept="image/*">
                    </div>
                    <div class="form-group">
                        <label class="enhanced-label">
                            <i class="fas fa-toggle-on"></i>
                            حالة الفئة
                        </label>
                        <label class="switch">
                            <input type="checkbox" id="categoryStatus" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeCategoryModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCategory()">حفظ</button>
            </div>
        </div>
    </div>

    <script src="js/categories-management.js"></script>
</body>
</html>
