// Product View More functionality
document.addEventListener('DOMContentLoaded', () => {
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        const productId = card.dataset.productId;
        
        // Check if product has landing page
        fetch(`php/api/product-landing.php?action=get_landing&productId=${productId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.hasLandingPage && data.data.landingPageEnabled) {
                    // Add View More link
                    const viewMoreLink = document.createElement('a');
                    viewMoreLink.href = `product/${data.data.slug}`;
                    viewMoreLink.className = 'view-more-link';
                    viewMoreLink.innerHTML = `
                        <i class="fas fa-external-link-alt"></i>
                        عرض المزيد
                    `;
                    
                    // Add social share buttons
                    const shareButtons = document.createElement('div');
                    shareButtons.className = 'social-share';
                    
                    const productUrl = `${window.location.origin}/product/${data.data.slug}`;
                    const productTitle = card.querySelector('.product-title').textContent;
                    
                    shareButtons.innerHTML = `
                        <a href="https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(productUrl)}"
                           target="_blank"
                           class="share-button facebook-share"
                           title="مشاركة على فيسبوك">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="https://api.whatsapp.com/send?text=${encodeURIComponent(productTitle + ' - ' + productUrl)}"
                           target="_blank"
                           class="share-button whatsapp-share"
                           title="مشاركة على واتساب">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    `;
                    
                    const cardActions = card.querySelector('.product-actions') || card.querySelector('.card-footer');
                    if (cardActions) {
                        cardActions.appendChild(viewMoreLink);
                        cardActions.appendChild(shareButtons);
                    }
                }
            })
            .catch(error => console.error('Error checking product landing page:', error));
    });
});