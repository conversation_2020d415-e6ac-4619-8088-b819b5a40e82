-- Add landing page columns to produits table
ALTER TABLE produits
ADD COLUMN has_landing_page BOOLEAN DEFAULT FALSE,
    ADD COLUMN landing_page_enabled BOOLEAN DEFAULT FALSE,
    ADD COLUMN slug VARCHAR(255) UNIQUE,
    ADD INDEX idx_slug (slug);
-- Create table for product images
CREATE TABLE IF NOT EXISTS product_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES produits(id) ON DELETE CASCADE,
    INDEX idx_product_images (product_id, sort_order)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Create table for product content blocks
CREATE TABLE IF NOT EXISTS product_content_blocks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    title VARCHAR(255),
    content TEXT,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES produits(id) ON DELETE CASCADE,
    INDEX idx_product_blocks (product_id, sort_order)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Generate slugs for existing products
UPDATE produits
SET slug = LOWER(
        REGEXP_REPLACE(
            REGEXP_REPLACE(
                titre,
                '[^a-zA-Z0-9\\s-]',
                ''
            ),
            '\\s+',
            '-'
        )
    )
WHERE slug IS NULL;
-- Create directory for product gallery images if it doesn't exist
SET @upload_dir = 'uploads/products/gallery';
SET @create_dir_query = CONCAT(
        'SELECT IF(NOT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = \'product_images\'), \'mkdir -p ',
        @upload_dir,
        '\', NULL) INTO @create_dir_cmd'
    );
PREPARE stmt
FROM @create_dir_query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
-- Set appropriate permissions on the upload directory
SET @chmod_query = CONCAT(
        'SELECT IF(NOT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = \'product_images\'), \'chmod 755 ',
        @upload_dir,
        '\', NULL) INTO @chmod_cmd'
    );
PREPARE stmt
FROM @chmod_query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
