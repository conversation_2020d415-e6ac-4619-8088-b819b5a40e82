<?php
/**
 * Security Headers Management for XSS and CSRF Protection
 * Provides comprehensive security headers for the Mossaab Landing Page project
 */

// Prevent direct access
if (!defined('SECURITY_CHECK')) {
    die('Direct access not allowed');
}

class SecurityHeaders
{
    /**
     * Set comprehensive security headers
     */
    public static function setSecurityHeaders($isAdmin = false)
    {
        // Prevent caching of sensitive pages
        if ($isAdmin) {
            header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
            header('Pragma: no-cache');
            header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
        }

        // Content Security Policy
        self::setContentSecurityPolicy($isAdmin);

        // XSS Protection
        header('X-XSS-Protection: 1; mode=block');

        // Content Type Options
        header('X-Content-Type-Options: nosniff');

        // Frame Options
        header('X-Frame-Options: DENY');

        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');

        // HTTPS Strict Transport Security (if using HTTPS)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }

        // Permissions Policy (formerly Feature Policy)
        header('Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()');
    }

    /**
     * Set Content Security Policy based on context
     */
    private static function setContentSecurityPolicy($isAdmin = false)
    {
        $baseUrl = self::getBaseUrl();
        
        if ($isAdmin) {
            // More permissive CSP for admin panel (TinyMCE, etc.)
            $csp = [
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' cdn.tiny.cloud",
                "style-src 'self' 'unsafe-inline' fonts.googleapis.com cdn.tiny.cloud",
                "font-src 'self' fonts.gstatic.com",
                "img-src 'self' data: blob: cdn.tiny.cloud",
                "connect-src 'self' cdn.tiny.cloud",
                "frame-src 'none'",
                "object-src 'none'",
                "base-uri 'self'",
                "form-action 'self'"
            ];
        } else {
            // Strict CSP for public pages
            $csp = [
                "default-src 'self'",
                "script-src 'self' 'sha256-" . base64_encode(hash('sha256', 'console.log', true)) . "'",
                "style-src 'self' 'unsafe-inline' fonts.googleapis.com",
                "font-src 'self' fonts.gstatic.com",
                "img-src 'self' data:",
                "connect-src 'self'",
                "frame-src 'none'",
                "object-src 'none'",
                "base-uri 'self'",
                "form-action 'self'"
            ];
        }

        header('Content-Security-Policy: ' . implode('; ', $csp));
    }

    /**
     * Get base URL for CSP
     */
    private static function getBaseUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host;
    }

    /**
     * Sanitize output for JSON responses with XSS protection
     */
    public static function sanitizeJsonOutput($data)
    {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeJsonOutput'], $data);
        }
        
        if (is_string($data)) {
            // Encode HTML entities and JavaScript-dangerous characters
            $data = htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8', false);
            
            // Additional protection against JSON injection
            $data = str_replace(['</', '<script', '</script'], ['<\/', '<\script', '<\/script'], $data);
        }
        
        return $data;
    }

    /**
     * Generate secure JSON response with proper headers
     */
    public static function sendSecureJsonResponse($data, $httpCode = 200)
    {
        // Set JSON content type with charset
        header('Content-Type: application/json; charset=utf-8');
        
        // Set security headers
        self::setSecurityHeaders();
        
        // Set HTTP response code
        http_response_code($httpCode);
        
        // Sanitize data before encoding
        $sanitizedData = self::sanitizeJsonOutput($data);
        
        // Encode with security flags
        $json = json_encode($sanitizedData, 
            JSON_UNESCAPED_UNICODE | 
            JSON_HEX_TAG | 
            JSON_HEX_APOS | 
            JSON_HEX_QUOT | 
            JSON_HEX_AMP
        );
        
        if ($json === false) {
            // JSON encoding failed
            $error = [
                'success' => false,
                'message' => 'خطأ في تشفير البيانات',
                'error_code' => 'JSON_ENCODING_ERROR'
            ];
            
            echo json_encode($error, JSON_UNESCAPED_UNICODE);
        } else {
            echo $json;
        }
    }

    /**
     * Validate and sanitize HTML content for rich text fields
     */
    public static function sanitizeRichText($html)
    {
        // Allowed HTML tags for Arabic content
        $allowedTags = [
            'p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote',
            'a', 'span', 'div'
        ];
        
        // Allowed attributes
        $allowedAttributes = [
            'href' => true,
            'title' => true,
            'class' => true,
            'id' => true,
            'dir' => true, // For RTL support
            'lang' => true
        ];
        
        // Strip all tags except allowed ones
        $html = strip_tags($html, '<' . implode('><', $allowedTags) . '>');
        
        // Remove dangerous attributes and JavaScript
        $html = preg_replace('/on\w+\s*=\s*["\'][^"\']*["\']/i', '', $html);
        $html = preg_replace('/javascript\s*:/i', '', $html);
        $html = preg_replace('/vbscript\s*:/i', '', $html);
        $html = preg_replace('/data\s*:/i', '', $html);
        
        // Validate href attributes
        $html = preg_replace_callback('/href\s*=\s*["\']([^"\']*)["\']/', function($matches) {
            $url = $matches[1];
            
            // Allow only safe URLs
            if (filter_var($url, FILTER_VALIDATE_URL) || 
                preg_match('/^\/[^\/]/', $url) || // Relative URLs starting with /
                preg_match('/^#/', $url)) { // Anchor links
                return 'href="' . htmlspecialchars($url, ENT_QUOTES, 'UTF-8') . '"';
            }
            
            return ''; // Remove invalid URLs
        }, $html);
        
        return $html;
    }

    /**
     * Generate nonce for inline scripts/styles
     */
    public static function generateNonce()
    {
        return base64_encode(random_bytes(16));
    }

    /**
     * Set CSP with nonce for specific inline content
     */
    public static function setCSPWithNonce($nonce, $isAdmin = false)
    {
        $baseUrl = self::getBaseUrl();
        
        if ($isAdmin) {
            $csp = [
                "default-src 'self'",
                "script-src 'self' 'nonce-{$nonce}' 'unsafe-eval' cdn.tiny.cloud",
                "style-src 'self' 'nonce-{$nonce}' fonts.googleapis.com cdn.tiny.cloud",
                "font-src 'self' fonts.gstatic.com",
                "img-src 'self' data: blob: cdn.tiny.cloud",
                "connect-src 'self' cdn.tiny.cloud",
                "frame-src 'none'",
                "object-src 'none'",
                "base-uri 'self'",
                "form-action 'self'"
            ];
        } else {
            $csp = [
                "default-src 'self'",
                "script-src 'self' 'nonce-{$nonce}'",
                "style-src 'self' 'nonce-{$nonce}' fonts.googleapis.com",
                "font-src 'self' fonts.gstatic.com",
                "img-src 'self' data:",
                "connect-src 'self'",
                "frame-src 'none'",
                "object-src 'none'",
                "base-uri 'self'",
                "form-action 'self'"
            ];
        }

        header('Content-Security-Policy: ' . implode('; ', $csp));
    }

    /**
     * Check if request is from admin area
     */
    public static function isAdminRequest()
    {
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($requestUri, '/admin/') !== false;
    }

    /**
     * Initialize security headers based on context
     */
    public static function init()
    {
        $isAdmin = self::isAdminRequest();
        self::setSecurityHeaders($isAdmin);
    }
}

// Auto-initialize security headers if not in CLI mode
if (php_sapi_name() !== 'cli' && !headers_sent()) {
    SecurityHeaders::init();
}
?>
