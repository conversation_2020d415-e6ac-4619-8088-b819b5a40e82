<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Button Responsiveness</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
        }
        
        .content-section {
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .content-section.active {
            display: block;
            opacity: 1;
        }
        
        .action-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px;
            transition: all 0.2s ease;
        }
        
        .action-button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .test-results {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .success {
            color: #28a745;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .nav-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>اختبار استجابة الأزرار - صفحات الهبوط</h1>
    
    <div class="test-results" id="testResults">
        <h3>نتائج الاختبار:</h3>
        <div id="results"></div>
    </div>
    
    <div>
        <button class="nav-button" onclick="showSection('landingPages')">عرض قسم صفحات الهبوط</button>
        <button class="nav-button" onclick="hideSection('landingPages')">إخفاء قسم صفحات الهبوط</button>
        <button class="nav-button" onclick="testButtonResponsiveness()">اختبار استجابة الأزرار</button>
    </div>
    
    <!-- Landing Pages Section (initially hidden like in admin panel) -->
    <section id="landingPages" class="content-section">
        <h2>صفحات هبوط</h2>
        <div class="section-header">
            <button id="addLandingPageBtn" class="action-button" onclick="safeAddLandingPage()">
                <i class="fas fa-plus"></i> أضف صفحة هبوط
            </button>
            <button id="testLandingPageBtn" class="action-button" onclick="safeTestLandingPageModal()">
                <i class="fas fa-bug"></i> اختبار المودال
            </button>
        </div>
        
        <!-- Mock landing page cards -->
        <div id="landingPagesContainer">
            <div class="landing-page-card" style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px;">
                <h4>صفحة هبوط تجريبية</h4>
                <div class="page-actions" style="display: flex; gap: 10px; margin-top: 10px;">
                    <button onclick="safeLandingPagesCall('clonePage', 1)" class="clone-btn action-button" style="background: #17a2b8;">
                        نسخ
                    </button>
                    <button onclick="safeLandingPagesCall('editPage', 1)" class="edit-btn action-button" style="background: #ffc107; color: #333;">
                        تعديل
                    </button>
                    <button onclick="safeLandingPagesCall('deletePage', 1)" class="delete-btn action-button" style="background: #dc3545;">
                        حذف
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Mock Modal -->
    <div id="landingPageModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 9999;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 500px; width: 90%;">
            <h3>مودال صفحة الهبوط</h3>
            <p>تم فتح المودال بنجاح!</p>
            <button onclick="closeModal()" class="action-button">إغلاق</button>
        </div>
    </div>

    <script>
        // Mock landing pages manager object
        const landingPagesManager = {
            initialized: false,
            globalDelegationSetup: false,
            
            init() {
                console.log('🚀 Initializing Landing Pages Manager...');
                this.bindEventsImmediate();
                this.initialized = true;
                console.log('✅ Landing Pages Manager initialized');
            },
            
            bindEventsImmediate() {
                console.log('🔗 Binding events immediately...');

                // Use event delegation on document body for add button
                document.body.addEventListener('click', (e) => {
                    if (e.target && (e.target.id === 'addLandingPageBtn' || e.target.closest('#addLandingPageBtn'))) {
                        e.preventDefault();
                        console.log('🖱️ Add landing page button clicked via delegation!');
                        this.openModal();
                    }
                });

                // Setup global delegation for action buttons
                this.setupGlobalActionDelegation();
            },
            
            setupGlobalActionDelegation() {
                if (this.globalDelegationSetup) return;
                
                console.log('🌐 Setting up global action delegation...');
                
                document.body.addEventListener('click', (e) => {
                    const button = e.target.closest('button');
                    if (!button) return;
                    
                    if (button.classList.contains('clone-btn') || 
                        button.classList.contains('edit-btn') || 
                        button.classList.contains('delete-btn')) {
                        
                        e.preventDefault();
                        console.log('🖱️ Action button clicked via global delegation:', button.className);
                        this.handleActionButtonClick(e);
                    }
                });
                
                this.globalDelegationSetup = true;
                console.log('✅ Global action delegation setup complete');
            },
            
            handleActionButtonClick(event) {
                const button = event.target.closest('button');
                if (!button) return;
                
                if (button.classList.contains('clone-btn')) {
                    console.log('Clone action triggered');
                    logResult('✅ زر النسخ يعمل بشكل صحيح', 'success');
                } else if (button.classList.contains('edit-btn')) {
                    console.log('Edit action triggered');
                    logResult('✅ زر التعديل يعمل بشكل صحيح', 'success');
                } else if (button.classList.contains('delete-btn')) {
                    console.log('Delete action triggered');
                    logResult('✅ زر الحذف يعمل بشكل صحيح', 'success');
                }
            },
            
            openModal() {
                console.log('🚀 Opening modal...');
                const modal = document.getElementById('landingPageModal');
                if (modal) {
                    modal.style.display = 'block';
                    logResult('✅ زر إضافة صفحة هبوط يعمل بشكل صحيح - تم فتح المودال', 'success');
                } else {
                    logResult('❌ لم يتم العثور على المودال', 'error');
                }
            }
        };
        
        // Safe wrapper functions
        window.safeLandingPagesCall = function(methodName, ...args) {
            if (typeof landingPagesManager !== 'undefined') {
                if (!landingPagesManager.initialized) {
                    landingPagesManager.init();
                }
                
                if (landingPagesManager[methodName]) {
                    return landingPagesManager[methodName](...args);
                } else {
                    console.error(`Method ${methodName} not found`);
                    logResult(`❌ الوظيفة ${methodName} غير موجودة`, 'error');
                }
            }
        };
        
        window.safeAddLandingPage = function() {
            console.log('🚀 Safe add landing page called...');
            return window.safeLandingPagesCall('openModal');
        };
        
        window.safeTestLandingPageModal = function() {
            console.log('🧪 Test modal called...');
            logResult('✅ زر اختبار المودال يعمل بشكل صحيح', 'success');
        };
        
        // Utility functions
        function showSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.classList.add('active');
                logResult('✅ تم عرض القسم', 'success');
            }
        }
        
        function hideSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.classList.remove('active');
                logResult('⚠️ تم إخفاء القسم', 'success');
            }
        }
        
        function closeModal() {
            const modal = document.getElementById('landingPageModal');
            if (modal) {
                modal.style.display = 'none';
                logResult('✅ تم إغلاق المودال', 'success');
            }
        }
        
        function logResult(message, type) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            results.appendChild(div);
        }
        
        function testButtonResponsiveness() {
            logResult('🧪 بدء اختبار استجابة الأزرار...', 'success');
            
            // Test 1: Button works when section is hidden
            hideSection('landingPages');
            setTimeout(() => {
                const addBtn = document.getElementById('addLandingPageBtn');
                if (addBtn) {
                    addBtn.click();
                    logResult('✅ اختبار 1: الزر يعمل عندما يكون القسم مخفي', 'success');
                } else {
                    logResult('❌ اختبار 1: لم يتم العثور على الزر', 'error');
                }
            }, 100);
            
            // Test 2: Button works when section is visible
            setTimeout(() => {
                showSection('landingPages');
                setTimeout(() => {
                    const addBtn = document.getElementById('addLandingPageBtn');
                    if (addBtn) {
                        addBtn.click();
                        logResult('✅ اختبار 2: الزر يعمل عندما يكون القسم مرئي', 'success');
                    }
                }, 100);
            }, 500);
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            landingPagesManager.init();
            logResult('✅ تم تحميل الصفحة وتهيئة النظام', 'success');
        });
    </script>
</body>
</html>
