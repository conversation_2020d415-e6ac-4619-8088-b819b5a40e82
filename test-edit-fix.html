<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح زر التعديل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .result {
            background: #fff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button {
            background: #ffc107;
            color: #333;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #e0a800; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 اختبار إصلاح زر التعديل</h1>
    
    <div class="test-section">
        <h2>1. اختبار notificationManager</h2>
        <button onclick="testNotificationManager()">اختبار الإشعارات</button>
        <div id="notifResults"></div>
    </div>

    <div class="test-section">
        <h2>2. اختبار وظيفة editPage المحدثة</h2>
        <button onclick="testEditPageFunction(2)">اختبار editPage(2)</button>
        <button onclick="testEditPageFunction(999)">اختبار editPage(999) - خطأ</button>
        <div id="editResults"></div>
    </div>

    <div class="test-section">
        <h2>3. اختبار API مباشرة</h2>
        <button onclick="testAPI(2)">اختبار API للصفحة 2</button>
        <div id="apiResults"></div>
    </div>

    <script>
        // Mock notificationManager for testing
        const notificationManager = {
            showSuccess(message) {
                this.showNotification(message, 'success');
            },
            showError(message) {
                this.showNotification(message, 'error');
            },
            showInfo(message) {
                this.showNotification(message, 'info');
            },
            showNotification(message, type) {
                console.log(`${type.toUpperCase()}: ${message}`);
                const container = document.getElementById('notifResults');
                if (container) {
                    const div = document.createElement('div');
                    div.className = `result ${type}`;
                    div.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
                    container.appendChild(div);
                }
            }
        };

        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(div);
            console.log(message);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // Test 1: Notification Manager
        function testNotificationManager() {
            clearResults('notifResults');
            
            log('notifResults', 'اختبار notificationManager...', 'info');
            
            try {
                notificationManager.showSuccess('رسالة نجاح تجريبية');
                notificationManager.showError('رسالة خطأ تجريبية');
                notificationManager.showInfo('رسالة معلومات تجريبية');
                
                log('notifResults', '✅ جميع وظائف الإشعارات تعمل بشكل صحيح', 'success');
            } catch (error) {
                log('notifResults', `❌ خطأ في notificationManager: ${error.message}`, 'error');
            }
        }

        // Test 2: Simulate editPage function
        async function testEditPageFunction(pageId) {
            clearResults('editResults');
            
            log('editResults', `🖊️ اختبار editPage للصفحة ID=${pageId}...`, 'info');
            
            // Safe notification function (same as in the fixed code)
            const showNotification = (message, type = 'info') => {
                try {
                    if (typeof notificationManager !== 'undefined' && notificationManager[`show${type.charAt(0).toUpperCase() + type.slice(1)}`]) {
                        notificationManager[`show${type.charAt(0).toUpperCase() + type.slice(1)}`](message);
                        log('editResults', `✅ إشعار ${type}: ${message}`, 'success');
                    } else {
                        log('editResults', `⚠️ إشعار ${type} (fallback): ${message}`, 'info');
                    }
                } catch (notifError) {
                    log('editResults', `⚠️ إشعار ${type} (console): ${message}`, 'info');
                }
            };
            
            try {
                showNotification('جاري تحميل بيانات الصفحة...', 'info');
                
                const response = await fetch(`php/api/landing-pages.php?id=${pageId}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const text = await response.text();
                
                if (!text.trim()) {
                    throw new Error('الاستجابة فارغة من الخادم');
                }
                
                let result;
                try {
                    result = JSON.parse(text);
                } catch (parseError) {
                    throw new Error('استجابة غير صحيحة من الخادم');
                }
                
                let pageData = null;
                if (result.success && result.data) {
                    pageData = result.data;
                } else if (result.data) {
                    pageData = result.data;
                } else {
                    throw new Error('لم يتم العثور على بيانات الصفحة');
                }
                
                log('editResults', `✅ تم تحميل بيانات الصفحة: ${pageData.titre}`, 'success');
                showNotification('تم تحميل البيانات بنجاح', 'success');
                
            } catch (error) {
                log('editResults', `❌ خطأ في editPage: ${error.message}`, 'error');
                showNotification('حدث خطأ أثناء تحميل بيانات الصفحة: ' + error.message, 'error');
            }
        }

        // Test 3: Direct API test
        async function testAPI(pageId) {
            clearResults('apiResults');
            
            try {
                log('apiResults', `📡 اختبار API للصفحة ID=${pageId}...`, 'info');
                
                const response = await fetch(`php/api/landing-pages.php?id=${pageId}`);
                log('apiResults', `📡 حالة الاستجابة: ${response.status}`, response.ok ? 'success' : 'error');
                
                const text = await response.text();
                log('apiResults', `📄 طول الاستجابة: ${text.length} حرف`, 'info');
                
                if (response.ok && text.trim()) {
                    const data = JSON.parse(text);
                    if (data.success && data.data) {
                        log('apiResults', `✅ API يعمل بشكل صحيح: ${data.data.titre}`, 'success');
                    } else {
                        log('apiResults', '⚠️ API يعمل لكن البيانات غير متوقعة', 'info');
                    }
                } else {
                    log('apiResults', '❌ مشكلة في API', 'error');
                }
                
            } catch (error) {
                log('apiResults', `❌ خطأ في API: ${error.message}`, 'error');
            }
        }

        // Auto-run tests
        window.addEventListener('load', () => {
            console.log('🚀 بدء الاختبارات التلقائية...');
            testNotificationManager();
            setTimeout(() => testAPI(2), 1000);
        });
    </script>
</body>
</html>
