// Récupérer les éléments du panier depuis le localStorage
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Afficher les articles dans le résumé de la commande
function displayOrderItems() {
    const orderItems = document.getElementById('orderItems');
    const subtotalElement = document.getElementById('subtotal');
    const totalElement = document.getElementById('total');

    // Vider le conteneur
    orderItems.innerHTML = '';

    // Calculer le total
    let subtotal = 0;

    // Ajouter chaque article
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        subtotal += itemTotal;

        const orderItem = document.createElement('div');
        orderItem.className = 'order-item';
        orderItem.innerHTML = `
            <img src="${item.image}" alt="${item.title}">
            <div class="item-info">
                <h4>${item.title}</h4>
                <span class="quantity">الكمية: ${item.quantity}</span>
            </div>
            <div class="item-price">${itemTotal} دج</div>
        `;

        orderItems.appendChild(orderItem);
    });

    // Mettre à jour les totaux
    const shipping = 500; // Frais de livraison fixes
    const total = subtotal + shipping;

    subtotalElement.textContent = `${subtotal} دج`;
    totalElement.textContent = `${total} دج`;
}

// Gérer la soumission du formulaire
document.getElementById('orderForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Récupérer les données du formulaire
    const formData = {
        name: document.getElementById('name').value,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        address: document.getElementById('address').value,
        items: cart,
        total: parseFloat(document.getElementById('total').textContent),
        orderDate: new Date().toISOString()
    };

    // Enregistrer la commande dans le localStorage (simulation)
    const orders = JSON.parse(localStorage.getItem('orders')) || [];
    orders.push(formData);
    localStorage.setItem('orders', JSON.stringify(orders));

    // Vider le panier
    localStorage.removeItem('cart');
    cart = [];

    // Afficher le modal de confirmation
    showConfirmationModal();
});

// Afficher le modal de confirmation
function showConfirmationModal() {
    const modal = document.getElementById('confirmationModal');
    modal.style.display = 'flex';

    // Fermer le modal en cliquant en dehors
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            window.location.href = 'index.html';
        }
    });
}

// Validation du formulaire en temps réel
const formInputs = document.querySelectorAll('#orderForm input, #orderForm textarea');

formInputs.forEach(input => {
    input.addEventListener('input', function() {
        validateInput(this);
    });
});

function validateInput(input) {
    const value = input.value.trim();
    
    switch(input.id) {
        case 'phone':
            // Validation du numéro de téléphone algérien
            const phoneRegex = /^(0|\+213)[5-7][0-9]{8}$/;
            input.setCustomValidity(phoneRegex.test(value) ? '' : 'يرجى إدخال رقم هاتف صحيح');
            break;
            
        case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            input.setCustomValidity(emailRegex.test(value) ? '' : 'يرجى إدخال بريد إلكتروني صحيح');
            break;
            
        case 'name':
            input.setCustomValidity(value.length >= 3 ? '' : 'يجب أن يحتوي الاسم على 3 أحرف على الأقل');
            break;
            
        case 'address':
            input.setCustomValidity(value.length >= 10 ? '' : 'يرجى إدخال عنوان مفصل');
            break;
    }
}

// Initialiser l'affichage au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    displayOrderItems();
});