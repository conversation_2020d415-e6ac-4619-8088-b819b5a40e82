<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - متجر مصعب</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="auth-box register-box">
            <div class="auth-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <h1>متجر مصعب</h1>
                </div>
                <h2>إنشاء حساب جديد</h2>
                <p>انضم إلينا واستمتع بتجربة تسوق مميزة</p>
            </div>

            <form id="registerForm" class="auth-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">
                            <i class="fas fa-user"></i>
                            الاسم الأول
                        </label>
                        <input type="text" id="firstName" name="first_name" required placeholder="أدخل اسمك الأول">
                    </div>
                    <div class="form-group">
                        <label for="lastName">
                            <i class="fas fa-user"></i>
                            الاسم الأخير
                        </label>
                        <input type="text" id="lastName" name="last_name" required placeholder="أدخل اسمك الأخير">
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i>
                        البريد الإلكتروني
                    </label>
                    <input type="email" id="email" name="email" required placeholder="أدخل بريدك الإلكتروني">
                </div>

                <div class="form-group">
                    <label for="phone">
                        <i class="fas fa-phone"></i>
                        رقم الهاتف (اختياري)
                    </label>
                    <input type="tel" id="phone" name="phone" placeholder="أدخل رقم هاتفك">
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="password-input">
                        <input type="password" id="password" name="password" required placeholder="أدخل كلمة المرور">
                        <button type="button" class="password-toggle" onclick="togglePassword('password', 'passwordIcon')">
                            <i class="fas fa-eye" id="passwordIcon"></i>
                        </button>
                    </div>
                    <div class="password-strength" id="passwordStrength"></div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">
                        <i class="fas fa-lock"></i>
                        تأكيد كلمة المرور
                    </label>
                    <div class="password-input">
                        <input type="password" id="confirmPassword" name="confirm_password" required placeholder="أعد إدخال كلمة المرور">
                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword', 'confirmPasswordIcon')">
                            <i class="fas fa-eye" id="confirmPasswordIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="checkbox-container">
                        <input type="checkbox" id="agreeTerms" required>
                        <span class="checkmark"></span>
                        أوافق على <a href="#" target="_blank">الشروط والأحكام</a> و <a href="#" target="_blank">سياسة الخصوصية</a>
                    </label>
                </div>

                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>

                <button type="submit" class="auth-button" id="registerButton">
                    <i class="fas fa-user-plus"></i>
                    إنشاء الحساب
                </button>
            </form>

            <div class="auth-divider">
                <span>أو</span>
            </div>

            <div class="auth-links">
                <p>لديك حساب بالفعل؟ <a href="login.html">تسجيل الدخول</a></p>
            </div>

            <div class="back-to-site">
                <a href="index.html">
                    <i class="fas fa-arrow-right"></i>
                    العودة إلى المتجر
                </a>
            </div>
        </div>
    </div>

    <script>
        // Toggle password visibility
        function togglePassword(inputId, iconId) {
            const passwordInput = document.getElementById(inputId);
            const passwordIcon = document.getElementById(iconId);
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }

        // Check password strength
        function checkPasswordStrength(password) {
            const strengthDiv = document.getElementById('passwordStrength');
            let strength = 0;
            let feedback = [];

            if (password.length >= 8) strength++;
            else feedback.push('8 أحرف على الأقل');

            if (/[a-z]/.test(password)) strength++;
            else feedback.push('حرف صغير');

            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('حرف كبير');

            if (/[0-9]/.test(password)) strength++;
            else feedback.push('رقم');

            if (/[^A-Za-z0-9]/.test(password)) strength++;
            else feedback.push('رمز خاص');

            const strengthLevels = ['ضعيفة جداً', 'ضعيفة', 'متوسطة', 'قوية', 'قوية جداً'];
            const strengthColors = ['#ff4757', '#ff6b7a', '#ffa502', '#2ed573', '#1dd1a1'];

            if (password.length > 0) {
                strengthDiv.innerHTML = `
                    <div class="strength-bar">
                        <div class="strength-fill" style="width: ${(strength/5)*100}%; background-color: ${strengthColors[strength-1] || '#ff4757'}"></div>
                    </div>
                    <div class="strength-text" style="color: ${strengthColors[strength-1] || '#ff4757'}">
                        ${strengthLevels[strength-1] || 'ضعيفة جداً'}
                        ${feedback.length > 0 ? ' - مطلوب: ' + feedback.join(', ') : ''}
                    </div>
                `;
            } else {
                strengthDiv.innerHTML = '';
            }

            return strength;
        }

        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });

        // Handle form submission
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                first_name: document.getElementById('firstName').value.trim(),
                last_name: document.getElementById('lastName').value.trim(),
                email: document.getElementById('email').value.trim(),
                phone: document.getElementById('phone').value.trim(),
                password: document.getElementById('password').value,
                confirm_password: document.getElementById('confirmPassword').value
            };

            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');
            const registerButton = document.getElementById('registerButton');

            // Clear previous messages
            errorDiv.textContent = '';
            successDiv.textContent = '';

            // Validate inputs
            if (!formData.first_name || !formData.last_name || !formData.email || !formData.password) {
                errorDiv.textContent = 'جميع الحقول المطلوبة يجب ملؤها';
                return;
            }

            if (formData.password !== formData.confirm_password) {
                errorDiv.textContent = 'كلمتا المرور غير متطابقتان';
                return;
            }

            if (formData.password.length < 8) {
                errorDiv.textContent = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
                return;
            }

            if (!document.getElementById('agreeTerms').checked) {
                errorDiv.textContent = 'يجب الموافقة على الشروط والأحكام';
                return;
            }

            // Show loading state
            registerButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء الحساب...';
            registerButton.disabled = true;

            try {
                const response = await fetch('php/api/user-auth.php?action=register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (data.success) {
                    successDiv.textContent = data.message;
                    
                    // Redirect to login page after successful registration
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                } else {
                    errorDiv.textContent = data.message || 'حدث خطأ في إنشاء الحساب';
                }
            } catch (error) {
                console.error('Registration error:', error);
                errorDiv.textContent = 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';
            } finally {
                // Reset button state
                registerButton.innerHTML = '<i class="fas fa-user-plus"></i> إنشاء الحساب';
                registerButton.disabled = false;
            }
        });
    </script>
</body>
</html>
