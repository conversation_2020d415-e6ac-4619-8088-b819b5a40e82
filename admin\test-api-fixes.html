<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات API</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #5a67d8;
        }
        .success {
            color: #10b981;
            background: #d1fae5;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .error {
            color: #ef4444;
            background: #fee2e2;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .info {
            color: #3b82f6;
            background: #dbeafe;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .log {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار إصلاحات API</h1>
        <p>اختبار الإصلاحات المطبقة على مشاكل API والإشعارات</p>

        <div class="test-section">
            <h2>📋 ملخص الإصلاحات المطبقة</h2>
            <div class="info">
                <h3>✅ الإصلاحات المطبقة:</h3>
                <ul>
                    <li><strong>إصلاح Notifications API:</strong> تم إضافة headers صحيحة ومعالجة أخطاء شاملة</li>
                    <li><strong>إصلاح Reports API:</strong> تم تصحيح متغيرات قاعدة البيانات من $conn إلى $pdo</li>
                    <li><strong>تحسين معالجة JSON:</strong> إضافة معالجة أفضل للاستجابات الفارغة والخاطئة</li>
                    <li><strong>إصلاح أخطاء المتصفح:</strong> إضافة معالجة لأخطاء إضافات المتصفح</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 اختبارات API</h2>
            <button class="test-button" onclick="testNotificationsAPI()">1. اختبار Notifications API</button>
            <button class="test-button" onclick="testReportsAPI()">2. اختبار Reports API</button>
            <button class="test-button" onclick="testAuthAPI()">3. اختبار Authentication API</button>
            <button class="test-button" onclick="testAllAPIs()">4. اختبار جميع APIs</button>
            
            <div id="testResults" class="log"></div>
        </div>

        <div class="test-section">
            <h2>📊 نتائج الاختبار</h2>
            <div id="summaryResults"></div>
        </div>
    </div>

    <script>
        let testLog = [];
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0
        };

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logMessage = `[${timestamp}] ${message}`;
            testLog.push(logMessage);
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = testLog.join('\n');
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            console.log(logMessage);
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('summaryResults');
            const successRate = testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0;
            
            summaryDiv.innerHTML = `
                <div class="${successRate >= 80 ? 'success' : successRate >= 50 ? 'info' : 'error'}">
                    <h3>📈 ملخص النتائج</h3>
                    <p><strong>إجمالي الاختبارات:</strong> ${testResults.total}</p>
                    <p><strong>نجح:</strong> ${testResults.passed}</p>
                    <p><strong>فشل:</strong> ${testResults.failed}</p>
                    <p><strong>معدل النجاح:</strong> ${successRate}%</p>
                </div>
            `;
        }

        async function testAPI(url, name) {
            testResults.total++;
            
            try {
                log(`🧪 اختبار ${name}...`);
                
                const response = await fetch(url);
                const text = await response.text();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${text}`);
                }
                
                if (!text.trim()) {
                    throw new Error('استجابة فارغة من الخادم');
                }
                
                const data = JSON.parse(text);
                
                if (data.success !== false) {
                    log(`✅ ${name}: نجح`);
                    testResults.passed++;
                    return true;
                } else {
                    log(`❌ ${name}: فشل - ${data.message || 'خطأ غير معروف'}`);
                    testResults.failed++;
                    return false;
                }
                
            } catch (error) {
                log(`❌ ${name}: خطأ - ${error.message}`);
                testResults.failed++;
                return false;
            }
        }

        async function testNotificationsAPI() {
            log('🔔 بدء اختبار Notifications API...');
            
            const tests = [
                { url: '../php/notifications.php', name: 'الإشعارات الافتراضية' },
                { url: '../php/notifications.php?action=unread', name: 'الإشعارات غير المقروءة' }
            ];
            
            for (const test of tests) {
                await testAPI(test.url, test.name);
            }
            
            updateSummary();
        }

        async function testReportsAPI() {
            log('📊 بدء اختبار Reports API...');
            
            const tests = [
                { url: '../php/api/reports.php?action=summary', name: 'ملخص التقارير' },
                { url: '../php/api/reports.php?action=sales', name: 'بيانات المبيعات' },
                { url: '../php/api/reports.php?action=orders', name: 'إحصائيات الطلبات' }
            ];
            
            for (const test of tests) {
                await testAPI(test.url, test.name);
            }
            
            updateSummary();
        }

        async function testAuthAPI() {
            log('🔐 بدء اختبار Authentication API...');
            
            await testAPI('../php/admin.php?action=check', 'فحص المصادقة');
            
            updateSummary();
        }

        async function testAllAPIs() {
            log('🚀 بدء اختبار جميع APIs...');
            
            // Reset results
            testResults = { total: 0, passed: 0, failed: 0 };
            
            await testNotificationsAPI();
            await testReportsAPI();
            await testAuthAPI();
            
            log('🎉 انتهى اختبار جميع APIs');
            updateSummary();
        }

        // Initialize
        log('🚀 مرحباً بك في اختبار إصلاحات API');
        log('📋 انقر على أي زر لبدء الاختبارات');
    </script>
</body>
</html>
