/**
 * Security Settings Management
 * Handles security configurations, monitoring, and audit functions
 */

// Default security settings
let securitySettings = {
    authentication: {
        requireStrongPassword: true,
        enableTwoFactor: false,
        sessionTimeout: 30,
        maxLoginAttempts: 5
    },
    accessControl: {
        enableIPWhitelist: false,
        ipWhitelist: '',
        enableGeoBlocking: false
    },
    dataProtection: {
        enableDataEncryption: true,
        enableBackupEncryption: true,
        backupRetention: 30,
        dataRetention: 365
    },
    monitoring: {
        enableActivityLogging: true,
        enableSecurityAlerts: true,
        alertEmail: ''
    },
    ssl: {
        forceHTTPS: true,
        enableHSTS: true,
        sslStatus: 'valid',
        sslExpiry: '2024-12-31'
    }
};

/**
 * Initialize security settings
 */
function initializeSecuritySettings() {
    console.log('Initializing security settings...');
    
    // Load settings
    loadSecuritySettings();
    
    // Apply settings to UI
    applySettingsToUI();
    
    // Update security level
    updateSecurityLevel();
    
    // Update last check timestamp
    updateLastSecurityCheck();
    
    // Add event listeners
    addSecurityEventListeners();
    
    console.log('Security settings initialized successfully');
}

/**
 * Load security settings
 */
async function loadSecuritySettings() {
    try {
        // Try to load from API first
        const apiLoaded = await loadFromAPI();
        
        if (!apiLoaded) {
            // Fallback to localStorage
            const savedSettings = localStorage.getItem('securitySettings');
            if (savedSettings) {
                const parsed = JSON.parse(savedSettings);
                securitySettings = { ...securitySettings, ...parsed };
                console.log('Security settings loaded from localStorage');
            }
        }
    } catch (error) {
        console.warn('Error loading security settings:', error);
        showNotification('تم تحميل الإعدادات الافتراضية للأمان', 'warning');
    }
}

/**
 * Load settings from API
 */
async function loadFromAPI() {
    try {
        const response = await fetch('../php/api/security-settings.php');
        
        if (!response.ok) {
            console.warn('Security API not available, using local settings');
            return false;
        }
        
        const data = await response.json();
        
        if (data.success && data.settings) {
            securitySettings = { ...securitySettings, ...data.settings };
            console.log('Security settings loaded from API successfully');
            return true;
        }
        
        return false;
    } catch (error) {
        console.warn('Security API not available:', error.message);
        return false;
    }
}

/**
 * Apply settings to UI elements
 */
function applySettingsToUI() {
    // Authentication settings
    document.getElementById('requireStrongPassword').checked = securitySettings.authentication.requireStrongPassword;
    document.getElementById('enableTwoFactor').checked = securitySettings.authentication.enableTwoFactor;
    document.getElementById('sessionTimeout').value = securitySettings.authentication.sessionTimeout;
    document.getElementById('maxLoginAttempts').value = securitySettings.authentication.maxLoginAttempts;
    
    // Access control settings
    document.getElementById('enableIPWhitelist').checked = securitySettings.accessControl.enableIPWhitelist;
    document.getElementById('ipWhitelist').value = securitySettings.accessControl.ipWhitelist;
    document.getElementById('enableGeoBlocking').checked = securitySettings.accessControl.enableGeoBlocking;
    
    // Data protection settings
    document.getElementById('enableDataEncryption').checked = securitySettings.dataProtection.enableDataEncryption;
    document.getElementById('enableBackupEncryption').checked = securitySettings.dataProtection.enableBackupEncryption;
    document.getElementById('backupRetention').value = securitySettings.dataProtection.backupRetention;
    document.getElementById('dataRetention').value = securitySettings.dataProtection.dataRetention;
    
    // Monitoring settings
    document.getElementById('enableActivityLogging').checked = securitySettings.monitoring.enableActivityLogging;
    document.getElementById('enableSecurityAlerts').checked = securitySettings.monitoring.enableSecurityAlerts;
    document.getElementById('alertEmail').value = securitySettings.monitoring.alertEmail;
    
    // SSL settings
    document.getElementById('forceHTTPS').checked = securitySettings.ssl.forceHTTPS;
    document.getElementById('enableHSTS').checked = securitySettings.ssl.enableHSTS;
    document.getElementById('sslStatus').textContent = getSSLStatusText(securitySettings.ssl.sslStatus);
    document.getElementById('sslExpiry').textContent = securitySettings.ssl.sslExpiry;
    
    // Toggle IP whitelist visibility
    toggleIPWhitelistVisibility();
}

/**
 * Get SSL status display text
 */
function getSSLStatusText(status) {
    const statusTexts = {
        'valid': 'صالحة',
        'expired': 'منتهية الصلاحية',
        'invalid': 'غير صالحة',
        'unknown': 'غير محدد'
    };
    return statusTexts[status] || 'غير محدد';
}

/**
 * Toggle IP whitelist visibility
 */
function toggleIPWhitelistVisibility() {
    const enableIPWhitelist = document.getElementById('enableIPWhitelist').checked;
    const ipWhitelistGroup = document.getElementById('ipWhitelistGroup');
    
    if (enableIPWhitelist) {
        ipWhitelistGroup.style.display = 'block';
    } else {
        ipWhitelistGroup.style.display = 'none';
    }
}

/**
 * Collect settings from UI
 */
function collectSettingsFromUI() {
    return {
        authentication: {
            requireStrongPassword: document.getElementById('requireStrongPassword').checked,
            enableTwoFactor: document.getElementById('enableTwoFactor').checked,
            sessionTimeout: parseInt(document.getElementById('sessionTimeout').value),
            maxLoginAttempts: parseInt(document.getElementById('maxLoginAttempts').value)
        },
        accessControl: {
            enableIPWhitelist: document.getElementById('enableIPWhitelist').checked,
            ipWhitelist: document.getElementById('ipWhitelist').value,
            enableGeoBlocking: document.getElementById('enableGeoBlocking').checked
        },
        dataProtection: {
            enableDataEncryption: document.getElementById('enableDataEncryption').checked,
            enableBackupEncryption: document.getElementById('enableBackupEncryption').checked,
            backupRetention: parseInt(document.getElementById('backupRetention').value),
            dataRetention: parseInt(document.getElementById('dataRetention').value)
        },
        monitoring: {
            enableActivityLogging: document.getElementById('enableActivityLogging').checked,
            enableSecurityAlerts: document.getElementById('enableSecurityAlerts').checked,
            alertEmail: document.getElementById('alertEmail').value
        },
        ssl: {
            forceHTTPS: document.getElementById('forceHTTPS').checked,
            enableHSTS: document.getElementById('enableHSTS').checked,
            sslStatus: securitySettings.ssl.sslStatus,
            sslExpiry: securitySettings.ssl.sslExpiry
        }
    };
}

/**
 * Calculate and update security level
 */
function updateSecurityLevel() {
    let score = 0;
    let maxScore = 0;
    
    // Authentication (30 points)
    maxScore += 30;
    if (securitySettings.authentication.requireStrongPassword) score += 10;
    if (securitySettings.authentication.enableTwoFactor) score += 15;
    if (securitySettings.authentication.sessionTimeout <= 60) score += 5;
    
    // Access Control (20 points)
    maxScore += 20;
    if (securitySettings.accessControl.enableIPWhitelist) score += 10;
    if (securitySettings.accessControl.enableGeoBlocking) score += 10;
    
    // Data Protection (30 points)
    maxScore += 30;
    if (securitySettings.dataProtection.enableDataEncryption) score += 15;
    if (securitySettings.dataProtection.enableBackupEncryption) score += 15;
    
    // Monitoring (10 points)
    maxScore += 10;
    if (securitySettings.monitoring.enableActivityLogging) score += 5;
    if (securitySettings.monitoring.enableSecurityAlerts) score += 5;
    
    // SSL (10 points)
    maxScore += 10;
    if (securitySettings.ssl.forceHTTPS) score += 5;
    if (securitySettings.ssl.enableHSTS) score += 5;
    
    const percentage = Math.round((score / maxScore) * 100);
    let level = 'منخفض';
    let levelClass = 'security-low';
    
    if (percentage >= 80) {
        level = 'عالي';
        levelClass = 'security-high';
    } else if (percentage >= 60) {
        level = 'متوسط';
        levelClass = 'security-medium';
    }
    
    const securityLevelElement = document.getElementById('securityLevel');
    securityLevelElement.textContent = `${level} (${percentage}%)`;
    securityLevelElement.className = `summary-value ${levelClass}`;
}

/**
 * Update last security check timestamp
 */
function updateLastSecurityCheck() {
    const now = new Date();
    const formatted = now.toLocaleString('ar-DZ', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const element = document.getElementById('lastSecurityCheck');
    if (element) {
        element.textContent = formatted;
    }
}

/**
 * Save security settings
 */
async function saveSecuritySettings() {
    try {
        // Show loading state
        const saveBtn = document.querySelector('.enhanced-save-btn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        saveBtn.disabled = true;
        
        // Collect settings from UI
        const newSettings = collectSettingsFromUI();
        
        // Validate settings
        if (newSettings.monitoring.enableSecurityAlerts && !newSettings.monitoring.alertEmail) {
            throw new Error('يرجى إدخال بريد إلكتروني للتنبيهات');
        }
        
        // Update local settings
        securitySettings = { ...securitySettings, ...newSettings };
        
        // Save to localStorage
        localStorage.setItem('securitySettings', JSON.stringify(securitySettings));
        
        // Try to save to API
        await saveToAPI(securitySettings);
        
        // Update security level
        updateSecurityLevel();
        
        // Update last check timestamp
        updateLastSecurityCheck();
        
        // Show success message
        showNotification('تم حفظ إعدادات الأمان بنجاح', 'success');
        
        // Restore button
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
        
    } catch (error) {
        console.error('Error saving security settings:', error);
        showNotification('خطأ في حفظ إعدادات الأمان: ' + error.message, 'error');
        
        // Restore button
        const saveBtn = document.querySelector('.enhanced-save-btn');
        saveBtn.innerHTML = '<i class="fas fa-save"></i><span class="btn-text">حفظ إعدادات الأمان</span>';
        saveBtn.disabled = false;
    }
}

/**
 * Save settings to API
 */
async function saveToAPI(settings) {
    try {
        const response = await fetch('../php/api/security-settings.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                console.log('Security settings saved to API successfully');
            }
        }
    } catch (error) {
        console.warn('Could not save to API:', error.message);
    }
}

/**
 * Test security settings
 */
async function testSecuritySettings() {
    try {
        const testBtn = document.querySelector('.enhanced-test-btn');
        const originalText = testBtn.innerHTML;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';
        testBtn.disabled = true;
        
        // Simulate security tests
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Mock test results
        const testResults = [
            { test: 'اختبار قوة كلمة المرور', status: 'pass', message: 'تم تفعيل كلمات المرور القوية' },
            { test: 'اختبار HTTPS', status: 'pass', message: 'تم تفعيل HTTPS بنجاح' },
            { test: 'اختبار تسجيل الأنشطة', status: 'pass', message: 'تسجيل الأنشطة يعمل بشكل صحيح' },
            { test: 'اختبار التشفير', status: 'warning', message: 'بعض البيانات غير مشفرة' }
        ];
        
        let message = 'نتائج اختبار الأمان:\n';
        testResults.forEach(result => {
            const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
            message += `${icon} ${result.test}: ${result.message}\n`;
        });
        
        showNotification('تم اختبار إعدادات الأمان بنجاح', 'success');
        alert(message);
        
        // Restore button
        testBtn.innerHTML = originalText;
        testBtn.disabled = false;
        
    } catch (error) {
        console.error('Error testing security settings:', error);
        showNotification('خطأ في اختبار إعدادات الأمان', 'error');
        
        // Restore button
        const testBtn = document.querySelector('.enhanced-test-btn');
        testBtn.innerHTML = '<i class="fas fa-vial"></i><span class="btn-text">اختبار الإعدادات</span>';
        testBtn.disabled = false;
    }
}

/**
 * Reset security settings to defaults
 */
function resetSecuritySettings() {
    if (confirm('هل أنت متأكد من استعادة إعدادات الأمان الافتراضية؟ سيتم فقدان جميع التغييرات الحالية.')) {
        // Reset to defaults
        securitySettings = {
            authentication: { requireStrongPassword: true, enableTwoFactor: false, sessionTimeout: 30, maxLoginAttempts: 5 },
            accessControl: { enableIPWhitelist: false, ipWhitelist: '', enableGeoBlocking: false },
            dataProtection: { enableDataEncryption: true, enableBackupEncryption: true, backupRetention: 30, dataRetention: 365 },
            monitoring: { enableActivityLogging: true, enableSecurityAlerts: true, alertEmail: '' },
            ssl: { forceHTTPS: true, enableHSTS: true, sslStatus: 'valid', sslExpiry: '2024-12-31' }
        };
        
        // Apply to UI
        applySettingsToUI();
        
        // Update security level
        updateSecurityLevel();
        
        // Clear localStorage
        localStorage.removeItem('securitySettings');
        
        showNotification('تم استعادة إعدادات الأمان الافتراضية', 'info');
    }
}

/**
 * Run security audit
 */
async function runSecurityAudit() {
    try {
        const auditResults = document.getElementById('auditResults');
        auditResults.innerHTML = '<div style="text-align: center; padding: 2rem;"><i class="fas fa-spinner fa-spin" style="font-size: 2rem;"></i><p>جاري تشغيل فحص الأمان...</p></div>';
        
        // Simulate audit process
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Mock audit results
        const results = [
            { category: 'المصادقة', score: 85, issues: ['تفعيل المصادقة الثنائية موصى به'] },
            { category: 'التحكم في الوصول', score: 70, issues: ['تفعيل قائمة IP المسموحة موصى به'] },
            { category: 'حماية البيانات', score: 95, issues: [] },
            { category: 'المراقبة', score: 90, issues: ['تحديد بريد التنبيهات'] },
            { category: 'SSL/HTTPS', score: 100, issues: [] }
        ];
        
        const overallScore = Math.round(results.reduce((sum, r) => sum + r.score, 0) / results.length);
        
        auditResults.innerHTML = `
            <div class="audit-summary">
                <h4>نتائج فحص الأمان</h4>
                <div class="overall-score">
                    <span class="score-label">النتيجة الإجمالية:</span>
                    <span class="score-value ${overallScore >= 80 ? 'score-good' : overallScore >= 60 ? 'score-medium' : 'score-poor'}">${overallScore}%</span>
                </div>
            </div>
            <div class="audit-details">
                ${results.map(result => `
                    <div class="audit-item">
                        <div class="audit-header">
                            <span class="audit-category">${result.category}</span>
                            <span class="audit-score ${result.score >= 80 ? 'score-good' : result.score >= 60 ? 'score-medium' : 'score-poor'}">${result.score}%</span>
                        </div>
                        ${result.issues.length > 0 ? `
                            <div class="audit-issues">
                                <strong>التوصيات:</strong>
                                <ul>
                                    ${result.issues.map(issue => `<li>${issue}</li>`).join('')}
                                </ul>
                            </div>
                        ` : '<div class="audit-success">✅ لا توجد مشاكل</div>'}
                    </div>
                `).join('')}
            </div>
        `;
        
        showNotification('تم إكمال فحص الأمان بنجاح', 'success');
        
    } catch (error) {
        console.error('Error running security audit:', error);
        auditResults.innerHTML = '<div class="audit-error"><i class="fas fa-exclamation-triangle"></i><p>حدث خطأ أثناء فحص الأمان</p></div>';
        showNotification('خطأ في تشغيل فحص الأمان', 'error');
    }
}

/**
 * Download audit report
 */
function downloadAuditReport() {
    const reportContent = `
تقرير فحص الأمان
==================

تاريخ الفحص: ${new Date().toLocaleDateString('ar-DZ')}

النتائج:
- المصادقة: 85%
- التحكم في الوصول: 70%
- حماية البيانات: 95%
- المراقبة: 90%
- SSL/HTTPS: 100%

النتيجة الإجمالية: 88%

التوصيات:
- تفعيل المصادقة الثنائية
- تفعيل قائمة IP المسموحة
- تحديد بريد التنبيهات
    `;
    
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `security-audit-${new Date().toISOString().split('T')[0]}.txt`;
    link.click();
    
    showNotification('تم تحميل تقرير الأمان بنجاح', 'success');
}

/**
 * Add event listeners
 */
function addSecurityEventListeners() {
    // IP whitelist toggle
    document.getElementById('enableIPWhitelist').addEventListener('change', toggleIPWhitelistVisibility);
    
    // Auto-save on input changes (debounced)
    const inputs = document.querySelectorAll('.enhanced-input, .enhanced-select, .enhanced-textarea, .form-check-input');
    inputs.forEach(input => {
        input.addEventListener('change', debounce(() => {
            const newSettings = collectSettingsFromUI();
            localStorage.setItem('securitySettings', JSON.stringify(newSettings));
            updateSecurityLevel();
        }, 1000));
    });
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeSecuritySettings();
});
