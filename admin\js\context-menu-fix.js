/**
 * Context Menu and Selection API Error Fix
 * Comprehensive solution for contextmenuhlpr.js and selection errors
 */

(function() {
    'use strict';
    
    console.log('🛡️ Context Menu Fix loaded');
    
    // Global error handler for selection-related errors
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message) {
            const message = event.error.message;
            
            // Check for known problematic errors
            if (message.includes('rangeCount') || 
                message.includes('selection is null') || 
                message.includes('contextmenuhlpr') ||
                message.includes('mozInputSource') ||
                message.includes('getSelection')) {
                
                console.log('🛡️ Selection/context menu error prevented:', message);
                event.preventDefault();
                event.stopPropagation();
                return true;
            }
        }
        return false;
    }, true);
    
    // Override problematic selection methods
    if (window.getSelection) {
        const originalGetSelection = window.getSelection;
        
        window.getSelection = function() {
            try {
                const selection = originalGetSelection.call(window);
                
                // Return safe fallback if selection is null or problematic
                if (!selection) {
                    return createSafeSelection();
                }
                
                // Wrap the selection object to handle rangeCount safely
                return wrapSelection(selection);
                
            } catch (error) {
                console.log('🛡️ getSelection error handled:', error.message);
                return createSafeSelection();
            }
        };
    }
    
    // Create a safe selection object
    function createSafeSelection() {
        return {
            rangeCount: 0,
            anchorNode: null,
            anchorOffset: 0,
            focusNode: null,
            focusOffset: 0,
            isCollapsed: true,
            type: 'None',
            
            addRange: function(range) {
                console.log('🛡️ Safe addRange called');
            },
            
            removeAllRanges: function() {
                console.log('🛡️ Safe removeAllRanges called');
            },
            
            removeRange: function(range) {
                console.log('🛡️ Safe removeRange called');
            },
            
            getRangeAt: function(index) {
                console.log('🛡️ Safe getRangeAt called');
                return document.createRange();
            },
            
            toString: function() {
                return '';
            },
            
            collapse: function(node, offset) {
                console.log('🛡️ Safe collapse called');
            },
            
            extend: function(node, offset) {
                console.log('🛡️ Safe extend called');
            },
            
            selectAllChildren: function(node) {
                console.log('🛡️ Safe selectAllChildren called');
            }
        };
    }
    
    // Wrap existing selection to handle errors
    function wrapSelection(selection) {
        const safeSelection = Object.create(selection);
        
        // Override rangeCount property with safe getter
        Object.defineProperty(safeSelection, 'rangeCount', {
            get: function() {
                try {
                    return selection.rangeCount || 0;
                } catch (error) {
                    console.log('🛡️ rangeCount error handled:', error.message);
                    return 0;
                }
            },
            enumerable: true,
            configurable: true
        });
        
        // Override problematic methods
        safeSelection.getRangeAt = function(index) {
            try {
                return selection.getRangeAt(index);
            } catch (error) {
                console.log('🛡️ getRangeAt error handled:', error.message);
                return document.createRange();
            }
        };
        
        safeSelection.addRange = function(range) {
            try {
                return selection.addRange(range);
            } catch (error) {
                console.log('🛡️ addRange error handled:', error.message);
            }
        };
        
        safeSelection.removeAllRanges = function() {
            try {
                return selection.removeAllRanges();
            } catch (error) {
                console.log('🛡️ removeAllRanges error handled:', error.message);
            }
        };
        
        return safeSelection;
    }
    
    // Handle selection change events safely
    document.addEventListener('selectionchange', function(event) {
        try {
            const selection = window.getSelection();
            if (selection && typeof selection.rangeCount !== 'undefined') {
                // Selection is valid, continue normally
                console.debug('🛡️ Selection change handled safely');
            }
        } catch (error) {
            console.log('🛡️ Selection change error handled:', error.message);
            event.preventDefault();
            event.stopPropagation();
        }
    }, true);
    
    // Handle context menu events safely
    document.addEventListener('contextmenu', function(event) {
        try {
            // Allow context menu but handle any selection errors
            const selection = window.getSelection();
            if (selection) {
                console.debug('🛡️ Context menu with selection handled safely');
            }
        } catch (error) {
            console.log('🛡️ Context menu error handled:', error.message);
            // Don't prevent the context menu, just handle the error
        }
    }, true);
    
    // Override document.getSelection as well
    if (document.getSelection) {
        const originalDocGetSelection = document.getSelection;
        
        document.getSelection = function() {
            try {
                const selection = originalDocGetSelection.call(document);
                if (!selection) {
                    return createSafeSelection();
                }
                return wrapSelection(selection);
            } catch (error) {
                console.log('🛡️ document.getSelection error handled:', error.message);
                return createSafeSelection();
            }
        };
    }
    
    // Handle TinyMCE specific issues if present
    if (window.tinymce) {
        console.log('🛡️ TinyMCE detected, applying additional fixes');
        
        // Override TinyMCE selection methods if they exist
        const originalTinyMCEInit = window.tinymce.init;
        window.tinymce.init = function(config) {
            // Add selection error handling to TinyMCE config
            config = config || {};
            config.setup = config.setup || function(editor) {};
            
            const originalSetup = config.setup;
            config.setup = function(editor) {
                originalSetup.call(this, editor);
                
                // Handle TinyMCE selection errors
                editor.on('SelectionChange', function(e) {
                    try {
                        const selection = editor.selection;
                        if (selection && selection.getRng) {
                            selection.getRng();
                        }
                    } catch (error) {
                        console.log('🛡️ TinyMCE selection error handled:', error.message);
                    }
                });
            };
            
            return originalTinyMCEInit.call(this, config);
        };
    }
    
    // Log successful initialization
    console.log('✅ Context menu and selection error fixes applied successfully');
    
})();

// Export for manual testing
window.contextMenuFix = {
    test: function() {
        console.log('🧪 Testing context menu fix...');
        
        try {
            const selection = window.getSelection();
            console.log('✅ getSelection works:', !!selection);
            console.log('✅ rangeCount:', selection.rangeCount);
            
            // Test context menu event
            const event = new Event('contextmenu');
            document.dispatchEvent(event);
            console.log('✅ Context menu event handled');
            
            return true;
        } catch (error) {
            console.error('❌ Context menu fix test failed:', error);
            return false;
        }
    }
};
