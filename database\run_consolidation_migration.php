<?php
/**
 * Database Consolidation Migration Runner
 * This script safely migrates from livres table to produits table
 */

require_once __DIR__ . '/../php/config.php';

try {
    echo "=== Database Consolidation Migration ===\n";
    echo "Starting migration from 'livres' to 'produits' table...\n\n";

    // Get PDO connection
    $pdo = getPDOConnection();
    
    // Check if livres table exists
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM information_schema.tables 
                        WHERE table_schema = DATABASE() AND table_name = 'livres'");
    $livresExists = $stmt->fetch()['count'] > 0;
    
    if ($livresExists) {
        echo "✓ Found 'livres' table - proceeding with migration\n";
        
        // Check data in livres table
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM livres");
        $livresCount = $stmt->fetch()['count'];
        echo "✓ Found {$livresCount} records in 'livres' table\n";
    } else {
        echo "ℹ No 'livres' table found - migration may have already been completed\n";
    }
    
    // Check produits table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM information_schema.tables 
                        WHERE table_schema = DATABASE() AND table_name = 'produits'");
    $produitsExists = $stmt->fetch()['count'] > 0;
    
    if ($produitsExists) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
        $produitsCount = $stmt->fetch()['count'];
        echo "✓ Found 'produits' table with {$produitsCount} records\n";
    } else {
        echo "✗ ERROR: 'produits' table not found! Please ensure the database schema is up to date.\n";
        exit(1);
    }
    
    echo "\n--- Starting Migration Process ---\n";
    
    // Read and execute migration SQL
    $migrationFile = __DIR__ . '/migrations/consolidate_livres_to_produits.sql';
    if (!file_exists($migrationFile)) {
        echo "✗ ERROR: Migration file not found: {$migrationFile}\n";
        exit(1);
    }
    
    $sql = file_get_contents($migrationFile);
    if ($sql === false) {
        echo "✗ ERROR: Could not read migration file\n";
        exit(1);
    }
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $pdo->beginTransaction();
    
    try {
        $executedCount = 0;
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                $pdo->exec($statement);
                $executedCount++;
            }
        }
        
        $pdo->commit();
        echo "✓ Successfully executed {$executedCount} SQL statements\n";
        
    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
    
    echo "\n--- Post-Migration Verification ---\n";
    
    // Verify migration results
    if ($produitsExists) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
        $finalCount = $stmt->fetch()['count'];
        echo "✓ Final product count: {$finalCount}\n";
        
        // Show product types
        $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM produits GROUP BY type");
        $types = $stmt->fetchAll();
        echo "✓ Product types:\n";
        foreach ($types as $type) {
            echo "  - {$type['type']}: {$type['count']} items\n";
        }
    }
    
    // Check foreign key constraints
    $stmt = $pdo->query("SELECT CONSTRAINT_NAME, REFERENCED_TABLE_NAME 
                        FROM information_schema.KEY_COLUMN_USAGE 
                        WHERE TABLE_SCHEMA = DATABASE() 
                        AND REFERENCED_TABLE_NAME IN ('livres', 'produits')");
    $constraints = $stmt->fetchAll();
    
    echo "\n✓ Foreign key constraints:\n";
    foreach ($constraints as $constraint) {
        echo "  - {$constraint['CONSTRAINT_NAME']} -> {$constraint['REFERENCED_TABLE_NAME']}\n";
    }
    
    echo "\n=== Migration Completed Successfully ===\n";
    echo "All database references have been consolidated to use 'produits' table.\n";
    echo "Please test the application thoroughly before deploying to production.\n";
    
} catch (Exception $e) {
    echo "\n✗ ERROR: Migration failed!\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    exit(1);
}
?>
