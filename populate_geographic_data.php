<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🗺️ Populating Geographic Data\n\n";

require_once 'php/config.php';

try {
    // First, let's check the current state
    $stmt = $conn->query("SELECT COUNT(*) as count FROM communes");
    $current_communes = $stmt->fetch()['count'];
    echo "Current communes in database: $current_communes\n";
    
    if ($current_communes > 100) {
        echo "✅ Communes already populated. Skipping import.\n";
        exit(0);
    }
    
    echo "📥 Importing communes from algeria_cities.sql...\n";
    
    // Read and execute the SQL file
    $sql_content = file_get_contents('sql/algeria_cities.sql');
    
    if (!$sql_content) {
        throw new Exception("Could not read algeria_cities.sql file");
    }
    
    // Split into individual statements
    $statements = explode(';', $sql_content);
    
    $conn->beginTransaction();
    
    // First, create the temporary table if it doesn't exist
    $create_table_found = false;
    $insert_count = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement)) continue;
        
        if (strpos($statement, 'CREATE TABLE') !== false) {
            echo "📋 Creating algeria_cities table...\n";
            $conn->exec($statement);
            $create_table_found = true;
        } elseif (strpos($statement, 'INSERT INTO algeria_cities') !== false) {
            $conn->exec($statement);
            $insert_count++;
            if ($insert_count % 100 == 0) {
                echo "Imported $insert_count records...\n";
            }
        }
    }
    
    echo "✅ Imported $insert_count records into algeria_cities table\n";
    
    // Now migrate data to our communes table
    echo "🔄 Migrating data to communes table...\n";
    
    $migrate_sql = "
        INSERT INTO communes (commune_code, commune_name_ar, commune_name_fr, wilaya_code, postal_code, is_active)
        SELECT 
            CONCAT(wilaya_code, LPAD(id, 3, '0')) as commune_code,
            commune_name as commune_name_ar,
            commune_name_ascii as commune_name_fr,
            wilaya_code,
            NULL as postal_code,
            TRUE as is_active
        FROM algeria_cities
        ON DUPLICATE KEY UPDATE
            commune_name_ar = VALUES(commune_name_ar),
            commune_name_fr = VALUES(commune_name_fr)
    ";
    
    $result = $conn->exec($migrate_sql);
    echo "✅ Migrated $result communes to the communes table\n";
    
    $conn->commit();
    
    // Verify the result
    $stmt = $conn->query("SELECT COUNT(*) as count FROM communes");
    $final_count = $stmt->fetch()['count'];
    echo "🎉 Final communes count: $final_count\n";
    
    // Test a few sample queries
    echo "\n🧪 Testing sample queries:\n";
    
    // Test wilayas
    $stmt = $conn->query("SELECT wilaya_code, wilaya_name_ar FROM wilayas LIMIT 5");
    $wilayas = $stmt->fetchAll();
    echo "Sample wilayas:\n";
    foreach ($wilayas as $wilaya) {
        echo "- {$wilaya['wilaya_code']}: {$wilaya['wilaya_name_ar']}\n";
    }
    
    // Test communes for Algiers (16)
    $stmt = $conn->prepare("SELECT commune_name_ar FROM communes WHERE wilaya_code = '16' LIMIT 5");
    $stmt->execute();
    $communes = $stmt->fetchAll();
    echo "\nSample communes for Algiers (16):\n";
    foreach ($communes as $commune) {
        echo "- {$commune['commune_name_ar']}\n";
    }
    
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
