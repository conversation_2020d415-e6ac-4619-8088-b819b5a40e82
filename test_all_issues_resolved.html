<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حل جميع المشاكل الحرجة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 30px 0;
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        .test-section.success {
            border-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        }
        .test-section.error {
            border-color: #dc3545;
            background: linear-gradient(135deg, #fff5f5 0%, #f8e8e8 100%);
        }
        .test-section.loading {
            border-color: #ffc107;
            background: linear-gradient(135deg, #fffbf0 0%, #fff3cd 100%);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background: white;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 8px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .btn-success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .btn-warning { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
        .btn-danger { background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); }
        .status-indicator {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .results-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.5s ease;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار حل جميع المشاكل الحرجة</h1>
            <p>فحص شامل للتأكد من حل المشاكل الثلاث الحرجة</p>
            <div class="progress-bar">
                <div class="progress-fill" id="overallProgress"></div>
            </div>
        </div>

        <!-- Issue 1: Products Test -->
        <div class="test-section loading" id="products-section">
            <h2>📦 المشكلة 1: المنتجات المفقودة في قاعدة البيانات</h2>
            <p>فحص وجود المنتجات الخمسة المطلوبة في قاعدة البيانات</p>
            <button class="btn" onclick="testProducts()">🔍 فحص المنتجات</button>
            <div id="products-results" class="results-container" style="display:none;"></div>
        </div>

        <!-- Issue 2: Landing Pages Test -->
        <div class="test-section loading" id="landing-pages-section">
            <h2>🎨 المشكلة 2: قسم صفحات الهبوط الفارغ</h2>
            <p>فحص هيكل قاعدة البيانات وواجهة برمجة التطبيقات لصفحات الهبوط</p>
            <button class="btn" onclick="testLandingPages()">🔍 فحص صفحات الهبوط</button>
            <div id="landing-pages-results" class="results-container" style="display:none;"></div>
        </div>

        <!-- Issue 3: Categories Test -->
        <div class="test-section loading" id="categories-section">
            <h2>🗂️ المشكلة 3: نظام الفئات الديناميكي</h2>
            <p>فحص تطبيق نظام الفئات الجديد وربطه بالمنتجات</p>
            <button class="btn" onclick="testCategories()">🔍 فحص نظام الفئات</button>
            <div id="categories-results" class="results-container" style="display:none;"></div>
        </div>

        <!-- Integration Test -->
        <div class="test-section loading" id="integration-section">
            <h2>🔗 اختبار التكامل الشامل</h2>
            <p>فحص التكامل بين جميع الأنظمة والتأكد من عملها معاً</p>
            <button class="btn" onclick="testIntegration()">🔗 اختبار التكامل</button>
            <div id="integration-results" class="results-container" style="display:none;"></div>
        </div>

        <!-- Summary -->
        <div class="test-section" id="summary-section" style="display:none;">
            <h2>📊 ملخص النتائج</h2>
            <div class="summary-stats" id="summaryStats"></div>
            <div id="final-recommendations"></div>
        </div>

        <!-- Quick Actions -->
        <div class="test-section">
            <h2>🚀 إجراءات سريعة</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h4>لوحة التحكم</h4>
                    <a href="admin/" class="btn btn-success">🏠 لوحة التحكم</a>
                </div>
                <div class="test-card">
                    <h4>واجهات برمجة التطبيقات</h4>
                    <a href="php/api/products.php" class="btn">📦 API المنتجات</a>
                    <a href="php/api/landing-pages.php" class="btn">🎨 API صفحات الهبوط</a>
                    <a href="php/api/categories.php" class="btn">🗂️ API الفئات</a>
                </div>
                <div class="test-card">
                    <h4>أدوات الإصلاح</h4>
                    <a href="fix_all_critical_issues.php" class="btn btn-warning">🔧 إصلاح المشاكل</a>
                    <a href="implement_categories_system.php" class="btn btn-warning">🗂️ تطبيق الفئات</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            products: null,
            landingPages: null,
            categories: null,
            integration: null
        };

        async function safeApiCall(url) {
            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const text = await response.text();
                if (!text.trim()) {
                    return { success: false, message: 'Empty response' };
                }
                return JSON.parse(text);
            } catch (error) {
                console.error('API call failed:', error);
                return { success: false, message: error.message };
            }
        }

        async function testProducts() {
            const section = document.getElementById('products-section');
            const resultsDiv = document.getElementById('products-results');
            
            section.className = 'test-section loading';
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<p>🔄 جاري فحص المنتجات...</p>';
            
            try {
                const response = await safeApiCall('/php/api/products.php');
                
                if (response.success && response.products) {
                    const activeProducts = response.products.filter(p => p.actif == 1);
                    const requiredProducts = [
                        'فن اللامبالاة - كتاب تطوير الذات',
                        'لابتوب Dell Inspiron 15 - للطلاب والمهنيين',
                        'حقيبة ظهر رياضية مقاومة للماء',
                        'قميص قطني كلاسيكي للرجال',
                        'خلاط كهربائي متعدد الاستخدامات'
                    ];
                    
                    let foundProducts = 0;
                    let html = `<h3>✅ تم العثور على ${activeProducts.length} منتجات نشطة</h3>`;
                    html += '<h4>المنتجات المطلوبة:</h4><ul>';
                    
                    requiredProducts.forEach(title => {
                        const found = activeProducts.find(p => p.titre.includes(title.split(' - ')[0]));
                        if (found) {
                            html += `<li style="color: green;">✅ ${title}</li>`;
                            foundProducts++;
                        } else {
                            html += `<li style="color: red;">❌ ${title}</li>`;
                        }
                    });
                    
                    html += '</ul>';
                    
                    if (foundProducts === requiredProducts.length) {
                        section.className = 'test-section success';
                        html += '<p style="color: green; font-weight: bold;">🎉 جميع المنتجات المطلوبة موجودة!</p>';
                        testResults.products = { success: true, count: foundProducts };
                    } else {
                        section.className = 'test-section error';
                        html += `<p style="color: red; font-weight: bold;">⚠️ يوجد ${requiredProducts.length - foundProducts} منتجات مفقودة</p>`;
                        testResults.products = { success: false, count: foundProducts };
                    }
                    
                    resultsDiv.innerHTML = html;
                } else {
                    throw new Error('فشل في تحميل المنتجات');
                }
            } catch (error) {
                section.className = 'test-section error';
                resultsDiv.innerHTML = `<p style="color: red;">❌ خطأ: ${error.message}</p>`;
                testResults.products = { success: false, count: 0 };
            }
            
            updateProgress();
        }

        async function testLandingPages() {
            const section = document.getElementById('landing-pages-section');
            const resultsDiv = document.getElementById('landing-pages-results');
            
            section.className = 'test-section loading';
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<p>🔄 جاري فحص صفحات الهبوط...</p>';
            
            try {
                const response = await safeApiCall('/php/api/landing-pages.php');
                
                let html = '<h3>📊 نتائج فحص صفحات الهبوط:</h3>';
                
                if (response.success) {
                    const pages = response.data || [];
                    html += `<p>✅ API يعمل بشكل صحيح</p>`;
                    html += `<p>📄 عدد صفحات الهبوط: ${pages.length}</p>`;
                    
                    if (pages.length > 0) {
                        html += '<h4>صفحات الهبوط الموجودة:</h4><ul>';
                        pages.forEach(page => {
                            html += `<li>${page.titre || 'صفحة بدون عنوان'} (المنتج: ${page.product_title || 'غير محدد'})</li>`;
                        });
                        html += '</ul>';
                    }
                    
                    section.className = 'test-section success';
                    testResults.landingPages = { success: true, count: pages.length };
                } else {
                    html += `<p style="color: red;">❌ خطأ في API: ${response.message}</p>`;
                    section.className = 'test-section error';
                    testResults.landingPages = { success: false, count: 0 };
                }
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                section.className = 'test-section error';
                resultsDiv.innerHTML = `<p style="color: red;">❌ خطأ: ${error.message}</p>`;
                testResults.landingPages = { success: false, count: 0 };
            }
            
            updateProgress();
        }

        async function testCategories() {
            const section = document.getElementById('categories-section');
            const resultsDiv = document.getElementById('categories-results');
            
            section.className = 'test-section loading';
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<p>🔄 جاري فحص نظام الفئات...</p>';
            
            try {
                const response = await safeApiCall('/php/api/categories.php');
                
                let html = '<h3>📊 نتائج فحص نظام الفئات:</h3>';
                
                if (response.success && response.categories) {
                    const categories = response.categories;
                    html += `<p>✅ API الفئات يعمل بشكل صحيح</p>`;
                    html += `<p>🗂️ عدد الفئات: ${categories.length}</p>`;
                    
                    html += '<h4>الفئات المتاحة:</h4><ul>';
                    categories.forEach(cat => {
                        html += `<li><i class="${cat.icone}" style="color: ${cat.couleur}"></i> ${cat.nom_ar} (${cat.nom_en}) - ${cat.products_count} منتجات</li>`;
                    });
                    html += '</ul>';
                    
                    const totalProducts = categories.reduce((sum, cat) => sum + parseInt(cat.products_count), 0);
                    html += `<p>📦 إجمالي المنتجات المربوطة بالفئات: ${totalProducts}</p>`;
                    
                    section.className = 'test-section success';
                    testResults.categories = { success: true, count: categories.length, productsLinked: totalProducts };
                } else {
                    html += `<p style="color: red;">❌ خطأ في API الفئات: ${response.message}</p>`;
                    section.className = 'test-section error';
                    testResults.categories = { success: false, count: 0, productsLinked: 0 };
                }
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                section.className = 'test-section error';
                resultsDiv.innerHTML = `<p style="color: red;">❌ خطأ: ${error.message}</p>`;
                testResults.categories = { success: false, count: 0, productsLinked: 0 };
            }
            
            updateProgress();
        }

        async function testIntegration() {
            const section = document.getElementById('integration-section');
            const resultsDiv = document.getElementById('integration-results');
            
            section.className = 'test-section loading';
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<p>🔄 جاري اختبار التكامل...</p>';
            
            try {
                // Test all APIs together
                const [productsResponse, landingPagesResponse, categoriesResponse, templatesResponse] = await Promise.all([
                    safeApiCall('/php/api/products.php'),
                    safeApiCall('/php/api/landing-pages.php'),
                    safeApiCall('/php/api/categories.php'),
                    safeApiCall('/php/api/templates.php?action=get_templates')
                ]);
                
                let html = '<h3>🔗 نتائج اختبار التكامل:</h3>';
                let successCount = 0;
                
                // Check each API
                if (productsResponse.success) {
                    html += '<p>✅ API المنتجات يعمل</p>';
                    successCount++;
                } else {
                    html += '<p>❌ API المنتجات لا يعمل</p>';
                }
                
                if (landingPagesResponse.success) {
                    html += '<p>✅ API صفحات الهبوط يعمل</p>';
                    successCount++;
                } else {
                    html += '<p>❌ API صفحات الهبوط لا يعمل</p>';
                }
                
                if (categoriesResponse.success) {
                    html += '<p>✅ API الفئات يعمل</p>';
                    successCount++;
                } else {
                    html += '<p>❌ API الفئات لا يعمل</p>';
                }
                
                if (templatesResponse.success) {
                    html += '<p>✅ API القوالب يعمل</p>';
                    successCount++;
                } else {
                    html += '<p>❌ API القوالب لا يعمل</p>';
                }
                
                html += `<h4>📊 النتيجة النهائية: ${successCount}/4 APIs تعمل بشكل صحيح</h4>`;
                
                if (successCount === 4) {
                    section.className = 'test-section success';
                    html += '<p style="color: green; font-weight: bold;">🎉 جميع الأنظمة تعمل بشكل متكامل!</p>';
                    testResults.integration = { success: true, apisWorking: successCount };
                } else {
                    section.className = 'test-section error';
                    html += '<p style="color: red; font-weight: bold;">⚠️ يوجد مشاكل في التكامل</p>';
                    testResults.integration = { success: false, apisWorking: successCount };
                }
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                section.className = 'test-section error';
                resultsDiv.innerHTML = `<p style="color: red;">❌ خطأ في اختبار التكامل: ${error.message}</p>`;
                testResults.integration = { success: false, apisWorking: 0 };
            }
            
            updateProgress();
            showSummary();
        }

        function updateProgress() {
            const completedTests = Object.values(testResults).filter(result => result !== null).length;
            const progress = (completedTests / 4) * 100;
            document.getElementById('overallProgress').style.width = progress + '%';
        }

        function showSummary() {
            const summarySection = document.getElementById('summary-section');
            const summaryStats = document.getElementById('summaryStats');
            const recommendations = document.getElementById('final-recommendations');
            
            summarySection.style.display = 'block';
            
            // Calculate stats
            const successfulTests = Object.values(testResults).filter(result => result && result.success).length;
            const totalProducts = testResults.products ? testResults.products.count : 0;
            const totalLandingPages = testResults.landingPages ? testResults.landingPages.count : 0;
            const totalCategories = testResults.categories ? testResults.categories.count : 0;
            
            summaryStats.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${successfulTests}/4</div>
                    <div class="stat-label">اختبارات ناجحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalProducts}</div>
                    <div class="stat-label">منتجات نشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalLandingPages}</div>
                    <div class="stat-label">صفحات هبوط</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalCategories}</div>
                    <div class="stat-label">فئات متاحة</div>
                </div>
            `;
            
            if (successfulTests === 4) {
                recommendations.innerHTML = `
                    <div style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 20px; border-radius: 10px; border: 1px solid #28a745;">
                        <h3 style="color: #155724; margin-top: 0;">🎉 تم حل جميع المشاكل الحرجة بنجاح!</h3>
                        <p>جميع الأنظمة تعمل بشكل صحيح ويمكنك الآن:</p>
                        <ul>
                            <li>إنشاء صفحات هبوط جديدة من لوحة التحكم</li>
                            <li>إدارة المنتجات والفئات</li>
                            <li>استخدام النظام في الإنتاج</li>
                        </ul>
                    </div>
                `;
            } else {
                recommendations.innerHTML = `
                    <div style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); padding: 20px; border-radius: 10px; border: 1px solid #dc3545;">
                        <h3 style="color: #721c24; margin-top: 0;">⚠️ يوجد مشاكل تحتاج إلى حل</h3>
                        <p>يرجى مراجعة النتائج أعلاه وتشغيل أدوات الإصلاح المناسبة.</p>
                    </div>
                `;
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testProducts();
                setTimeout(() => {
                    testLandingPages();
                    setTimeout(() => {
                        testCategories();
                        setTimeout(() => {
                            testIntegration();
                        }, 1000);
                    }, 1000);
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
