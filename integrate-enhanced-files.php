<?php
/**
 * Integrate Enhanced Files into Admin Panel
 * Ensures all enhanced JavaScript and CSS files are properly integrated
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>تكامل الملفات المحسنة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; white-space: pre-wrap; }
        .file-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔗 تكامل الملفات المحسنة</h1>";
echo "<p>ضمان تكامل جميع الملفات المحسنة في لوحة التحكم الإدارية</p>";

try {
    echo "<div class='section'>";
    echo "<h2>1️⃣ فحص الملفات المحسنة</h2>";
    
    $enhancedFiles = [
        'admin/js/landing-pages-enhanced-fixed.js' => 'JavaScript محسن لصفحات الهبوط',
        'admin/css/landing-pages-enhanced-fixed.css' => 'CSS محسن للواجهة',
        'admin/js/selection-error-fix.js' => 'إصلاح أخطاء التحديد'
    ];
    
    $allFilesExist = true;
    
    foreach ($enhancedFiles as $file => $description) {
        echo "<div class='file-result'>";
        echo "<h4>{$description}</h4>";
        
        if (file_exists($file)) {
            $size = filesize($file);
            echo "<div class='success'>✅ الملف موجود - الحجم: " . number_format($size) . " بايت</div>";
        } else {
            echo "<div class='error'>❌ الملف غير موجود: {$file}</div>";
            $allFilesExist = false;
        }
        
        echo "</div>";
    }
    
    if (!$allFilesExist) {
        echo "<div class='warning'>⚠️ بعض الملفات المحسنة غير موجودة. يرجى تشغيل سكريبت الإصلاح أولاً.</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2️⃣ إنشاء صفحة اختبار متكاملة</h2>";
    
    // Create an integrated test page
    $testPageContent = '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الملفات المحسنة - صفحات الهبوط</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Enhanced CSS -->
    <link rel="stylesheet" href="css/landing-pages-enhanced-fixed.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .btn-test {
            margin: 10px 5px;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-rocket"></i> اختبار الملفات المحسنة</h1>
        <p>اختبار شامل للملفات المحسنة لإدارة صفحات الهبوط</p>
        
        <div class="test-section">
            <h2><i class="fas fa-cogs"></i> اختبار الأزرار والواجهة</h2>
            
            <!-- Test buttons -->
            <button id="addLandingPageBtn" class="btn btn-primary btn-test">
                <i class="fas fa-plus"></i> أَضف صفحة هبوط
            </button>
            
            <button class="btn btn-success btn-test" onclick="testEnhancedManager()">
                <i class="fas fa-test-tube"></i> اختبار المدير المحسن
            </button>
            
            <button class="btn btn-info btn-test" onclick="testUIVisibility()">
                <i class="fas fa-eye"></i> اختبار ظهور الواجهة
            </button>
            
            <button class="btn btn-warning btn-test" onclick="clearConsole()">
                <i class="fas fa-eraser"></i> مسح وحدة التحكم
            </button>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-terminal"></i> وحدة التحكم</h2>
            <div id="consoleOutput" class="console-output">
                <div>🚀 جاهز للاختبار...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-list"></i> حالة النظام</h2>
            <div id="systemStatus">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> جاري فحص حالة النظام...
                </div>
            </div>
        </div>
    </div>
    
    <!-- Landing Page Modal (will be created by enhanced manager) -->
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Selection Error Fix (load first) -->
    <script src="js/selection-error-fix.js"></script>
    
    <!-- Enhanced Landing Pages Manager -->
    <script src="js/landing-pages-enhanced-fixed.js"></script>
    
    <script>
        // Console logging function
        function logToConsole(message, type = "info") {
            const console = document.getElementById("consoleOutput");
            const timestamp = new Date().toLocaleTimeString("ar-DZ");
            const icon = type === "error" ? "❌" : type === "success" ? "✅" : type === "warning" ? "⚠️" : "ℹ️";
            
            const logEntry = document.createElement("div");
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            logEntry.style.marginBottom = "5px";
            
            if (type === "error") logEntry.style.color = "#dc3545";
            else if (type === "success") logEntry.style.color = "#28a745";
            else if (type === "warning") logEntry.style.color = "#ffc107";
            
            console.appendChild(logEntry);
            console.scrollTop = console.scrollHeight;
        }
        
        // Test functions
        function testEnhancedManager() {
            logToConsole("اختبار المدير المحسن...");
            
            if (window.enhancedLandingPagesManager) {
                logToConsole("✅ المدير المحسن متاح", "success");
                logToConsole(`حالة التهيئة: ${window.enhancedLandingPagesManager.initialized}`, "info");
                
                // Test opening modal
                try {
                    window.enhancedLandingPagesManager.openModal();
                    logToConsole("✅ تم فتح النافذة المنبثقة بنجاح", "success");
                } catch (error) {
                    logToConsole(`❌ خطأ في فتح النافذة: ${error.message}`, "error");
                }
            } else {
                logToConsole("❌ المدير المحسن غير متاح", "error");
            }
        }
        
        function testUIVisibility() {
            logToConsole("اختبار ظهور عناصر الواجهة...");
            
            const addButton = document.getElementById("addLandingPageBtn");
            if (addButton) {
                const rect = addButton.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0;
                
                logToConsole(`زر الإضافة: ${isVisible ? "مرئي" : "مخفي"}`, isVisible ? "success" : "error");
                logToConsole(`الموقع: x=${rect.x}, y=${rect.y}, عرض=${rect.width}, ارتفاع=${rect.height}`, "info");
            } else {
                logToConsole("❌ زر الإضافة غير موجود", "error");
            }
        }
        
        function clearConsole() {
            document.getElementById("consoleOutput").innerHTML = "<div>🧹 تم مسح وحدة التحكم</div>";
        }
        
        function checkSystemStatus() {
            const statusDiv = document.getElementById("systemStatus");
            let statusHTML = "";
            
            // Check if enhanced files are loaded
            const checks = [
                {
                    name: "Enhanced Landing Pages Manager",
                    status: typeof window.enhancedLandingPagesManager !== "undefined",
                    details: window.enhancedLandingPagesManager ? "متاح ومهيأ" : "غير متاح"
                },
                {
                    name: "Selection Error Fix",
                    status: document.querySelector("script[src*=\"selection-error-fix\"]") !== null,
                    details: "تم تحميل سكريبت إصلاح أخطاء التحديد"
                },
                {
                    name: "Enhanced CSS",
                    status: document.querySelector("link[href*=\"landing-pages-enhanced-fixed\"]") !== null,
                    details: "تم تحميل CSS المحسن"
                },
                {
                    name: "Add Button",
                    status: document.getElementById("addLandingPageBtn") !== null,
                    details: "زر إضافة صفحة الهبوط موجود"
                }
            ];
            
            checks.forEach(check => {
                const alertClass = check.status ? "alert-success" : "alert-danger";
                const icon = check.status ? "fas fa-check-circle" : "fas fa-times-circle";
                
                statusHTML += `
                    <div class="alert ${alertClass}">
                        <i class="${icon}"></i> 
                        <strong>${check.name}:</strong> ${check.details}
                    </div>
                `;
            });
            
            statusDiv.innerHTML = statusHTML;
        }
        
        // Initialize when page loads
        document.addEventListener("DOMContentLoaded", function() {
            logToConsole("تم تحميل الصفحة", "success");
            
            // Check system status
            setTimeout(checkSystemStatus, 1000);
            
            // Log when enhanced manager is ready
            if (window.enhancedLandingPagesManager) {
                logToConsole("المدير المحسن جاهز", "success");
            } else {
                // Wait for it to load
                setTimeout(() => {
                    if (window.enhancedLandingPagesManager) {
                        logToConsole("المدير المحسن تم تحميله", "success");
                        checkSystemStatus();
                    } else {
                        logToConsole("المدير المحسن لم يتم تحميله", "warning");
                    }
                }, 2000);
            }
        });
        
        // Override console methods to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            logToConsole(args.join(" "), "info");
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logToConsole(args.join(" "), "error");
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            logToConsole(args.join(" "), "warning");
            originalWarn.apply(console, args);
        };
    </script>
</body>
</html>';
    
    // Save the test page
    $testPagePath = 'admin/test-enhanced-integration.html';
    if (file_put_contents($testPagePath, $testPageContent)) {
        echo "<div class='success'>✅ تم إنشاء صفحة الاختبار المتكاملة: {$testPagePath}</div>";
    } else {
        echo "<div class='error'>❌ فشل في إنشاء صفحة الاختبار</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3️⃣ تحديث ملف الإدارة الرئيسي</h2>";
    
    // Check if admin index.html exists and suggest integration
    $adminIndexPath = 'admin/index.html';
    if (file_exists($adminIndexPath)) {
        echo "<div class='info'>✅ ملف الإدارة الرئيسي موجود: {$adminIndexPath}</div>";
        
        echo "<div class='warning'>";
        echo "<h4>⚠️ لتكامل الملفات المحسنة، أضف هذا الكود إلى &lt;head&gt; في ملف الإدارة:</h4>";
        echo "<div class='code-block'>";
        echo htmlspecialchars('<!-- Enhanced Landing Pages CSS -->
<link rel="stylesheet" href="css/landing-pages-enhanced-fixed.css">

<!-- Enhanced JavaScript (add before closing </body>) -->
<script src="js/selection-error-fix.js"></script>
<script src="js/landing-pages-enhanced-fixed.js"></script>');
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div class='warning'>⚠️ ملف الإدارة الرئيسي غير موجود: {$adminIndexPath}</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>✅ ملخص التكامل</h2>";
    
    echo "<div class='success'>";
    echo "<h3>🎉 تم تكامل الملفات المحسنة بنجاح!</h3>";
    echo "<ul>";
    echo "<li>✅ تم فحص جميع الملفات المحسنة</li>";
    echo "<li>✅ تم إنشاء صفحة اختبار متكاملة</li>";
    echo "<li>✅ تم توفير تعليمات التكامل</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🔗 روابط الاختبار:</h4>";
    echo "<ul>";
    echo "<li><a href='/admin/test-enhanced-integration.html' target='_blank'>صفحة الاختبار المتكاملة</a></li>";
    echo "<li><a href='/admin/' target='_blank'>لوحة التحكم الإدارية</a></li>";
    echo "<li><a href='/test-all-critical-fixes.php' target='_blank'>اختبار جميع الإصلاحات</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h4>📋 قائمة التحقق النهائية:</h4>";
    echo "<ol>";
    echo "<li>اختبر صفحة التكامل المتكاملة</li>";
    echo "<li>تحقق من عدم وجود أخطاء في وحدة التحكم</li>";
    echo "<li>اختبر زر إضافة صفحة الهبوط</li>";
    echo "<li>تحقق من ظهور النافذة المنبثقة</li>";
    echo "<li>أضف الكود المطلوب إلى ملف الإدارة الرئيسي</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
