<?php
/**
 * Simple API Test Script
 * Tests the fixed API endpoints to verify they're working
 */

// Define security check constant BEFORE including security.php
define('SECURITY_CHECK', true);

require_once 'php/config.php';

echo "<h1>API Test Results</h1>";

// Test database connection
echo "<h2>1. Database Connection Test</h2>";
try {
    if (isset($conn) && $conn) {
        echo "✅ Database connection successful<br>";
        
        // Test a simple query
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM produits");
        $stmt->execute();
        $result = $stmt->fetch();
        echo "✅ Products table accessible - Found {$result['count']} products<br>";
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test API endpoints
echo "<h2>2. API Endpoints Test</h2>";

$apiEndpoints = [
    'products.php' => 'php/api/products.php',
    'landing-pages.php' => 'php/api/landing-pages.php',
    'categories.php' => 'php/api/categories.php'
];

foreach ($apiEndpoints as $name => $path) {
    echo "<h3>Testing $name:</h3>";
    
    // Test if file exists and is readable
    if (file_exists($path)) {
        echo "✅ File exists<br>";
        
        // Check if it contains the security check
        $content = file_get_contents($path);
        if (strpos($content, "define('SECURITY_CHECK', true);") !== false) {
            echo "✅ Security check properly defined<br>";
        } else {
            echo "❌ Security check not found or incorrectly placed<br>";
        }
        
        // Check if security.php is included after the define
        $definePos = strpos($content, "define('SECURITY_CHECK', true);");
        $includePos = strpos($content, "require_once '../security.php';");
        
        if ($definePos !== false && $includePos !== false && $definePos < $includePos) {
            echo "✅ Security check defined before security.php include<br>";
        } else {
            echo "❌ Security check not properly ordered<br>";
        }
    } else {
        echo "❌ File not found: $path<br>";
    }
}

echo "<h2>3. Security Check</h2>";
echo "SECURITY_CHECK constant defined: " . (defined('SECURITY_CHECK') ? '✅ Yes' : '❌ No') . "<br>";

echo "<h2>4. PHP Configuration</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "PDO MySQL Extension: " . (extension_loaded('pdo_mysql') ? '✅ Loaded' : '❌ Not loaded') . "<br>";
echo "JSON Extension: " . (extension_loaded('json') ? '✅ Loaded' : '❌ Not loaded') . "<br>";

echo "<h2>5. Direct API Test</h2>";
echo "<p>Now test the API endpoints directly in your browser:</p>";
echo "<ul>";
echo "<li><a href='php/api/products.php' target='_blank'>Products API</a></li>";
echo "<li><a href='php/api/landing-pages.php' target='_blank'>Landing Pages API</a></li>";
echo "<li><a href='php/api/categories.php' target='_blank'>Categories API</a></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
