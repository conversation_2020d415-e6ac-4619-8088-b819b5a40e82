<?php

/**
 * Database Configuration for Mossaab Landing Page
 * Uses environment variables for secure configuration
 */

// Security check - only define if not already defined
if (!defined('SECURITY_CHECK')) {
    define('SECURITY_CHECK', true);
}

// Load environment configuration
require_once __DIR__ . '/config/env.php';

// Enable error reporting for debugging
ini_set('display_errors', EnvLoader::getBool('APP_DEBUG', false) ? 1 : 0);
ini_set('display_startup_errors', EnvLoader::getBool('APP_DEBUG', false) ? 1 : 0);
error_reporting(EnvLoader::getBool('APP_DEBUG', false) ? E_ALL : E_ERROR);

// Check required PHP extensions
$required_extensions = ['pdo', 'pdo_mysql'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        die("L'extension PHP {$ext} est requise mais n'est pas installée.");
    }
}

// Database connection parameters from environment variables
define('DB_HOST', EnvLoader::get('DB_HOST', 'localhost'));
define('DB_PORT', EnvLoader::get('DB_PORT', '3307'));
define('DB_NAME', EnvLoader::get('DB_DATABASE', 'mossab-landing-page'));
define('DB_USER', EnvLoader::get('DB_USERNAME', 'root'));
define('DB_PASS', EnvLoader::get('DB_PASSWORD', ''));

// PDO connection options optimized for MariaDB
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
    PDO::ATTR_TIMEOUT => 30,
    PDO::ATTR_PERSISTENT => false
];

// Global PDO connection for MariaDB
try {
    $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);

    // MariaDB specific settings
    $conn->exec("SET time_zone = '+01:00'");
    $conn->exec("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
    $conn->exec("SET SESSION innodb_strict_mode = ON");

    // Log successful connection
    error_log("✅ Connexion réussie à MariaDB 11.5.2 sur localhost:3307/mossab-landing-page");
} catch (PDOException $e) {
    error_log("❌ Erreur de connexion MariaDB: " . $e->getMessage());
    error_log("Code d'erreur: " . $e->getCode());
    error_log("DSN utilisé: " . $dsn);

    // More detailed error for debugging
    $error_details = [
        'host' => DB_HOST,
        'port' => DB_PORT,
        'database' => DB_NAME,
        'user' => DB_USER,
        'error' => $e->getMessage(),
        'code' => $e->getCode()
    ];
    error_log("Détails de l'erreur: " . json_encode($error_details));

    throw new PDOException('Impossible de se connecter à la base de données MariaDB: ' . $e->getMessage());
}

// Function to get PDO connection (for compatibility)
function getPDOConnection()
{
    global $conn;
    if (!$conn) {
        throw new Exception("Connexion à la base de données non disponible");
    }
    return $conn;
}

// Function to test database connection
function testDatabaseConnection()
{
    try {
        $pdo = getPDOConnection();
        $stmt = $pdo->query("SELECT VERSION() as version, DATABASE() as database_name");
        $result = $stmt->fetch();
        return [
            'success' => true,
            'version' => $result['version'],
            'database' => $result['database_name'],
            'host' => DB_HOST,
            'port' => DB_PORT
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Utility functions
function sanitize($data)
{
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

function generateOrderId()
{
    return uniqid('CMD_', true);
}

function formatPrice($price)
{
    return number_format($price, 2, '.', ' ') . ' دج';
}

function isAdminLoggedIn()
{
    return isset($_SESSION['admin_id']);
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Log configuration loaded
error_log("📋 Configuration chargée pour MariaDB mossab-landing-page sur port 3307");
