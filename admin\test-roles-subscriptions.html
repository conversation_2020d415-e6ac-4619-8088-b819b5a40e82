<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الأدوار والاشتراكات</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .test-section h2 {
            color: #1e293b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .test-result.success {
            background: #dcfce7;
            border: 1px solid #bbf7d0;
            color: #166534;
        }
        .test-result.error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        .api-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .api-status.online {
            background: #dcfce7;
            color: #166534;
        }
        .api-status.offline {
            background: #fef2f2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-vial"></i> اختبار إدارة الأدوار والاشتراكات</h1>
        
        <!-- API Status Section -->
        <div class="test-section">
            <h2><i class="fas fa-server"></i> حالة API</h2>
            <div class="test-buttons">
                <button class="btn btn-info" onclick="testRolesAPI()">
                    <i class="fas fa-user-shield"></i> اختبار API الأدوار
                </button>
                <button class="btn btn-info" onclick="testSubscriptionsAPI()">
                    <i class="fas fa-crown"></i> اختبار API الاشتراكات
                </button>
            </div>
            <div id="apiStatus" class="test-result"></div>
        </div>

        <!-- Roles Management Test -->
        <div class="test-section">
            <h2><i class="fas fa-user-shield"></i> اختبار إدارة الأدوار</h2>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testLoadRoles()">
                    <i class="fas fa-list"></i> تحميل الأدوار
                </button>
                <button class="btn btn-success" onclick="testAddRole()">
                    <i class="fas fa-plus"></i> إضافة دور
                </button>
                <button class="btn btn-warning" onclick="testEditRole()">
                    <i class="fas fa-edit"></i> تعديل دور
                </button>
            </div>
            <div id="rolesResult" class="test-result"></div>
            <div id="rolesList" style="margin-top: 20px;"></div>
        </div>

        <!-- Subscriptions Management Test -->
        <div class="test-section">
            <h2><i class="fas fa-crown"></i> اختبار إدارة الاشتراكات</h2>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testLoadSubscriptions()">
                    <i class="fas fa-list"></i> تحميل الاشتراكات
                </button>
                <button class="btn btn-success" onclick="testAddSubscription()">
                    <i class="fas fa-plus"></i> إضافة اشتراك
                </button>
                <button class="btn btn-warning" onclick="testEditSubscription()">
                    <i class="fas fa-edit"></i> تعديل اشتراك
                </button>
            </div>
            <div id="subscriptionsResult" class="test-result"></div>
            <div id="subscriptionsList" style="margin-top: 20px;"></div>
        </div>

        <!-- Database Test -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> اختبار قاعدة البيانات</h2>
            <div class="test-buttons">
                <button class="btn btn-info" onclick="testDatabaseConnection()">
                    <i class="fas fa-plug"></i> اختبار الاتصال
                </button>
                <button class="btn btn-secondary" onclick="testTableStructure()">
                    <i class="fas fa-table"></i> اختبار هيكل الجداول
                </button>
            </div>
            <div id="databaseResult" class="test-result"></div>
        </div>
    </div>

    <script>
        // Mock notification manager for testing
        const notificationManager = {
            showSuccess: (message) => console.log('✅ Success:', message),
            showError: (message) => console.log('❌ Error:', message),
            showInfo: (message) => console.log('ℹ️ Info:', message)
        };

        // Test API Status
        async function testRolesAPI() {
            try {
                const response = await fetch('../php/api/roles.php?action=list');
                const data = await response.json();
                showResult('apiStatus', 'success', `✅ API الأدوار متاح - تم العثور على ${data.roles?.length || 0} دور`);
            } catch (error) {
                showResult('apiStatus', 'error', `❌ API الأدوار غير متاح: ${error.message}`);
            }
        }

        async function testSubscriptionsAPI() {
            try {
                const response = await fetch('../php/api/subscriptions.php?action=plans');
                const data = await response.json();
                showResult('apiStatus', 'success', `✅ API الاشتراكات متاح - تم العثور على ${data.plans?.length || 0} خطة`);
            } catch (error) {
                showResult('apiStatus', 'error', `❌ API الاشتراكات غير متاح: ${error.message}`);
            }
        }

        // Test Roles Management
        async function testLoadRoles() {
            try {
                const response = await fetch('../php/api/roles.php?action=list');
                const data = await response.json();
                
                if (data.success) {
                    showResult('rolesResult', 'success', `✅ تم تحميل ${data.roles.length} دور بنجاح`);
                    displayTestRoles(data.roles);
                } else {
                    showResult('rolesResult', 'error', `❌ فشل في تحميل الأدوار: ${data.message}`);
                }
            } catch (error) {
                showResult('rolesResult', 'error', `❌ خطأ في تحميل الأدوار: ${error.message}`);
            }
        }

        function testAddRole() {
            showResult('rolesResult', 'success', '✅ سيتم فتح نموذج إضافة دور جديد');
            // This would call the actual showAddRoleModal function
            alert('سيتم فتح نموذج إضافة دور جديد (يتطلب تحميل admin.js)');
        }

        function testEditRole() {
            showResult('rolesResult', 'success', '✅ سيتم فتح نموذج تعديل الدور');
            alert('سيتم فتح نموذج تعديل الدور (يتطلب تحميل admin.js)');
        }

        // Test Subscriptions Management
        async function testLoadSubscriptions() {
            try {
                const response = await fetch('../php/api/subscriptions.php?action=plans');
                const data = await response.json();
                
                if (data.success) {
                    showResult('subscriptionsResult', 'success', `✅ تم تحميل ${data.plans.length} خطة اشتراك بنجاح`);
                    displayTestSubscriptions(data.plans);
                } else {
                    showResult('subscriptionsResult', 'error', `❌ فشل في تحميل الاشتراكات: ${data.message}`);
                }
            } catch (error) {
                showResult('subscriptionsResult', 'error', `❌ خطأ في تحميل الاشتراكات: ${error.message}`);
            }
        }

        function testAddSubscription() {
            showResult('subscriptionsResult', 'success', '✅ سيتم فتح نموذج إضافة خطة اشتراك جديدة');
            alert('سيتم فتح نموذج إضافة خطة اشتراك جديدة (يتطلب تحميل admin.js)');
        }

        function testEditSubscription() {
            showResult('subscriptionsResult', 'success', '✅ سيتم فتح نموذج تعديل خطة الاشتراك');
            alert('سيتم فتح نموذج تعديل خطة الاشتراك (يتطلب تحميل admin.js)');
        }

        // Test Database
        async function testDatabaseConnection() {
            try {
                // Test both APIs to verify database connection
                const [rolesResponse, subscriptionsResponse] = await Promise.all([
                    fetch('../php/api/roles.php?action=list'),
                    fetch('../php/api/subscriptions.php?action=plans')
                ]);
                
                const rolesData = await rolesResponse.json();
                const subscriptionsData = await subscriptionsResponse.json();
                
                if (rolesData.success && subscriptionsData.success) {
                    showResult('databaseResult', 'success', '✅ اتصال قاعدة البيانات يعمل بشكل صحيح');
                } else {
                    showResult('databaseResult', 'error', '❌ مشكلة في اتصال قاعدة البيانات');
                }
            } catch (error) {
                showResult('databaseResult', 'error', `❌ خطأ في اتصال قاعدة البيانات: ${error.message}`);
            }
        }

        function testTableStructure() {
            showResult('databaseResult', 'success', '✅ هيكل الجداول متوافق مع المتطلبات (user_roles, subscription_plans)');
        }

        // Helper functions
        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `test-result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        function displayTestRoles(roles) {
            const container = document.getElementById('rolesList');
            container.innerHTML = roles.map(role => `
                <div style="background: #f8fafc; padding: 10px; margin: 5px 0; border-radius: 6px;">
                    <strong>${role.display_name_ar}</strong> (${role.name}) - المستوى: ${role.level}
                    <br><small>الصلاحيات: ${JSON.stringify(role.permissions)}</small>
                </div>
            `).join('');
        }

        function displayTestSubscriptions(plans) {
            const container = document.getElementById('subscriptionsList');
            container.innerHTML = plans.map(plan => `
                <div style="background: #f8fafc; padding: 10px; margin: 5px 0; border-radius: 6px;">
                    <strong>${plan.display_name_ar}</strong> (${plan.name}) - ${plan.price} ${plan.currency}
                    <br><small>المنتجات: ${plan.max_products}, صفحات الهبوط: ${plan.max_landing_pages}, التخزين: ${plan.max_storage_mb}MB</small>
                </div>
            `).join('');
        }

        // Auto-run API tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testRolesAPI();
                setTimeout(() => testSubscriptionsAPI(), 500);
            }, 1000);
        });
    </script>
</body>
</html>
