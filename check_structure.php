<?php
require_once __DIR__ . '/php/config.php';

try {
    $pdo = getPDOConnection();
    $stmt = $pdo->query('DESCRIBE produits');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Structure de la table produits:\n";
    foreach ($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
    }
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}