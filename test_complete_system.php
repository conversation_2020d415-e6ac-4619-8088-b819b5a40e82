<?php
/**
 * Complete System Test for MariaDB 11.5.2 on localhost:3307
 * Tests all components of the landing page system
 */

require_once 'php/config.php';

echo "<h1>🧪 Complete System Test - MariaDB Port 3307</h1>\n";
echo "<style>
body{font-family:Arial;margin:20px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);min-height:100vh;} 
.container{max-width:1200px;margin:0 auto;background:white;padding:30px;border-radius:15px;box-shadow:0 10px 30px rgba(0,0,0,0.2);}
.success{color:#28a745;font-weight:bold;} 
.error{color:#dc3545;font-weight:bold;} 
.warning{color:#ffc107;font-weight:bold;} 
.info{color:#17a2b8;font-weight:bold;} 
pre{background:#f8f9fa;padding:15px;border-radius:5px;border-left:4px solid #007bff;} 
table{border-collapse:collapse;width:100%;margin:15px 0;} 
th,td{border:1px solid #dee2e6;padding:12px;text-align:left;} 
th{background:#e9ecef;font-weight:bold;}
.test-section{background:#f0f8ff;padding:25px;margin:20px 0;border-radius:12px;border-left:5px solid #007bff;}
.highlight{background:#fff3cd;padding:15px;border-radius:8px;margin:15px 0;}
.progress{width:100%;height:20px;background:#e9ecef;border-radius:10px;overflow:hidden;margin:15px 0;}
.progress-bar{height:100%;background:linear-gradient(90deg,#28a745,#20c997);transition:width 0.5s ease;}
.test-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:20px;margin:20px 0;}
.test-card{background:#f8f9fa;border:1px solid #dee2e6;border-radius:8px;padding:20px;}
.test-card.pass{border-color:#28a745;background:#f8fff9;}
.test-card.fail{border-color:#dc3545;background:#fff5f5;}
.test-card.warning{border-color:#ffc107;background:#fffbf0;}
</style>\n";

echo "<div class='container'>\n";

$totalTests = 8;
$passedTests = 0;
$testResults = [];

try {
    // Test 1: Database Connection
    echo "<div class='test-section'>\n";
    echo "<h2>🔌 Test 1: MariaDB Connection</h2>\n";
    
    $connectionTest = testDatabaseConnection();
    if ($connectionTest['success']) {
        echo "<p class='success'>✅ Connected to MariaDB {$connectionTest['version']}</p>\n";
        echo "<p class='info'>Database: {$connectionTest['database']} on {$connectionTest['host']}:{$connectionTest['port']}</p>\n";
        $testResults['connection'] = 'pass';
        $passedTests++;
    } else {
        echo "<p class='error'>❌ Connection failed: {$connectionTest['error']}</p>\n";
        $testResults['connection'] = 'fail';
    }
    echo "</div>\n";
    
    $pdo = getPDOConnection();
    
    // Test 2: Database Structure
    echo "<div class='test-section'>\n";
    echo "<h2>🗂️ Test 2: Database Structure</h2>\n";
    
    $requiredTables = ['produits', 'categories', 'landing_pages', 'landing_page_images'];
    $existingTables = [];
    
    $stmt = $pdo->query("SHOW TABLES");
    $allTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $structureOk = true;
    foreach ($requiredTables as $table) {
        if (in_array($table, $allTables)) {
            echo "<p class='success'>✅ Table '$table' exists</p>\n";
            $existingTables[] = $table;
        } else {
            echo "<p class='error'>❌ Table '$table' missing</p>\n";
            $structureOk = false;
        }
    }
    
    if ($structureOk) {
        $testResults['structure'] = 'pass';
        $passedTests++;
    } else {
        $testResults['structure'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 3: Products Data
    echo "<div class='test-section'>\n";
    echo "<h2>📦 Test 3: Products Data</h2>\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits WHERE actif = 1");
    $productsCount = $stmt->fetch()['count'];
    
    echo "<p class='info'>Active products found: $productsCount</p>\n";
    
    if ($productsCount >= 5) {
        echo "<p class='success'>✅ Sufficient products available ($productsCount)</p>\n";
        
        // Show sample products
        $stmt = $pdo->query("SELECT id, titre, type, prix FROM produits WHERE actif = 1 LIMIT 5");
        $products = $stmt->fetchAll();
        
        echo "<table>\n";
        echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>Price</th></tr>\n";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>" . htmlspecialchars($product['titre']) . "</td>";
            echo "<td>{$product['type']}</td>";
            echo "<td>{$product['prix']} DZD</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        $testResults['products'] = 'pass';
        $passedTests++;
    } else {
        echo "<p class='error'>❌ Insufficient products (need 5, found $productsCount)</p>\n";
        $testResults['products'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 4: Categories System
    echo "<div class='test-section'>\n";
    echo "<h2>🗂️ Test 4: Categories System</h2>\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE actif = 1");
    $categoriesCount = $stmt->fetch()['count'];
    
    echo "<p class='info'>Active categories found: $categoriesCount</p>\n";
    
    if ($categoriesCount >= 5) {
        echo "<p class='success'>✅ Categories system working ($categoriesCount categories)</p>\n";
        
        // Show categories
        $stmt = $pdo->query("SELECT nom_ar, nom_en, icone, couleur FROM categories WHERE actif = 1");
        $categories = $stmt->fetchAll();
        
        echo "<table>\n";
        echo "<tr><th>Arabic Name</th><th>English Name</th><th>Icon</th><th>Color</th></tr>\n";
        foreach ($categories as $category) {
            echo "<tr>";
            echo "<td>{$category['nom_ar']}</td>";
            echo "<td>{$category['nom_en']}</td>";
            echo "<td><i class='{$category['icone']}' style='color:{$category['couleur']}'></i> {$category['icone']}</td>";
            echo "<td><span style='background:{$category['couleur']};color:white;padding:2px 8px;border-radius:3px;'>{$category['couleur']}</span></td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        $testResults['categories'] = 'pass';
        $passedTests++;
    } else {
        echo "<p class='error'>❌ Insufficient categories (need 5, found $categoriesCount)</p>\n";
        $testResults['categories'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 5: Landing Pages
    echo "<div class='test-section'>\n";
    echo "<h2>🎨 Test 5: Landing Pages</h2>\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM landing_pages");
    $landingPagesCount = $stmt->fetch()['count'];
    
    echo "<p class='info'>Landing pages found: $landingPagesCount</p>\n";
    
    if ($landingPagesCount > 0) {
        echo "<p class='success'>✅ Landing pages system working ($landingPagesCount pages)</p>\n";
        
        // Show landing pages
        $stmt = $pdo->query("
            SELECT lp.id, lp.titre, lp.template_id, p.titre as product_title 
            FROM landing_pages lp 
            LEFT JOIN produits p ON lp.produit_id = p.id 
            LIMIT 5
        ");
        $pages = $stmt->fetchAll();
        
        echo "<table>\n";
        echo "<tr><th>ID</th><th>Page Title</th><th>Template</th><th>Product</th></tr>\n";
        foreach ($pages as $page) {
            echo "<tr>";
            echo "<td>{$page['id']}</td>";
            echo "<td>" . htmlspecialchars($page['titre']) . "</td>";
            echo "<td>{$page['template_id']}</td>";
            echo "<td>" . htmlspecialchars($page['product_title']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        $testResults['landing_pages'] = 'pass';
        $passedTests++;
    } else {
        echo "<p class='warning'>⚠️ No landing pages found</p>\n";
        $testResults['landing_pages'] = 'warning';
    }
    echo "</div>\n";
    
    // Test 6: API Endpoints
    echo "<div class='test-section'>\n";
    echo "<h2>🔌 Test 6: API Endpoints</h2>\n";
    
    $apis = [
        'Products API' => 'php/api/products.php',
        'Categories API' => 'php/api/categories.php',
        'Landing Pages API' => 'php/api/landing-pages.php',
        'Templates API' => 'php/api/templates.php'
    ];
    
    $apiResults = [];
    foreach ($apis as $name => $endpoint) {
        if (file_exists($endpoint)) {
            echo "<p class='success'>✅ $name file exists</p>\n";
            $apiResults[$name] = true;
        } else {
            echo "<p class='error'>❌ $name file missing</p>\n";
            $apiResults[$name] = false;
        }
    }
    
    $workingApis = array_sum($apiResults);
    if ($workingApis === count($apis)) {
        $testResults['apis'] = 'pass';
        $passedTests++;
    } else {
        $testResults['apis'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 7: Admin Panel
    echo "<div class='test-section'>\n";
    echo "<h2>🏠 Test 7: Admin Panel</h2>\n";
    
    $adminFiles = [
        'Main Admin' => 'admin/index.html',
        'Categories Management' => 'admin/categories_management.html',
        'Payment Settings' => 'admin/payment_settings.html'
    ];
    
    $adminResults = [];
    foreach ($adminFiles as $name => $file) {
        if (file_exists($file)) {
            echo "<p class='success'>✅ $name exists</p>\n";
            $adminResults[$name] = true;
        } else {
            echo "<p class='error'>❌ $name missing</p>\n";
            $adminResults[$name] = false;
        }
    }
    
    $workingAdmin = array_sum($adminResults);
    if ($workingAdmin === count($adminFiles)) {
        $testResults['admin'] = 'pass';
        $passedTests++;
    } else {
        $testResults['admin'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 8: System Integration
    echo "<div class='test-section'>\n";
    echo "<h2>🔗 Test 8: System Integration</h2>\n";
    
    // Test foreign key relationships
    $stmt = $pdo->query("
        SELECT p.id, p.titre, c.nom_ar as category_name 
        FROM produits p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.actif = 1 
        LIMIT 3
    ");
    $productCategories = $stmt->fetchAll();
    
    $integrationOk = true;
    foreach ($productCategories as $pc) {
        if ($pc['category_name']) {
            echo "<p class='success'>✅ Product '{$pc['titre']}' linked to category '{$pc['category_name']}'</p>\n";
        } else {
            echo "<p class='warning'>⚠️ Product '{$pc['titre']}' not linked to any category</p>\n";
            $integrationOk = false;
        }
    }
    
    if ($integrationOk) {
        $testResults['integration'] = 'pass';
        $passedTests++;
    } else {
        $testResults['integration'] = 'warning';
    }
    echo "</div>\n";
    
    // Final Results
    echo "<div class='test-section'>\n";
    echo "<h2>📊 Final Test Results</h2>\n";
    
    $successRate = ($passedTests / $totalTests) * 100;
    
    echo "<div class='test-grid'>\n";
    foreach ($testResults as $test => $result) {
        $cardClass = $result;
        $icon = $result === 'pass' ? '✅' : ($result === 'warning' ? '⚠️' : '❌');
        $status = $result === 'pass' ? 'PASSED' : ($result === 'warning' ? 'WARNING' : 'FAILED');
        
        echo "<div class='test-card $cardClass'>\n";
        echo "<h4>$icon " . ucfirst($test) . "</h4>\n";
        echo "<p>Status: <strong>$status</strong></p>\n";
        echo "</div>\n";
    }
    echo "</div>\n";
    
    echo "<div class='highlight'>\n";
    echo "<h3>🎯 Overall System Status</h3>\n";
    echo "<p><strong>Tests Passed:</strong> $passedTests / $totalTests (" . round($successRate, 1) . "%)</p>\n";
    
    if ($successRate >= 90) {
        echo "<p class='success'>🎉 System is fully operational and ready for production!</p>\n";
    } elseif ($successRate >= 70) {
        echo "<p class='warning'>⚠️ System is mostly working but needs some attention</p>\n";
    } else {
        echo "<p class='error'>❌ System has significant issues that need to be addressed</p>\n";
    }
    echo "</div>\n";
    
    echo "<h3>🔗 Quick Access Links:</h3>\n";
    echo "<div style='display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin:20px 0;'>\n";
    echo "<a href='admin/' style='background:#007bff;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🏠 Admin Panel</a>\n";
    echo "<a href='admin/categories_management.html' style='background:#28a745;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🗂️ Categories</a>\n";
    echo "<a href='admin/payment_settings.html' style='background:#fd7e14;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>💳 Payment Settings</a>\n";
    echo "<a href='verify_mariadb_connection.php' style='background:#6f42c1;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🔍 Database Check</a>\n";
    echo "</div>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='test-section'>\n";
    echo "<h2 class='error'>❌ Critical Test Error</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    echo "</div>\n";
}

echo "</div>\n";
?>

<script>
console.log('🧪 Complete system test finished');
console.log('MariaDB database: mossab-landing-page on localhost:3307');
</script>
