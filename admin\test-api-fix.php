<?php
echo "🔧 TESTING STORES API FIX\n";
echo "========================\n\n";

// Test the stores API directly
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/stores.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "📊 API Test Results:\n";
echo "HTTP Status: {$httpCode}\n";

if ($httpCode === 200) {
    echo "✅ API accessible\n";
    
    $data = json_decode($response, true);
    if ($data && isset($data['success'])) {
        echo "✅ Valid JSON response\n";
        echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        echo "Message: " . ($data['message'] ?? 'No message') . "\n";
        echo "Total stores: " . ($data['total'] ?? 'Not set') . "\n";
        
        if ($data['success']) {
            echo "🎉 STORES API FIX SUCCESSFUL!\n";
        } else {
            echo "❌ API returned error: " . ($data['message'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "❌ Invalid JSON response\n";
        echo "Raw response: " . substr($response, 0, 300) . "...\n";
    }
} else {
    echo "❌ API not accessible\n";
    echo "Response: " . substr($response, 0, 300) . "...\n";
}

echo "\n📋 Next Steps:\n";
echo "1. If API is working, refresh admin panel\n";
echo "2. Go to إعدادات النظام → المتاجر\n";
echo "3. Store management should load within 3 seconds\n";
?>
