<?php
/**
 * Fix Critical Config Path Issues
 * Resolves "Failed to open stream: No such file or directory" errors in API files
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>إصلاح مشاكل مسارات الملفات الحرجة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
        .fix-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; white-space: pre-wrap; }
        .file-list { background: #f8f9fa; padding: 15px; border-radius: 5px; }
        .file-list ul { margin: 0; padding-left: 20px; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .test-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 إصلاح مشاكل مسارات الملفات الحرجة</h1>";
echo "<p>حل مشاكل 'Failed to open stream: No such file or directory' في ملفات API</p>";

$fixedFiles = [];
$errors = [];

try {
    echo "<div class='section'>";
    echo "<h2>1️⃣ فحص هيكل الملفات الحالي</h2>";
    
    // Check current file structure
    $configPaths = [
        'php/config.php' => file_exists('php/config.php'),
        'config/config.php' => file_exists('config/config.php'),
        'config.php' => file_exists('config.php')
    ];
    
    echo "<div class='fix-result'>";
    echo "<h4>ملفات Config الموجودة:</h4>";
    echo "<ul>";
    foreach ($configPaths as $path => $exists) {
        $status = $exists ? '✅ موجود' : '❌ غير موجود';
        echo "<li><code>{$path}</code> - {$status}</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // Determine the correct config path
    $correctConfigPath = '';
    if (file_exists('php/config.php')) {
        $correctConfigPath = '../config.php'; // From php/api/ to php/config.php
        echo "<div class='success'>✅ سيتم استخدام php/config.php كملف التكوين الرئيسي</div>";
    } elseif (file_exists('config/config.php')) {
        $correctConfigPath = '../../config/config.php'; // From php/api/ to config/config.php
        echo "<div class='success'>✅ سيتم استخدام config/config.php كملف التكوين الرئيسي</div>";
    } elseif (file_exists('config.php')) {
        $correctConfigPath = '../../config.php'; // From php/api/ to config.php
        echo "<div class='success'>✅ سيتم استخدام config.php كملف التكوين الرئيسي</div>";
    } else {
        echo "<div class='error'>❌ لم يتم العثور على أي ملف config.php</div>";
        throw new Exception("No config.php file found");
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2️⃣ إصلاح ملفات API</h2>";
    
    // Get all PHP files in the API directory
    $apiFiles = glob('php/api/*.php');
    
    foreach ($apiFiles as $apiFile) {
        echo "<div class='fix-result'>";
        echo "<h4>إصلاح: " . basename($apiFile) . "</h4>";
        
        try {
            $content = file_get_contents($apiFile);
            $originalContent = $content;
            
            // Fix various config path patterns
            $patterns = [
                "/require_once\s+['\"]\.\.\/config\.php['\"]/",
                "/require_once\s+['\"]\.\.\/\.\.\/config\.php['\"]/",
                "/include_once\s+['\"]\.\.\/config\.php['\"]/",
                "/include\s+['\"]\.\.\/config\.php['\"]/",
                "/require\s+['\"]\.\.\/config\.php['\"]/",
            ];
            
            $replacement = "require_once '{$correctConfigPath}'";
            
            foreach ($patterns as $pattern) {
                $content = preg_replace($pattern, $replacement, $content);
            }
            
            // Also fix any hardcoded paths
            $content = str_replace("'../config.php'", "'{$correctConfigPath}'", $content);
            $content = str_replace('"../config.php"', "'{$correctConfigPath}'", $content);
            
            if ($content !== $originalContent) {
                if (file_put_contents($apiFile, $content)) {
                    echo "<div class='success'>✅ تم إصلاح مسار config.php</div>";
                    $fixedFiles[] = $apiFile;
                } else {
                    echo "<div class='error'>❌ فشل في كتابة الملف</div>";
                    $errors[] = "Failed to write: " . $apiFile;
                }
            } else {
                echo "<div class='info'>ℹ️ لا يحتاج إلى إصلاح</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
            $errors[] = $apiFile . ": " . $e->getMessage();
        }
        
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3️⃣ إنشاء ملف config موحد محسن</h2>";
    
    echo "<div class='fix-result'>";
    echo "<h4>إنشاء config.php محسن</h4>";
    
    // Create an enhanced config file that works from any location
    $enhancedConfigContent = '<?php
/**
 * Enhanced Config File - Works from any location
 * Automatically detects the correct path and loads configuration
 */

// Prevent direct access
if (!defined("CONFIG_LOADED")) {
    define("CONFIG_LOADED", true);
}

// Auto-detect the correct base path
function getBasePath() {
    $currentDir = __DIR__;
    
    // Check if we\'re in the root directory
    if (file_exists($currentDir . "/php/config.php")) {
        return $currentDir;
    }
    
    // Check if we\'re in php/ directory
    if (file_exists($currentDir . "/config.php")) {
        return dirname($currentDir);
    }
    
    // Check if we\'re in php/api/ directory
    if (file_exists($currentDir . "/../config.php")) {
        return dirname(dirname($currentDir));
    }
    
    // Check if we\'re in a subdirectory
    $parentDir = dirname($currentDir);
    if (file_exists($parentDir . "/php/config.php")) {
        return $parentDir;
    }
    
    // Default to current directory
    return $currentDir;
}

$basePath = getBasePath();

// Database configuration
$dbConfig = [
    "host" => "localhost",
    "dbname" => "mossaab_landing_page",
    "username" => "root",
    "password" => "",
    "charset" => "utf8mb4",
    "options" => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]
];

// Global PDO connection
$conn = null;
$pdo = null;

/**
 * Get PDO connection
 */
function getPDOConnection() {
    global $pdo, $dbConfig;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host={$dbConfig[\"host\"]};dbname={$dbConfig[\"dbname\"]};charset={$dbConfig[\"charset\"]}";
            $pdo = new PDO($dsn, $dbConfig[\"username\"], $dbConfig[\"password\"], $dbConfig[\"options\"]);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }
    
    return $pdo;
}

/**
 * Get legacy connection (for backward compatibility)
 */
function getConnection() {
    global $conn;
    
    if ($conn === null) {
        $conn = getPDOConnection();
    }
    
    return $conn;
}

// Initialize connection
try {
    $conn = getPDOConnection();
    $pdo = $conn;
} catch (Exception $e) {
    error_log("Failed to initialize database connection: " . $e->getMessage());
}

// Application configuration
define("APP_NAME", "Mossaab Landing Page");
define("APP_VERSION", "2.0.0");
define("BASE_PATH", $basePath);
define("UPLOAD_PATH", $basePath . "/uploads");
define("IMAGES_PATH", $basePath . "/images");

// Security settings
define("SESSION_TIMEOUT", 3600); // 1 hour
define("MAX_LOGIN_ATTEMPTS", 5);
define("LOCKOUT_TIME", 900); // 15 minutes

// API settings
define("API_TIMEOUT", 30);
define("MAX_API_REQUESTS_PER_MINUTE", 60);

// File upload settings
define("MAX_FILE_SIZE", 10 * 1024 * 1024); // 10MB
define("ALLOWED_IMAGE_TYPES", ["jpg", "jpeg", "png", "gif", "webp"]);

// Subscription limits (default values)
define("DEFAULT_MAX_PRODUCTS", 100);
define("DEFAULT_MAX_LANDING_PAGES", 50);
define("DEFAULT_MAX_STORAGE_MB", 1000);

// Error reporting (disable in production)
if (!defined("PRODUCTION")) {
    error_reporting(E_ALL);
    ini_set("display_errors", 1);
}

// Set timezone
date_default_timezone_set("Africa/Algiers");

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Helper functions
function isLoggedIn() {
    return isset($_SESSION["user_id"]) && !empty($_SESSION["user_id"]);
}

function getCurrentUserId() {
    return $_SESSION["user_id"] ?? null;
}

function getCurrentUserRole() {
    return $_SESSION["user_role"] ?? null;
}

function isAdmin() {
    return getCurrentUserRole() === "admin" || getCurrentUserRole() === 1;
}

function redirectToLogin() {
    header("Location: /login.html");
    exit;
}

function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header("Content-Type: application/json; charset=utf-8");
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

function logError($message, $context = []) {
    $logMessage = date("Y-m-d H:i:s") . " - " . $message;
    if (!empty($context)) {
        $logMessage .= " - Context: " . json_encode($context);
    }
    error_log($logMessage);
}

// Load additional configuration files if they exist
$additionalConfigs = [
    $basePath . "/config/database.php",
    $basePath . "/config/security.php",
    $basePath . "/php/security.php"
];

foreach ($additionalConfigs as $configFile) {
    if (file_exists($configFile)) {
        require_once $configFile;
    }
}

// Configuration loaded successfully
define("CONFIG_INITIALIZED", true);
?>';
    
    // Save the enhanced config file
    if (file_put_contents('php/config-enhanced.php', $enhancedConfigContent)) {
        echo "<div class='success'>✅ تم إنشاء ملف config محسن: php/config-enhanced.php</div>";
    } else {
        echo "<div class='error'>❌ فشل في إنشاء ملف config محسن</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4️⃣ اختبار ملفات API بعد الإصلاح</h2>";
    
    $testResults = [];
    
    echo "<div class='test-grid'>";
    
    // Test key API files
    $keyApiFiles = [
        'php/api/templates.php',
        'php/api/landing-pages.php',
        'php/api/users.php',
        'php/api/subscription-limits.php'
    ];
    
    foreach ($keyApiFiles as $apiFile) {
        echo "<div class='test-card'>";
        echo "<h4>" . basename($apiFile) . "</h4>";
        
        if (file_exists($apiFile)) {
            try {
                // Test if the file can be included without errors
                ob_start();
                $errorOutput = '';
                
                // Capture errors
                set_error_handler(function($severity, $message, $file, $line) use (&$errorOutput) {
                    $errorOutput .= "Error: {$message} in {$file} on line {$line}\n";
                });
                
                // Try to include the file
                include $apiFile;
                
                restore_error_handler();
                $output = ob_get_clean();
                
                if (empty($errorOutput)) {
                    echo "<div class='success'>✅ يتم تحميله بدون أخطاء</div>";
                    $testResults[$apiFile] = true;
                } else {
                    echo "<div class='error'>❌ أخطاء في التحميل:</div>";
                    echo "<div class='code-block'>" . htmlspecialchars($errorOutput) . "</div>";
                    $testResults[$apiFile] = false;
                }
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
                $testResults[$apiFile] = false;
            } catch (Error $e) {
                echo "<div class='error'>❌ خطأ فادح: " . $e->getMessage() . "</div>";
                $testResults[$apiFile] = false;
            }
        } else {
            echo "<div class='error'>❌ الملف غير موجود</div>";
            $testResults[$apiFile] = false;
        }
        
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>✅ ملخص الإصلاحات</h2>";
    
    $successCount = count($fixedFiles);
    $errorCount = count($errors);
    $testPassCount = count(array_filter($testResults));
    $testTotalCount = count($testResults);
    
    echo "<div class='success'>";
    echo "<h3>🎉 تم إصلاح مشاكل مسارات الملفات!</h3>";
    echo "<ul>";
    echo "<li>✅ تم إصلاح {$successCount} ملف API</li>";
    echo "<li>✅ تم إنشاء ملف config محسن</li>";
    echo "<li>✅ نجح {$testPassCount}/{$testTotalCount} اختبارات API</li>";
    if ($errorCount > 0) {
        echo "<li>⚠️ {$errorCount} أخطاء تحتاج إلى مراجعة</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    if (!empty($fixedFiles)) {
        echo "<div class='info'>";
        echo "<h4>📁 الملفات التي تم إصلاحها:</h4>";
        echo "<ul>";
        foreach ($fixedFiles as $file) {
            echo "<li><code>" . htmlspecialchars($file) . "</code></li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($errors)) {
        echo "<div class='warning'>";
        echo "<h4>⚠️ أخطاء تحتاج إلى مراجعة:</h4>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div class='info'>";
    echo "<h4>🔄 الخطوات التالية:</h4>";
    echo "<ol>";
    echo "<li>اختبر APIs من المتصفح مباشرة</li>";
    echo "<li>تحقق من عدم وجود أخطاء في ملفات السجل</li>";
    echo "<li>اختبر لوحة التحكم الإدارية</li>";
    echo "<li>تحقق من وظائف صفحات الهبوط</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div class='code-block'>";
    echo "<!-- اختبار APIs -->\n";
    echo "Templates API: /php/api/templates.php?action=get_templates\n";
    echo "Landing Pages API: /php/api/landing-pages.php\n";
    echo "Users API: /php/api/users.php?action=list\n";
    echo "Subscription Limits API: /php/api/subscription-limits.php?action=limits";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
