<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الكامل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
        }
        .test-section.success {
            border-color: #28a745;
            background: #f8fff9;
        }
        .test-section.error {
            border-color: #dc3545;
            background: #fff5f5;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #6c757d; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
        .template-preview {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار النظام الكامل - المنتجات والقوالب</h1>
        
        <!-- Products Test -->
        <div class="test-section" id="products-section">
            <h2>📦 اختبار المنتجات</h2>
            <p>فحص المنتجات المتاحة في النظام:</p>
            <button class="btn" onclick="testProducts()">🔍 فحص المنتجات</button>
            <div id="products-results"></div>
        </div>

        <!-- Templates Test -->
        <div class="test-section" id="templates-section">
            <h2>🎨 اختبار القوالب</h2>
            <p>فحص قوالب صفحات الهبوط المتاحة:</p>
            <button class="btn" onclick="testTemplates()">🎨 فحص القوالب</button>
            <div id="templates-results"></div>
        </div>

        <!-- Integration Test -->
        <div class="test-section" id="integration-section">
            <h2>🔗 اختبار التكامل</h2>
            <p>اختبار التكامل بين المنتجات والقوالب:</p>
            <button class="btn" onclick="testIntegration()">🔗 اختبار التكامل</button>
            <div id="integration-results"></div>
        </div>

        <!-- Admin Panel Links -->
        <div class="test-section">
            <h2>🚀 روابط سريعة</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h4>لوحة التحكم</h4>
                    <a href="admin/" class="btn btn-success">🏠 لوحة التحكم</a>
                </div>
                <div class="test-card">
                    <h4>APIs مباشرة</h4>
                    <a href="php/api/products.php" class="btn">📦 API المنتجات</a>
                    <a href="php/api/templates.php?action=get_templates" class="btn">🎨 API القوالب</a>
                </div>
                <div class="test-card">
                    <h4>أدوات التشخيص</h4>
                    <a href="debug_products.php" class="btn">🔍 تشخيص المنتجات</a>
                    <a href="test_api_products.html" class="btn">🧪 اختبار API</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testProducts() {
            const resultsDiv = document.getElementById('products-results');
            resultsDiv.innerHTML = '<p>🔄 جاري فحص المنتجات...</p>';
            
            try {
                const response = await fetch('/php/api/products.php');
                const data = await response.json();
                
                if (data.success && data.products) {
                    const products = data.products.filter(p => p.actif == 1);
                    
                    let html = `<h3>✅ تم العثور على ${products.length} منتجات نشطة:</h3>`;
                    html += '<div class="test-grid">';
                    
                    products.forEach(product => {
                        html += `
                            <div class="test-card">
                                <h4>${product.titre}</h4>
                                <p><strong>النوع:</strong> ${product.type}</p>
                                <p><strong>السعر:</strong> ${product.prix} دج</p>
                                <p><strong>المخزون:</strong> ${product.stock}</p>
                                <span class="status-indicator status-success"></span>
                            </div>
                        `;
                    });
                    
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                    
                    document.getElementById('products-section').className = 'test-section success';
                } else {
                    throw new Error('لا توجد منتجات أو خطأ في API');
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p style="color: red;">❌ خطأ: ${error.message}</p>`;
                document.getElementById('products-section').className = 'test-section error';
            }
        }

        async function testTemplates() {
            const resultsDiv = document.getElementById('templates-results');
            resultsDiv.innerHTML = '<p>🔄 جاري فحص القوالب...</p>';
            
            try {
                const response = await fetch('/php/api/templates.php?action=get_templates');
                const data = await response.json();
                
                if (data.success && data.templates) {
                    let html = `<h3>✅ تم العثور على ${data.templates.length} قوالب:</h3>`;
                    html += '<div class="test-grid">';
                    
                    data.templates.forEach(template => {
                        html += `
                            <div class="test-card">
                                <h4><i class="${template.icon}"></i> ${template.name}</h4>
                                <p>${template.description}</p>
                                <p><strong>المعرف:</strong> ${template.id}</p>
                                <span class="status-indicator status-success"></span>
                            </div>
                        `;
                    });
                    
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                    
                    document.getElementById('templates-section').className = 'test-section success';
                } else {
                    throw new Error('لا توجد قوالب أو خطأ في API');
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p style="color: red;">❌ خطأ: ${error.message}</p>`;
                document.getElementById('templates-section').className = 'test-section error';
            }
        }

        async function testIntegration() {
            const resultsDiv = document.getElementById('integration-results');
            resultsDiv.innerHTML = '<p>🔄 جاري اختبار التكامل...</p>';
            
            try {
                // Test both APIs
                const [productsResponse, templatesResponse] = await Promise.all([
                    fetch('/php/api/products.php'),
                    fetch('/php/api/templates.php?action=get_templates')
                ]);
                
                const productsData = await productsResponse.json();
                const templatesData = await templatesResponse.json();
                
                if (productsData.success && templatesData.success) {
                    const activeProducts = productsData.products.filter(p => p.actif == 1);
                    const templates = templatesData.templates;
                    
                    let html = '<h3>✅ اختبار التكامل ناجح!</h3>';
                    html += `<p>المنتجات النشطة: ${activeProducts.length}</p>`;
                    html += `<p>القوالب المتاحة: ${templates.length}</p>`;
                    
                    // Test template matching
                    html += '<h4>🎯 مطابقة القوالب مع المنتجات:</h4>';
                    html += '<div class="test-grid">';
                    
                    activeProducts.forEach(product => {
                        const matchingTemplate = templates.find(t => t.id === product.type) || templates.find(t => t.id === 'custom');
                        html += `
                            <div class="test-card">
                                <h5>${product.titre}</h5>
                                <p><strong>نوع المنتج:</strong> ${product.type}</p>
                                <p><strong>القالب المطابق:</strong> ${matchingTemplate.name}</p>
                                <span class="status-indicator status-success"></span>
                            </div>
                        `;
                    });
                    
                    html += '</div>';
                    
                    // Test template content generation
                    html += '<h4>📝 اختبار توليد المحتوى:</h4>';
                    const sampleProduct = activeProducts[0];
                    const sampleTemplate = templates.find(t => t.id === sampleProduct.type) || templates.find(t => t.id === 'custom');
                    
                    if (sampleProduct && sampleTemplate) {
                        const generatedTitle = sampleTemplate.content.titre.replace('{product_title}', sampleProduct.titre);
                        html += `
                            <div class="template-preview">
                                <h5>مثال على توليد المحتوى:</h5>
                                <p><strong>المنتج:</strong> ${sampleProduct.titre}</p>
                                <p><strong>القالب:</strong> ${sampleTemplate.name}</p>
                                <p><strong>العنوان المولد:</strong> ${generatedTitle}</p>
                            </div>
                        `;
                    }
                    
                    resultsDiv.innerHTML = html;
                    document.getElementById('integration-section').className = 'test-section success';
                } else {
                    throw new Error('فشل في تحميل البيانات من إحدى APIs');
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p style="color: red;">❌ خطأ في التكامل: ${error.message}</p>`;
                document.getElementById('integration-section').className = 'test-section error';
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testProducts();
                setTimeout(() => {
                    testTemplates();
                    setTimeout(() => {
                        testIntegration();
                    }, 1000);
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
