/* Styles de la page de paiement */
.checkout-page {
    padding: 120px 0 60px;
    background-color: #f8f9fa;
}

.checkout-container {
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    gap: 30px;
}

/* Styles du formulaire */
.checkout-form {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.checkout-form h2 {
    color: #2c3e50;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Noto Sans Arabic', sans-serif;
    font-size: 1rem;
}

.form-group textarea {
    height: 100px;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52,152,219,0.2);
}

/* Styles des informations de paiement */
.payment-info {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #eee;
}

.payment-info h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.ccp-info {
    text-align: center;
}

.ccp-info p {
    margin: 10px 0;
    color: #666;
}

.ccp-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 15px 0;
    letter-spacing: 2px;
}

/* Styles du bouton de soumission */
.submit-order {
    width: 100%;
    padding: 15px;
    background: #2ecc71;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.submit-order:hover {
    background: #27ae60;
}

/* Styles du résumé de la commande */
.order-summary {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    height: fit-content;
}

.order-summary h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.order-items {
    margin-bottom: 30px;
}

.order-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.order-item:last-child {
    border-bottom: none;
}

.order-item img {
    width: 60px;
    height: 90px;
    object-fit: cover;
    border-radius: 5px;
    margin-left: 15px;
}

.item-info {
    flex: 1;
}

.item-info h4 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.item-info .quantity {
    color: #666;
    font-size: 0.9rem;
}

.item-price {
    font-weight: 600;
    color: #2c3e50;
}

/* Styles du modal de confirmation */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: #fff;
    padding: 40px;
    border-radius: 10px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    position: relative;
}

.modal-content i {
    font-size: 4rem;
    color: #2ecc71;
    margin-bottom: 20px;
}

.modal-content h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.modal-content p {
    color: #666;
    margin-bottom: 10px;
}

.modal-content .email {
    color: #3498db;
    font-weight: 600;
    margin: 15px 0;
}

.back-home {
    display: inline-block;
    padding: 12px 25px;
    background: #3498db;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Noto Sans Arabic', sans-serif;
    transition: background-color 0.3s ease;
    margin-top: 20px;
}

.back-home:hover {
    background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .checkout-container {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 20px;
        padding: 30px;
    }
}