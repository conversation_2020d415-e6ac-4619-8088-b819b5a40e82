<?php

/**
 * Fix All Critical Issues
 * Comprehensive fix for config paths, API errors, JavaScript issues, and UI visibility
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>إصلاح جميع المشاكل الحرجة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .critical-fix { border: 3px solid #28a745; background: #d4edda; }
        h1, h2, h3 { color: #333; }
        .fix-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .fix-failed { border-left-color: #dc3545; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; white-space: pre-wrap; }
        .progress { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-bar { background: #28a745; height: 100%; transition: width 0.3s ease; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 إصلاح جميع المشاكل الحرجة</h1>";
echo "<p>إصلاح شامل لمسارات التكوين، أخطاء APIs، مشاكل JavaScript، وظهور الواجهة</p>";

$fixedIssues = [];
$failedFixes = [];
$totalFixes = 0;

try {
    // FIX 1: Config Path Resolution
    echo "<div class='section critical-fix'>";
    echo "<h2>🔥 الإصلاح 1: حل مسارات ملف التكوين</h2>";

    // First, determine the correct config path
    $configPaths = [
        'php/config.php' => '../config.php',
        'config/config.php' => '../../config/config.php',
        'php/config/config.php' => '../config/config.php'
    ];

    $correctConfigPath = null;
    $correctRelativePath = null;

    foreach ($configPaths as $fullPath => $relativePath) {
        if (file_exists($fullPath)) {
            $correctConfigPath = $fullPath;
            $correctRelativePath = $relativePath;
            break;
        }
    }

    if ($correctConfigPath) {
        echo "<div class='success'>✅ تم العثور على ملف التكوين: {$correctConfigPath}</div>";
        echo "<div class='info'>المسار النسبي المطلوب: {$correctRelativePath}</div>";

        // Fix all API files
        $apiFiles = glob('php/api/*.php');
        $fixedApiFiles = 0;

        foreach ($apiFiles as $apiFile) {
            $totalFixes++;
            try {
                $content = file_get_contents($apiFile);
                $originalContent = $content;

                // Replace various config path patterns
                $patterns = [
                    "/require_once\s+['\"]\.\.\/config\.php['\"]/",
                    "/require_once\s+['\"]\.\.\/\.\.\/config\/config\.php['\"]/",
                    "/require_once\s+['\"]\.\.\/config\/config\.php['\"]/",
                    "/include_once\s+['\"]\.\.\/config\.php['\"]/",
                    "/include\s+['\"]\.\.\/config\.php['\"]/",
                    "/require\s+['\"]\.\.\/config\.php['\"]/",
                ];

                $replacement = "require_once '{$correctRelativePath}'";

                foreach ($patterns as $pattern) {
                    $content = preg_replace($pattern, $replacement, $content);
                }

                if ($content !== $originalContent) {
                    if (file_put_contents($apiFile, $content)) {
                        echo "<div class='fix-result'>✅ تم إصلاح: " . basename($apiFile) . "</div>";
                        $fixedApiFiles++;
                        $fixedIssues[] = "Config path in " . basename($apiFile);
                    } else {
                        echo "<div class='fix-result fix-failed'>❌ فشل في كتابة: " . basename($apiFile) . "</div>";
                        $failedFixes[] = "Config path in " . basename($apiFile);
                    }
                } else {
                    echo "<div class='fix-result'>ℹ️ لا يحتاج إصلاح: " . basename($apiFile) . "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='fix-result fix-failed'>❌ خطأ في " . basename($apiFile) . ": " . $e->getMessage() . "</div>";
                $failedFixes[] = "Config path in " . basename($apiFile);
            }
        }

        echo "<div class='success'>✅ تم إصلاح {$fixedApiFiles} ملف API</div>";
    } else {
        echo "<div class='error'>❌ لم يتم العثور على ملف تكوين صالح</div>";
        $failedFixes[] = "Config file not found";
    }
    echo "</div>";

    // FIX 2: JavaScript Files Cleanup
    echo "<div class='section critical-fix'>";
    echo "<h2>🔥 الإصلاح 2: تنظيف ملفات JavaScript</h2>";

    $jsFiles = [
        'js/utils.js',
        'js/main.js',
        'js/product-view.js',
        'js/auth.js'
    ];

    foreach ($jsFiles as $jsFile) {
        echo "<div class='fix-result'>";
        echo "<h4>" . basename($jsFile) . "</h4>";

        $totalFixes++;
        if (file_exists($jsFile)) {
            try {
                $content = file_get_contents($jsFile);
                $originalContent = $content;
                $issues = [];

                // Check if file contains HTML content
                if (preg_match('/<!DOCTYPE|<html|<head|<body/i', $content)) {
                    $issues[] = "HTML content detected";
                    // Try to extract JavaScript from HTML
                    if (preg_match('/<script[^>]*>(.*?)<\/script>/is', $content, $matches)) {
                        $content = $matches[1];
                    } else {
                        // If no script tags, assume entire content is problematic
                        $content = "// File was corrupted with HTML content - needs manual review\nconsole.log('JavaScript file needs manual review: {$jsFile}');";
                    }
                }

                // Check for PHP content
                if (preg_match('/<\?php|\?>/i', $content)) {
                    $issues[] = "PHP content detected";
                    $content = preg_replace('/<\?php.*?\?>/is', '', $content);
                }

                if (!empty($issues)) {
                    if (file_put_contents($jsFile, $content)) {
                        echo "<div class='success'>✅ تم تنظيف الملف من: " . implode(', ', $issues) . "</div>";
                        $fixedIssues[] = "Cleaned " . basename($jsFile);
                    } else {
                        echo "<div class='error'>❌ فشل في كتابة الملف المنظف</div>";
                        $failedFixes[] = "Clean " . basename($jsFile);
                    }
                } else {
                    echo "<div class='success'>✅ الملف نظيف</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
                $failedFixes[] = "Clean " . basename($jsFile);
            }
        } else {
            echo "<div class='warning'>⚠️ الملف غير موجود</div>";
        }

        echo "</div>";
    }
    echo "</div>";

    // FIX 3: Admin JavaScript Syntax Issues
    echo "<div class='section critical-fix'>";
    echo "<h2>🔥 الإصلاح 3: إصلاح أخطاء JavaScript في الإدارة</h2>";

    $adminJsFile = 'admin/js/admin.js';
    echo "<div class='fix-result'>";
    echo "<h4>إصلاح admin.js</h4>";

    $totalFixes++;
    if (file_exists($adminJsFile)) {
        try {
            $content = file_get_contents($adminJsFile);
            $originalContent = $content;
            $fixes = [];

            // Fix duplicate async keywords
            if (preg_match('/async\s+async\s+function/m', $content)) {
                $content = preg_replace('/async\s+async\s+function/m', 'async function', $content);
                $fixes[] = "duplicate async keywords";
            }

            // Fix orphaned await statements (remove them if they're not in async functions)
            $lines = explode("\n", $content);
            $inAsyncFunction = false;
            $braceLevel = 0;
            $fixedLines = [];

            foreach ($lines as $lineNum => $line) {
                // Track if we're inside an async function
                if (preg_match('/async\s+function/', $line)) {
                    $inAsyncFunction = true;
                    $braceLevel = 0;
                }

                // Track brace levels
                $braceLevel += substr_count($line, '{') - substr_count($line, '}');

                if ($braceLevel <= 0 && $inAsyncFunction) {
                    $inAsyncFunction = false;
                }

                // Check for await outside async function
                if (preg_match('/\bawait\s+/', $line) && !$inAsyncFunction) {
                    // Comment out the problematic line
                    $line = '// ' . $line . ' // Fixed: await outside async function';
                    $fixes[] = "orphaned await on line " . ($lineNum + 1);
                }

                $fixedLines[] = $line;
            }

            if (!empty($fixes)) {
                $content = implode("\n", $fixedLines);

                if (file_put_contents($adminJsFile, $content)) {
                    echo "<div class='success'>✅ تم إصلاح: " . implode(', ', $fixes) . "</div>";
                    $fixedIssues[] = "Admin.js syntax fixes";
                } else {
                    echo "<div class='error'>❌ فشل في كتابة الملف المصحح</div>";
                    $failedFixes[] = "Admin.js syntax fixes";
                }
            } else {
                echo "<div class='success'>✅ لا توجد مشاكل في بناء الجملة</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
            $failedFixes[] = "Admin.js syntax fixes";
        }
    } else {
        echo "<div class='error'>❌ ملف admin.js غير موجود</div>";
        $failedFixes[] = "Admin.js not found";
    }

    echo "</div>";
    echo "</div>";

    // FIX 4: Create Enhanced CSS for UI Visibility
    echo "<div class='section critical-fix'>";
    echo "<h2>🔥 الإصلاح 4: إصلاح ظهور الواجهة</h2>";

    $enhancedCss = '/* Enhanced UI Visibility Fixes */

/* Force visibility for landing pages elements */
.landing-pages-section,
#landingPagesContent,
[data-section="landing-pages"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure add button is always visible */
#addLandingPageBtn,
.add-landing-page-btn,
[data-action="add-landing-page"] {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
    min-width: 120px !important;
    min-height: 36px !important;
    padding: 8px 16px !important;
    margin: 5px !important;
}

/* Fix for hidden content sections */
.content-section {
    min-height: 1px !important;
}

.content-section.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Modal fixes */
.modal {
    z-index: 1050 !important;
}

.modal.show {
    display: block !important;
    opacity: 1 !important;
}

.modal-backdrop {
    z-index: 1040 !important;
}

/* Button styling improvements */
.btn {
    cursor: pointer !important;
    user-select: none !important;
    border: 1px solid transparent !important;
    border-radius: 4px !important;
    text-align: center !important;
    vertical-align: middle !important;
    white-space: nowrap !important;
}

.btn:hover {
    opacity: 0.9 !important;
}

.btn-primary {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: #fff !important;
}

/* Landing pages table improvements */
.landing-pages-table {
    width: 100% !important;
    margin-top: 20px !important;
    display: table !important;
}

/* Form improvements */
.form-control {
    width: 100% !important;
    padding: 8px 12px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    display: block !important;
}

/* Arabic RTL improvements */
[dir="rtl"] .modal-header .close {
    margin-left: auto !important;
    margin-right: -1rem !important;
}

[dir="rtl"] .form-group label {
    text-align: right !important;
}

/* Debug styles for development */
.debug-visible {
    border: 2px solid red !important;
    background-color: rgba(255, 0, 0, 0.1) !important;
}

/* Force visibility for troubleshooting */
.force-visible {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    width: auto !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 10px !important;
        max-width: calc(100% - 20px) !important;
    }

    #addLandingPageBtn {
        width: 100% !important;
        margin: 10px 0 !important;
    }
}

/* Ensure sidebar and navigation are visible */
.sidebar,
.admin-sidebar {
    display: block !important;
    visibility: visible !important;
}

.main-content {
    display: block !important;
    visibility: visible !important;
}

/* Fix for any hidden admin sections */
.admin-section {
    display: block !important;
}

.admin-section.active {
    display: block !important;
    visibility: visible !important;
}';

    $totalFixes++;
    $cssPath = 'admin/css/critical-fixes.css';

    // Ensure directory exists
    if (!is_dir('admin/css')) {
        mkdir('admin/css', 0755, true);
    }

    if (file_put_contents($cssPath, $enhancedCss)) {
        echo "<div class='success'>✅ تم إنشاء ملف CSS للإصلاحات الحرجة: {$cssPath}</div>";
        $fixedIssues[] = "Created critical fixes CSS";
    } else {
        echo "<div class='error'>❌ فشل في إنشاء ملف CSS</div>";
        $failedFixes[] = "Create critical fixes CSS";
    }
    echo "</div>";

    // PROGRESS SUMMARY
    $successRate = (count($fixedIssues) / $totalFixes) * 100;

    echo "<div class='section'>";
    echo "<h2>📊 ملخص الإصلاحات</h2>";

    echo "<div class='progress'>";
    echo "<div class='progress-bar' style='width: {$successRate}%'></div>";
    echo "</div>";

    echo "<div class='info'>";
    echo "<h3>📈 الإحصائيات:</h3>";
    echo "<ul>";
    echo "<li>إجمالي الإصلاحات: {$totalFixes}</li>";
    echo "<li>الإصلاحات الناجحة: " . count($fixedIssues) . "</li>";
    echo "<li>الإصلاحات الفاشلة: " . count($failedFixes) . "</li>";
    echo "<li>نسبة النجاح: " . round($successRate, 1) . "%</li>";
    echo "</ul>";
    echo "</div>";

    if (!empty($fixedIssues)) {
        echo "<div class='success'>";
        echo "<h4>✅ الإصلاحات المكتملة:</h4>";
        echo "<ul>";
        foreach ($fixedIssues as $fix) {
            echo "<li>{$fix}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }

    if (!empty($failedFixes)) {
        echo "<div class='warning'>";
        echo "<h4>⚠️ الإصلاحات التي تحتاج مراجعة:</h4>";
        echo "<ul>";
        foreach ($failedFixes as $fix) {
            echo "<li>{$fix}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }

    // FINAL TESTING
    echo "<div class='section'>";
    echo "<h2>🧪 اختبار الإصلاحات</h2>";

    echo "<div class='info'>";
    echo "<h4>🔗 روابط الاختبار:</h4>";
    echo "<ul>";
    echo "<li><a href='/php/api/templates.php?action=get_templates' target='_blank'>اختبار Templates API</a></li>";
    echo "<li><a href='/php/api/landing-pages.php' target='_blank'>اختبار Landing Pages API</a></li>";
    echo "<li><a href='/admin/' target='_blank'>لوحة التحكم الإدارية</a></li>";
    echo "<li><a href='/test-critical-fixes-final.php' target='_blank'>اختبار شامل للإصلاحات</a></li>";
    echo "</ul>";
    echo "</div>";

    echo "<div class='warning'>";
    echo "<h4>📋 خطوات التحقق:</h4>";
    echo "<ol>";
    echo "<li>اختبر APIs للتأكد من عدم وجود أخطاء 500</li>";
    echo "<li>افتح لوحة التحكم وتحقق من عدم وجود أخطاء JavaScript</li>";
    echo "<li>اختبر ظهور زر إضافة صفحة الهبوط</li>";
    echo "<li>تحقق من عمل النوافذ المنبثقة</li>";
    echo "<li>أضف ملف CSS الجديد إلى صفحات الإدارة</li>";
    echo "</ol>";
    echo "</div>";

    echo "<div class='code-block'>";
    echo "<!-- أضف هذا إلى head في ملفات الإدارة -->\n";
    echo "&lt;link rel=\"stylesheet\" href=\"css/critical-fixes.css\"&gt;";
    echo "</div>";

    echo "</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام في الإصلاح: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
