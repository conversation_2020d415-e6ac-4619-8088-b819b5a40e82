<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🧪 Testing Geographic API\n\n";

// Test 1: Direct database queries
require_once 'php/config.php';

try {
    echo "1. Testing direct database queries:\n";
    
    // Test wilayas
    $stmt = $conn->query("SELECT COUNT(*) as count FROM wilayas WHERE is_active = 1");
    $wilaya_count = $stmt->fetch()['count'];
    echo "✅ Active wilayas: $wilaya_count\n";
    
    // Test communes
    $stmt = $conn->query("SELECT COUNT(*) as count FROM communes WHERE is_active = 1");
    $commune_count = $stmt->fetch()['count'];
    echo "✅ Active communes: $commune_count\n";
    
    // Test communes for specific wilaya
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM communes WHERE wilaya_code = '16' AND is_active = 1");
    $stmt->execute();
    $algiers_communes = $stmt->fetch()['count'];
    echo "✅ Communes in Algiers (16): $algiers_communes\n";
    
    echo "\n2. Testing API response simulation:\n";
    
    // Simulate wilayas API
    $stmt = $conn->prepare("
        SELECT wilaya_code, wilaya_name_ar, wilaya_name_fr, zone_number
        FROM wilayas 
        WHERE is_active = 1 
        ORDER BY wilaya_name_ar
        LIMIT 5
    ");
    $stmt->execute();
    $wilayas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Sample wilayas API response:\n";
    $wilayas_response = [
        'success' => true,
        'wilayas' => $wilayas,
        'total_count' => count($wilayas)
    ];
    echo json_encode($wilayas_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    echo "\n3. Testing communes API simulation:\n";
    
    // Simulate communes API for Algiers
    $stmt = $conn->prepare("
        SELECT commune_code, commune_name_ar, commune_name_fr, postal_code
        FROM communes 
        WHERE wilaya_code = '16' AND is_active = 1 
        ORDER BY commune_name_ar
        LIMIT 5
    ");
    $stmt->execute();
    $communes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Sample communes API response for Algiers:\n";
    $communes_response = [
        'success' => true,
        'communes' => $communes,
        'wilaya_code' => '16',
        'total_count' => count($communes)
    ];
    echo json_encode($communes_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
