<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Landing Pages API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            direction: rtl;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover { background: #005a8b; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .log {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Landing Pages API Test</h1>
    <p>This page tests the fixed landing pages API endpoints to ensure they work correctly.</p>

    <div class="test-section">
        <h2>1. API Endpoints Test</h2>
        <button onclick="testLandingPagesAPI()">Test Landing Pages API</button>
        <button onclick="testProductsAPI()">Test Products API</button>
        <button onclick="testTemplatesAPI()">Test Templates API</button>
        <div id="apiResults" class="log"></div>
    </div>

    <div class="test-section">
        <h2>2. Landing Pages Data</h2>
        <button onclick="loadLandingPages()">Load Landing Pages</button>
        <div id="landingPagesResults" class="log"></div>
        <pre id="landingPagesData"></pre>
    </div>

    <div class="test-section">
        <h2>3. Products Data</h2>
        <button onclick="loadProducts()">Load Products</button>
        <div id="productsResults" class="log"></div>
        <pre id="productsData"></pre>
    </div>

    <div class="test-section">
        <h2>4. Console Logs</h2>
        <p>Check browser console (F12) for detailed logs and any errors.</p>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <script>
        // Utility function for API calls (same as in landing-pages.js)
        async function safeApiCall(url, options = {}) {
            try {
                console.log('🔄 Making API call to:', url);
                const response = await fetch(url, options);

                console.log('📡 Response status:', response.status);
                console.log('📡 Response headers:', [...response.headers.entries()]);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const text = await response.text();
                console.log('📄 Raw response text:', text.substring(0, 200) + '...');

                if (!text.trim()) {
                    console.warn('⚠️ Empty response from API:', url);
                    return [];
                }

                try {
                    const jsonData = JSON.parse(text);
                    console.log('✅ JSON parsed successfully');
                    return jsonData;
                } catch (parseError) {
                    console.error('❌ JSON parse error:', parseError);
                    console.error('📄 Full response text:', text);
                    throw new Error('Invalid JSON response from server: ' + text.substring(0, 100));
                }
            } catch (error) {
                console.error('❌ API call failed:', error);
                throw error;
            }
        }

        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }

        async function testLandingPagesAPI() {
            logResult('apiResults', 'Testing Landing Pages API...', 'info');
            try {
                const response = await safeApiCall('php/api/landing-pages.php');
                logResult('apiResults', '✅ Landing Pages API working!', 'success');
                logResult('apiResults', `Response type: ${typeof response}`, 'info');
                if (Array.isArray(response)) {
                    logResult('apiResults', `Found ${response.length} landing pages`, 'success');
                } else if (response.success) {
                    logResult('apiResults', `API returned success: ${response.message || 'OK'}`, 'success');
                }
            } catch (error) {
                logResult('apiResults', `❌ Landing Pages API failed: ${error.message}`, 'error');
            }
        }

        async function testProductsAPI() {
            logResult('apiResults', 'Testing Products API...', 'info');
            try {
                const response = await safeApiCall('php/api/products.php');
                logResult('apiResults', '✅ Products API working!', 'success');
                if (Array.isArray(response)) {
                    logResult('apiResults', `Found ${response.length} products`, 'success');
                } else if (response.success && response.data) {
                    logResult('apiResults', `Found ${response.data.length} products`, 'success');
                }
            } catch (error) {
                logResult('apiResults', `❌ Products API failed: ${error.message}`, 'error');
            }
        }

        async function testTemplatesAPI() {
            logResult('apiResults', 'Testing Templates API...', 'info');
            try {
                const response = await safeApiCall('php/api/templates.php?action=get_templates');
                logResult('apiResults', '✅ Templates API working!', 'success');
                if (response.success && response.templates) {
                    logResult('apiResults', `Found ${response.templates.length} templates`, 'success');
                }
            } catch (error) {
                logResult('apiResults', `❌ Templates API failed: ${error.message}`, 'error');
            }
        }

        async function loadLandingPages() {
            logResult('landingPagesResults', 'Loading landing pages...', 'info');
            try {
                const response = await safeApiCall('php/api/landing-pages.php');
                logResult('landingPagesResults', '✅ Landing pages loaded successfully!', 'success');
                
                document.getElementById('landingPagesData').textContent = 
                    JSON.stringify(response, null, 2);
                
                if (Array.isArray(response)) {
                    logResult('landingPagesResults', `Loaded ${response.length} landing pages`, 'success');
                } else if (response.success && response.data) {
                    logResult('landingPagesResults', `Loaded ${response.data.length} landing pages`, 'success');
                }
            } catch (error) {
                logResult('landingPagesResults', `❌ Failed to load landing pages: ${error.message}`, 'error');
                document.getElementById('landingPagesData').textContent = `Error: ${error.message}`;
            }
        }

        async function loadProducts() {
            logResult('productsResults', 'Loading products...', 'info');
            try {
                const response = await safeApiCall('php/api/products.php');
                logResult('productsResults', '✅ Products loaded successfully!', 'success');
                
                document.getElementById('productsData').textContent = 
                    JSON.stringify(response, null, 2);
                
                if (Array.isArray(response)) {
                    logResult('productsResults', `Loaded ${response.length} products`, 'success');
                } else if (response.success && response.data) {
                    logResult('productsResults', `Loaded ${response.data.length} products`, 'success');
                }
            } catch (error) {
                logResult('productsResults', `❌ Failed to load products: ${error.message}`, 'error');
                document.getElementById('productsData').textContent = `Error: ${error.message}`;
            }
        }

        function clearLogs() {
            document.getElementById('apiResults').innerHTML = '';
            document.getElementById('landingPagesResults').innerHTML = '';
            document.getElementById('productsResults').innerHTML = '';
            document.getElementById('landingPagesData').textContent = '';
            document.getElementById('productsData').textContent = '';
            console.clear();
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            console.log('🚀 Landing Pages API Test Page Loaded');
            logResult('apiResults', 'Page loaded. Click buttons to test APIs.', 'info');
        });
    </script>
</body>
</html>
