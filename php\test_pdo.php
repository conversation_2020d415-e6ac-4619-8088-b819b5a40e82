<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    echo "Extensions PHP chargées :\n";
    print_r(get_loaded_extensions());
    
    echo "\nTentative de connexion à MySQL...\n";
    $dsn = "mysql:host=localhost;port=3307;dbname=mossab-landing-page;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ];
    
    $pdo = new PDO($dsn, 'root', '', $options);
    echo "Connexion réussie !\n";
    
    $result = $pdo->query("SELECT * FROM admins LIMIT 1");
    echo "\nContenu de la table admins :\n";
    print_r($result->fetch());
    
} catch (PDOException $e) {
    echo "Erreur PDO : " . $e->getMessage() . "\n";
    echo "Code d'erreur : " . $e->getCode() . "\n";
    echo "Trace :\n" . $e->getTraceAsString() . "\n";
}