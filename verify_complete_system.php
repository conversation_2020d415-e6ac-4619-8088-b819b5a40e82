<?php
/**
 * Complete System Verification for MariaDB Landing Page System
 * Comprehensive test of all components and integrations
 */

require_once 'php/config.php';

echo "<h1>🔍 Complete System Verification - MariaDB Port 3307</h1>\n";
echo "<style>
body{font-family:Arial;margin:20px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);min-height:100vh;} 
.container{max-width:1200px;margin:0 auto;background:white;padding:30px;border-radius:15px;box-shadow:0 10px 30px rgba(0,0,0,0.2);}
.success{color:#28a745;font-weight:bold;} 
.error{color:#dc3545;font-weight:bold;} 
.warning{color:#ffc107;font-weight:bold;} 
.info{color:#17a2b8;font-weight:bold;} 
pre{background:#f8f9fa;padding:15px;border-radius:5px;border-left:4px solid #007bff;} 
table{border-collapse:collapse;width:100%;margin:15px 0;} 
th,td{border:1px solid #dee2e6;padding:12px;text-align:left;} 
th{background:#e9ecef;font-weight:bold;}
.test-section{background:#f0f8ff;padding:25px;margin:20px 0;border-radius:12px;border-left:5px solid #007bff;}
.highlight{background:#fff3cd;padding:15px;border-radius:8px;margin:15px 0;}
.test-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:20px;margin:20px 0;}
.test-card{background:#f8f9fa;border:1px solid #dee2e6;border-radius:8px;padding:20px;}
.test-card.pass{border-color:#28a745;background:#f8fff9;}
.test-card.fail{border-color:#dc3545;background:#fff5f5;}
.test-card.warning{border-color:#ffc107;background:#fffbf0;}
.score-circle{width:100px;height:100px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.5em;font-weight:bold;margin:0 auto 15px;}
.score-excellent{background:#28a745;color:white;}
.score-good{background:#ffc107;color:#333;}
.score-poor{background:#dc3545;color:white;}
</style>\n";

echo "<div class='container'>\n";

$totalTests = 10;
$passedTests = 0;
$testResults = [];

try {
    $pdo = getPDOConnection();
    echo "<div class='highlight'>\n";
    echo "<h2>✅ Connected to MariaDB 11.5.2</h2>\n";
    echo "<p>Database: mossab-landing-page on localhost:3307</p>\n";
    echo "</div>\n";
    
    // Test 1: Database Connection & Configuration
    echo "<div class='test-section'>\n";
    echo "<h2>🔌 Test 1: Database Connection & Configuration</h2>\n";
    
    $connectionTest = testDatabaseConnection();
    if ($connectionTest['success']) {
        echo "<p class='success'>✅ MariaDB Connection: {$connectionTest['version']}</p>\n";
        echo "<p class='info'>Database: {$connectionTest['database']} on {$connectionTest['host']}:{$connectionTest['port']}</p>\n";
        $testResults['connection'] = 'pass';
        $passedTests++;
    } else {
        echo "<p class='error'>❌ Connection failed: {$connectionTest['error']}</p>\n";
        $testResults['connection'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 2: Products System (5/5 Required)
    echo "<div class='test-section'>\n";
    echo "<h2>📦 Test 2: Products System (Target: 5 Products)</h2>\n";
    
    $stmt = $pdo->query("SELECT id, titre, type, prix, category_id FROM produits WHERE actif = 1");
    $products = $stmt->fetchAll();
    $productsCount = count($products);
    
    echo "<p class='info'>Active products found: $productsCount/5</p>\n";
    
    if ($productsCount >= 5) {
        echo "<p class='success'>✅ Products requirement met ($productsCount products)</p>\n";
        
        // Verify product diversity
        $productTypes = array_unique(array_column($products, 'type'));
        echo "<p class='info'>Product types: " . implode(', ', $productTypes) . "</p>\n";
        
        // Show products table
        echo "<table>\n";
        echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>Price</th><th>Category</th></tr>\n";
        foreach ($products as $product) {
            // Get category name
            $catStmt = $pdo->prepare("SELECT nom_ar FROM categories WHERE id = ?");
            $catStmt->execute([$product['category_id']]);
            $categoryName = $catStmt->fetch()['nom_ar'] ?? 'غير محدد';
            
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>" . htmlspecialchars($product['titre']) . "</td>";
            echo "<td>{$product['type']}</td>";
            echo "<td>" . number_format($product['prix'], 0) . " DZD</td>";
            echo "<td>$categoryName</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        $testResults['products'] = 'pass';
        $passedTests++;
    } else {
        echo "<p class='error'>❌ Insufficient products (need 5, found $productsCount)</p>\n";
        $testResults['products'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 3: Categories System
    echo "<div class='test-section'>\n";
    echo "<h2>🗂️ Test 3: Categories System</h2>\n";
    
    $stmt = $pdo->query("SELECT id, nom_ar, nom_en, icone, couleur FROM categories WHERE actif = 1");
    $categories = $stmt->fetchAll();
    $categoriesCount = count($categories);
    
    echo "<p class='info'>Active categories: $categoriesCount</p>\n";
    
    if ($categoriesCount >= 5) {
        echo "<p class='success'>✅ Categories system operational</p>\n";
        
        // Show categories
        echo "<table>\n";
        echo "<tr><th>ID</th><th>Arabic Name</th><th>English Name</th><th>Icon</th><th>Color</th></tr>\n";
        foreach ($categories as $category) {
            echo "<tr>";
            echo "<td>{$category['id']}</td>";
            echo "<td>{$category['nom_ar']}</td>";
            echo "<td>{$category['nom_en']}</td>";
            echo "<td><i class='{$category['icone']}' style='color:{$category['couleur']}'></i> {$category['icone']}</td>";
            echo "<td><span style='background:{$category['couleur']};color:white;padding:2px 8px;border-radius:3px;'>{$category['couleur']}</span></td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        $testResults['categories'] = 'pass';
        $passedTests++;
    } else {
        echo "<p class='error'>❌ Insufficient categories</p>\n";
        $testResults['categories'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 4: Landing Pages System (5/5 Required)
    echo "<div class='test-section'>\n";
    echo "<h2>🎨 Test 4: Landing Pages System (Target: 5 Pages)</h2>\n";
    
    $stmt = $pdo->query("
        SELECT lp.id, lp.titre, lp.template_id, lp.produit_id, p.titre as product_title 
        FROM landing_pages lp 
        LEFT JOIN produits p ON lp.produit_id = p.id 
        WHERE lp.actif = 1
    ");
    $landingPages = $stmt->fetchAll();
    $landingPagesCount = count($landingPages);
    
    echo "<p class='info'>Active landing pages: $landingPagesCount/5</p>\n";
    
    if ($landingPagesCount >= 5) {
        echo "<p class='success'>✅ Landing pages requirement met</p>\n";
        
        // Verify template diversity
        $templates = array_unique(array_column($landingPages, 'template_id'));
        echo "<p class='info'>Templates used: " . implode(', ', $templates) . "</p>\n";
        
        // Show landing pages
        echo "<table>\n";
        echo "<tr><th>ID</th><th>Page Title</th><th>Template</th><th>Product</th></tr>\n";
        foreach ($landingPages as $page) {
            echo "<tr>";
            echo "<td>{$page['id']}</td>";
            echo "<td>" . htmlspecialchars($page['titre']) . "</td>";
            echo "<td>{$page['template_id']}</td>";
            echo "<td>" . htmlspecialchars($page['product_title']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        $testResults['landing_pages'] = 'pass';
        $passedTests++;
    } else {
        echo "<p class='error'>❌ Insufficient landing pages (need 5, found $landingPagesCount)</p>\n";
        $testResults['landing_pages'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 5: Foreign Key Relationships
    echo "<div class='test-section'>\n";
    echo "<h2>🔗 Test 5: Foreign Key Relationships</h2>\n";
    
    // Test product-category relationships
    $stmt = $pdo->query("
        SELECT p.id, p.titre, c.nom_ar as category_name 
        FROM produits p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.actif = 1
    ");
    $productCategories = $stmt->fetchAll();
    
    $relationshipsOk = true;
    $linkedProducts = 0;
    
    foreach ($productCategories as $pc) {
        if ($pc['category_name']) {
            $linkedProducts++;
        } else {
            echo "<p class='warning'>⚠️ Product '{$pc['titre']}' not linked to category</p>\n";
            $relationshipsOk = false;
        }
    }
    
    echo "<p class='info'>Products linked to categories: $linkedProducts/" . count($productCategories) . "</p>\n";
    
    // Test landing page-product relationships
    $stmt = $pdo->query("
        SELECT lp.id, lp.titre, p.titre as product_title 
        FROM landing_pages lp 
        LEFT JOIN produits p ON lp.produit_id = p.id
    ");
    $pageProducts = $stmt->fetchAll();
    
    $linkedPages = 0;
    foreach ($pageProducts as $pp) {
        if ($pp['product_title']) {
            $linkedPages++;
        } else {
            echo "<p class='warning'>⚠️ Landing page '{$pp['titre']}' not linked to product</p>\n";
            $relationshipsOk = false;
        }
    }
    
    echo "<p class='info'>Landing pages linked to products: $linkedPages/" . count($pageProducts) . "</p>\n";
    
    if ($relationshipsOk && $linkedProducts > 0 && $linkedPages > 0) {
        echo "<p class='success'>✅ Foreign key relationships working correctly</p>\n";
        $testResults['relationships'] = 'pass';
        $passedTests++;
    } else {
        echo "<p class='error'>❌ Foreign key relationship issues detected</p>\n";
        $testResults['relationships'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 6: Admin Panel Files
    echo "<div class='test-section'>\n";
    echo "<h2>🏠 Test 6: Admin Panel System</h2>\n";
    
    $adminFiles = [
        'Main Admin Panel' => 'admin/index.html',
        'Categories Management' => 'admin/categories_management.html',
        'Payment Settings' => 'admin/payment_settings.html',
        'Categories JS' => 'admin/js/categories-management.js',
        'Payment JS' => 'admin/js/payment-settings.js'
    ];
    
    $adminFilesOk = true;
    foreach ($adminFiles as $name => $file) {
        if (file_exists($file)) {
            echo "<p class='success'>✅ $name exists</p>\n";
        } else {
            echo "<p class='error'>❌ $name missing</p>\n";
            $adminFilesOk = false;
        }
    }
    
    if ($adminFilesOk) {
        $testResults['admin'] = 'pass';
        $passedTests++;
    } else {
        $testResults['admin'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 7: API Endpoints
    echo "<div class='test-section'>\n";
    echo "<h2>🔌 Test 7: API Endpoints</h2>\n";
    
    $apiFiles = [
        'Products API' => 'php/api/products.php',
        'Categories API' => 'php/api/categories.php',
        'Landing Pages API' => 'php/api/landing-pages.php',
        'Templates API' => 'php/api/templates.php'
    ];
    
    $apiFilesOk = true;
    foreach ($apiFiles as $name => $file) {
        if (file_exists($file)) {
            echo "<p class='success'>✅ $name exists</p>\n";
        } else {
            echo "<p class='error'>❌ $name missing</p>\n";
            $apiFilesOk = false;
        }
    }
    
    if ($apiFilesOk) {
        $testResults['apis'] = 'pass';
        $passedTests++;
    } else {
        $testResults['apis'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 8: Database Schema Integrity
    echo "<div class='test-section'>\n";
    echo "<h2>🗄️ Test 8: Database Schema Integrity</h2>\n";
    
    $requiredTables = ['produits', 'categories', 'landing_pages', 'landing_page_images'];
    $schemaOk = true;
    
    foreach ($requiredTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'>✅ Table '$table' exists</p>\n";
            
            // Check table engine and collation
            $stmt = $pdo->query("SHOW TABLE STATUS LIKE '$table'");
            $tableInfo = $stmt->fetch();
            
            if ($tableInfo['Engine'] === 'InnoDB') {
                echo "<p class='success'>  ✅ Engine: InnoDB</p>\n";
            } else {
                echo "<p class='warning'>  ⚠️ Engine: {$tableInfo['Engine']} (should be InnoDB)</p>\n";
            }
            
            if (strpos($tableInfo['Collation'], 'utf8mb4') !== false) {
                echo "<p class='success'>  ✅ Collation: {$tableInfo['Collation']}</p>\n";
            } else {
                echo "<p class='warning'>  ⚠️ Collation: {$tableInfo['Collation']} (should be utf8mb4)</p>\n";
            }
        } else {
            echo "<p class='error'>❌ Table '$table' missing</p>\n";
            $schemaOk = false;
        }
    }
    
    if ($schemaOk) {
        $testResults['schema'] = 'pass';
        $passedTests++;
    } else {
        $testResults['schema'] = 'fail';
    }
    echo "</div>\n";
    
    // Test 9: Content Quality
    echo "<div class='test-section'>\n";
    echo "<h2>📝 Test 9: Content Quality</h2>\n";
    
    // Check for Arabic content in products
    $arabicProductsCount = 0;
    foreach ($products as $product) {
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $product['titre'])) {
            $arabicProductsCount++;
        }
    }
    
    echo "<p class='info'>Products with Arabic titles: $arabicProductsCount/" . count($products) . "</p>\n";
    
    // Check for rich content in landing pages
    $richContentPages = 0;
    foreach ($landingPages as $page) {
        $stmt = $pdo->prepare("SELECT contenu_droit, contenu_gauche FROM landing_pages WHERE id = ?");
        $stmt->execute([$page['id']]);
        $content = $stmt->fetch();
        
        if (strlen($content['contenu_droit']) > 100 && strlen($content['contenu_gauche']) > 100) {
            $richContentPages++;
        }
    }
    
    echo "<p class='info'>Landing pages with rich content: $richContentPages/" . count($landingPages) . "</p>\n";
    
    $contentQuality = ($arabicProductsCount >= 4 && $richContentPages >= 4);
    
    if ($contentQuality) {
        echo "<p class='success'>✅ Content quality meets standards</p>\n";
        $testResults['content'] = 'pass';
        $passedTests++;
    } else {
        echo "<p class='warning'>⚠️ Content quality could be improved</p>\n";
        $testResults['content'] = 'warning';
    }
    echo "</div>\n";
    
    // Test 10: System Performance
    echo "<div class='test-section'>\n";
    echo "<h2>⚡ Test 10: System Performance</h2>\n";
    
    $startTime = microtime(true);
    
    // Test query performance
    $stmt = $pdo->query("
        SELECT p.id, p.titre, c.nom_ar, lp.id as landing_page_id 
        FROM produits p 
        LEFT JOIN categories c ON p.category_id = c.id 
        LEFT JOIN landing_pages lp ON lp.produit_id = p.id 
        WHERE p.actif = 1
    ");
    $complexQuery = $stmt->fetchAll();
    
    $endTime = microtime(true);
    $queryTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
    
    echo "<p class='info'>Complex query execution time: " . round($queryTime, 2) . " ms</p>\n";
    echo "<p class='info'>Query returned " . count($complexQuery) . " records</p>\n";
    
    if ($queryTime < 100) {
        echo "<p class='success'>✅ Excellent performance (< 100ms)</p>\n";
        $testResults['performance'] = 'pass';
        $passedTests++;
    } elseif ($queryTime < 500) {
        echo "<p class='warning'>⚠️ Good performance (< 500ms)</p>\n";
        $testResults['performance'] = 'warning';
    } else {
        echo "<p class='error'>❌ Poor performance (> 500ms)</p>\n";
        $testResults['performance'] = 'fail';
    }
    echo "</div>\n";
    
    // Final Results Dashboard
    echo "<div class='test-section'>\n";
    echo "<h2>📊 Final System Assessment</h2>\n";
    
    $successRate = ($passedTests / $totalTests) * 100;
    
    // Score circle
    $scoreClass = $successRate >= 90 ? 'score-excellent' : ($successRate >= 70 ? 'score-good' : 'score-poor');
    echo "<div class='$scoreClass score-circle'>" . round($successRate) . "%</div>\n";
    
    echo "<div class='test-grid'>\n";
    $testNames = [
        'connection' => 'Database Connection',
        'products' => 'Products System',
        'categories' => 'Categories System', 
        'landing_pages' => 'Landing Pages',
        'relationships' => 'Foreign Keys',
        'admin' => 'Admin Panel',
        'apis' => 'API Endpoints',
        'schema' => 'Database Schema',
        'content' => 'Content Quality',
        'performance' => 'Performance'
    ];
    
    foreach ($testResults as $test => $result) {
        $cardClass = $result;
        $icon = $result === 'pass' ? '✅' : ($result === 'warning' ? '⚠️' : '❌');
        $status = $result === 'pass' ? 'PASSED' : ($result === 'warning' ? 'WARNING' : 'FAILED');
        $testName = $testNames[$test] ?? ucfirst($test);
        
        echo "<div class='test-card $cardClass'>\n";
        echo "<h4>$icon $testName</h4>\n";
        echo "<p>Status: <strong>$status</strong></p>\n";
        echo "</div>\n";
    }
    echo "</div>\n";
    
    echo "<div class='highlight'>\n";
    echo "<h3>🎯 Overall System Status</h3>\n";
    echo "<p><strong>Tests Passed:</strong> $passedTests / $totalTests (" . round($successRate, 1) . "%)</p>\n";
    
    if ($successRate >= 90) {
        echo "<p class='success'>🎉 EXCELLENT: System is fully operational and production-ready!</p>\n";
        echo "<p>All major components are working correctly. Your landing page system is ready for immediate use.</p>\n";
    } elseif ($successRate >= 70) {
        echo "<p class='warning'>⚠️ GOOD: System is mostly operational with minor issues</p>\n";
        echo "<p>Most components are working well. Address the warnings for optimal performance.</p>\n";
    } else {
        echo "<p class='error'>❌ NEEDS ATTENTION: System has significant issues</p>\n";
        echo "<p>Several critical components need to be fixed before production use.</p>\n";
    }
    
    echo "<h4>🔗 Quick Access Links:</h4>\n";
    echo "<div style='display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin:20px 0;'>\n";
    echo "<a href='admin/' style='background:#007bff;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🏠 Admin Panel</a>\n";
    echo "<a href='admin/categories_management.html' style='background:#28a745;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>🗂️ Categories</a>\n";
    echo "<a href='admin/payment_settings.html' style='background:#fd7e14;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>💳 Payment Settings</a>\n";
    echo "<a href='php/api/products.php' style='background:#6f42c1;color:white;padding:15px;text-decoration:none;border-radius:8px;text-align:center;'>📦 Products API</a>\n";
    echo "</div>\n";
    echo "</div>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='test-section'>\n";
    echo "<h2 class='error'>❌ Critical System Error</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    echo "</div>\n";
}

echo "</div>\n";
?>

<script>
console.log('🔍 Complete system verification finished');
console.log('Score: <?php echo round($successRate ?? 0); ?>%');
</script>
