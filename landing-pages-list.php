<?php
/**
 * Landing Pages List - Test All Created Landing Pages
 * Shows all landing pages with links to view them
 */

require_once 'php/config.php';

echo "<h1>🎨 Landing Pages List - Test System</h1>\n";
echo "<style>
body{font-family:Arial;margin:20px;background:#f8f9fa;} 
.container{max-width:1200px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 4px 6px rgba(0,0,0,0.1);}
.success{color:#28a745;font-weight:bold;} 
.error{color:#dc3545;font-weight:bold;} 
.warning{color:#ffc107;font-weight:bold;} 
.info{color:#17a2b8;font-weight:bold;} 
table{border-collapse:collapse;width:100%;margin:15px 0;} 
th,td{border:1px solid #dee2e6;padding:12px;text-align:left;} 
th{background:#e9ecef;font-weight:bold;}
.page-card{background:#f8f9fa;border:1px solid #dee2e6;border-radius:8px;padding:20px;margin:15px 0;}
.page-card h3{color:#333;margin:0 0 10px 0;}
.page-card p{margin:5px 0;color:#666;}
.btn{display:inline-block;padding:8px 16px;margin:5px;text-decoration:none;border-radius:5px;font-weight:bold;}
.btn-primary{background:#007bff;color:white;}
.btn-success{background:#28a745;color:white;}
.btn-info{background:#17a2b8;color:white;}
.btn-warning{background:#ffc107;color:#333;}
.template-badge{display:inline-block;padding:4px 8px;border-radius:4px;font-size:0.8em;font-weight:bold;margin:0 5px;}
.template-modern{background:#007bff;color:white;}
.template-classic{background:#6c757d;color:white;}
.template-minimal{background:#28a745;color:white;}
.template-elegant{background:#6f42c1;color:white;}
.template-professional{background:#fd7e14;color:white;}
</style>\n";

echo "<div class='container'>\n";

try {
    $pdo = getPDOConnection();
    echo "<p class='success'>✅ Connected to MariaDB successfully</p>\n";
    
    // Get all landing pages with product information
    $stmt = $pdo->query("
        SELECT lp.id, lp.titre, lp.template_id, lp.produit_id, lp.lien_url, lp.actif,
               p.titre as product_title, p.type, p.prix
        FROM landing_pages lp 
        LEFT JOIN produits p ON lp.produit_id = p.id 
        ORDER BY lp.id
    ");
    $landingPages = $stmt->fetchAll();
    
    echo "<h2>📋 All Landing Pages (" . count($landingPages) . " total)</h2>\n";
    
    if (empty($landingPages)) {
        echo "<p class='warning'>⚠️ No landing pages found in the database</p>\n";
        echo "<p>You may need to run the complete_missing_components.php script first.</p>\n";
    } else {
        echo "<div style='margin:20px 0;'>\n";
        echo "<a href='complete_missing_components.php' class='btn btn-warning'>🔧 Add Missing Components</a>\n";
        echo "<a href='verify_complete_system.php' class='btn btn-info'>🧪 Verify System</a>\n";
        echo "<a href='admin/' class='btn btn-primary'>🏠 Admin Panel</a>\n";
        echo "</div>\n";
        
        foreach ($landingPages as $page) {
            $statusClass = $page['actif'] ? 'success' : 'warning';
            $statusText = $page['actif'] ? 'Active' : 'Inactive';
            
            echo "<div class='page-card'>\n";
            echo "<h3>" . htmlspecialchars($page['titre']) . "</h3>\n";
            echo "<p><strong>Product:</strong> " . htmlspecialchars($page['product_title'] ?? 'Unknown') . "</p>\n";
            echo "<p><strong>Type:</strong> {$page['type']} | <strong>Price:</strong> " . number_format($page['prix'] ?? 0, 0) . " DZD</p>\n";
            echo "<p><strong>Template:</strong> <span class='template-badge template-{$page['template_id']}'>{$page['template_id']}</span></p>\n";
            echo "<p><strong>Status:</strong> <span class='$statusClass'>$statusText</span></p>\n";
            
            echo "<div style='margin-top:15px;'>\n";
            
            // Test different URL formats
            echo "<a href='landing-page.php?id={$page['id']}' class='btn btn-primary' target='_blank'>📄 View by ID</a>\n";
            echo "<a href='landing-page.php?product={$page['produit_id']}' class='btn btn-success' target='_blank'>🔗 View by Product</a>\n";
            
            // If there's a custom URL
            if ($page['lien_url']) {
                $customUrl = ltrim($page['lien_url'], '/');
                echo "<a href='$customUrl' class='btn btn-info' target='_blank'>🌐 Custom URL</a>\n";
            }
            
            echo "</div>\n";
            echo "</div>\n";
        }
        
        // Summary statistics
        echo "<h3>📊 Landing Pages Summary</h3>\n";
        echo "<table>\n";
        echo "<tr><th>Metric</th><th>Count</th><th>Details</th></tr>\n";
        
        $activePages = array_filter($landingPages, function($p) { return $p['actif']; });
        $templates = array_count_values(array_column($landingPages, 'template_id'));
        $productTypes = array_count_values(array_column($landingPages, 'type'));
        
        echo "<tr><td>Total Pages</td><td>" . count($landingPages) . "</td><td>All landing pages</td></tr>\n";
        echo "<tr><td>Active Pages</td><td>" . count($activePages) . "</td><td>Currently visible</td></tr>\n";
        echo "<tr><td>Templates Used</td><td>" . count($templates) . "</td><td>" . implode(', ', array_keys($templates)) . "</td></tr>\n";
        echo "<tr><td>Product Types</td><td>" . count($productTypes) . "</td><td>" . implode(', ', array_keys($productTypes)) . "</td></tr>\n";
        
        echo "</table>\n";
        
        // Template distribution
        echo "<h3>🎨 Template Distribution</h3>\n";
        echo "<table>\n";
        echo "<tr><th>Template</th><th>Count</th><th>Percentage</th></tr>\n";
        
        foreach ($templates as $template => $count) {
            $percentage = round(($count / count($landingPages)) * 100, 1);
            echo "<tr>";
            echo "<td><span class='template-badge template-$template'>$template</span></td>";
            echo "<td>$count</td>";
            echo "<td>{$percentage}%</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // Test database queries
    echo "<h3>🔍 Database Test Queries</h3>\n";
    
    // Test products count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits WHERE actif = 1");
    $productsCount = $stmt->fetch()['count'];
    echo "<p class='info'>Active Products: $productsCount</p>\n";
    
    // Test categories count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE actif = 1");
    $categoriesCount = $stmt->fetch()['count'];
    echo "<p class='info'>Active Categories: $categoriesCount</p>\n";
    
    // Test landing page images
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM landing_page_images");
    $imagesCount = $stmt->fetch()['count'];
    echo "<p class='info'>Landing Page Images: $imagesCount</p>\n";
    
    // Test foreign key relationships
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM landing_pages lp 
        INNER JOIN produits p ON lp.produit_id = p.id 
        WHERE lp.actif = 1 AND p.actif = 1
    ");
    $linkedPages = $stmt->fetch()['count'];
    echo "<p class='info'>Properly Linked Pages: $linkedPages</p>\n";
    
    echo "<div style='background:#e8f5e9;padding:20px;border-radius:8px;margin:20px 0;'>\n";
    echo "<h3>🎯 System Status</h3>\n";
    
    if ($productsCount >= 5 && count($landingPages) >= 5 && $linkedPages >= 5) {
        echo "<p class='success'>🎉 System is fully operational!</p>\n";
        echo "<ul>\n";
        echo "<li>✅ Products: $productsCount/5 (Complete)</li>\n";
        echo "<li>✅ Landing Pages: " . count($landingPages) . "/5 (Complete)</li>\n";
        echo "<li>✅ Categories: $categoriesCount (Active)</li>\n";
        echo "<li>✅ Relationships: $linkedPages (Linked)</li>\n";
        echo "</ul>\n";
    } else {
        echo "<p class='warning'>⚠️ System needs completion</p>\n";
        echo "<ul>\n";
        echo "<li>Products: $productsCount/5 " . ($productsCount >= 5 ? '✅' : '❌') . "</li>\n";
        echo "<li>Landing Pages: " . count($landingPages) . "/5 " . (count($landingPages) >= 5 ? '✅' : '❌') . "</li>\n";
        echo "<li>Linked Pages: $linkedPages " . ($linkedPages >= 5 ? '✅' : '❌') . "</li>\n";
        echo "</ul>\n";
        echo "<p><a href='complete_missing_components.php' class='btn btn-warning'>🔧 Complete Missing Components</a></p>\n";
    }
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "</div>\n";
?>

<script>
console.log('🎨 Landing pages list loaded');
console.log('Testing landing page system functionality');
</script>
