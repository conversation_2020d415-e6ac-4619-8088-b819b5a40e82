<?php
/**
 * Create 5 Demo Landing Pages for Demo User "مصعب التجريبي"
 * This script creates unique landing pages for different products
 */

require_once 'php/config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>إنشاء صفحات الهبوط التجريبية</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2 { color: #333; }
        .landing-page { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🌐 إنشاء صفحات الهبوط التجريبية لمصعب التجريبي</h1>";

try {
    $pdo = getPDOConnection();
    
    // Get demo user info
    $stmt = $pdo->prepare("SELECT id, store_id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $demoUser = $stmt->fetch();
    
    if (!$demoUser) {
        throw new Exception('Demo user not found');
    }
    
    $userId = $demoUser['id'];
    $storeId = $demoUser['store_id'] ?? 1; // Default to store 1
    
    echo "<div class='info'>👤 Demo User ID: {$userId} | Store ID: {$storeId}</div>";
    
    // First, let's create some demo products if they don't exist
    $demoProducts = [
        [
            'type' => 'book',
            'titre' => 'كتاب فن اللامبالاة - مارك مانسون',
            'description' => '<h3>كتاب فن اللامبالاة</h3><p>نهج جديد في التفكير والحياة من مارك مانسون</p><ul><li>📚 كتاب مترجم بعناية</li><li>🎯 نصائح عملية للحياة</li><li>💡 أسلوب مباشر وصريح</li><li>⭐ من أكثر الكتب مبيعاً</li></ul>',
            'prix' => 2500.00,
            'stock' => 50,
            'auteur' => 'مارك مانسون'
        ],
        [
            'type' => 'laptop',
            'titre' => 'لابتوب Dell Inspiron 15 - للطلاب والمهنيين',
            'description' => '<h3>Dell Inspiron 15</h3><p>حاسوب محمول مثالي للطلاب والمهنيين</p><ul><li>💻 معالج Intel Core i5</li><li>🧠 ذاكرة 8GB RAM</li><li>💾 تخزين 256GB SSD</li><li>🖥️ شاشة 15.6 بوصة Full HD</li></ul>',
            'prix' => 85000.00,
            'stock' => 10,
            'processeur' => 'Intel Core i5-1135G7',
            'ram' => '8GB DDR4',
            'stockage' => '256GB SSD'
        ],
        [
            'type' => 'bag',
            'titre' => 'حقيبة ظهر رياضية مقاومة للماء',
            'description' => '<h3>حقيبة ظهر رياضية</h3><p>حقيبة عملية ومقاومة للماء للاستخدام اليومي</p><ul><li>🎒 تصميم عصري وأنيق</li><li>💧 مقاومة للماء</li><li>📱 جيب مخصص للهاتف</li><li>💻 مقصورة للحاسوب المحمول</li></ul>',
            'prix' => 4500.00,
            'stock' => 25,
            'materiel' => 'نايلون مقاوم للماء',
            'capacite' => '30 لتر'
        ],
        [
            'type' => 'smartphone',
            'titre' => 'هاتف Samsung Galaxy A54 5G',
            'description' => '<h3>Samsung Galaxy A54 5G</h3><p>هاتف ذكي متطور بتقنية 5G</p><ul><li>📱 شاشة Super AMOLED 6.4 بوصة</li><li>📸 كاميرا ثلاثية 50MP</li><li>🔋 بطارية 5000mAh</li><li>🌐 دعم شبكات 5G</li></ul>',
            'prix' => 45000.00,
            'stock' => 15
        ],
        [
            'type' => 'home',
            'titre' => 'خلاط كهربائي متعدد الاستخدامات',
            'description' => '<h3>خلاط كهربائي متطور</h3><p>جهاز مطبخ متعدد الاستخدامات</p><ul><li>⚡ قوة 1000 واط</li><li>🥤 إبريق زجاجي 1.5 لتر</li><li>⚙️ 5 سرعات مختلفة</li><li>🧊 قادر على طحن الثلج</li></ul>',
            'prix' => 12000.00,
            'stock' => 20
        ]
    ];
    
    echo "<div class='section'>";
    echo "<h2>📦 إنشاء المنتجات التجريبية</h2>";
    
    $productIds = [];
    foreach ($demoProducts as $product) {
        // Check if product exists
        $stmt = $pdo->prepare("SELECT id FROM produits WHERE titre = ? AND store_id = ?");
        $stmt->execute([$product['titre'], $storeId]);
        $existingProduct = $stmt->fetch();
        
        if ($existingProduct) {
            $productIds[] = $existingProduct['id'];
            echo "<div class='info'>ℹ️ المنتج موجود: {$product['titre']} (ID: {$existingProduct['id']})</div>";
        } else {
            // Create product
            $stmt = $pdo->prepare("
                INSERT INTO produits (store_id, type, titre, description, prix, stock, auteur, processeur, ram, stockage, materiel, capacite, actif, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW())
            ");
            
            $stmt->execute([
                $storeId,
                $product['type'],
                $product['titre'],
                $product['description'],
                $product['prix'],
                $product['stock'],
                $product['auteur'] ?? null,
                $product['processeur'] ?? null,
                $product['ram'] ?? null,
                $product['stockage'] ?? null,
                $product['materiel'] ?? null,
                $product['capacite'] ?? null
            ]);
            
            $productId = $pdo->lastInsertId();
            $productIds[] = $productId;
            echo "<div class='success'>✅ تم إنشاء المنتج: {$product['titre']} (ID: {$productId})</div>";
        }
    }
    echo "</div>";
    
    // Now create landing pages
    echo "<div class='section'>";
    echo "<h2>🌐 إنشاء صفحات الهبوط</h2>";
    
    $landingPageTemplates = [
        'modern' => 'عصري',
        'classic' => 'كلاسيكي', 
        'minimal' => 'بسيط',
        'elegant' => 'أنيق',
        'professional' => 'مهني'
    ];
    
    $templateKeys = array_keys($landingPageTemplates);
    
    foreach ($productIds as $index => $productId) {
        // Get product details
        $stmt = $pdo->prepare("SELECT * FROM produits WHERE id = ?");
        $stmt->execute([$productId]);
        $product = $stmt->fetch();
        
        if (!$product) continue;
        
        // Check if landing page already exists
        $stmt = $pdo->prepare("SELECT id FROM landing_pages WHERE produit_id = ? AND store_id = ?");
        $stmt->execute([$productId, $storeId]);
        $existingLandingPage = $stmt->fetch();
        
        if ($existingLandingPage) {
            echo "<div class='info'>ℹ️ صفحة الهبوط موجودة للمنتج: {$product['titre']}</div>";
            continue;
        }
        
        // Generate unique content for each landing page
        $templateId = $templateKeys[$index % count($templateKeys)];
        $templateName = $landingPageTemplates[$templateId];
        
        $landingPageTitle = "صفحة {$product['titre']} - قالب {$templateName}";
        $linkUrl = "/landing/product-{$productId}-" . uniqid();
        
        // Generate content based on product type
        $rightContent = generateRightContent($product);
        $leftContent = generateLeftContent($product);
        
        // Create landing page
        $stmt = $pdo->prepare("
            INSERT INTO landing_pages (store_id, produit_id, titre, contenu_droit, contenu_gauche, lien_url, template_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $storeId,
            $productId,
            $landingPageTitle,
            $rightContent,
            $leftContent,
            $linkUrl,
            $templateId
        ]);
        
        $landingPageId = $pdo->lastInsertId();
        
        echo "<div class='landing-page'>";
        echo "<h4>✅ صفحة هبوط جديدة: {$landingPageTitle}</h4>";
        echo "<p><strong>المنتج:</strong> {$product['titre']}</p>";
        echo "<p><strong>القالب:</strong> {$templateName} ({$templateId})</p>";
        echo "<p><strong>الرابط:</strong> <a href='{$linkUrl}' target='_blank'>{$linkUrl}</a></p>";
        echo "<p><strong>ID:</strong> {$landingPageId}</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // Summary
    echo "<div class='section'>";
    echo "<h2>📊 ملخص النتائج</h2>";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM produits WHERE store_id = ?");
    $stmt->execute([$storeId]);
    $productCount = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM landing_pages WHERE store_id = ?");
    $stmt->execute([$storeId]);
    $landingPageCount = $stmt->fetchColumn();
    
    echo "<div class='success'>";
    echo "<h3>✅ تم الانتهاء بنجاح!</h3>";
    echo "<ul>";
    echo "<li>👤 المستخدم التجريبي: مصعب التجريبي (ID: {$userId})</li>";
    echo "<li>🏪 المتجر: متجر مصعب (ID: {$storeId})</li>";
    echo "<li>📦 إجمالي المنتجات: {$productCount}</li>";
    echo "<li>🌐 إجمالي صفحات الهبوط: {$landingPageCount}</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🔗 روابط مفيدة:</h4>";
    echo "<ul>";
    echo "<li><a href='/store.php?slug=mossaab-store' target='_blank'>متجر مصعب التجريبي</a></li>";
    echo "<li><a href='/admin/' target='_blank'>لوحة التحكم الإدارية</a></li>";
    echo "<li><a href='/dashboard/' target='_blank'>لوحة تحكم المتجر</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";

/**
 * Generate right content based on product type
 */
function generateRightContent($product) {
    $content = "<h3>🌟 لماذا تختار {$product['titre']}؟</h3>";
    
    switch ($product['type']) {
        case 'book':
            $content .= "
            <ul>
                <li><strong>📚 محتوى غني ومفيد:</strong> معلومات قيمة ومفصلة</li>
                <li><strong>✍️ كاتب محترف:</strong> خبرة واسعة في المجال</li>
                <li><strong>📖 سهل القراءة:</strong> أسلوب واضح ومفهوم</li>
                <li><strong>⭐ تقييمات ممتازة:</strong> آراء إيجابية من القراء</li>
                <li><strong>💡 تطبيق عملي:</strong> يمكن تطبيق المحتوى في الحياة</li>
            </ul>";
            break;
            
        case 'laptop':
            $content .= "
            <ul>
                <li><strong>⚡ أداء فائق:</strong> معالج قوي وذاكرة كافية</li>
                <li><strong>🖥️ شاشة عالية الجودة:</strong> وضوح ممتاز للعمل</li>
                <li><strong>🔋 بطارية طويلة المدى:</strong> يدوم لساعات طويلة</li>
                <li><strong>💼 تصميم محمول:</strong> خفيف وسهل النقل</li>
                <li><strong>🛡️ ضمان شامل:</strong> خدمة ما بعد البيع ممتازة</li>
            </ul>";
            break;
            
        case 'bag':
            $content .= "
            <ul>
                <li><strong>🎒 تصميم عملي:</strong> مقصورات متعددة ومنظمة</li>
                <li><strong>💧 مقاومة للماء:</strong> حماية ممتازة للمحتويات</li>
                <li><strong>👜 راحة في الحمل:</strong> أحزمة مريحة ومبطنة</li>
                <li><strong>🧵 جودة عالية:</strong> خامات متينة وتصنيع ممتاز</li>
                <li><strong>🎨 تصميم أنيق:</strong> مناسب لجميع المناسبات</li>
            </ul>";
            break;
            
        case 'smartphone':
            $content .= "
            <ul>
                <li><strong>📱 تقنية حديثة:</strong> أحدث المواصفات والميزات</li>
                <li><strong>📸 كاميرا احترافية:</strong> صور عالية الجودة</li>
                <li><strong>🔋 بطارية قوية:</strong> يدوم طوال اليوم</li>
                <li><strong>🌐 اتصال سريع:</strong> دعم شبكات 5G</li>
                <li><strong>🛡️ أمان متقدم:</strong> حماية البيانات والخصوصية</li>
            </ul>";
            break;
            
        case 'home':
            $content .= "
            <ul>
                <li><strong>⚡ قوة وكفاءة:</strong> أداء ممتاز وموفر للطاقة</li>
                <li><strong>🏠 مناسب للمنزل:</strong> تصميم يناسب المطبخ العصري</li>
                <li><strong>🔧 سهل الاستخدام:</strong> تشغيل بسيط وآمن</li>
                <li><strong>🧽 سهل التنظيف:</strong> صيانة بسيطة وسريعة</li>
                <li><strong>⭐ جودة موثوقة:</strong> علامة تجارية معروفة</li>
            </ul>";
            break;
            
        default:
            $content .= "
            <ul>
                <li><strong>⭐ جودة ممتازة:</strong> منتج عالي الجودة</li>
                <li><strong>💰 سعر مناسب:</strong> قيمة ممتازة مقابل السعر</li>
                <li><strong>🚚 توصيل سريع:</strong> خدمة توصيل موثوقة</li>
                <li><strong>🛡️ ضمان شامل:</strong> حماية لاستثمارك</li>
                <li><strong>👥 دعم العملاء:</strong> خدمة عملاء ممتازة</li>
            </ul>";
    }
    
    return $content;
}

/**
 * Generate left content based on product
 */
function generateLeftContent($product) {
    $content = "<h3>🛒 اطلب الآن واحصل على:</h3>";
    $content .= "
    <ul>
        <li>🚚 <strong>توصيل مجاني</strong> لجميع أنحاء الجزائر</li>
        <li>💳 <strong>دفع آمن</strong> عند الاستلام أو بالبطاقة</li>
        <li>📞 <strong>دعم فني</strong> على مدار 24 ساعة</li>
        <li>🔄 <strong>إمكانية الإرجاع</strong> خلال 15 يوم</li>
        <li>🎁 <strong>عروض خاصة</strong> للعملاء الجدد</li>
    </ul>
    
    <div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;'>
        <h4 style='color: #28a745; margin-bottom: 10px;'>💰 السعر: " . number_format($product['prix'], 2) . " دج</h4>
        <p style='color: #6c757d; margin: 5px 0;'>📦 متوفر في المخزون: {$product['stock']} قطعة</p>
        <p style='color: #dc3545; font-weight: bold;'>⏰ عرض محدود - اطلب الآن!</p>
    </div>";
    
    return $content;
}
?>
