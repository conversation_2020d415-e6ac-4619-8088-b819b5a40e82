<?php
/**
 * Create sample landing pages for different product types
 */

require_once 'php/config.php';

echo "=== Creating Sample Landing Pages ===\n";

try {
    // Get products of different types
    $stmt = $conn->query("SELECT id, titre, type, prix FROM produits WHERE actif = 1 ORDER BY type");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "No active products found\n";
        exit(1);
    }
    
    // Group products by type
    $productsByType = [];
    foreach ($products as $product) {
        $productsByType[$product['type']][] = $product;
    }
    
    echo "Found products by type:\n";
    foreach ($productsByType as $type => $typeProducts) {
        echo "  - {$type}: " . count($typeProducts) . " products\n";
    }
    
    // Create landing pages for each type
    $landingPageData = [
        'book' => [
            'titre' => 'اكتشف عالم المعرفة - كتب تغير حياتك',
            'contenu_droit' => '<h2>لماذا تختار كتبنا؟</h2>
                <p>نقدم لك مجموعة مختارة من أفضل الكتب التي تساعدك على تطوير ذاتك وتحقيق أهدافك.</p>
                <ul>
                    <li>محتوى عالي الجودة من أفضل المؤلفين</li>
                    <li>ترجمة احترافية ودقيقة</li>
                    <li>تصميم أنيق وطباعة فاخرة</li>
                    <li>أسعار منافسة وعروض خاصة</li>
                </ul>
                <p><strong>استثمر في معرفتك اليوم!</strong></p>',
            'contenu_gauche' => '<h2>ما يقوله قراؤنا</h2>
                <blockquote>
                    "كتب رائعة غيرت طريقة تفكيري وساعدتني على تحقيق النجاح في حياتي المهنية"
                    <cite>- أحمد محمد، رائد أعمال</cite>
                </blockquote>
                <blockquote>
                    "جودة عالية وأسعار معقولة، أنصح الجميع بالشراء من هنا"
                    <cite>- فاطمة علي، طالبة جامعية</cite>
                </blockquote>
                <h3>ضمان الجودة</h3>
                <p>نضمن لك جودة المحتوى والطباعة، أو استرداد كامل للمبلغ خلال 30 يوماً.</p>'
        ],
        'laptop' => [
            'titre' => 'أحدث أجهزة الحاسوب المحمولة - قوة وأداء استثنائي',
            'contenu_droit' => '<h2>تكنولوجيا متقدمة</h2>
                <p>اكتشف مجموعتنا المتميزة من أجهزة الحاسوب المحمولة عالية الأداء.</p>
                <ul>
                    <li>معالجات قوية من Intel و AMD</li>
                    <li>ذاكرة عشوائية كبيرة للأداء السريع</li>
                    <li>مساحة تخزين SSD فائقة السرعة</li>
                    <li>شاشات عالية الدقة والوضوح</li>
                    <li>بطارية طويلة المدى</li>
                </ul>
                <p><strong>مثالي للعمل والدراسة والترفيه!</strong></p>',
            'contenu_gauche' => '<h2>خدمات ما بعد البيع</h2>
                <ul>
                    <li>ضمان شامل لمدة سنتين</li>
                    <li>دعم فني متخصص 24/7</li>
                    <li>خدمة الصيانة المجانية</li>
                    <li>تحديثات البرامج المجانية</li>
                </ul>
                <h3>عروض خاصة</h3>
                <p>احصل على خصم 10% عند شراء جهازين أو أكثر</p>
                <p>توصيل مجاني لجميع أنحاء الجزائر</p>
                <p><em>العرض محدود لفترة قصيرة!</em></p>'
        ],
        'bag' => [
            'titre' => 'حقائب عملية وأنيقة - رفيقك المثالي',
            'contenu_droit' => '<h2>تصميم عملي ومتين</h2>
                <p>حقائب مصممة خصيصاً لتلبية احتياجاتك اليومية بأسلوب عصري.</p>
                <ul>
                    <li>مواد عالية الجودة ومقاومة للماء</li>
                    <li>تصميم مريح للظهر والكتفين</li>
                    <li>جيوب متعددة للتنظيم الأمثل</li>
                    <li>مقاس مثالي للحاسوب المحمول</li>
                    <li>ألوان متنوعة تناسب جميع الأذواق</li>
                </ul>
                <p><strong>الخيار الأمثل للطلاب والمهنيين!</strong></p>',
            'contenu_gauche' => '<h2>مميزات إضافية</h2>
                <ul>
                    <li>حماية مبطنة للأجهزة الإلكترونية</li>
                    <li>سحاب قوي ومقاوم للكسر</li>
                    <li>حزام قابل للتعديل</li>
                    <li>جيب خفي للأشياء الثمينة</li>
                </ul>
                <h3>اختر المقاس المناسب</h3>
                <p>متوفر بأحجام مختلفة:</p>
                <ul>
                    <li>صغير: مثالي للاستخدام اليومي</li>
                    <li>متوسط: للطلاب والموظفين</li>
                    <li>كبير: للسفر والرحلات</li>
                </ul>'
        ]
    ];
    
    foreach ($productsByType as $type => $typeProducts) {
        if (!isset($landingPageData[$type])) {
            echo "No landing page data for type: {$type}\n";
            continue;
        }
        
        $product = $typeProducts[0]; // Use first product of this type
        $data = $landingPageData[$type];
        
        echo "\nCreating landing page for {$type}: {$product['titre']}\n";
        
        // Generate URL
        $slug = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '-', $product['titre']));
        $url = "/product-{$product['id']}-{$slug}";
        
        // Check if landing page already exists
        $stmt = $conn->prepare("SELECT id FROM landing_pages WHERE produit_id = ?");
        $stmt->execute([$product['id']]);
        $existingPage = $stmt->fetch();
        
        if ($existingPage) {
            echo "  - Landing page already exists, updating...\n";
            $stmt = $conn->prepare("
                UPDATE landing_pages 
                SET titre = ?, contenu_droit = ?, contenu_gauche = ?, lien_url = ?
                WHERE produit_id = ?
            ");
            $stmt->execute([
                $data['titre'],
                $data['contenu_droit'],
                $data['contenu_gauche'],
                $url,
                $product['id']
            ]);
            $landingPageId = $existingPage['id'];
        } else {
            echo "  - Creating new landing page...\n";
            $stmt = $conn->prepare("
                INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $product['id'],
                $data['titre'],
                $data['contenu_droit'],
                $data['contenu_gauche'],
                $url
            ]);
            $landingPageId = $conn->lastInsertId();
        }
        
        // Add sample images
        $sampleImages = [
            'book' => [
                'images/book1.svg',
                'images/book2.svg',
                'images/book3.svg'
            ],
            'laptop' => [
                '/images/default-laptop.jpg',
                '/images/default-laptop.jpg'
            ],
            'bag' => [
                '/images/default-bag.jpg',
                '/images/default-bag.jpg'
            ]
        ];
        
        if (isset($sampleImages[$type])) {
            // Clear existing images
            $stmt = $conn->prepare("DELETE FROM landing_page_images WHERE landing_page_id = ?");
            $stmt->execute([$landingPageId]);
            
            // Add new images
            foreach ($sampleImages[$type] as $index => $imageUrl) {
                $stmt = $conn->prepare("
                    INSERT INTO landing_page_images (landing_page_id, image_url, ordre)
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$landingPageId, $imageUrl, $index]);
            }
            echo "  - Added " . count($sampleImages[$type]) . " sample images\n";
        }
        
        echo "  ✓ Landing page created: {$url}\n";
    }
    
    echo "\n=== Sample Landing Pages Created Successfully ===\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
