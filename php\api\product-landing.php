<?php
header('Content-Type: application/json; charset=utf-8');
require_once '../ProductLanding.php';

// Verify admin authentication
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$productLanding = new ProductLanding();

try {
    $action = $_GET['action'] ?? '';
    
    // Validate product ID for all actions that require it
    $productId = 0;
    if (in_array($action, ['update_landing', 'get_landing', 'delete_image', 'reorder_images'])) {
        $productId = isset($_GET['productId']) ? (int)$_GET['productId'] : 
                     (isset($_POST['productId']) ? (int)$_POST['productId'] : 
                     (($data = json_decode(file_get_contents('php://input'), true)) ? (int)($data['productId'] ?? 0) : 0));
        
        if ($productId <= 0) {
            throw new Exception('معرف المنتج غير صالح');
        }
    }
    
    switch ($action) {
        case 'update_landing':
            $data = json_decode(file_get_contents('php://input'), true);
            $productId = $data['productId'] ?? 0;
            $hasLanding = $data['hasLandingPage'] ?? false;
            $isEnabled = $data['landingPageEnabled'] ?? false;
            $title = $data['title'] ?? '';
            
            // Update landing page settings
            $success = $productLanding->updateLandingSettings($productId, $hasLanding, $isEnabled, $title);
            
            if ($success && isset($data['contentBlocks'])) {
                // Clear existing content blocks
                $stmt = $pdo->prepare('DELETE FROM product_content_blocks WHERE product_id = ?');
                $stmt->execute([$productId]);
                
                // Add new content blocks
                foreach ($data['contentBlocks'] as $block) {
                    $productLanding->saveContentBlock(
                        $productId,
                        $block['title'],
                        $block['content'],
                        $block['sortOrder']
                    );
                }
            }
            
            // Handle image uploads
            if ($success && isset($_FILES['gallery'])) {
                $uploadDir = '../../uploads/products/gallery/' . $productId . '/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                foreach ($_FILES['gallery']['tmp_name'] as $index => $tmpName) {
                    $fileName = time() . '_' . $_FILES['gallery']['name'][$index];
                    $filePath = $uploadDir . $fileName;
                    
                    if (move_uploaded_file($tmpName, $filePath)) {
                        $imageUrl = 'uploads/products/gallery/' . $productId . '/' . $fileName;
                        $productLanding->addProductImage($productId, $imageUrl);
                    }
                }
            }
            
            echo json_encode([
                'success' => $success,
                'message' => $success ? 'تم تحديث صفحة المنتج بنجاح' : 'حدث خطأ أثناء التحديث'
            ]);
            break;
            
        case 'get_landing':
            $productId = $_GET['productId'] ?? 0;
            
            // Get product details
            $stmt = $pdo->prepare('SELECT has_landing_page, landing_page_enabled, slug FROM livres WHERE id = ?');
            $stmt->execute([$productId]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($product) {
                // Get content blocks
                $contentBlocks = $productLanding->getContentBlocks($productId);
                
                // Get gallery images
                $galleryImages = $productLanding->getProductImages($productId);
                
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'hasLandingPage' => (bool)$product['has_landing_page'],
                        'landingPageEnabled' => (bool)$product['landing_page_enabled'],
                        'slug' => $product['slug'],
                        'contentBlocks' => $contentBlocks,
                        'galleryImages' => $galleryImages
                    ]
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'المنتج غير موجود']);
            }
            break;
            
        case 'delete_image':
            $data = json_decode(file_get_contents('php://input'), true);
            $imageId = $data['imageId'] ?? 0;
            
            if ($imageId <= 0) {
                throw new Exception('معرف الصورة غير صالح');
            }
            
            // Delete image record and file
            $stmt = $pdo->prepare('SELECT image_url FROM product_images WHERE id = ? AND product_id = ?');
            $stmt->execute([$imageId, $productId]);
            $image = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($image) {
                // Delete file
                $filePath = '../../' . $image['image_url'];
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
                
                // Delete record
                $stmt = $pdo->prepare('DELETE FROM product_images WHERE id = ?');
                $success = $stmt->execute([$imageId]);
                
                echo json_encode([
                    'success' => $success,
                    'message' => $success ? 'تم حذف الصورة بنجاح' : 'حدث خطأ أثناء حذف الصورة'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'الصورة غير موجودة']);
            }
            break;
            
        case 'reorder_images':
            $data = json_decode(file_get_contents('php://input'), true);
            $imageIds = $data['imageIds'] ?? [];
            
            if (empty($imageIds)) {
                throw new Exception('لم يتم تحديد ترتيب الصور');
            }
            
            // Verify all images belong to the product
            $stmt = $pdo->prepare('SELECT COUNT(*) FROM product_images WHERE id IN (' . implode(',', array_fill(0, count($imageIds), '?')) . ') AND product_id = ?');
            $params = array_merge($imageIds, [$productId]);
            $stmt->execute($params);
            $count = $stmt->fetchColumn();
            
            if ($count !== count($imageIds)) {
                throw new Exception('بعض الصور غير موجودة أو لا تنتمي لهذا المنتج');
            }
            
            // Update sort order
            $pdo->beginTransaction();
            try {
                foreach ($imageIds as $order => $id) {
                    $stmt = $pdo->prepare('UPDATE product_images SET sort_order = ? WHERE id = ?');
                    $stmt->execute([$order, $id]);
                }
                $pdo->commit();
                echo json_encode(['success' => true, 'message' => 'تم تحديث ترتيب الصور بنجاح']);
            } catch (Exception $e) {
                $pdo->rollBack();
                throw new Exception('فشل تحديث ترتيب الصور');
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'إجراء غير صالح']);
    }
    
} catch (Exception $e) {
    error_log('Product Landing API Error: ' . $e->getMessage());
    $statusCode = $e->getCode() ?: 500;
    http_response_code($statusCode);
    echo json_encode([
        'success' => false,
        'message' => $statusCode === 500 ? 'حدث خطأ في الخادم' : $e->getMessage(),
        'error' => $e->getMessage()
    ]);
}