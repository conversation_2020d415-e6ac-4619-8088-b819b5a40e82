<?php
/**
 * Comprehensive Security Audit for Mossaab Landing Page
 * Tests SQL injection prevention, input validation, XSS protection, and more
 */

require_once 'php/config.php';

echo "<h1>🔒 Security Audit Report</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .audit-section { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
    .success { color: #28a745; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .critical { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .secure { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .vulnerable { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .code-sample { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 5px 0; }
</style>\n";

$securityScore = 0;
$maxScore = 500;
$vulnerabilities = [];
$recommendations = [];

try {
    // Test 1: SQL Injection Prevention Audit
    echo "<div class='audit-section'>\n";
    echo "<h2>🛡️ Test 1: SQL Injection Prevention Audit</h2>\n";
    
    $sqlInjectionScore = 0;
    $apiFiles = ['products.php', 'landing-pages.php', 'categories.php'];
    
    foreach ($apiFiles as $file) {
        $filePath = "php/api/$file";
        echo "<h3>Auditing: $file</h3>\n";
        
        if (file_exists($filePath)) {
            $content = file_get_contents($filePath);
            
            // Check for prepared statements
            $preparedStatements = preg_match_all('/\$\w+->prepare\s*\(/', $content);
            $directQueries = preg_match_all('/\$\w+->query\s*\(/', $content);
            $execCalls = preg_match_all('/\$\w+->exec\s*\(/', $content);
            
            echo "<table>\n";
            echo "<tr><th>Security Check</th><th>Count</th><th>Status</th></tr>\n";
            echo "<tr><td>Prepared Statements</td><td>$preparedStatements</td><td>" . 
                 ($preparedStatements > 0 ? '<span class="success">✅ Good</span>' : '<span class="error">❌ None Found</span>') . "</td></tr>\n";
            echo "<tr><td>Direct Queries</td><td>$directQueries</td><td>" . 
                 ($directQueries === 0 ? '<span class="success">✅ Safe</span>' : '<span class="warning">⚠️ Found</span>') . "</td></tr>\n";
            echo "<tr><td>Exec Calls</td><td>$execCalls</td><td>" . 
                 ($execCalls === 0 ? '<span class="success">✅ Safe</span>' : '<span class="warning">⚠️ Found</span>') . "</td></tr>\n";
            echo "</table>\n";
            
            // Check for potential SQL injection patterns
            $dangerousPatterns = [
                '/\$_GET\[.*?\].*?SELECT/' => 'Direct GET parameter in SELECT',
                '/\$_POST\[.*?\].*?SELECT/' => 'Direct POST parameter in SELECT',
                '/\$_REQUEST\[.*?\].*?SELECT/' => 'Direct REQUEST parameter in SELECT',
                '/SELECT.*?\$_/' => 'User input directly in SELECT',
                '/INSERT.*?\$_/' => 'User input directly in INSERT',
                '/UPDATE.*?\$_/' => 'User input directly in UPDATE',
                '/DELETE.*?\$_/' => 'User input directly in DELETE'
            ];
            
            $vulnerablePatterns = 0;
            foreach ($dangerousPatterns as $pattern => $description) {
                if (preg_match($pattern, $content)) {
                    $vulnerablePatterns++;
                    $vulnerabilities[] = "$file: $description";
                    echo "<div class='vulnerable'>❌ Potential vulnerability: $description</div>\n";
                }
            }
            
            if ($vulnerablePatterns === 0) {
                echo "<div class='secure'>✅ No obvious SQL injection patterns found</div>\n";
                $sqlInjectionScore += 30;
            } else {
                echo "<div class='critical'>🚨 Found $vulnerablePatterns potential SQL injection vulnerabilities</div>\n";
            }
            
            // Check parameter binding
            $parameterBinding = preg_match_all('/\$\w+->execute\s*\(\s*\[/', $content);
            if ($parameterBinding > 0) {
                echo "<div class='secure'>✅ Parameter binding found ($parameterBinding instances)</div>\n";
                $sqlInjectionScore += 20;
            } else {
                echo "<div class='vulnerable'>❌ No parameter binding found</div>\n";
                $vulnerabilities[] = "$file: Missing parameter binding in prepared statements";
            }
            
        } else {
            echo "<p class='error'>❌ File not found: $filePath</p>\n";
        }
    }
    
    $securityScore += $sqlInjectionScore;
    echo "<p><strong>SQL Injection Prevention Score: $sqlInjectionScore/150</strong></p>\n";
    echo "</div>\n";
    
    // Test 2: Input Validation Assessment
    echo "<div class='audit-section'>\n";
    echo "<h2>🔍 Test 2: Input Validation Assessment</h2>\n";
    
    $validationScore = 0;
    
    // Check if security.php exists and has validation functions
    if (file_exists('php/security.php')) {
        $securityContent = file_get_contents('php/security.php');
        
        $validationFunctions = [
            'sanitizeInput' => 'Basic input sanitization',
            'validateFileUpload' => 'File upload validation',
            'validateURL' => 'URL validation',
            'validateCSRFToken' => 'CSRF token validation'
        ];
        
        echo "<h3>Security Functions Available:</h3>\n";
        echo "<table>\n";
        echo "<tr><th>Function</th><th>Purpose</th><th>Status</th></tr>\n";
        
        foreach ($validationFunctions as $func => $purpose) {
            $exists = strpos($securityContent, "function $func") !== false;
            echo "<tr><td>$func</td><td>$purpose</td><td>" . 
                 ($exists ? '<span class="success">✅ Available</span>' : '<span class="error">❌ Missing</span>') . "</td></tr>\n";
            if ($exists) $validationScore += 15;
        }
        echo "</table>\n";
        
        // Check for Arabic text handling
        if (strpos($securityContent, 'UTF-8') !== false) {
            echo "<div class='secure'>✅ UTF-8 encoding support for Arabic text</div>\n";
            $validationScore += 20;
        } else {
            echo "<div class='vulnerable'>❌ No explicit UTF-8 handling for Arabic text</div>\n";
            $vulnerabilities[] = "Missing UTF-8 encoding in input validation";
        }
        
        // Check for HTML entity encoding
        if (strpos($securityContent, 'htmlspecialchars') !== false) {
            echo "<div class='secure'>✅ HTML entity encoding implemented</div>\n";
            $validationScore += 20;
        } else {
            echo "<div class='vulnerable'>❌ No HTML entity encoding found</div>\n";
            $vulnerabilities[] = "Missing HTML entity encoding for XSS prevention";
        }
        
    } else {
        echo "<p class='error'>❌ Security.php file not found</p>\n";
        $vulnerabilities[] = "Missing security.php file";
    }
    
    $securityScore += $validationScore;
    echo "<p><strong>Input Validation Score: $validationScore/120</strong></p>\n";
    echo "</div>\n";
    
    // Test 3: XSS Protection Assessment
    echo "<div class='audit-section'>\n";
    echo "<h2>🛡️ Test 3: XSS Protection Assessment</h2>\n";
    
    $xssScore = 0;
    
    // Check for Content Security Policy
    $hasCSP = false;
    $phpFiles = glob('php/api/*.php');
    
    foreach ($phpFiles as $file) {
        $content = file_get_contents($file);
        if (strpos($content, 'Content-Security-Policy') !== false) {
            $hasCSP = true;
            break;
        }
    }
    
    if ($hasCSP) {
        echo "<div class='secure'>✅ Content Security Policy headers found</div>\n";
        $xssScore += 30;
    } else {
        echo "<div class='vulnerable'>❌ No Content Security Policy headers found</div>\n";
        $vulnerabilities[] = "Missing Content Security Policy headers";
        $recommendations[] = "Implement CSP headers to prevent XSS attacks";
    }
    
    // Check for output encoding
    $outputEncodingFiles = 0;
    foreach ($phpFiles as $file) {
        $content = file_get_contents($file);
        if (strpos($content, 'htmlspecialchars') !== false || strpos($content, 'htmlentities') !== false) {
            $outputEncodingFiles++;
        }
    }
    
    if ($outputEncodingFiles > 0) {
        echo "<div class='secure'>✅ Output encoding found in $outputEncodingFiles files</div>\n";
        $xssScore += 25;
    } else {
        echo "<div class='vulnerable'>❌ No output encoding found</div>\n";
        $vulnerabilities[] = "Missing output encoding in API responses";
    }
    
    // Check for JSON encoding with proper flags
    $jsonEncodingFiles = 0;
    foreach ($phpFiles as $file) {
        $content = file_get_contents($file);
        if (strpos($content, 'JSON_HEX_TAG') !== false) {
            $jsonEncodingFiles++;
        }
    }
    
    if ($jsonEncodingFiles > 0) {
        echo "<div class='secure'>✅ Secure JSON encoding found in $jsonEncodingFiles files</div>\n";
        $xssScore += 25;
    } else {
        echo "<div class='warning'>⚠️ Basic JSON encoding found, consider adding security flags</div>\n";
        $recommendations[] = "Use JSON_HEX_TAG and other security flags in json_encode()";
    }
    
    $securityScore += $xssScore;
    echo "<p><strong>XSS Protection Score: $xssScore/80</strong></p>\n";
    echo "</div>\n";
    
    // Test 4: Authentication and Session Security
    echo "<div class='audit-section'>\n";
    echo "<h2>🔐 Test 4: Authentication and Session Security</h2>\n";
    
    $authScore = 0;
    
    // Check session configuration
    $sessionSecure = 0;
    if (ini_get('session.cookie_httponly')) {
        echo "<div class='secure'>✅ HTTP-only session cookies enabled</div>\n";
        $sessionSecure++;
    } else {
        echo "<div class='vulnerable'>❌ HTTP-only session cookies not enabled</div>\n";
        $vulnerabilities[] = "Session cookies not set to HTTP-only";
    }
    
    if (ini_get('session.cookie_secure')) {
        echo "<div class='secure'>✅ Secure session cookies enabled</div>\n";
        $sessionSecure++;
    } else {
        echo "<div class='warning'>⚠️ Secure session cookies not enabled (requires HTTPS)</div>\n";
        $recommendations[] = "Enable secure session cookies for HTTPS";
    }
    
    $authScore += $sessionSecure * 15;
    
    // Check for password hashing
    if (file_exists('php/security.php')) {
        $securityContent = file_get_contents('php/security.php');
        if (strpos($securityContent, 'password_hash') !== false || strpos($securityContent, 'password_verify') !== false) {
            echo "<div class='secure'>✅ Password hashing functions found</div>\n";
            $authScore += 30;
        } else {
            echo "<div class='vulnerable'>❌ No password hashing functions found</div>\n";
            $vulnerabilities[] = "Missing secure password hashing";
        }
    }
    
    // Check for rate limiting
    if (file_exists('php/security.php')) {
        $securityContent = file_get_contents('php/security.php');
        if (strpos($securityContent, 'checkRateLimit') !== false) {
            echo "<div class='secure'>✅ Rate limiting function available</div>\n";
            $authScore += 25;
        } else {
            echo "<div class='vulnerable'>❌ No rate limiting found</div>\n";
            $vulnerabilities[] = "Missing rate limiting for authentication";
        }
    }
    
    $securityScore += $authScore;
    echo "<p><strong>Authentication Security Score: $authScore/100</strong></p>\n";
    echo "</div>\n";
    
    // Test 5: File Upload Security
    echo "<div class='audit-section'>\n";
    echo "<h2>📁 Test 5: File Upload Security</h2>\n";
    
    $fileUploadScore = 0;
    
    if (file_exists('php/security.php')) {
        $securityContent = file_get_contents('php/security.php');
        
        // Check for file validation
        if (strpos($securityContent, 'validateFileUpload') !== false) {
            echo "<div class='secure'>✅ File upload validation function exists</div>\n";
            $fileUploadScore += 20;
            
            // Check for MIME type validation
            if (strpos($securityContent, 'finfo_file') !== false) {
                echo "<div class='secure'>✅ MIME type validation implemented</div>\n";
                $fileUploadScore += 15;
            }
            
            // Check for malicious content detection
            if (strpos($securityContent, '<?php') !== false && strpos($securityContent, '<script') !== false) {
                echo "<div class='secure'>✅ Malicious content detection implemented</div>\n";
                $fileUploadScore += 15;
            }
        } else {
            echo "<div class='vulnerable'>❌ No file upload validation found</div>\n";
            $vulnerabilities[] = "Missing file upload validation";
        }
    }
    
    $securityScore += $fileUploadScore;
    echo "<p><strong>File Upload Security Score: $fileUploadScore/50</strong></p>\n";
    echo "</div>\n";
    
    // Overall Security Assessment
    echo "<div class='audit-section' style='border-left-color: #28a745;'>\n";
    echo "<h2>📊 Overall Security Assessment</h2>\n";
    
    $securityPercentage = ($securityScore / $maxScore) * 100;
    
    echo "<h3>Security Score: $securityScore/$maxScore (" . number_format($securityPercentage, 1) . "%)</h3>\n";
    
    if ($securityPercentage >= 80) {
        echo "<div class='secure'>🎉 Excellent security posture! Your application is well-protected.</div>\n";
    } elseif ($securityPercentage >= 60) {
        echo "<div class='warning'>⚠️ Good security with some areas for improvement.</div>\n";
    } else {
        echo "<div class='critical'>🚨 Security needs significant improvement!</div>\n";
    }
    
    // Vulnerabilities Summary
    if (!empty($vulnerabilities)) {
        echo "<h3>🚨 Critical Vulnerabilities Found:</h3>\n";
        echo "<ul>\n";
        foreach ($vulnerabilities as $vuln) {
            echo "<li class='error'>$vuln</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<h3>✅ No Critical Vulnerabilities Found</h3>\n";
    }
    
    // Recommendations
    if (!empty($recommendations)) {
        echo "<h3>💡 Security Recommendations:</h3>\n";
        echo "<ul>\n";
        foreach ($recommendations as $rec) {
            echo "<li class='warning'>$rec</li>\n";
        }
        echo "</ul>\n";
    }
    
    // Next Steps
    echo "<h3>🚀 Next Steps:</h3>\n";
    echo "<ol>\n";
    echo "<li>Address critical vulnerabilities immediately</li>\n";
    echo "<li>Implement missing security functions</li>\n";
    echo "<li>Add Content Security Policy headers</li>\n";
    echo "<li>Enable HTTPS and secure session cookies</li>\n";
    echo "<li>Implement comprehensive input validation</li>\n";
    echo "<li>Add rate limiting to all API endpoints</li>\n";
    echo "<li>Regular security audits and penetration testing</li>\n";
    echo "</ol>\n";
    
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='audit-section' style='border-left-color: #dc3545;'>\n";
    echo "<h2>❌ Error During Security Audit</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}
?>

<script>
console.log('🔒 Security audit completed');
console.log('Security Score: <?php echo number_format($securityPercentage ?? 0, 1); ?>%');
console.log('Vulnerabilities found: <?php echo count($vulnerabilities); ?>');
</script>
