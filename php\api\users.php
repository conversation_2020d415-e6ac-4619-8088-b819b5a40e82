<?php

/**
 * Users Management API
 * Handles CRUD operations for the multi-user system
 */

require_once __DIR__ . '/../config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Session is already started in config.php

try {
    $db = getPDOConnection();
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    // Handle different HTTP methods and actions
    switch ($method) {
        case 'GET':
            if ($action === 'list' || empty($action)) {
                handleGetUsers($db);
            } elseif ($action === 'roles') {
                handleGetRoles($db);
            } elseif ($action === 'subscriptions') {
                handleGetSubscriptions($db);
            } elseif ($action === 'stats') {
                handleGetUserStats($db);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        case 'POST':
            if ($action === 'create') {
                handleCreateUser($db);
            } elseif ($action === 'bulk') {
                handleBulkAction($db);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        case 'PUT':
            if ($action === 'update') {
                handleUpdateUser($db);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        case 'DELETE':
            if ($action === 'delete') {
                handleDeleteUser($db);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        default:
            throw new Exception('Method not allowed');
    }
} catch (Exception $e) {
    error_log("Users API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get all users with their roles and subscriptions
 */
function handleGetUsers($db)
{
    try {
        $stmt = $db->prepare("
            SELECT
                u.*,
                ur.display_name_ar as role_name,
                ur.level as role_level,
                sp.display_name_ar as subscription_name,
                sp.max_products,
                sp.max_landing_pages,
                sp.max_storage_mb,
                s.store_name,
                s.store_slug
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.id
            LEFT JOIN subscription_plans sp ON u.subscription_id = sp.id
            LEFT JOIN stores s ON u.store_id = s.id
            ORDER BY u.created_at DESC
        ");

        $stmt->execute();
        $users = $stmt->fetchAll();

        // Format users data
        $formattedUsers = array_map(function ($user) {
            return [
                'id' => (int)$user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'name' => trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')),
                'phone' => $user['phone'],
                'role' => $user['role_name'] ?? 'عميل',
                'role_level' => (int)($user['role_level'] ?? 20),
                'subscription' => $user['subscription_name'] ?? 'مجاني',
                'status' => $user['status'],
                'avatar' => $user['avatar'] ?? 'https://via.placeholder.com/40',
                'registeredAt' => date('Y-m-d', strtotime($user['created_at'])),
                'lastLogin' => $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : null,
                'store' => [
                    'name' => $user['store_name'],
                    'slug' => $user['store_slug']
                ],
                'limits' => [
                    'products' => (int)($user['max_products'] ?? 5),
                    'landing_pages' => (int)($user['max_landing_pages'] ?? 2),
                    'storage_mb' => (int)($user['max_storage_mb'] ?? 100)
                ]
            ];
        }, $users);

        // Get additional statistics
        $stats = getUserStats($db);

        echo json_encode([
            'success' => true,
            'users' => $formattedUsers,
            'total' => count($formattedUsers),
            'stats' => $stats
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to fetch users: ' . $e->getMessage());
    }
}

/**
 * Get user statistics
 */
function getUserStats($db)
{
    try {
        $stats = [];

        // Total users
        $stmt = $db->prepare("SELECT COUNT(*) FROM users");
        $stmt->execute();
        $stats['total_users'] = (int)$stmt->fetchColumn();

        // Active users
        $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE status = 'active'");
        $stmt->execute();
        $stats['active_users'] = (int)$stmt->fetchColumn();

        // Users by role
        $stmt = $db->prepare("
            SELECT ur.display_name_ar, COUNT(*) as count
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.id
            GROUP BY u.role_id, ur.display_name_ar
        ");
        $stmt->execute();
        $stats['users_by_role'] = $stmt->fetchAll();

        // Users by subscription
        $stmt = $db->prepare("
            SELECT sp.display_name_ar, COUNT(*) as count
            FROM users u
            LEFT JOIN subscription_plans sp ON u.subscription_id = sp.id
            GROUP BY u.subscription_id, sp.display_name_ar
        ");
        $stmt->execute();
        $stats['users_by_subscription'] = $stmt->fetchAll();

        // Recent registrations (last 30 days)
        $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $stmt->execute();
        $stats['recent_registrations'] = (int)$stmt->fetchColumn();

        return $stats;
    } catch (Exception $e) {
        error_log("Error getting user stats: " . $e->getMessage());
        return [];
    }
}

/**
 * Get all available roles
 */
function handleGetRoles($db)
{
    try {
        $stmt = $db->prepare("SELECT * FROM user_roles WHERE is_active = 1 ORDER BY level DESC");
        $stmt->execute();
        $roles = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'roles' => $roles
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to fetch roles: ' . $e->getMessage());
    }
}

/**
 * Get all subscription plans
 */
function handleGetSubscriptions($db)
{
    try {
        $stmt = $db->prepare("SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY sort_order ASC");
        $stmt->execute();
        $subscriptions = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'subscriptions' => $subscriptions
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to fetch subscriptions: ' . $e->getMessage());
    }
}

/**
 * Get user statistics
 */
function handleGetUserStats($db)
{
    try {
        // Total users
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM users");
        $stmt->execute();
        $totalUsers = $stmt->fetch()['total'];

        // Active users
        $stmt = $db->prepare("SELECT COUNT(*) as active FROM users WHERE status = 'active'");
        $stmt->execute();
        $activeUsers = $stmt->fetch()['active'];

        // Users by role
        $stmt = $db->prepare("
            SELECT ur.display_name_ar as role_name, COUNT(u.id) as count
            FROM user_roles ur
            LEFT JOIN users u ON ur.id = u.role_id
            GROUP BY ur.id, ur.display_name_ar
            ORDER BY ur.level DESC
        ");
        $stmt->execute();
        $usersByRole = $stmt->fetchAll();

        // Users by subscription
        $stmt = $db->prepare("
            SELECT sp.display_name_ar as plan_name, COUNT(u.id) as count
            FROM subscription_plans sp
            LEFT JOIN users u ON sp.id = u.subscription_id
            GROUP BY sp.id, sp.display_name_ar
            ORDER BY sp.sort_order ASC
        ");
        $stmt->execute();
        $usersBySubscription = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'stats' => [
                'total_users' => (int)$totalUsers,
                'active_users' => (int)$activeUsers,
                'inactive_users' => (int)($totalUsers - $activeUsers),
                'by_role' => $usersByRole,
                'by_subscription' => $usersBySubscription
            ]
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to fetch user stats: ' . $e->getMessage());
    }
}

/**
 * Create a new user
 */
function handleCreateUser($db)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            throw new Exception('Invalid input data');
        }

        // Validate required fields
        $required = ['username', 'email', 'password', 'role_id', 'subscription_id'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Check if username or email already exists
        $stmt = $db->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$input['username'], $input['email']]);
        if ($stmt->fetch()) {
            throw new Exception('Username or email already exists');
        }

        // Hash password
        $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);

        // Insert user
        $stmt = $db->prepare("
            INSERT INTO users (username, email, password, first_name, last_name, phone, role_id, subscription_id, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $input['username'],
            $input['email'],
            $hashedPassword,
            $input['first_name'] ?? '',
            $input['last_name'] ?? '',
            $input['phone'] ?? '',
            $input['role_id'],
            $input['subscription_id'],
            $input['status'] ?? 'active'
        ]);

        $userId = $db->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'User created successfully',
            'user_id' => $userId
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to create user: ' . $e->getMessage());
    }
}

/**
 * Update an existing user
 */
function handleUpdateUser($db)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || empty($input['id'])) {
            throw new Exception('Invalid input data or missing user ID');
        }

        $userId = $input['id'];
        $updates = [];
        $params = [];

        // Build dynamic update query
        $allowedFields = ['username', 'email', 'first_name', 'last_name', 'phone', 'role_id', 'subscription_id', 'status'];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updates[] = "$field = ?";
                $params[] = $input[$field];
            }
        }

        if (empty($updates)) {
            throw new Exception('No fields to update');
        }

        // Handle password update separately
        if (!empty($input['password'])) {
            $updates[] = "password = ?";
            $params[] = password_hash($input['password'], PASSWORD_DEFAULT);
        }

        $params[] = $userId;

        $sql = "UPDATE users SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute($params);

        echo json_encode([
            'success' => true,
            'message' => 'User updated successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to update user: ' . $e->getMessage());
    }
}

/**
 * Delete a user
 */
function handleDeleteUser($db)
{
    try {
        $userId = $_GET['id'] ?? '';

        if (empty($userId)) {
            throw new Exception('User ID is required');
        }

        $stmt = $db->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$userId]);

        if ($stmt->rowCount() === 0) {
            throw new Exception('User not found');
        }

        echo json_encode([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to delete user: ' . $e->getMessage());
    }
}

/**
 * Handle bulk actions on users
 */
function handleBulkAction($db)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || empty($input['action']) || empty($input['user_ids'])) {
            throw new Exception('Invalid input data');
        }

        $action = $input['action'];
        $userIds = $input['user_ids'];
        $placeholders = str_repeat('?,', count($userIds) - 1) . '?';

        switch ($action) {
            case 'activate':
                $stmt = $db->prepare("UPDATE users SET status = 'active' WHERE id IN ($placeholders)");
                $stmt->execute($userIds);
                break;

            case 'deactivate':
                $stmt = $db->prepare("UPDATE users SET status = 'inactive' WHERE id IN ($placeholders)");
                $stmt->execute($userIds);
                break;

            case 'delete':
                $stmt = $db->prepare("DELETE FROM users WHERE id IN ($placeholders)");
                $stmt->execute($userIds);
                break;

            default:
                throw new Exception('Invalid bulk action');
        }

        echo json_encode([
            'success' => true,
            'message' => "Bulk action '$action' completed successfully",
            'affected_count' => $stmt->rowCount()
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to perform bulk action: ' . $e->getMessage());
    }
}
