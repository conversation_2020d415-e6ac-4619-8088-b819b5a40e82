<?php
require_once __DIR__ . '/../php/config.php';

echo "🔧 TESTING STORE PRODUCT FILTERING FIX\n";
echo "======================================\n\n";

try {
    $pdo = getPDOConnection();
    
    // Test the exact query from store.php
    echo "📦 Testing Store Product Query...\n";
    
    $storeId = 1; // Demo store ID
    
    $stmt = $pdo->prepare("
        SELECT p.*
        FROM produits p
        WHERE p.store_id = ? AND p.actif = 1
        ORDER BY p.created_at DESC
    ");
    $stmt->execute([$storeId]);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Store ID: {$storeId}\n";
    echo "Products found: " . count($products) . "\n\n";
    
    if (count($products) > 0) {
        echo "✅ Store-specific products found!\n";
        echo "📋 Product List:\n";
        
        foreach ($products as $index => $product) {
            echo "   " . ($index + 1) . ". {$product['titre']}\n";
            echo "      Price: {$product['prix']} DZD\n";
            echo "      Type: {$product['type']}\n";
            echo "      Store ID: {$product['store_id']}\n\n";
        }
        
        if (count($products) === 10) {
            echo "🎉 PERFECT! Exactly 10 store-specific products found.\n";
        } elseif (count($products) < 10) {
            echo "⚠️ Only " . count($products) . " products found, expected 10.\n";
        } else {
            echo "⚠️ " . count($products) . " products found, expected 10.\n";
        }
    } else {
        echo "❌ No store-specific products found!\n";
        echo "This means the store page will show empty state.\n";
    }
    
    // Test the store page response
    echo "\n🌐 Testing Store Page Response...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/store/mossaab-store');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Status: {$httpCode}\n";
    
    if ($httpCode === 200) {
        echo "✅ Store page accessible\n";
        
        // Check for store name
        if (strpos($response, 'متجر مصعب') !== false) {
            echo "✅ Store name displayed correctly\n";
        } else {
            echo "❌ Store name not found\n";
        }
        
        // Count product cards in HTML
        $productCardCount = substr_count($response, 'product-card');
        echo "Product cards in HTML: {$productCardCount}\n";
        
        if ($productCardCount === count($products)) {
            echo "✅ HTML shows correct number of products\n";
        } else {
            echo "⚠️ HTML product count mismatch\n";
        }
        
        // Check for specific products
        $sampleProducts = [
            'كتاب الطبخ الجزائري الأصيل',
            'Samsung Galaxy A54 5G',
            'حقيبة ظهر جلدية فاخرة'
        ];
        
        $foundProducts = 0;
        foreach ($sampleProducts as $productName) {
            if (strpos($response, $productName) !== false) {
                echo "   ✅ Found: {$productName}\n";
                $foundProducts++;
            } else {
                echo "   ❌ Missing: {$productName}\n";
            }
        }
        
        if ($foundProducts > 0) {
            echo "✅ Store-specific products visible in HTML\n";
        } else {
            echo "❌ No store-specific products visible\n";
        }
        
    } else {
        echo "❌ Store page not accessible\n";
    }
    
    echo "\n🎯 SUMMARY\n";
    echo "==========\n";
    
    if (count($products) === 10 && $httpCode === 200 && $foundProducts > 0) {
        echo "🎉 STORE FILTERING FIX SUCCESSFUL!\n";
        echo "✅ Shows exactly 10 store-specific products\n";
        echo "✅ No global products included\n";
        echo "✅ Store page displays correctly\n\n";
        echo "📋 Test the fix:\n";
        echo "Visit: http://localhost:8000/store/mossaab-store\n";
        echo "Expected: 10 products instead of 45\n";
    } else {
        echo "⚠️ Issues detected:\n";
        if (count($products) !== 10) {
            echo "   • Product count: " . count($products) . " (expected 10)\n";
        }
        if ($httpCode !== 200) {
            echo "   • Store page not accessible\n";
        }
        if ($foundProducts === 0) {
            echo "   • Products not visible in HTML\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
