<?php
// Test AI API endpoint
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Testing AI API Endpoint ===\n\n";

// Test 1: Check if AI API file exists
echo "1. Checking AI API file...\n";
$apiFile = 'php/api/ai.php';
if (file_exists($apiFile)) {
    echo "✅ AI API file exists: $apiFile\n";
} else {
    echo "❌ AI API file not found: $apiFile\n";
    exit(1);
}

// Test 2: Test AI API with a simple request
echo "\n2. Testing AI API with POST request...\n";

$testData = [
    'action' => 'generate_product_description',
    'product' => [
        'title' => 'فن اللامبالاة - كتاب تطوير الذات',
        'category' => 'كتب',
        'price' => '2500'
    ]
];

$postData = json_encode($testData);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $postData
    ]
]);

$url = 'http://localhost:8000/php/api/ai.php';
echo "Making request to: $url\n";
echo "Request data: " . $postData . "\n\n";

$response = file_get_contents($url, false, $context);

if ($response === false) {
    echo "❌ Failed to get response from AI API\n";
    $error = error_get_last();
    if ($error) {
        echo "Error: " . $error['message'] . "\n";
    }
} else {
    echo "✅ Response received\n";
    echo "Raw response: " . substr($response, 0, 500) . "...\n\n";
    
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ Valid JSON response\n";
        echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        
        if ($data['success']) {
            echo "Generated content available: " . (isset($data['data']) ? 'yes' : 'no') . "\n";
            if (isset($data['data'])) {
                echo "Data keys: " . implode(', ', array_keys($data['data'])) . "\n";
            }
        } else {
            echo "Error: " . ($data['error'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "❌ Invalid JSON response: " . json_last_error_msg() . "\n";
        echo "Response: " . $response . "\n";
    }
}

// Test 3: Check AI settings
echo "\n3. Checking AI settings...\n";
$settingsUrl = 'http://localhost:8000/api/get-ai-settings.php';
$settingsResponse = file_get_contents($settingsUrl);

if ($settingsResponse !== false) {
    $settingsData = json_decode($settingsResponse, true);
    if (json_last_error() === JSON_ERROR_NONE && $settingsData['success']) {
        echo "✅ AI settings retrieved\n";
        
        $enabledProviders = [];
        foreach ($settingsData['data'] as $provider => $config) {
            if ($config['enabled'] && !empty($config['api_key'])) {
                $enabledProviders[] = $provider;
            }
        }
        
        if (count($enabledProviders) > 0) {
            echo "✅ Enabled AI providers: " . implode(', ', $enabledProviders) . "\n";
        } else {
            echo "⚠️ No enabled AI providers found\n";
        }
    } else {
        echo "❌ Failed to get AI settings\n";
    }
} else {
    echo "❌ Failed to fetch AI settings\n";
}

echo "\n=== Test Complete ===\n";
