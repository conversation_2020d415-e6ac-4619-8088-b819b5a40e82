<?php
require_once 'config.php';

class ProductLanding
{
    private $pdo;

    public function __construct()
    {
        $this->pdo = getPDOConnection();
    }

    // Generate and validate unique slug
    public function generateSlug($title, $productId = null)
    {
        $slug = preg_replace('/[^\p{L}\p{N}]+/u', '-', mb_strtolower($title));
        $slug = trim($slug, '-');

        // Check if slug exists
        $query = 'SELECT id FROM produits WHERE slug = ? AND id != ?';
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$slug, $productId]);

        if ($stmt->rowCount() > 0) {
            $slug .= '-' . time();
        }

        return $slug;
    }

    // Update product landing page settings
    public function updateLandingSettings($productId, $hasLanding, $isEnabled, $title)
    {
        try {
            $slug = $this->generateSlug($title, $productId);
            $query = 'UPDATE produits SET has_landing_page = ?, landing_page_enabled = ?, slug = ? WHERE id = ?';
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([$hasLanding, $isEnabled, $slug, $productId]);
        } catch (PDOException $e) {
            error_log('Error updating landing settings: ' . $e->getMessage());
            return false;
        }
    }

    // Add product images
    public function addProductImage($productId, $file)
    {
        try {
            // Create upload directory if it doesn't exist
            $uploadDir = '../uploads/products/gallery/' . $productId . '/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = uniqid() . '.' . $extension;
            $filepath = $uploadDir . $filename;

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                // Get next sort order
                $query = 'SELECT COALESCE(MAX(sort_order), -1) + 1 FROM product_images WHERE product_id = ?';
                $stmt = $this->pdo->prepare($query);
                $stmt->execute([$productId]);
                $sortOrder = $stmt->fetchColumn();

                // Save image record
                $imageUrl = 'uploads/products/gallery/' . $productId . '/' . $filename;
                $query = 'INSERT INTO product_images (product_id, image_url, sort_order) VALUES (?, ?, ?)';
                $stmt = $this->pdo->prepare($query);
                $stmt->execute([$productId, $imageUrl, $sortOrder]);

                return $this->pdo->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log('Error adding product image: ' . $e->getMessage());
            return false;
        }
    }

    // Get product images
    public function getProductImages($productId)
    {
        try {
            $query = 'SELECT * FROM product_images WHERE product_id = ? ORDER BY sort_order';
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$productId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log('Error getting product images: ' . $e->getMessage());
            return [];
        }
    }

    // Add/Update content block
    public function saveContentBlock($productId, $title, $content, $sortOrder, $blockId = null)
    {
        try {
            if ($blockId) {
                $query = 'UPDATE product_content_blocks SET title = ?, content = ?, sort_order = ? WHERE id = ? AND product_id = ?';
                $stmt = $this->pdo->prepare($query);
                return $stmt->execute([$title, $content, $sortOrder, $blockId, $productId]);
            } else {
                $query = 'INSERT INTO product_content_blocks (product_id, title, content, sort_order) VALUES (?, ?, ?, ?)';
                $stmt = $this->pdo->prepare($query);
                return $stmt->execute([$productId, $title, $content, $sortOrder]);
            }
        } catch (PDOException $e) {
            error_log('Error saving content block: ' . $e->getMessage());
            return false;
        }
    }

    // Get product content blocks
    public function getContentBlocks($productId)
    {
        try {
            $query = 'SELECT * FROM product_content_blocks WHERE product_id = ? ORDER BY sort_order';
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$productId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log('Error getting content blocks: ' . $e->getMessage());
            return [];
        }
    }

    // Get product by slug
    public function getProductBySlug($slug)
    {
        try {
            // Get basic product information
            $query = 'SELECT * FROM produits WHERE slug = ? AND has_landing_page = 1 AND landing_page_enabled = 1';
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$slug]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($product) {
                // Get gallery images
                $query = 'SELECT * FROM product_images WHERE product_id = ? ORDER BY sort_order';
                $stmt = $this->pdo->prepare($query);
                $stmt->execute([$product['id']]);
                $product['gallery_images'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Get content blocks
                $query = 'SELECT * FROM product_content_blocks WHERE product_id = ? ORDER BY sort_order';
                $stmt = $this->pdo->prepare($query);
                $stmt->execute([$product['id']]);
                $product['content_blocks'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }

            return $product;
        } catch (PDOException $e) {
            error_log('Error getting product by slug: ' . $e->getMessage());
            return null;
        }
    }

    // Delete product image
    public function deleteProductImage($imageId)
    {
        try {
            // Get image details
            $query = 'SELECT image_url FROM product_images WHERE id = ?';
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$imageId]);
            $image = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($image) {
                // Delete file
                $filepath = '../' . $image['image_url'];
                if (file_exists($filepath)) {
                    unlink($filepath);
                }

                // Delete record
                $query = 'DELETE FROM product_images WHERE id = ?';
                $stmt = $this->pdo->prepare($query);
                return $stmt->execute([$imageId]);
            }
            return false;
        } catch (PDOException $e) {
            error_log('Error deleting product image: ' . $e->getMessage());
            return false;
        }
    }

    // Reorder product images
    public function reorderProductImages($productId, $imageIds)
    {
        try {
            $this->pdo->beginTransaction();

            foreach ($imageIds as $order => $id) {
                $query = 'UPDATE product_images SET sort_order = ? WHERE id = ? AND product_id = ?';
                $stmt = $this->pdo->prepare($query);
                $stmt->execute([$order, $id, $productId]);
            }

            $this->pdo->commit();
            return true;
        } catch (PDOException $e) {
            $this->pdo->rollBack();
            error_log('Error reordering product images: ' . $e->getMessage());
            return false;
        }
    }
}
