<?php
require_once 'php/ProductLanding.php';

// Get product slug from URL
$slug = isset($_GET['slug']) ? htmlspecialchars($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: index.html');
    exit;
}

// Get product details
$productLanding = new ProductLanding();
$product = $productLanding->getProductBySlug($slug);

if (!$product) {
    header('Location: index.html');
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($product['titre']); ?> - متجر الكتب</title>
    
    <!-- Social Media Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($product['titre']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars(substr($product['description'], 0, 200)); ?>...">
    <meta property="og:image" content="<?php echo htmlspecialchars($product['image_url']); ?>">
    <meta property="og:url" content="<?php echo 'https://' . $_SERVER['HTTP_HOST'] . '/product-landing.php?slug=' . urlencode($slug); ?>">
    
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/product-view.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css">
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .product-header h1 {
            font-size: 2em;
            color: #2c3e50;
            margin: 0;
        }

        .back-link {
            color: #4a90e2;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .product-gallery {
            margin-bottom: 30px;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .swiper-container {
            width: 100%;
            height: 400px;
        }

        .swiper-slide img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .swiper-button-next,
        .swiper-button-prev {
            color: #4a90e2;
        }

        .swiper-pagination-bullet-active {
            background: #4a90e2;
        }

        .content-block {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .content-block h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #4a90e2;
            padding-bottom: 10px;
        }

        .block-content {
            line-height: 1.6;
            color: #444;
        }

        .social-share {
            position: fixed;
            bottom: 20px;
            left: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }

        .share-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 20px;
            border-radius: 5px;
            color: #fff;
            text-decoration: none;
            font-weight: 500;
            transition: transform 0.2s ease;
            width: 180px;
        }

        .share-button:hover {
            transform: scale(1.05);
        }

        .share-button i {
            margin-left: 8px;
            font-size: 1.2em;
        }

        .facebook-share {
            background: #1877f2;
        }

        .whatsapp-share {
            background: #25d366;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .product-header h1 {
                font-size: 1.5em;
            }

            .swiper-container {
                height: 300px;
            }

            .social-share {
                position: static;
                flex-direction: row;
                margin: 20px 0;
                padding: 10px;
            }

            .share-button {
                width: auto;
                flex: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="product-header">
            <h1><?php echo htmlspecialchars($product['titre']); ?></h1>
            <a href="index.html" class="back-link">العودة للرئيسية</a>
        </header>

        <main class="product-content">
            <!-- Product Gallery -->
            <?php if (!empty($product['gallery_images'])): ?>
            <div class="product-gallery">
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        <?php foreach ($product['gallery_images'] as $image): ?>
                        <div class="swiper-slide">
                            <img src="<?php echo htmlspecialchars($image); ?>" 
                                 alt="<?php echo htmlspecialchars($product['titre']); ?>">
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Social Share Buttons -->
            <div class="social-share">
                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode('https://' . $_SERVER['HTTP_HOST'] . '/product-landing.php?slug=' . $slug); ?>" 
                   target="_blank" 
                   class="share-button facebook-share">
                    <i class="fab fa-facebook-f"></i>
                    مشاركة على فيسبوك
                </a>
                <a href="https://api.whatsapp.com/send?text=<?php echo urlencode($product['titre'] . ' - ' . 'https://' . $_SERVER['HTTP_HOST'] . '/product-landing.php?slug=' . $slug); ?>" 
                   target="_blank" 
                   class="share-button whatsapp-share">
                    <i class="fab fa-whatsapp"></i>
                    مشاركة على واتساب
                </a>
            </div>

            <!-- Content Blocks -->
            <?php if (!empty($product['content_blocks'])): ?>
            <div class="content-blocks">
                <?php foreach ($product['content_blocks'] as $block): ?>
                <div class="content-block">
                    <?php if (!empty($block['title'])): ?>
                    <h3><?php echo htmlspecialchars($block['title']); ?></h3>
                    <?php endif; ?>
                    <div class="block-content">
                        <?php echo $block['content']; // Allow HTML content ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </main>
    </div>

    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    <script>
        // Initialize Swiper
        new Swiper('.swiper-container', {
            loop: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev'
            },
            autoplay: {
                delay: 5000
            }
        });
    </script>
</body>
</html>