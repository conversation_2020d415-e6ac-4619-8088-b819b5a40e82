<?php
/**
 * Store Products API
 * Handles CRUD operations for store products
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../config.php';

try {
    $pdo = getPDOConnection();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetProducts($pdo);
            break;
            
        case 'POST':
            handleCreateProduct($pdo);
            break;
            
        case 'PUT':
            handleUpdateProduct($pdo);
            break;
            
        case 'DELETE':
            handleDeleteProduct($pdo);
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
            break;
    }
    
} catch (Exception $e) {
    error_log("Store Products API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'error' => $e->getMessage()
    ]);
}

/**
 * Get products for a specific store
 */
function handleGetProducts($pdo) {
    $storeId = $_GET['store_id'] ?? null;
    
    if (!$storeId) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Store ID is required'
        ]);
        return;
    }
    
    try {
        // Get store products
        $stmt = $pdo->prepare("
            SELECT 
                p.*,
                CASE 
                    WHEN p.store_id = ? THEN 'store'
                    WHEN p.store_id IS NULL THEN 'global'
                    ELSE 'other'
                END as product_source
            FROM produits p
            WHERE (p.store_id = ? OR p.store_id IS NULL) AND p.actif = 1
            ORDER BY 
                CASE WHEN p.store_id = ? THEN 0 ELSE 1 END,
                p.created_at DESC
        ");
        $stmt->execute([$storeId, $storeId, $storeId]);
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get store information
        $stmt = $pdo->prepare("
            SELECT store_name, store_slug, status
            FROM stores 
            WHERE id = ?
        ");
        $stmt->execute([$storeId]);
        $store = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$store) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Store not found'
            ]);
            return;
        }
        
        // Count products by source
        $storeProducts = array_filter($products, function($p) use ($storeId) {
            return $p['store_id'] == $storeId;
        });
        
        $globalProducts = array_filter($products, function($p) {
            return $p['store_id'] === null;
        });
        
        echo json_encode([
            'success' => true,
            'message' => 'Products loaded successfully',
            'store' => $store,
            'products' => $products,
            'stats' => [
                'total' => count($products),
                'store_specific' => count($storeProducts),
                'global' => count($globalProducts)
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * Create a new product for the store
 */
function handleCreateProduct($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $required = ['titre', 'type', 'prix', 'stock', 'store_id'];
    foreach ($required as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => "Field '{$field}' is required"
            ]);
            return;
        }
    }
    
    try {
        // Verify store exists and user has permission
        $stmt = $pdo->prepare("SELECT id FROM stores WHERE id = ? AND status = 'active'");
        $stmt->execute([$input['store_id']]);
        
        if (!$stmt->fetch()) {
            http_response_code(403);
            echo json_encode([
                'success' => false,
                'message' => 'Store not found or access denied'
            ]);
            return;
        }
        
        // Insert new product
        $stmt = $pdo->prepare("
            INSERT INTO produits (
                store_id, type, titre, description, prix, stock, 
                image_url, actif, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
        ");
        
        $stmt->execute([
            $input['store_id'],
            $input['type'],
            $input['titre'],
            $input['description'] ?? '',
            $input['prix'],
            $input['stock'],
            $input['image_url'] ?? null
        ]);
        
        $productId = $pdo->lastInsertId();
        
        // Get the created product
        $stmt = $pdo->prepare("SELECT * FROM produits WHERE id = ?");
        $stmt->execute([$productId]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'message' => 'Product created successfully',
            'product' => $product
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * Update an existing product
 */
function handleUpdateProduct($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    $productId = $input['id'] ?? null;
    
    if (!$productId) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Product ID is required'
        ]);
        return;
    }
    
    try {
        // Check if product exists and belongs to the store
        $stmt = $pdo->prepare("
            SELECT p.*, s.status as store_status 
            FROM produits p
            LEFT JOIN stores s ON p.store_id = s.id
            WHERE p.id = ?
        ");
        $stmt->execute([$productId]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Product not found'
            ]);
            return;
        }
        
        // Build update query dynamically
        $updateFields = [];
        $updateValues = [];
        
        $allowedFields = ['titre', 'type', 'description', 'prix', 'stock', 'image_url', 'actif'];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "{$field} = ?";
                $updateValues[] = $input[$field];
            }
        }
        
        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'No fields to update'
            ]);
            return;
        }
        
        $updateFields[] = "updated_at = NOW()";
        $updateValues[] = $productId;
        
        $sql = "UPDATE produits SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($updateValues);
        
        // Get updated product
        $stmt = $pdo->prepare("SELECT * FROM produits WHERE id = ?");
        $stmt->execute([$productId]);
        $updatedProduct = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'message' => 'Product updated successfully',
            'product' => $updatedProduct
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * Delete a product
 */
function handleDeleteProduct($pdo) {
    $productId = $_GET['id'] ?? null;
    
    if (!$productId) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Product ID is required'
        ]);
        return;
    }
    
    try {
        // Check if product exists
        $stmt = $pdo->prepare("SELECT id, titre, store_id FROM produits WHERE id = ?");
        $stmt->execute([$productId]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Product not found'
            ]);
            return;
        }
        
        // Soft delete (set actif = 0) instead of hard delete
        $stmt = $pdo->prepare("UPDATE produits SET actif = 0, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$productId]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Product deleted successfully',
            'product_id' => $productId
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        throw $e;
    }
}
?>
