<?php

/**
 * Security utilities for production deployment
 */

// Prevent direct access
if (!defined('SECURITY_CHECK')) {
    die('Direct access not allowed');
}

/**
 * Enhanced input sanitization with Arabic text support
 */
function sanitizeInput($data, $allowHtml = false)
{
    if (is_array($data)) {
        return array_map(function ($item) use ($allowHtml) {
            return sanitizeInput($item, $allowHtml);
        }, $data);
    }

    if (!is_string($data)) {
        return $data;
    }

    // Trim whitespace and normalize Unicode
    $data = trim($data);
    $data = mb_convert_encoding($data, 'UTF-8', 'UTF-8');

    // Remove null bytes and control characters (except newlines and tabs for Arabic text)
    $data = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $data);

    // Handle slashes carefully for Arabic text
    // Note: magic_quotes_gpc was removed in PHP 5.4.0
    if (function_exists('get_magic_quotes_gpc') && get_magic_quotes_gpc()) {
        $data = stripslashes($data);
    }

    // XSS prevention
    if (!$allowHtml) {
        $data = htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8', false);
    } else {
        // For rich text content, use more selective filtering
        $data = strip_tags($data, '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6>');
        $data = preg_replace('/on\w+\s*=\s*["\'][^"\']*["\']/i', '', $data); // Remove event handlers
        $data = preg_replace('/javascript\s*:/i', '', $data); // Remove javascript: URLs
    }

    return $data;
}

/**
 * Validate Arabic text input
 */
function validateArabicText($text, $minLength = 1, $maxLength = 1000)
{
    if (empty($text)) {
        return ['valid' => false, 'error' => 'النص مطلوب'];
    }

    $length = mb_strlen($text, 'UTF-8');
    if ($length < $minLength) {
        return ['valid' => false, 'error' => "النص قصير جداً (الحد الأدنى $minLength أحرف)"];
    }

    if ($length > $maxLength) {
        return ['valid' => false, 'error' => "النص طويل جداً (الحد الأقصى $maxLength حرف)"];
    }

    // Check for valid Arabic/English characters and common punctuation
    if (!preg_match('/^[\p{Arabic}\p{Latin}\p{N}\s\.\,\!\?\:\;\-\(\)\"\']+$/u', $text)) {
        return ['valid' => false, 'error' => 'النص يحتوي على أحرف غير مسموحة'];
    }

    return ['valid' => true];
}

/**
 * Validate email with Arabic domain support
 */
function validateEmail($email)
{
    if (empty($email)) {
        return ['valid' => false, 'error' => 'البريد الإلكتروني مطلوب'];
    }

    // Convert to ASCII for validation
    $email = idn_to_ascii($email, IDNA_DEFAULT, INTL_IDNA_VARIANT_UTS46);

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['valid' => false, 'error' => 'البريد الإلكتروني غير صحيح'];
    }

    return ['valid' => true];
}

/**
 * Validate phone number (Algerian format)
 */
function validatePhone($phone)
{
    if (empty($phone)) {
        return ['valid' => false, 'error' => 'رقم الهاتف مطلوب'];
    }

    // Remove spaces and common separators
    $phone = preg_replace('/[\s\-\(\)\.]+/', '', $phone);

    // Algerian phone number patterns
    $patterns = [
        '/^(0|\+213)[5-7][0-9]{8}$/', // Mobile numbers
        '/^(0|\+213)[2-9][0-9]{7}$/'  // Landline numbers
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $phone)) {
            return ['valid' => true];
        }
    }

    return ['valid' => false, 'error' => 'رقم الهاتف غير صحيح'];
}

/**
 * Validate price input
 */
function validatePrice($price)
{
    if (empty($price)) {
        return ['valid' => false, 'error' => 'السعر مطلوب'];
    }

    // Remove currency symbols and spaces
    $price = preg_replace('/[^\d\.]/', '', $price);

    if (!is_numeric($price) || floatval($price) <= 0) {
        return ['valid' => false, 'error' => 'السعر يجب أن يكون رقماً موجباً'];
    }

    if (floatval($price) > 999999.99) {
        return ['valid' => false, 'error' => 'السعر مرتفع جداً'];
    }

    return ['valid' => true, 'value' => floatval($price)];
}

/**
 * Validate CSRF token
 */
function validateCSRFToken($token)
{
    if (!isset($_SESSION['csrf_token'])) {
        return false;
    }

    return hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Generate CSRF token
 */
function generateCSRFToken()
{
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Rate limiting
 */
function checkRateLimit($action, $limit = 10, $window = 60)
{
    $key = $action . '_' . $_SERVER['REMOTE_ADDR'];

    if (!isset($_SESSION['rate_limit'])) {
        $_SESSION['rate_limit'] = [];
    }

    $now = time();

    // Clean old entries
    $_SESSION['rate_limit'] = array_filter($_SESSION['rate_limit'], function ($timestamp) use ($now, $window) {
        return ($now - $timestamp) < $window;
    });

    // Count requests for this action
    $count = isset($_SESSION['rate_limit'][$key]) ? count($_SESSION['rate_limit'][$key]) : 0;

    if ($count >= $limit) {
        return false;
    }

    // Add current request
    if (!isset($_SESSION['rate_limit'][$key])) {
        $_SESSION['rate_limit'][$key] = [];
    }
    $_SESSION['rate_limit'][$key][] = $now;

    return true;
}

/**
 * Validate file upload
 */
function validateFileUpload($file, $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'], $maxSize = 5242880)
{
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['valid' => false, 'error' => 'ملف غير صحيح أو لم يتم رفعه بشكل صحيح'];
    }

    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'الملف كبير جداً (تجاوز حد الخادم)',
            UPLOAD_ERR_FORM_SIZE => 'الملف كبير جداً (تجاوز حد النموذج)',
            UPLOAD_ERR_PARTIAL => 'تم رفع الملف جزئياً فقط',
            UPLOAD_ERR_NO_FILE => 'لم يتم رفع أي ملف',
            UPLOAD_ERR_NO_TMP_DIR => 'مجلد مؤقت مفقود',
            UPLOAD_ERR_CANT_WRITE => 'فشل في كتابة الملف',
            UPLOAD_ERR_EXTENSION => 'امتداد PHP أوقف رفع الملف'
        ];

        $errorMsg = $errorMessages[$file['error']] ?? 'خطأ غير معروف في رفع الملف';
        return ['valid' => false, 'error' => $errorMsg];
    }

    // Check file size
    if ($file['size'] > $maxSize) {
        $maxSizeMB = round($maxSize / 1024 / 1024, 1);
        return ['valid' => false, 'error' => "حجم الملف كبير جداً (الحد الأقصى {$maxSizeMB} ميجابايت)"];
    }

    // Check minimum file size (avoid empty files)
    if ($file['size'] < 100) {
        return ['valid' => false, 'error' => 'الملف صغير جداً أو فارغ'];
    }

    // Validate MIME type using multiple methods
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    if (!in_array($mimeType, $allowedTypes)) {
        return ['valid' => false, 'error' => 'نوع الملف غير مسموح. الأنواع المسموحة: ' . implode(', ', $allowedTypes)];
    }

    // Additional MIME type verification for images
    if (strpos($mimeType, 'image/') === 0) {
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            return ['valid' => false, 'error' => 'الملف ليس صورة صحيحة'];
        }

        // Check image dimensions
        if ($imageInfo[0] < 50 || $imageInfo[1] < 50) {
            return ['valid' => false, 'error' => 'أبعاد الصورة صغيرة جداً (الحد الأدنى 50x50 بكسل)'];
        }

        if ($imageInfo[0] > 5000 || $imageInfo[1] > 5000) {
            return ['valid' => false, 'error' => 'أبعاد الصورة كبيرة جداً (الحد الأقصى 5000x5000 بكسل)'];
        }
    }

    // Check for malicious content (enhanced)
    $content = file_get_contents($file['tmp_name'], false, null, 0, 8192); // Read first 8KB
    $maliciousPatterns = [
        '/<\?php/i',
        '/<script/i',
        '/javascript:/i',
        '/vbscript:/i',
        '/onload\s*=/i',
        '/onerror\s*=/i',
        '/eval\s*\(/i',
        '/base64_decode/i'
    ];

    foreach ($maliciousPatterns as $pattern) {
        if (preg_match($pattern, $content)) {
            logSecurityEvent('MALICIOUS_FILE_UPLOAD', "Detected pattern: $pattern in file: " . $file['name']);
            return ['valid' => false, 'error' => 'محتوى الملف غير آمن - تم اكتشاف كود ضار'];
        }
    }

    // Validate file extension matches MIME type
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $mimeToExtension = [
        'image/jpeg' => ['jpg', 'jpeg'],
        'image/png' => ['png'],
        'image/gif' => ['gif'],
        'image/webp' => ['webp']
    ];

    $validExtensions = $mimeToExtension[$mimeType] ?? [];
    if (!empty($validExtensions) && !in_array($extension, $validExtensions)) {
        return ['valid' => false, 'error' => 'امتداد الملف لا يتطابق مع نوعه'];
    }

    return ['valid' => true, 'mime_type' => $mimeType, 'image_info' => $imageInfo ?? null];
}

/**
 * Log security events
 */
function logSecurityEvent($event, $details = '')
{
    $logFile = '../logs/security.log';
    $logDir = dirname($logFile);

    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    $logEntry = "[{$timestamp}] IP: {$ip} | Event: {$event} | Details: {$details} | User-Agent: {$userAgent}" . PHP_EOL;

    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Check if admin is logged in
 */
function requireAdminLogin()
{
    if (!isset($_SESSION['admin_id'])) {
        logSecurityEvent('UNAUTHORIZED_ACCESS', 'Attempt to access admin area without login');
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'غير مصرح بالوصول']);
        exit;
    }
}

/**
 * Validate admin session
 */
function validateAdminSession()
{
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_login_time'])) {
        return false;
    }

    // Check session timeout (24 hours)
    if (time() - $_SESSION['admin_login_time'] > 86400) {
        session_destroy();
        return false;
    }

    return true;
}

/**
 * Generate secure filename
 */
function generateSecureFilename($originalName)
{
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $filename = bin2hex(random_bytes(16)) . '.' . $extension;
    return $filename;
}

/**
 * Validate URL
 */
function validateURL($url)
{
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Clean output for JSON response
 */
function cleanJsonOutput($data)
{
    return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
}
