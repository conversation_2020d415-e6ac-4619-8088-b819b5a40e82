/**
 * Mobile Enhancements for Mossaab Landing Page
 * Improves mobile user experience with touch-friendly interactions
 */

class MobileEnhancements {
    constructor() {
        this.init();
    }

    init() {
        this.setupTouchEnhancements();
        this.setupMobileNavigation();
        this.setupFormEnhancements();
        this.setupImageOptimization();
        this.setupAccessibilityEnhancements();
        this.setupPerformanceOptimizations();
    }

    /**
     * Setup touch-friendly enhancements
     */
    setupTouchEnhancements() {
        // Add touch feedback to buttons
        const buttons = document.querySelectorAll('button, .btn, a[role="button"]');
        
        buttons.forEach(button => {
            // Ensure minimum touch target size
            const rect = button.getBoundingClientRect();
            if (rect.width < 44 || rect.height < 44) {
                button.style.minWidth = '44px';
                button.style.minHeight = '44px';
                button.style.display = 'flex';
                button.style.alignItems = 'center';
                button.style.justifyContent = 'center';
            }

            // Add touch feedback
            button.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
            button.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
        });

        // Improve scrolling performance
        document.addEventListener('touchstart', function() {}, { passive: true });
        document.addEventListener('touchmove', function() {}, { passive: true });
    }

    /**
     * Handle touch start events
     */
    handleTouchStart(event) {
        const element = event.currentTarget;
        element.style.transform = 'scale(0.98)';
        element.style.transition = 'transform 0.1s ease';
    }

    /**
     * Handle touch end events
     */
    handleTouchEnd(event) {
        const element = event.currentTarget;
        setTimeout(() => {
            element.style.transform = '';
        }, 100);
    }

    /**
     * Setup mobile navigation enhancements
     */
    setupMobileNavigation() {
        // Improve mobile menu accessibility
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const sidebar = document.querySelector('.sidebar');

        if (mobileMenuToggle && sidebar) {
            // Add ARIA attributes
            mobileMenuToggle.setAttribute('aria-label', 'فتح القائمة الجانبية');
            mobileMenuToggle.setAttribute('aria-expanded', 'false');
            sidebar.setAttribute('aria-hidden', 'true');

            // Improve menu toggle functionality
            mobileMenuToggle.addEventListener('click', (e) => {
                e.preventDefault();
                const isOpen = sidebar.classList.contains('active');
                
                if (isOpen) {
                    this.closeMobileMenu();
                } else {
                    this.openMobileMenu();
                }
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (window.innerWidth <= 768 && 
                    sidebar.classList.contains('active') && 
                    !sidebar.contains(e.target) && 
                    !mobileMenuToggle.contains(e.target)) {
                    this.closeMobileMenu();
                }
            });

            // Close menu on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && sidebar.classList.contains('active')) {
                    this.closeMobileMenu();
                }
            });
        }
    }

    /**
     * Open mobile menu
     */
    openMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        
        if (sidebar && mobileMenuToggle) {
            sidebar.classList.add('active');
            sidebar.setAttribute('aria-hidden', 'false');
            mobileMenuToggle.setAttribute('aria-expanded', 'true');
            mobileMenuToggle.setAttribute('aria-label', 'إغلاق القائمة الجانبية');
            
            // Focus first menu item
            const firstMenuItem = sidebar.querySelector('a, button');
            if (firstMenuItem) {
                firstMenuItem.focus();
            }
        }
    }

    /**
     * Close mobile menu
     */
    closeMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        
        if (sidebar && mobileMenuToggle) {
            sidebar.classList.remove('active');
            sidebar.setAttribute('aria-hidden', 'true');
            mobileMenuToggle.setAttribute('aria-expanded', 'false');
            mobileMenuToggle.setAttribute('aria-label', 'فتح القائمة الجانبية');
            
            // Return focus to toggle button
            mobileMenuToggle.focus();
        }
    }

    /**
     * Setup form enhancements for mobile
     */
    setupFormEnhancements() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            // Improve input focus behavior
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                // Prevent zoom on iOS
                if (input.type !== 'file') {
                    input.style.fontSize = '16px';
                }

                // Add better focus indicators
                input.addEventListener('focus', () => {
                    input.style.borderColor = '#3b82f6';
                    input.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                });

                input.addEventListener('blur', () => {
                    input.style.borderColor = '#e5e7eb';
                    input.style.boxShadow = 'none';
                });

                // Improve validation feedback
                input.addEventListener('invalid', (e) => {
                    e.preventDefault();
                    this.showValidationError(input);
                });
            });

            // Improve form submission
            form.addEventListener('submit', (e) => {
                const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.textContent = 'جاري الإرسال...';
                    
                    // Re-enable after 3 seconds to prevent permanent disable
                    setTimeout(() => {
                        submitButton.disabled = false;
                        submitButton.textContent = submitButton.getAttribute('data-original-text') || 'إرسال';
                    }, 3000);
                }
            });
        });
    }

    /**
     * Show validation error for form inputs
     */
    showValidationError(input) {
        // Remove existing error message
        const existingError = input.parentNode.querySelector('.validation-error');
        if (existingError) {
            existingError.remove();
        }

        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-error';
        errorDiv.style.color = '#ef4444';
        errorDiv.style.fontSize = '0.875rem';
        errorDiv.style.marginTop = '4px';
        errorDiv.textContent = input.validationMessage || 'هذا الحقل مطلوب';

        // Insert after input
        input.parentNode.insertBefore(errorDiv, input.nextSibling);

        // Remove error on input
        input.addEventListener('input', () => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, { once: true });
    }

    /**
     * Setup image optimization for mobile
     */
    setupImageOptimization() {
        // Lazy load images that aren't already handled
        const images = document.querySelectorAll('img:not([data-src])');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        
                        // Add loading animation
                        img.style.transition = 'opacity 0.3s ease';
                        img.style.opacity = '0.5';
                        
                        img.addEventListener('load', () => {
                            img.style.opacity = '1';
                        });

                        imageObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px'
            });

            images.forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Setup accessibility enhancements
     */
    setupAccessibilityEnhancements() {
        // Add skip link for keyboard navigation
        this.addSkipLink();

        // Improve focus management
        this.improveFocusManagement();

        // Add ARIA labels where missing
        this.addMissingAriaLabels();
    }

    /**
     * Add skip link for accessibility
     */
    addSkipLink() {
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = 'تخطي إلى المحتوى الرئيسي';
        skipLink.className = 'skip-link';
        skipLink.style.cssText = `
            position: absolute;
            top: -40px;
            left: 6px;
            background: #000;
            color: #fff;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 9999;
            transition: top 0.3s ease;
        `;

        skipLink.addEventListener('focus', () => {
            skipLink.style.top = '6px';
        });

        skipLink.addEventListener('blur', () => {
            skipLink.style.top = '-40px';
        });

        document.body.insertBefore(skipLink, document.body.firstChild);
    }

    /**
     * Improve focus management
     */
    improveFocusManagement() {
        // Add visible focus indicators
        const style = document.createElement('style');
        style.textContent = `
            *:focus {
                outline: 2px solid #3b82f6 !important;
                outline-offset: 2px !important;
            }
            
            .skip-link:focus {
                outline: 2px solid #fff !important;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Add missing ARIA labels
     */
    addMissingAriaLabels() {
        // Add labels to buttons without text
        const buttons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
        buttons.forEach(button => {
            if (!button.textContent.trim()) {
                const icon = button.querySelector('i');
                if (icon) {
                    const iconClass = icon.className;
                    if (iconClass.includes('cart')) {
                        button.setAttribute('aria-label', 'عربة التسوق');
                    } else if (iconClass.includes('menu')) {
                        button.setAttribute('aria-label', 'القائمة');
                    } else if (iconClass.includes('close')) {
                        button.setAttribute('aria-label', 'إغلاق');
                    } else {
                        button.setAttribute('aria-label', 'زر');
                    }
                }
            }
        });

        // Add labels to form inputs without labels
        const inputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
        inputs.forEach(input => {
            if (!input.previousElementSibling || input.previousElementSibling.tagName !== 'LABEL') {
                const placeholder = input.getAttribute('placeholder');
                if (placeholder) {
                    input.setAttribute('aria-label', placeholder);
                }
            }
        });
    }

    /**
     * Setup performance optimizations
     */
    setupPerformanceOptimizations() {
        // Debounce scroll events
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            scrollTimeout = setTimeout(() => {
                // Handle scroll events here
                this.handleScroll();
            }, 16); // ~60fps
        }, { passive: true });

        // Optimize resize events
        let resizeTimeout;
        window.addEventListener('resize', () => {
            if (resizeTimeout) {
                clearTimeout(resizeTimeout);
            }
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
    }

    /**
     * Handle scroll events
     */
    handleScroll() {
        // Add scroll-based optimizations here
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Hide/show mobile menu toggle based on scroll direction
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        if (mobileMenuToggle) {
            if (scrollTop > 100) {
                mobileMenuToggle.style.opacity = '0.8';
            } else {
                mobileMenuToggle.style.opacity = '1';
            }
        }
    }

    /**
     * Handle resize events
     */
    handleResize() {
        // Close mobile menu on resize to desktop
        if (window.innerWidth > 768) {
            this.closeMobileMenu();
        }
    }
}

// Initialize mobile enhancements when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new MobileEnhancements();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileEnhancements;
}
