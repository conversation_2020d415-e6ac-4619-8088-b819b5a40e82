[{"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "',' expected.", "source": "ts", "startLineNumber": 972, "startColumn": 11, "endLineNumber": 972, "endColumn": 25, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "',' expected.", "source": "ts", "startLineNumber": 972, "startColumn": 25, "endLineNumber": 972, "endColumn": 26, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "'=>' expected.", "source": "ts", "startLineNumber": 972, "startColumn": 28, "endLineNumber": 972, "endColumn": 29, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1028, "startColumn": 30, "endLineNumber": 1028, "endColumn": 31, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1085, "startColumn": 6, "endLineNumber": 1085, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1088, "startColumn": 23, "endLineNumber": 1088, "endColumn": 24, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1102, "startColumn": 6, "endLineNumber": 1102, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1105, "startColumn": 22, "endLineNumber": 1105, "endColumn": 23, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1118, "startColumn": 6, "endLineNumber": 1118, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1434", "severity": 8, "message": "Unexpected keyword or identifier.", "source": "ts", "startLineNumber": 1120, "startColumn": 5, "endLineNumber": 1120, "endColumn": 10, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1120, "startColumn": 31, "endLineNumber": 1120, "endColumn": 32, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1280, "startColumn": 6, "endLineNumber": 1280, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1434", "severity": 8, "message": "Unexpected keyword or identifier.", "source": "ts", "startLineNumber": 1282, "startColumn": 5, "endLineNumber": 1282, "endColumn": 10, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1282, "startColumn": 24, "endLineNumber": 1282, "endColumn": 25, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1345, "startColumn": 6, "endLineNumber": 1345, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1347, "startColumn": 29, "endLineNumber": 1347, "endColumn": 30, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1357, "startColumn": 6, "endLineNumber": 1357, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1359, "startColumn": 28, "endLineNumber": 1359, "endColumn": 29, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1407, "startColumn": 6, "endLineNumber": 1407, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1409, "startColumn": 34, "endLineNumber": 1409, "endColumn": 35, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1439, "startColumn": 6, "endLineNumber": 1439, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1441, "startColumn": 32, "endLineNumber": 1441, "endColumn": 33, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1494, "startColumn": 6, "endLineNumber": 1494, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1496, "startColumn": 32, "endLineNumber": 1496, "endColumn": 33, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1546, "startColumn": 6, "endLineNumber": 1546, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1548, "startColumn": 35, "endLineNumber": 1548, "endColumn": 36, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1582, "startColumn": 6, "endLineNumber": 1582, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1434", "severity": 8, "message": "Unexpected keyword or identifier.", "source": "ts", "startLineNumber": 1584, "startColumn": 5, "endLineNumber": 1584, "endColumn": 10, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1584, "startColumn": 26, "endLineNumber": 1584, "endColumn": 27, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1603, "startColumn": 6, "endLineNumber": 1603, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1434", "severity": 8, "message": "Unexpected keyword or identifier.", "source": "ts", "startLineNumber": 1605, "startColumn": 5, "endLineNumber": 1605, "endColumn": 10, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1605, "startColumn": 29, "endLineNumber": 1605, "endColumn": 30, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1626, "startColumn": 6, "endLineNumber": 1626, "endColumn": 7, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1005", "severity": 8, "message": "';' expected.", "source": "ts", "startLineNumber": 1629, "startColumn": 20, "endLineNumber": 1629, "endColumn": 21, "extensionID": "vscode.typescript-language-features"}, {"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage/admin/js/landing-pages.js", "owner": "typescript", "code": "1128", "severity": 8, "message": "Declaration or statement expected.", "source": "ts", "startLineNumber": 1641, "startColumn": 1, "endLineNumber": 1641, "endColumn": 2, "extensionID": "vscode.typescript-language-features"}]