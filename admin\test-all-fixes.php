<?php
/**
 * Comprehensive Test for All Critical Fixes
 * Tests HTTP 500 errors, language files, CSS, and API endpoints
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل لجميع الإصلاحات</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .score {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .score.excellent {
            background: #d4edda;
            color: #155724;
        }
        .score.good {
            background: #fff3cd;
            color: #856404;
        }
        .score.poor {
            background: #f8d7da;
            color: #721c24;
        }
        .api-test {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .loading {
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار شامل لجميع الإصلاحات</h1>
        <p>هذا الاختبار يتحقق من حل جميع المشاكل الحرجة في لوحة التحكم.</p>

        <?php
        $totalTests = 0;
        $passedTests = 0;
        $failedTests = 0;
        $warningTests = 0;

        function recordTest($passed, $warning = false) {
            global $totalTests, $passedTests, $failedTests, $warningTests;
            $totalTests++;
            if ($passed) {
                $passedTests++;
            } elseif ($warning) {
                $warningTests++;
            } else {
                $failedTests++;
            }
        }

        try {
            // Test 1: PHP Configuration and Functions
            echo '<div class="test-section">';
            echo '<h3>🔧 اختبار 1: تكوين PHP والدوال</h3>';
            
            try {
                require_once '../php/config.php';
                echo '<div class="result pass">✅ تم تحميل config.php</div>';
                recordTest(true);
                
                if (function_exists('getPDOConnection')) {
                    echo '<div class="result pass">✅ دالة getPDOConnection موجودة</div>';
                    recordTest(true);
                    
                    $pdo = getPDOConnection();
                    echo '<div class="result pass">✅ اتصال قاعدة البيانات ناجح</div>';
                    recordTest(true);
                } else {
                    echo '<div class="result fail">❌ دالة getPDOConnection مفقودة</div>';
                    recordTest(false);
                }
                
                if (function_exists('sanitize')) {
                    echo '<div class="result pass">✅ دالة sanitize موجودة</div>';
                    recordTest(true);
                } else {
                    echo '<div class="result fail">❌ دالة sanitize مفقودة</div>';
                    recordTest(false);
                }
                
                if (session_status() === PHP_SESSION_ACTIVE) {
                    echo '<div class="result pass">✅ الجلسة نشطة</div>';
                    recordTest(true);
                } else {
                    echo '<div class="result warning">⚠️ الجلسة غير نشطة</div>';
                    recordTest(false, true);
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في PHP: ' . $e->getMessage() . '</div>';
                recordTest(false);
            }
            echo '</div>';

            // Test 2: Database Tables
            echo '<div class="test-section">';
            echo '<h3>🗄️ اختبار 2: جداول قاعدة البيانات</h3>';
            
            $requiredTables = ['admins', 'produits', 'categories', 'store_settings', 'ai_settings'];
            
            foreach ($requiredTables as $table) {
                try {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() > 0) {
                        echo '<div class="result pass">✅ جدول ' . $table . ' موجود</div>';
                        recordTest(true);
                    } else {
                        echo '<div class="result fail">❌ جدول ' . $table . ' مفقود</div>';
                        recordTest(false);
                    }
                } catch (Exception $e) {
                    echo '<div class="result fail">❌ خطأ في فحص جدول ' . $table . ': ' . $e->getMessage() . '</div>';
                    recordTest(false);
                }
            }
            echo '</div>';

            // Test 3: Admin User
            echo '<div class="test-section">';
            echo '<h3>👤 اختبار 3: مستخدم الإدارة</h3>';
            
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM admins WHERE actif = 1");
                $adminCount = $stmt->fetch()['count'];
                
                if ($adminCount > 0) {
                    echo '<div class="result pass">✅ يوجد ' . $adminCount . ' مستخدم إدارة نشط</div>';
                    recordTest(true);
                } else {
                    echo '<div class="result fail">❌ لا يوجد مستخدمين إدارة نشطين</div>';
                    recordTest(false);
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في فحص مستخدمي الإدارة: ' . $e->getMessage() . '</div>';
                recordTest(false);
            }
            echo '</div>';

            // Test 4: Arabic Language File
            echo '<div class="test-section">';
            echo '<h3>🌐 اختبار 4: ملف اللغة العربية</h3>';
            
            $langFile = 'js/langs/ar.js';
            if (file_exists($langFile)) {
                echo '<div class="result pass">✅ ملف اللغة العربية موجود</div>';
                recordTest(true);
                
                $content = file_get_contents($langFile);
                if (strpos($content, "tinymce.addI18n('ar'") !== false) {
                    echo '<div class="result pass">✅ ملف اللغة صحيح التنسيق</div>';
                    recordTest(true);
                    
                    $translations = substr_count($content, ':');
                    echo '<div class="result info">📊 عدد الترجمات: ' . $translations . '</div>';
                } else {
                    echo '<div class="result fail">❌ ملف اللغة تنسيق خاطئ</div>';
                    recordTest(false);
                }
            } else {
                echo '<div class="result fail">❌ ملف اللغة العربية غير موجود</div>';
                recordTest(false);
            }
            echo '</div>';

            // Test 5: CSS Files and RTL Support
            echo '<div class="test-section">';
            echo '<h3>🎨 اختبار 5: ملفات CSS ودعم RTL</h3>';
            
            $cssFiles = [
                'css/admin.css' => 'CSS لوحة التحكم',
                'css/critical-fixes.css' => 'CSS الإصلاحات الحرجة'
            ];
            
            foreach ($cssFiles as $file => $description) {
                if (file_exists($file)) {
                    echo '<div class="result pass">✅ ' . $description . ' موجود</div>';
                    recordTest(true);
                    
                    $content = file_get_contents($file);
                    
                    // Check for :root with writing-mode
                    if (preg_match('/:root\s*{[^}]*writing-mode\s*:\s*horizontal-tb[^}]*}/s', $content)) {
                        echo '<div class="result pass">✅ ' . $description . ': writing-mode في :root</div>';
                        recordTest(true);
                    } else {
                        echo '<div class="result warning">⚠️ ' . $description . ': writing-mode قد يحتاج تحديث</div>';
                        recordTest(false, true);
                    }
                    
                    // Check for RTL direction
                    if (preg_match('/:root\s*{[^}]*direction\s*:\s*rtl[^}]*}/s', $content)) {
                        echo '<div class="result pass">✅ ' . $description . ': direction RTL في :root</div>';
                        recordTest(true);
                    } else {
                        echo '<div class="result warning">⚠️ ' . $description . ': direction RTL قد يحتاج تحديث</div>';
                        recordTest(false, true);
                    }
                    
                } else {
                    echo '<div class="result fail">❌ ملف CSS غير موجود: ' . $file . '</div>';
                    recordTest(false);
                }
            }
            echo '</div>';

            // Test 6: Admin.php File
            echo '<div class="test-section">';
            echo '<h3>🔐 اختبار 6: ملف admin.php</h3>';
            
            $adminFile = '../php/admin.php';
            if (file_exists($adminFile)) {
                echo '<div class="result pass">✅ ملف admin.php موجود</div>';
                recordTest(true);
                
                // Test if we can include it without fatal errors
                try {
                    ob_start();
                    $_SERVER['REQUEST_METHOD'] = 'GET';
                    $_GET['action'] = 'test';
                    include $adminFile;
                    $output = ob_get_clean();
                    
                    echo '<div class="result pass">✅ admin.php يتم تحميله بدون أخطاء فادحة</div>';
                    recordTest(true);
                    
                } catch (Exception $e) {
                    echo '<div class="result fail">❌ خطأ في admin.php: ' . $e->getMessage() . '</div>';
                    recordTest(false);
                }
                
            } else {
                echo '<div class="result fail">❌ ملف admin.php غير موجود</div>';
                recordTest(false);
            }
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">❌ خطأ عام في الاختبار: ' . $e->getMessage() . '</div>';
            recordTest(false);
        }

        // Calculate score
        $successRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;
        $scoreClass = 'poor';
        $scoreText = 'ضعيف';
        $scoreIcon = '❌';

        if ($successRate >= 90) {
            $scoreClass = 'excellent';
            $scoreText = 'ممتاز';
            $scoreIcon = '🎉';
        } elseif ($successRate >= 70) {
            $scoreClass = 'good';
            $scoreText = 'جيد';
            $scoreIcon = '✅';
        }

        echo '<div class="score ' . $scoreClass . '">';
        echo $scoreIcon . ' النتيجة النهائية: ' . round($successRate, 1) . '% (' . $scoreText . ')';
        echo '<br>نجح: ' . $passedTests . ' | فشل: ' . $failedTests . ' | تحذيرات: ' . $warningTests . ' | المجموع: ' . $totalTests;
        echo '</div>';
        ?>

        <!-- JavaScript API Tests -->
        <div class="test-section">
            <h3>🔌 اختبار 7: API Endpoints عبر JavaScript</h3>
            <div id="apiTestResults">
                <div class="loading">جاري تحميل اختبارات API...</div>
            </div>
        </div>

        <!-- Summary and Next Steps -->
        <div class="test-section">
            <h3>📋 الملخص والخطوات التالية</h3>
            
            <?php if ($successRate >= 90): ?>
                <div class="result pass">🎉 ممتاز! جميع الإصلاحات تعمل بشكل صحيح</div>
                <div class="result pass">✅ لوحة التحكم جاهزة للاستخدام الكامل</div>
            <?php elseif ($successRate >= 70): ?>
                <div class="result warning">⚠️ جيد! معظم الإصلاحات تعمل مع بعض التحذيرات</div>
                <div class="result info">💡 راجع التحذيرات أعلاه لتحسين الأداء</div>
            <?php else: ?>
                <div class="result fail">❌ يحتاج إصلاحات إضافية</div>
                <div class="result info">💡 راجع الأخطاء أعلاه وقم بتشغيل سكريبتات الإصلاح</div>
            <?php endif; ?>
            
            <h4>🛠️ أدوات الإصلاح:</h4>
            <a href="setup-admin-user.php" class="test-button">إعداد مستخدم الإدارة</a>
            <a href="fix-critical-admin-errors.php" class="test-button">إصلاح الأخطاء الحرجة</a>
            <a href="create-database.php" class="test-button">إنشاء قاعدة البيانات</a>
            
            <h4>🚀 اختبار الوظائف:</h4>
            <a href="index.html" class="test-button">فتح لوحة التحكم</a>
            <a href="landing-pages-management.html" class="test-button">إدارة صفحات الهبوط</a>
            <a href="ai-settings.html" class="test-button">إعدادات الذكاء الاصطناعي</a>
        </div>

    </div>

    <script>
        // API Tests via JavaScript
        async function runAPITests() {
            const resultsDiv = document.getElementById('apiTestResults');
            let results = '';
            let apiTestsPassed = 0;
            let apiTestsTotal = 0;

            // Test Admin API
            try {
                apiTestsTotal++;
                const response = await fetch('../php/admin.php?action=test', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.status < 500) {
                    results += '<div class="result pass">✅ Admin API متاح - Status: ' + response.status + '</div>';
                    apiTestsPassed++;
                } else {
                    results += '<div class="result fail">❌ Admin API خطأ 500 - Status: ' + response.status + '</div>';
                }
            } catch (error) {
                results += '<div class="result fail">❌ Admin API غير متاح: ' + error.message + '</div>';
            }

            // Test Arabic Language File
            try {
                apiTestsTotal++;
                const response = await fetch('js/langs/ar.js');
                
                if (response.ok) {
                    results += '<div class="result pass">✅ ملف اللغة العربية يتم تحميله عبر HTTP</div>';
                    apiTestsPassed++;
                } else {
                    results += '<div class="result fail">❌ ملف اللغة العربية لا يتم تحميله - Status: ' + response.status + '</div>';
                }
            } catch (error) {
                results += '<div class="result fail">❌ خطأ في تحميل ملف اللغة: ' + error.message + '</div>';
            }

            // Test CSS Files
            const cssFiles = ['css/admin.css', 'css/critical-fixes.css'];
            for (const cssFile of cssFiles) {
                try {
                    apiTestsTotal++;
                    const response = await fetch(cssFile);
                    
                    if (response.ok) {
                        results += '<div class="result pass">✅ ' + cssFile + ' يتم تحميله عبر HTTP</div>';
                        apiTestsPassed++;
                    } else {
                        results += '<div class="result warning">⚠️ ' + cssFile + ' لا يتم تحميله - Status: ' + response.status + '</div>';
                    }
                } catch (error) {
                    results += '<div class="result fail">❌ خطأ في تحميل ' + cssFile + ': ' + error.message + '</div>';
                }
            }

            // Add API test summary
            const apiSuccessRate = apiTestsTotal > 0 ? (apiTestsPassed / apiTestsTotal) * 100 : 0;
            results += '<div class="api-test">';
            results += '<strong>نتائج اختبار API:</strong> ' + apiTestsPassed + '/' + apiTestsTotal + ' (' + Math.round(apiSuccessRate) + '%)';
            results += '</div>';

            resultsDiv.innerHTML = results;
        }

        // Run API tests after page loads
        document.addEventListener('DOMContentLoaded', runAPITests);
    </script>
</body>
</html>
