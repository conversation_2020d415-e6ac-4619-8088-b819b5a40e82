const fs = require('fs');

console.log('Checking JavaScript syntax...');

const jsFiles = [
    'admin/js/admin.js',
    'js/main.js',
    'js/utils.js',
    'js/auth.js'
];

jsFiles.forEach(file => {
    try {
        if (fs.existsSync(file)) {
            const content = fs.readFileSync(file, 'utf8');
            
            // Try to parse the content
            try {
                new Function(content);
                console.log(`✅ ${file}: Syntax OK`);
            } catch (syntaxError) {
                console.log(`❌ ${file}: Syntax Error - ${syntaxError.message}`);
                
                // Try to find the line number
                const lines = content.split('\n');
                const errorLine = syntaxError.message.match(/line (\d+)/);
                if (errorLine) {
                    const lineNum = parseInt(errorLine[1]);
                    console.log(`   Line ${lineNum}: ${lines[lineNum - 1]}`);
                }
            }
        } else {
            console.log(`⚠️  ${file}: File not found`);
        }
    } catch (error) {
        console.log(`❌ ${file}: Error reading file - ${error.message}`);
    }
});

console.log('Syntax check completed.');
