<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guide de Test des Sections Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        .guide-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-card {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .section-card.dashboard { border-left: 4px solid #007bff; }
        .section-card.products { border-left: 4px solid #28a745; }
        .section-card.orders { border-left: 4px solid #ffc107; }
        .section-card.landing-pages { border-left: 4px solid #17a2b8; }
        .section-card.reports { border-left: 4px solid #6f42c1; }
        .section-card.settings { border-left: 4px solid #fd7e14; }
        .section-card.logout { border-left: 4px solid #dc3545; }
        
        .test-checklist {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .test-checklist input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        .test-checklist label {
            margin-bottom: 8px;
            display: block;
            cursor: pointer;
        }
        .expected-behavior {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .troubleshooting {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .admin-preview {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="guide-container">
        <h1><i class="fas fa-clipboard-check"></i> Guide de Test des Sections Admin</h1>
        <p class="lead">Guide complet pour vérifier que toutes les sections de l'admin sont fonctionnelles et cliquables</p>
        
        <div class="alert alert-info">
            <h4><i class="fas fa-info-circle"></i> Instructions Générales</h4>
            <ol>
                <li>Ouvrez la page admin dans un nouvel onglet : <a href="/admin/" target="_blank" class="btn btn-primary btn-sm">Ouvrir Admin</a></li>
                <li>Ouvrez les outils de développement (F12) pour surveiller les erreurs</li>
                <li>Testez chaque section en suivant les instructions ci-dessous</li>
                <li>Cochez les cases au fur et à mesure de vos tests</li>
            </ol>
        </div>
        
        <!-- Section 1: Dashboard -->
        <div class="section-card dashboard">
            <h2><i class="fas fa-home"></i> 1. الرئيسية (Dashboard)</h2>
            
            <div class="test-checklist">
                <h5>Tests à effectuer :</h5>
                <label><input type="checkbox"> Cliquer sur "الرئيسية" dans la barre latérale</label>
                <label><input type="checkbox"> Vérifier que le contenu du dashboard s'affiche</label>
                <label><input type="checkbox"> Vérifier qu'il n'y a pas d'erreurs JavaScript</label>
                <label><input type="checkbox"> Vérifier que les statistiques/widgets se chargent</label>
            </div>
            
            <div class="expected-behavior">
                <h6><i class="fas fa-check-circle"></i> Comportement attendu :</h6>
                <ul>
                    <li>Le contenu principal change pour afficher le tableau de bord</li>
                    <li>Les statistiques et graphiques se chargent</li>
                    <li>L'élément de menu "الرئيسية" devient actif/surligné</li>
                </ul>
            </div>
            
            <div class="troubleshooting">
                <h6><i class="fas fa-exclamation-triangle"></i> En cas de problème :</h6>
                <ul>
                    <li>Vérifier que l'élément a un attribut onclick ou data-section</li>
                    <li>Vérifier que #dashboardContent existe dans le DOM</li>
                    <li>Vérifier les erreurs dans la console</li>
                </ul>
            </div>
        </div>
        
        <!-- Section 2: Products -->
        <div class="section-card products">
            <h2><i class="fas fa-box"></i> 2. إدارة المنتجات (Gestion des Produits)</h2>
            
            <div class="test-checklist">
                <h5>Tests à effectuer :</h5>
                <label><input type="checkbox"> Cliquer sur "إدارة المنتجات" dans la barre latérale</label>
                <label><input type="checkbox"> Vérifier que la liste des produits s'affiche</label>
                <label><input type="checkbox"> Tester le bouton "إضافة منتج جديد" (Ajouter produit)</label>
                <label><input type="checkbox"> Vérifier que les actions (modifier, supprimer) fonctionnent</label>
            </div>
            
            <div class="expected-behavior">
                <h6><i class="fas fa-check-circle"></i> Comportement attendu :</h6>
                <ul>
                    <li>La liste des produits se charge avec un tableau</li>
                    <li>Le bouton "إضافة منتج جديد" est visible et cliquable</li>
                    <li>Les boutons d'action (modifier, supprimer) sont présents</li>
                </ul>
            </div>
            
            <div class="troubleshooting">
                <h6><i class="fas fa-exclamation-triangle"></i> En cas de problème :</h6>
                <ul>
                    <li>Vérifier que l'API products.php fonctionne</li>
                    <li>Vérifier que #productsContent est visible</li>
                    <li>Vérifier les appels AJAX dans l'onglet Network</li>
                </ul>
            </div>
        </div>
        
        <!-- Section 3: Orders -->
        <div class="section-card orders">
            <h2><i class="fas fa-shopping-cart"></i> 3. الطلبات (Commandes)</h2>
            
            <div class="test-checklist">
                <h5>Tests à effectuer :</h5>
                <label><input type="checkbox"> Cliquer sur "الطلبات" dans la barre latérale</label>
                <label><input type="checkbox"> Vérifier que la liste des commandes s'affiche</label>
                <label><input type="checkbox"> Vérifier les filtres de statut des commandes</label>
                <label><input type="checkbox"> Tester la visualisation des détails d'une commande</label>
            </div>
            
            <div class="expected-behavior">
                <h6><i class="fas fa-check-circle"></i> Comportement attendu :</h6>
                <ul>
                    <li>La liste des commandes se charge</li>
                    <li>Les statuts des commandes sont affichés</li>
                    <li>Les actions de gestion des commandes sont disponibles</li>
                </ul>
            </div>
        </div>
        
        <!-- Section 4: Landing Pages -->
        <div class="section-card landing-pages">
            <h2><i class="fas fa-bullhorn"></i> 4. صفحات هبوط (Pages de Destination)</h2>
            
            <div class="test-checklist">
                <h5>Tests à effectuer :</h5>
                <label><input type="checkbox"> Cliquer sur "صفحات هبوط" dans la barre latérale</label>
                <label><input type="checkbox"> Vérifier que la liste des pages de destination s'affiche</label>
                <label><input type="checkbox"> Tester le bouton "أَضف صفحة هبوط" (Ajouter page)</label>
                <label><input type="checkbox"> Vérifier que le modal d'ajout s'ouvre correctement</label>
                <label><input type="checkbox"> Tester les actions (modifier, supprimer, prévisualiser)</label>
            </div>
            
            <div class="expected-behavior">
                <h6><i class="fas fa-check-circle"></i> Comportement attendu :</h6>
                <ul>
                    <li>La liste des pages de destination se charge</li>
                    <li>Le bouton "أَضف صفحة هبوط" est visible et fonctionnel</li>
                    <li>Le modal d'ajout/modification s'ouvre sans erreurs</li>
                    <li>Les templates et produits se chargent dans le modal</li>
                </ul>
            </div>
        </div>
        
        <!-- Section 5: Reports -->
        <div class="section-card reports">
            <h2><i class="fas fa-chart-bar"></i> 5. التقارير والإحصائيات (Rapports)</h2>
            
            <div class="test-checklist">
                <h5>Tests à effectuer :</h5>
                <label><input type="checkbox"> Cliquer sur "التقارير والإحصائيات" dans la barre latérale</label>
                <label><input type="checkbox"> Vérifier que les graphiques se chargent</label>
                <label><input type="checkbox"> Tester les filtres de date</label>
                <label><input type="checkbox"> Vérifier l'export des données</label>
            </div>
            
            <div class="expected-behavior">
                <h6><i class="fas fa-check-circle"></i> Comportement attendu :</h6>
                <ul>
                    <li>Les graphiques et statistiques se chargent</li>
                    <li>Les filtres de période fonctionnent</li>
                    <li>Les données sont mises à jour dynamiquement</li>
                </ul>
            </div>
        </div>
        
        <!-- Section 6: Settings -->
        <div class="section-card settings">
            <h2><i class="fas fa-cog"></i> 6. إعدادات النظام (Paramètres Système)</h2>
            
            <div class="test-checklist">
                <h5>Tests à effectuer :</h5>
                <label><input type="checkbox"> Cliquer sur "إعدادات النظام" dans la barre latérale</label>
                <label><input type="checkbox"> Vérifier que les onglets de paramètres s'affichent</label>
                <label><input type="checkbox"> Tester la sauvegarde des paramètres</label>
                <label><input type="checkbox"> Vérifier les paramètres de sécurité</label>
            </div>
            
            <div class="expected-behavior">
                <h6><i class="fas fa-check-circle"></i> Comportement attendu :</h6>
                <ul>
                    <li>Les différents onglets de paramètres sont accessibles</li>
                    <li>Les formulaires de configuration se chargent</li>
                    <li>La sauvegarde des paramètres fonctionne</li>
                </ul>
            </div>
        </div>
        
        <!-- Section 7: Logout -->
        <div class="section-card logout">
            <h2><i class="fas fa-sign-out-alt"></i> 7. تسجيل الخروج (Déconnexion)</h2>
            
            <div class="test-checklist">
                <h5>Tests à effectuer :</h5>
                <label><input type="checkbox"> Cliquer sur "تسجيل الخروج" dans la barre latérale</label>
                <label><input type="checkbox"> Vérifier que la confirmation de déconnexion apparaît</label>
                <label><input type="checkbox"> Confirmer la déconnexion</label>
                <label><input type="checkbox"> Vérifier la redirection vers la page de connexion</label>
            </div>
            
            <div class="expected-behavior">
                <h6><i class="fas fa-check-circle"></i> Comportement attendu :</h6>
                <ul>
                    <li>Une confirmation de déconnexion peut apparaître</li>
                    <li>L'utilisateur est déconnecté et redirigé</li>
                    <li>La session est correctement terminée</li>
                </ul>
            </div>
        </div>
        
        <!-- Test automatique -->
        <div class="section-card" style="border-left: 4px solid #6c757d;">
            <h2><i class="fas fa-robot"></i> Test Automatique</h2>
            <p>Vous pouvez également exécuter un test automatique en copiant et collant le script suivant dans la console de votre navigateur sur la page admin :</p>
            
            <div class="alert alert-secondary">
                <h6>Instructions :</h6>
                <ol>
                    <li>Ouvrez la page admin : <a href="/admin/" target="_blank">http://localhost:8000/admin/</a></li>
                    <li>Ouvrez la console (F12 → Console)</li>
                    <li>Copiez et collez le contenu du fichier <code>test-clickable-sections.js</code></li>
                    <li>Appuyez sur Entrée pour exécuter le script</li>
                    <li>Observez les résultats dans la console</li>
                </ol>
            </div>
            
            <button class="btn btn-info" onclick="window.open('/test-clickable-sections.js', '_blank')">
                <i class="fas fa-download"></i> Voir le Script de Test
            </button>
        </div>
        
        <!-- Résumé -->
        <div class="alert alert-success">
            <h4><i class="fas fa-trophy"></i> Résumé des Tests</h4>
            <p>Une fois tous les tests effectués, vous devriez avoir :</p>
            <ul>
                <li>✅ Toutes les sections cliquables et fonctionnelles</li>
                <li>✅ Aucune erreur JavaScript dans la console</li>
                <li>✅ Tous les contenus qui se chargent correctement</li>
                <li>✅ Toutes les fonctionnalités principales accessibles</li>
            </ul>
            
            <div class="mt-3">
                <button class="btn btn-primary" onclick="window.open('/admin/', '_blank')">
                    <i class="fas fa-external-link-alt"></i> Commencer les Tests
                </button>
                <button class="btn btn-secondary" onclick="window.open('/test-admin-sections-functionality.html', '_blank')">
                    <i class="fas fa-test-tube"></i> Test Interactif
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Fonction pour compter les cases cochées
        function updateProgress() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            const checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked');
            const progress = (checkedBoxes.length / checkboxes.length) * 100;
            
            console.log(`Progression des tests: ${progress.toFixed(1)}% (${checkedBoxes.length}/${checkboxes.length})`);
        }
        
        // Ajouter des écouteurs d'événements aux cases à cocher
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateProgress);
            });
        });
    </script>
</body>
</html>
