/**
 * General Settings Management
 * Handles all general system settings functionality
 */

// Default settings object
let generalSettings = {
    store: {
        name: '',
        slogan: '',
        email: '',
        phone: '',
        address: ''
    },
    business: {
        currency: 'DZD',
        taxRate: 19.00,
        minOrderAmount: 500,
        freeShippingThreshold: 5000
    },
    system: {
        timezone: 'Africa/Algiers',
        language: 'ar',
        maintenanceMode: false,
        enableRegistration: true
    },
    seo: {
        title: '',
        description: '',
        keywords: ''
    }
};

/**
 * Initialize general settings page
 */
function initializeGeneralSettings() {
    console.log('Initializing general settings...');
    
    // Load settings from localStorage or API
    loadSettings();
    
    // Apply settings to UI
    applySettingsToUI();
    
    // Update last updated timestamp
    updateLastUpdated();
    
    // Add event listeners
    addEventListeners();
    
    console.log('General settings initialized successfully');
}

/**
 * Load settings from localStorage or API
 */
async function loadSettings() {
    try {
        // Try to load from API first
        const apiLoaded = await loadFromAPI();
        
        if (!apiLoaded) {
            // Fallback to localStorage
            const savedSettings = localStorage.getItem('generalSettings');
            if (savedSettings) {
                const parsed = JSON.parse(savedSettings);
                generalSettings = { ...generalSettings, ...parsed };
                console.log('Settings loaded from localStorage');
            }
        }
    } catch (error) {
        console.warn('Error loading settings:', error);
        showNotification('تم تحميل الإعدادات الافتراضية', 'warning');
    }
}

/**
 * Load settings from API
 */
async function loadFromAPI() {
    try {
        const response = await fetch('../php/api/general-settings.php');
        
        if (!response.ok) {
            console.warn('API not available, using local settings');
            return false;
        }
        
        const data = await response.json();
        
        if (data.success && data.settings) {
            generalSettings = { ...generalSettings, ...data.settings };
            console.log('Settings loaded from API successfully');
            return true;
        }
        
        return false;
    } catch (error) {
        console.warn('API not available:', error.message);
        return false;
    }
}

/**
 * Apply settings to UI elements
 */
function applySettingsToUI() {
    // Store information
    document.getElementById('storeName').value = generalSettings.store.name || '';
    document.getElementById('storeSlogan').value = generalSettings.store.slogan || '';
    document.getElementById('storeEmail').value = generalSettings.store.email || '';
    document.getElementById('storePhone').value = generalSettings.store.phone || '';
    document.getElementById('storeAddress').value = generalSettings.store.address || '';
    
    // Business settings
    document.getElementById('currency').value = generalSettings.business.currency || 'DZD';
    document.getElementById('taxRate').value = generalSettings.business.taxRate || 19.00;
    document.getElementById('minOrderAmount').value = generalSettings.business.minOrderAmount || 500;
    document.getElementById('freeShippingThreshold').value = generalSettings.business.freeShippingThreshold || 5000;
    
    // System settings
    document.getElementById('timezone').value = generalSettings.system.timezone || 'Africa/Algiers';
    document.getElementById('language').value = generalSettings.system.language || 'ar';
    document.getElementById('maintenanceMode').checked = generalSettings.system.maintenanceMode || false;
    document.getElementById('enableRegistration').checked = generalSettings.system.enableRegistration !== false;
    
    // SEO settings
    document.getElementById('siteTitle').value = generalSettings.seo.title || '';
    document.getElementById('siteDescription').value = generalSettings.seo.description || '';
    document.getElementById('siteKeywords').value = generalSettings.seo.keywords || '';
}

/**
 * Collect settings from UI
 */
function collectSettingsFromUI() {
    return {
        store: {
            name: document.getElementById('storeName').value,
            slogan: document.getElementById('storeSlogan').value,
            email: document.getElementById('storeEmail').value,
            phone: document.getElementById('storePhone').value,
            address: document.getElementById('storeAddress').value
        },
        business: {
            currency: document.getElementById('currency').value,
            taxRate: parseFloat(document.getElementById('taxRate').value) || 0,
            minOrderAmount: parseInt(document.getElementById('minOrderAmount').value) || 0,
            freeShippingThreshold: parseInt(document.getElementById('freeShippingThreshold').value) || 0
        },
        system: {
            timezone: document.getElementById('timezone').value,
            language: document.getElementById('language').value,
            maintenanceMode: document.getElementById('maintenanceMode').checked,
            enableRegistration: document.getElementById('enableRegistration').checked
        },
        seo: {
            title: document.getElementById('siteTitle').value,
            description: document.getElementById('siteDescription').value,
            keywords: document.getElementById('siteKeywords').value
        }
    };
}

/**
 * Save all settings
 */
async function saveAllSettings() {
    try {
        // Show loading state
        const saveBtn = document.querySelector('.enhanced-save-btn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        saveBtn.disabled = true;
        
        // Collect settings from UI
        const newSettings = collectSettingsFromUI();
        
        // Validate required fields
        if (!newSettings.store.name || !newSettings.store.email) {
            throw new Error('اسم المتجر والبريد الإلكتروني مطلوبان');
        }
        
        // Update local settings
        generalSettings = { ...generalSettings, ...newSettings };
        
        // Save to localStorage
        localStorage.setItem('generalSettings', JSON.stringify(generalSettings));
        
        // Try to save to API
        await saveToAPI(generalSettings);
        
        // Update last updated timestamp
        updateLastUpdated();
        
        // Show success message
        showNotification('تم حفظ الإعدادات بنجاح', 'success');
        
        // Restore button
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
        
    } catch (error) {
        console.error('Error saving settings:', error);
        showNotification('خطأ في حفظ الإعدادات: ' + error.message, 'error');
        
        // Restore button
        const saveBtn = document.querySelector('.enhanced-save-btn');
        saveBtn.innerHTML = '<i class="fas fa-save"></i><span class="btn-text">حفظ جميع الإعدادات</span>';
        saveBtn.disabled = false;
    }
}

/**
 * Save settings to API
 */
async function saveToAPI(settings) {
    try {
        const response = await fetch('../php/api/general-settings.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                console.log('Settings saved to API successfully');
            }
        }
    } catch (error) {
        console.warn('Could not save to API:', error.message);
    }
}

/**
 * Reset to default settings
 */
function resetToDefaults() {
    if (confirm('هل أنت متأكد من استعادة الإعدادات الافتراضية؟ سيتم فقدان جميع التغييرات الحالية.')) {
        // Reset to defaults
        generalSettings = {
            store: { name: '', slogan: '', email: '', phone: '', address: '' },
            business: { currency: 'DZD', taxRate: 19.00, minOrderAmount: 500, freeShippingThreshold: 5000 },
            system: { timezone: 'Africa/Algiers', language: 'ar', maintenanceMode: false, enableRegistration: true },
            seo: { title: '', description: '', keywords: '' }
        };
        
        // Apply to UI
        applySettingsToUI();
        
        // Clear localStorage
        localStorage.removeItem('generalSettings');
        
        showNotification('تم استعادة الإعدادات الافتراضية', 'info');
    }
}

/**
 * Export settings as JSON
 */
function exportSettings() {
    const settings = collectSettingsFromUI();
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `general-settings-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    showNotification('تم تصدير الإعدادات بنجاح', 'success');
}

/**
 * Update last updated timestamp
 */
function updateLastUpdated() {
    const now = new Date();
    const formatted = now.toLocaleString('ar-DZ', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const element = document.getElementById('lastUpdated');
    if (element) {
        element.textContent = formatted;
    }
}

/**
 * Add event listeners
 */
function addEventListeners() {
    // Auto-save on input changes (debounced)
    const inputs = document.querySelectorAll('.enhanced-input, .enhanced-select, .enhanced-textarea');
    inputs.forEach(input => {
        input.addEventListener('input', debounce(() => {
            const newSettings = collectSettingsFromUI();
            localStorage.setItem('generalSettings', JSON.stringify(newSettings));
        }, 1000));
    });
    
    // Maintenance mode warning
    document.getElementById('maintenanceMode').addEventListener('change', function() {
        if (this.checked) {
            showNotification('تحذير: تفعيل وضع الصيانة سيجعل الموقع غير متاح للزوار', 'warning');
        }
    });
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeGeneralSettings();
});
