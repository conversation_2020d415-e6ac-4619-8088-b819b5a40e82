<?php

/**
 * Multi-User Products API
 * Products API with user isolation and admin oversight
 */

header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID, X-User-Role");

// Handle preflight requests
if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit();
}

try {
    require_once __DIR__ . '/../config.php';
    $pdo = getPDOConnection();

    if (!$pdo) {
        throw new Exception("Database connection failed");
    }

    // Get user information from headers or session
    $currentUserId = $_SERVER['HTTP_X_USER_ID'] ?? $_GET['user_id'] ?? 1; // Default to demo user for testing
    $currentUserRole = $_SERVER['HTTP_X_USER_ROLE'] ?? $_GET['user_role'] ?? 'seller'; // Default to seller

    // Determine if user is admin
    $isAdmin = ($currentUserRole === 'admin' || $currentUserId == 1);

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            handleGetProducts($pdo, $currentUserId, $isAdmin);
            break;
        case 'POST':
            handleCreateProduct($pdo, $currentUserId);
            break;
        default:
            throw new Exception("Method not allowed");
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'error' => true
    ], JSON_UNESCAPED_UNICODE);
}

function handleGetProducts($pdo, $currentUserId, $isAdmin)
{
    try {
        if ($isAdmin) {
            // Admin sees ALL products with user ownership information
            $sql = "
                SELECT
                    p.*,
                    u.username as owner_username,
                    u.email as owner_email,
                    u.first_name as owner_first_name,
                    u.last_name as owner_last_name,
                    ur.name as owner_role
                FROM produits p
                LEFT JOIN users u ON p.user_id = u.id
                LEFT JOIN user_roles ur ON u.role_id = ur.id
                WHERE p.actif = 1
                ORDER BY p.created_at DESC
            ";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
        } else {
            // Regular users see only their own products
            $sql = "
                SELECT p.*
                FROM produits p
                WHERE p.actif = 1 AND p.user_id = ?
                ORDER BY p.created_at DESC
            ";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$currentUserId]);
        }

        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Add additional metadata for each product
        foreach ($products as &$product) {
            // Check if product has landing page
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM landing_pages WHERE produit_id = ?");
            $stmt->execute([$product['id']]);
            $product['has_landing_page'] = $stmt->fetch()['count'] > 0;

            // Get product images count
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM product_images WHERE product_id = ?");
            $stmt->execute([$product['id']]);
            $product['images_count'] = $stmt->fetch()['count'];

            // Format price
            $product['formatted_price'] = number_format($product['prix'], 2) . ' دج';

            // Add ownership info for admin view
            if ($isAdmin && isset($product['owner_username'])) {
                $product['ownership_info'] = [
                    'user_id' => $product['user_id'],
                    'username' => $product['owner_username'],
                    'email' => $product['owner_email'],
                    'full_name' => trim($product['owner_first_name'] . ' ' . $product['owner_last_name']),
                    'role' => $product['owner_role'],
                    'store_id' => $product['store_id']
                ];
            }
        }

        // Get summary statistics
        if ($isAdmin) {
            // Admin statistics - all users
            $stmt = $pdo->prepare("
                SELECT
                    COUNT(*) as total_products,
                    COUNT(DISTINCT user_id) as total_sellers,
                    SUM(CASE WHEN actif = 1 THEN 1 ELSE 0 END) as active_products,
                    SUM(CASE WHEN has_landing_page = 1 THEN 1 ELSE 0 END) as products_with_landing_pages
                FROM produits
                WHERE user_id IS NOT NULL
            ");
            $stmt->execute();
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);

            // Get top sellers
            $stmt = $pdo->prepare("
                SELECT
                    u.username,
                    u.first_name,
                    u.last_name,
                    COUNT(p.id) as product_count
                FROM users u
                LEFT JOIN produits p ON u.id = p.user_id AND p.actif = 1
                GROUP BY u.id
                ORDER BY product_count DESC
                LIMIT 5
            ");
            $stmt->execute();
            $topSellers = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $stats['top_sellers'] = $topSellers;
        } else {
            // User-specific statistics
            $stmt = $pdo->prepare("
                SELECT
                    COUNT(*) as total_products,
                    SUM(CASE WHEN actif = 1 THEN 1 ELSE 0 END) as active_products,
                    SUM(CASE WHEN has_landing_page = 1 THEN 1 ELSE 0 END) as products_with_landing_pages,
                    AVG(prix) as average_price
                FROM produits
                WHERE user_id = ?
            ");
            $stmt->execute([$currentUserId]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        }

        echo json_encode([
            'success' => true,
            'data' => $products,
            'stats' => $stats,
            'user_info' => [
                'user_id' => $currentUserId,
                'is_admin' => $isAdmin,
                'role' => $isAdmin ? 'admin' : 'seller'
            ],
            'message' => $isAdmin ?
                'Admin view: All products loaded with ownership info' :
                'User products loaded successfully',
            'count' => count($products)
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        throw new Exception("Error loading products: " . $e->getMessage());
    }
}

function handleCreateProduct($pdo, $currentUserId)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            throw new Exception("Invalid JSON input");
        }

        // Required fields
        $requiredFields = ['titre', 'description', 'prix', 'type'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }

        // Get user's store_id
        $stmt = $pdo->prepare("SELECT store_id FROM users WHERE id = ?");
        $stmt->execute([$currentUserId]);
        $user = $stmt->fetch();

        if (!$user || !$user['store_id']) {
            throw new Exception("User does not have an associated store");
        }

        // Insert new product
        $sql = "
            INSERT INTO produits (
                user_id, store_id, titre, description, prix, type,
                stock, image_url, actif, auteur, materiel,
                created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?,
                ?, ?, 1, ?, ?,
                NOW(), NOW()
            )
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $currentUserId,
            $user['store_id'],
            $input['titre'],
            $input['description'],
            $input['prix'],
            $input['type'],
            $input['stock'] ?? 0,
            $input['image_url'] ?? null,
            $input['auteur'] ?? null,
            $input['materiel'] ?? null
        ]);

        $productId = $pdo->lastInsertId();

        // Get the created product
        $stmt = $pdo->prepare("SELECT * FROM produits WHERE id = ?");
        $stmt->execute([$productId]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $product,
            'message' => 'Product created successfully',
            'product_id' => $productId
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        throw new Exception("Error creating product: " . $e->getMessage());
    }
}
