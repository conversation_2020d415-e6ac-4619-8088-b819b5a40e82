<?php
/**
 * Test and Fix Demo User Login System
 * Verifies and fixes authentication issues for the demo user
 */

require_once 'php/config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار وإصلاح نظام دخول المستخدم التجريبي</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2 { color: #333; }
        .credentials { background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; }
        .test-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .json-output { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto; }
        .login-form { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔐 اختبار وإصلاح نظام دخول المستخدم التجريبي</h1>";

try {
    $pdo = getPDOConnection();
    
    echo "<div class='section'>";
    echo "<h2>1️⃣ فحص المستخدم التجريبي</h2>";
    
    // Check demo user
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $demoUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$demoUser) {
        echo "<div class='error'>❌ المستخدم التجريبي غير موجود</div>";
        
        // Create demo user
        echo "<div class='info'>🔧 إنشاء المستخدم التجريبي...</div>";
        
        $hashedPassword = password_hash('demo123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, first_name, last_name, phone, role_id, subscription_id, status, email_verified, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            'demo_user',
            '<EMAIL>',
            $hashedPassword,
            'مصعب',
            'التجريبي',
            '+213 555 123 456',
            2, // Store owner role
            2, // Premium subscription
            'active',
            1
        ]);
        
        $demoUserId = $pdo->lastInsertId();
        echo "<div class='success'>✅ تم إنشاء المستخدم التجريبي بنجاح (ID: {$demoUserId})</div>";
        
        // Get the newly created user
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$demoUserId]);
        $demoUser = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        echo "<div class='success'>✅ تم العثور على المستخدم التجريبي</div>";
    }
    
    echo "<div class='credentials'>";
    echo "<h4>👤 معلومات المستخدم التجريبي:</h4>";
    echo "<ul>";
    echo "<li>ID: {$demoUser['id']}</li>";
    echo "<li>اسم المستخدم: {$demoUser['username']}</li>";
    echo "<li>البريد الإلكتروني: {$demoUser['email']}</li>";
    echo "<li>الاسم: {$demoUser['first_name']} {$demoUser['last_name']}</li>";
    echo "<li>الهاتف: {$demoUser['phone']}</li>";
    echo "<li>الدور: {$demoUser['role_id']}</li>";
    echo "<li>الاشتراك: {$demoUser['subscription_id']}</li>";
    echo "<li>Store ID: " . ($demoUser['store_id'] ?? 'غير محدد') . "</li>";
    echo "<li>الحالة: {$demoUser['status']}</li>";
    echo "<li>البريد مؤكد: " . ($demoUser['email_verified'] ? 'نعم' : 'لا') . "</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2️⃣ اختبار كلمة المرور</h2>";
    
    $testPassword = 'demo123';
    $passwordValid = password_verify($testPassword, $demoUser['password']);
    
    if ($passwordValid) {
        echo "<div class='success'>✅ كلمة المرور صحيحة</div>";
    } else {
        echo "<div class='error'>❌ كلمة المرور غير صحيحة - سيتم إعادة تعيينها</div>";
        
        // Reset password
        $newHashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt->execute([$newHashedPassword, $demoUser['id']]);
        
        echo "<div class='success'>✅ تم إعادة تعيين كلمة المرور بنجاح</div>";
    }
    
    echo "<div class='credentials'>";
    echo "<h4>🔑 بيانات الدخول:</h4>";
    echo "<ul>";
    echo "<li><strong>البريد الإلكتروني:</strong> <EMAIL></li>";
    echo "<li><strong>كلمة المرور:</strong> demo123</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3️⃣ اختبار API المصادقة</h2>";
    
    // Test authentication API
    $loginData = [
        'email' => '<EMAIL>',
        'password' => 'demo123'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/user-auth.php?action=login');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<div class='info'>🌐 رمز الاستجابة HTTP: {$httpCode}</div>";
    
    if ($error) {
        echo "<div class='error'>❌ خطأ في cURL: {$error}</div>";
    } elseif ($httpCode === 200) {
        $authResponse = json_decode($response, true);
        
        if ($authResponse && isset($authResponse['success']) && $authResponse['success']) {
            echo "<div class='success'>✅ تم تسجيل الدخول بنجاح عبر API</div>";
            
            if (isset($authResponse['user'])) {
                echo "<div class='test-result'>";
                echo "<h4>👤 بيانات المستخدم من API:</h4>";
                echo "<ul>";
                echo "<li>ID: {$authResponse['user']['id']}</li>";
                echo "<li>البريد الإلكتروني: {$authResponse['user']['email']}</li>";
                echo "<li>الاسم: {$authResponse['user']['name']}</li>";
                echo "<li>الدور: {$authResponse['user']['role_id']}</li>";
                echo "<li>الاشتراك: {$authResponse['user']['subscription_id']}</li>";
                echo "</ul>";
                echo "</div>";
            }
        } else {
            echo "<div class='error'>❌ فشل تسجيل الدخول: " . ($authResponse['message'] ?? 'خطأ غير معروف') . "</div>";
        }
        
        echo "<h4>📄 استجابة API:</h4>";
        echo "<div class='json-output'>" . htmlspecialchars(json_encode($authResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</div>";
    } else {
        echo "<div class='error'>❌ فشل في استدعاء API - رمز الخطأ: {$httpCode}</div>";
        echo "<div class='json-output'>" . htmlspecialchars($response) . "</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4️⃣ فحص متجر المستخدم التجريبي</h2>";
    
    // Check user's store
    $stmt = $pdo->prepare("SELECT * FROM stores WHERE user_id = ?");
    $stmt->execute([$demoUser['id']]);
    $userStore = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userStore) {
        echo "<div class='warning'>⚠️ المستخدم التجريبي لا يملك متجر - سيتم إنشاؤه</div>";
        
        // Create store for demo user
        $settings = json_encode([
            'currency' => 'DZD',
            'language' => 'ar',
            'timezone' => 'Africa/Algiers',
            'theme_color' => '#667eea',
            'allow_reviews' => true,
            'auto_approve_products' => false
        ]);
        
        $stmt = $pdo->prepare("
            INSERT INTO stores (user_id, store_name, store_slug, description, status, theme, settings, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $demoUser['id'],
            'متجر مصعب التجريبي',
            'mossaab-demo-store',
            'متجر تجريبي شامل للكتب والإلكترونيات والمنتجات المتنوعة',
            'active',
            'default',
            $settings
        ]);
        
        $storeId = $pdo->lastInsertId();
        
        // Update user's store_id
        $stmt = $pdo->prepare("UPDATE users SET store_id = ? WHERE id = ?");
        $stmt->execute([$storeId, $demoUser['id']]);
        
        echo "<div class='success'>✅ تم إنشاء متجر للمستخدم التجريبي (ID: {$storeId})</div>";
        
        // Get the newly created store
        $stmt = $pdo->prepare("SELECT * FROM stores WHERE id = ?");
        $stmt->execute([$storeId]);
        $userStore = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        echo "<div class='success'>✅ المستخدم التجريبي يملك متجر</div>";
        
        // Update user's store_id if not set
        if (!$demoUser['store_id']) {
            $stmt = $pdo->prepare("UPDATE users SET store_id = ? WHERE id = ?");
            $stmt->execute([$userStore['id'], $demoUser['id']]);
            echo "<div class='success'>✅ تم ربط المستخدم بالمتجر</div>";
        }
    }
    
    echo "<div class='test-result'>";
    echo "<h4>🏪 معلومات المتجر:</h4>";
    echo "<ul>";
    echo "<li>ID: {$userStore['id']}</li>";
    echo "<li>اسم المتجر: {$userStore['store_name']}</li>";
    echo "<li>الرابط: {$userStore['store_slug']}</li>";
    echo "<li>الحالة: {$userStore['status']}</li>";
    echo "<li>إجمالي المنتجات: {$userStore['total_products']}</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5️⃣ اختبار منتجات المتجر</h2>";
    
    // Check store products
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM produits WHERE store_id = ? AND actif = 1");
    $stmt->execute([$userStore['id']]);
    $productCount = $stmt->fetchColumn();
    
    echo "<div class='info'>📦 عدد المنتجات في المتجر: {$productCount}</div>";
    
    if ($productCount > 0) {
        // Show some products
        $stmt = $pdo->prepare("SELECT id, titre, prix, type FROM produits WHERE store_id = ? AND actif = 1 LIMIT 5");
        $stmt->execute([$userStore['id']]);
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='test-result'>";
        echo "<h4>📋 عينة من المنتجات:</h4>";
        echo "<ul>";
        foreach ($products as $product) {
            echo "<li>{$product['titre']} - {$product['prix']} دج ({$product['type']})</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>6️⃣ اختبار صفحات الهبوط</h2>";
    
    // Check landing pages
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM landing_pages WHERE store_id = ?");
    $stmt->execute([$userStore['id']]);
    $landingPageCount = $stmt->fetchColumn();
    
    echo "<div class='info'>🌐 عدد صفحات الهبوط: {$landingPageCount}</div>";
    
    if ($landingPageCount > 0) {
        // Show some landing pages
        $stmt = $pdo->prepare("
            SELECT lp.id, lp.titre, lp.lien_url, p.titre as product_title
            FROM landing_pages lp
            LEFT JOIN produits p ON lp.produit_id = p.id
            WHERE lp.store_id = ?
            LIMIT 5
        ");
        $stmt->execute([$userStore['id']]);
        $landingPages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='test-result'>";
        echo "<h4>📋 عينة من صفحات الهبوط:</h4>";
        echo "<ul>";
        foreach ($landingPages as $page) {
            echo "<li><a href='{$page['lien_url']}' target='_blank'>{$page['titre']}</a> (المنتج: {$page['product_title']})</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>✅ ملخص النتائج</h2>";
    
    $allGood = true;
    $issues = [];
    
    if (!$demoUser) {
        $allGood = false;
        $issues[] = "المستخدم التجريبي غير موجود";
    }
    
    if (!$passwordValid && !isset($newHashedPassword)) {
        $allGood = false;
        $issues[] = "كلمة المرور غير صحيحة";
    }
    
    if ($httpCode !== 200) {
        $allGood = false;
        $issues[] = "API المصادقة لا يعمل";
    }
    
    if (!$userStore) {
        $allGood = false;
        $issues[] = "المستخدم لا يملك متجر";
    }
    
    if ($allGood) {
        echo "<div class='success'>";
        echo "<h3>🎉 جميع الاختبارات نجحت!</h3>";
        echo "<p>نظام دخول المستخدم التجريبي يعمل بشكل صحيح.</p>";
        echo "</div>";
        
        echo "<div class='credentials'>";
        echo "<h4>🔑 بيانات الدخول النهائية:</h4>";
        echo "<ul>";
        echo "<li><strong>البريد الإلكتروني:</strong> <EMAIL></li>";
        echo "<li><strong>كلمة المرور:</strong> demo123</li>";
        echo "<li><strong>رابط المتجر:</strong> <a href='/store.php?slug={$userStore['store_slug']}' target='_blank'>/store.php?slug={$userStore['store_slug']}</a></li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h3>⚠️ توجد مشاكل تحتاج إلى إصلاح:</h3>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>{$issue}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div class='info'>";
    echo "<h4>🔗 روابط مفيدة:</h4>";
    echo "<ul>";
    echo "<li><a href='/login.html' target='_blank'>صفحة تسجيل الدخول</a></li>";
    echo "<li><a href='/dashboard/' target='_blank'>لوحة تحكم المتجر</a></li>";
    echo "<li><a href='/admin/' target='_blank'>لوحة التحكم الإدارية</a></li>";
    if (isset($userStore)) {
        echo "<li><a href='/store.php?slug={$userStore['store_slug']}' target='_blank'>المتجر التجريبي</a></li>";
    }
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
