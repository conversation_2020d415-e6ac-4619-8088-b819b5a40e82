<?php
/**
 * System Testing Page for Mossaab Landing Page Admin
 * Tests all system components and database connectivity
 */

// Start session and check authentication
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

// Include database configuration
require_once '../php/config.php';

/**
 * Test database connection
 */
function testDatabaseConnection() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT 1");
        return [
            'status' => 'success',
            'message' => 'اتصال قاعدة البيانات ناجح'
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => 'فشل في الاتصال بقاعدة البيانات: ' . $e->getMessage()
        ];
    }
}

/**
 * Test database tables
 */
function testDatabaseTables() {
    global $pdo;
    $requiredTables = [
        'products', 'orders', 'landing_pages', 'categories',
        'users', 'user_roles', 'subscription_plans', 'user_stores'
    ];
    
    $results = [];
    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $results[$table] = [
                    'status' => 'success',
                    'message' => "جدول $table موجود"
                ];
            } else {
                $results[$table] = [
                    'status' => 'error',
                    'message' => "جدول $table غير موجود"
                ];
            }
        } catch (Exception $e) {
            $results[$table] = [
                'status' => 'error',
                'message' => "خطأ في فحص جدول $table: " . $e->getMessage()
            ];
        }
    }
    
    return $results;
}

/**
 * Test API endpoints
 */
function testAPIEndpoints() {
    $endpoints = [
        'dashboard-stats.php' => 'إحصائيات لوحة المعلومات',
        'users.php' => 'إدارة المستخدمين',
        'roles.php' => 'إدارة الأدوار',
        'subscriptions.php' => 'إدارة الاشتراكات'
    ];
    
    $results = [];
    foreach ($endpoints as $endpoint => $description) {
        $url = '../php/api/' . $endpoint;
        if (file_exists($url)) {
            $results[$endpoint] = [
                'status' => 'success',
                'message' => "$description - الملف موجود"
            ];
        } else {
            $results[$endpoint] = [
                'status' => 'error',
                'message' => "$description - الملف غير موجود"
            ];
        }
    }
    
    return $results;
}

/**
 * Test file permissions
 */
function testFilePermissions() {
    $paths = [
        '../uploads/' => 'مجلد الرفع',
        '../php/api/' => 'مجلد API',
        '../admin/css/' => 'مجلد CSS',
        '../admin/js/' => 'مجلد JavaScript'
    ];
    
    $results = [];
    foreach ($paths as $path => $description) {
        if (is_dir($path)) {
            if (is_writable($path)) {
                $results[$path] = [
                    'status' => 'success',
                    'message' => "$description - قابل للكتابة"
                ];
            } else {
                $results[$path] = [
                    'status' => 'warning',
                    'message' => "$description - غير قابل للكتابة"
                ];
            }
        } else {
            $results[$path] = [
                'status' => 'error',
                'message' => "$description - المجلد غير موجود"
            ];
        }
    }
    
    return $results;
}

// Run tests if requested
$testResults = [];
if (isset($_GET['run_tests'])) {
    $testResults = [
        'database' => testDatabaseConnection(),
        'tables' => testDatabaseTables(),
        'apis' => testAPIEndpoints(),
        'permissions' => testFilePermissions()
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - مصعب لاندينغ بيج</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Noto Sans Arabic', sans-serif;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-result {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
            gap: 12px;
        }
        
        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .test-result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .run-tests-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .run-tests-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(40, 167, 69, 0.3);
        }
        
        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> اختبار النظام الشامل</h1>
            <p>فحص جميع مكونات النظام والتأكد من سلامة العمل</p>
        </div>
        
        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
        </a>
        
        <div class="test-section">
            <h2><i class="fas fa-play-circle"></i> تشغيل الاختبارات</h2>
            <p>اضغط على الزر أدناه لتشغيل جميع اختبارات النظام</p>
            <a href="?run_tests=1" class="run-tests-btn">
                <i class="fas fa-rocket"></i> تشغيل الاختبار الشامل
            </a>
        </div>
        
        <?php if (!empty($testResults)): ?>
        
        <!-- Database Connection Test -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> اختبار اتصال قاعدة البيانات</h2>
            <div class="test-result <?php echo $testResults['database']['status']; ?>">
                <i class="fas <?php echo $testResults['database']['status'] === 'success' ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                <span><?php echo $testResults['database']['message']; ?></span>
            </div>
        </div>
        
        <!-- Database Tables Test -->
        <div class="test-section">
            <h2><i class="fas fa-table"></i> اختبار جداول قاعدة البيانات</h2>
            <?php foreach ($testResults['tables'] as $table => $result): ?>
            <div class="test-result <?php echo $result['status']; ?>">
                <i class="fas <?php echo $result['status'] === 'success' ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                <span><?php echo $result['message']; ?></span>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- API Endpoints Test -->
        <div class="test-section">
            <h2><i class="fas fa-code"></i> اختبار نقاط API</h2>
            <?php foreach ($testResults['apis'] as $api => $result): ?>
            <div class="test-result <?php echo $result['status']; ?>">
                <i class="fas <?php echo $result['status'] === 'success' ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                <span><?php echo $result['message']; ?></span>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- File Permissions Test -->
        <div class="test-section">
            <h2><i class="fas fa-folder-open"></i> اختبار صلاحيات الملفات</h2>
            <?php foreach ($testResults['permissions'] as $path => $result): ?>
            <div class="test-result <?php echo $result['status']; ?>">
                <i class="fas <?php 
                    echo $result['status'] === 'success' ? 'fa-check-circle' : 
                         ($result['status'] === 'warning' ? 'fa-exclamation-triangle' : 'fa-times-circle'); 
                ?>"></i>
                <span><?php echo $result['message']; ?></span>
            </div>
            <?php endforeach; ?>
        </div>
        
        <?php 
        // Calculate overall success rate
        $totalTests = 0;
        $successfulTests = 0;
        
        // Count database test
        $totalTests++;
        if ($testResults['database']['status'] === 'success') $successfulTests++;
        
        // Count table tests
        foreach ($testResults['tables'] as $result) {
            $totalTests++;
            if ($result['status'] === 'success') $successfulTests++;
        }
        
        // Count API tests
        foreach ($testResults['apis'] as $result) {
            $totalTests++;
            if ($result['status'] === 'success') $successfulTests++;
        }
        
        // Count permission tests
        foreach ($testResults['permissions'] as $result) {
            $totalTests++;
            if ($result['status'] === 'success') $successfulTests++;
        }
        
        $successRate = $totalTests > 0 ? round(($successfulTests / $totalTests) * 100) : 0;
        ?>
        
        <!-- Overall Results -->
        <div class="test-section">
            <h2><i class="fas fa-chart-pie"></i> النتائج الإجمالية</h2>
            <div class="test-result <?php echo $successRate >= 80 ? 'success' : ($successRate >= 60 ? 'warning' : 'error'); ?>">
                <i class="fas fa-chart-line"></i>
                <span>معدل النجاح: <?php echo $successRate; ?>% (<?php echo $successfulTests; ?>/<?php echo $totalTests; ?> اختبارات ناجحة)</span>
            </div>
        </div>
        
        <?php endif; ?>
    </div>
</body>
</html>
