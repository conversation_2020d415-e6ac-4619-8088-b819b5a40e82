<?php

/**
 * Check Products and Landing Pages
 */

require_once 'php/config.php';

header('Content-Type: text/html');

echo "<h1>Check Products and Landing Pages</h1>";

try {
    $pdo = getPDOConnection();

    // Check products (get all products first to see structure)
    echo "<h2>All Products</h2>";
    $stmt = $pdo->query("SELECT * FROM produits ORDER BY id LIMIT 5");
    $products = $stmt->fetchAll();

    if ($products) {
        echo "<table border='1'>";
        echo "<tr>";
        // Show all column names
        foreach (array_keys($products[0]) as $column) {
            echo "<th>" . htmlspecialchars($column) . "</th>";
        }
        echo "</tr>";

        foreach ($products as $product) {
            echo "<tr>";
            foreach ($product as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No products found.</p>";
    }

    // Check existing landing pages
    echo "<h2>Existing Landing Pages</h2>";
    $stmt = $pdo->query("
        SELECT lp.id, lp.titre, lp.produit_id, p.titre as product_title, lp.lien_url, lp.actif
        FROM landing_pages lp
        LEFT JOIN produits p ON lp.produit_id = p.id
        ORDER BY lp.id
    ");
    $landingPages = $stmt->fetchAll();

    if ($landingPages) {
        echo "<table border='1'>";
        echo "<tr><th>LP ID</th><th>LP Title</th><th>Product ID</th><th>Product Title</th><th>URL</th><th>Active</th></tr>";

        foreach ($landingPages as $lp) {
            echo "<tr>";
            echo "<td>" . $lp['id'] . "</td>";
            echo "<td>" . htmlspecialchars($lp['titre']) . "</td>";
            echo "<td>" . $lp['produit_id'] . "</td>";
            echo "<td>" . htmlspecialchars($lp['product_title']) . "</td>";
            echo "<td>" . htmlspecialchars($lp['lien_url']) . "</td>";
            echo "<td>" . ($lp['actif'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No landing pages found.</p>";
    }

    // Check demo user
    echo "<h2>Demo User Information</h2>";
    $stmt = $pdo->prepare("SELECT id, username, email, first_name, last_name FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $demoUser = $stmt->fetch();

    if ($demoUser) {
        echo "<p>Demo User Found:</p>";
        echo "<ul>";
        echo "<li>ID: " . $demoUser['id'] . "</li>";
        echo "<li>Username: " . $demoUser['username'] . "</li>";
        echo "<li>Email: " . $demoUser['email'] . "</li>";
        echo "<li>Name: " . $demoUser['first_name'] . " " . $demoUser['last_name'] . "</li>";
        echo "</ul>";
    } else {
        echo "<p>Demo user not found.</p>";
    }

    // Recommendations
    echo "<h2>Recommendations for Creating Demo Landing Pages</h2>";
    echo "<ol>";
    echo "<li>Select 5 different products from the active products list</li>";
    echo "<li>Create unique landing pages for each product</li>";
    echo "<li>Use different templates and content for variety</li>";
    echo "<li>Ensure Arabic RTL support in all landing pages</li>";
    echo "<li>Test that all landing pages are accessible</li>";
    echo "</ol>";
} catch (Exception $e) {
    echo "<h2>Error</h2>";
    echo "<p>❌ " . $e->getMessage() . "</p>";
}
