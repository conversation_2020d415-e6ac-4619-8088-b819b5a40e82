<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

/**
 * Migration script to create and update AI settings table
 */
try {
    $db = Database::getInstance();

    // Create ai_settings table if it doesn't exist
    $db->exec("
        CREATE TABLE IF NOT EXISTS ai_settings (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            provider VARCHAR(50) NOT NULL,
            api_key VARCHAR(255) NOT NULL,
            model VARCHAR(100) DEFAULT 'default',
            enabled BOOLEAN DEFAULT FALSE,
            status_message VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY idx_provider (provider)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ");

    echo "Created/verified ai_settings table structure\n";

    // Migrate existing settings from environment variables
    $providers = ['openai', 'anthropic', 'gemini'];

    foreach ($providers as $provider) {
        $envKey = strtoupper($provider) . '_API_KEY';
        $apiKey = Config::get($envKey);

        if ($apiKey) {
            $model = Config::get(strtoupper($provider) . '_MODEL', 'default');

            $stmt = $db->prepare("
                INSERT INTO ai_settings
                    (provider, api_key, model, enabled, status_message)
                VALUES
                    (:provider, :api_key, :model, TRUE, 'Configured from environment')
                ON DUPLICATE KEY UPDATE
                    api_key = VALUES(api_key),
                    model = COALESCE(model, VALUES(model)),
                    enabled = TRUE,
                    status_message = 'Updated from environment'
            ");

            $stmt->execute([
                'provider' => $provider,
                'api_key' => $apiKey,
                'model' => $model
            ]);

            echo "Migrated settings for {$provider}\n";
        } else {
            echo "No API key found for {$provider} in environment\n";
        }
    }

    echo "\nMigration completed successfully\n";
} catch (Exception $e) {
    echo "Error during migration: " . $e->getMessage() . "\n";
    exit(1);
}
