<?php
/**
 * Test OpenAI Settings Persistence
 * Comprehensive testing of save/load cycle and .env integration
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار استمرارية إعدادات OpenAI</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border-radius: 12px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-button {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .test-step {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .step-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .step-icon {
            margin-left: 10px;
            font-size: 20px;
        }
        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff9a9e, #fecfef);
            transition: width 0.5s ease;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 اختبار استمرارية إعدادات OpenAI</h1>
            <p>اختبار شامل لدورة الحفظ/التحميل وتكامل .env</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
        <div id="progressText" style="text-align: center; margin: 10px 0;">جاري البدء في الاختبارات...</div>

        <?php
        $testResults = [];
        $totalTests = 0;
        $passedTests = 0;

        function recordTest($testName, $passed, $message = '') {
            global $testResults, $totalTests, $passedTests;
            $totalTests++;
            if ($passed) $passedTests++;
            $testResults[$testName] = ['passed' => $passed, 'message' => $message];
            return $passed;
        }

        try {
            require_once '../php/config.php';
            
            // Test 1: Environment Variable Integration
            echo '<div class="test-section">';
            echo '<h3>📁 اختبار 1: تكامل متغيرات البيئة</h3>';
            
            $envApiKey = $_ENV['OPENAI_API_KEY'] ?? '';
            if (recordTest('env_api_key_exists', !empty($envApiKey))) {
                echo '<div class="result pass">✅ مفتاح OpenAI API موجود في .env</div>';
                echo '<div class="data-display">API Key: ' . substr($envApiKey, 0, 20) . '...' . substr($envApiKey, -10) . '</div>';
            } else {
                echo '<div class="result fail">❌ مفتاح OpenAI API غير موجود في .env</div>';
            }
            echo '</div>';

            // Test 2: Database Table Structure
            echo '<div class="test-section">';
            echo '<h3>🗄️ اختبار 2: هيكل جدول قاعدة البيانات</h3>';
            
            if (isset($conn) && $conn instanceof PDO) {
                // Check if table exists
                $stmt = $conn->query("SHOW TABLES LIKE 'ai_settings'");
                if (recordTest('table_exists', $stmt->rowCount() > 0)) {
                    echo '<div class="result pass">✅ جدول ai_settings موجود</div>';
                    
                    // Check table structure
                    $stmt = $conn->query("DESCRIBE ai_settings");
                    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    $requiredColumns = ['provider', 'api_key', 'model', 'max_tokens', 'temperature', 'is_active'];
                    $hasAllColumns = true;
                    
                    foreach ($requiredColumns as $col) {
                        if (!in_array($col, $columns)) {
                            $hasAllColumns = false;
                            break;
                        }
                    }
                    
                    if (recordTest('table_structure', $hasAllColumns)) {
                        echo '<div class="result pass">✅ هيكل الجدول صحيح</div>';
                        echo '<div class="data-display">الأعمدة: ' . implode(', ', $columns) . '</div>';
                    } else {
                        echo '<div class="result fail">❌ هيكل الجدول غير مكتمل</div>';
                    }
                } else {
                    echo '<div class="result fail">❌ جدول ai_settings غير موجود</div>';
                }
            } else {
                recordTest('database_connection', false);
                echo '<div class="result fail">❌ اتصال قاعدة البيانات غير متاح</div>';
            }
            echo '</div>';

            // Test 3: Current OpenAI Settings
            echo '<div class="test-section">';
            echo '<h3>⚙️ اختبار 3: الإعدادات الحالية لـ OpenAI</h3>';
            
            $stmt = $conn->prepare("SELECT * FROM ai_settings WHERE provider = 'openai'");
            $stmt->execute();
            $currentSettings = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (recordTest('openai_settings_exist', $currentSettings !== false)) {
                echo '<div class="result pass">✅ إعدادات OpenAI موجودة في قاعدة البيانات</div>';
                
                if ($currentSettings) {
                    echo '<div class="test-step">';
                    echo '<div class="step-title"><span class="step-icon">🧠</span>إعدادات OpenAI الحالية</div>';
                    echo '<div class="data-display">';
                    echo 'Provider: ' . $currentSettings['provider'] . '<br>';
                    echo 'API Key: ' . (empty($currentSettings['api_key']) ? 'غير مُعين' : 'مُعين (' . strlen($currentSettings['api_key']) . ' حرف)') . '<br>';
                    echo 'Model: ' . $currentSettings['model'] . '<br>';
                    echo 'Max Tokens: ' . $currentSettings['max_tokens'] . '<br>';
                    echo 'Temperature: ' . $currentSettings['temperature'] . '<br>';
                    echo 'Active: ' . ($currentSettings['is_active'] ? 'نعم' : 'لا') . '<br>';
                    echo 'Created: ' . $currentSettings['created_at'] . '<br>';
                    echo 'Updated: ' . $currentSettings['updated_at'];
                    echo '</div>';
                    echo '</div>';
                }
            } else {
                echo '<div class="result warning">⚠️ إعدادات OpenAI غير موجودة - سيتم إنشاؤها</div>';
            }
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ في الاختبار</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
        }

        // Interactive Testing Section
        echo '<div class="test-section">';
        echo '<h3>🧪 اختبارات تفاعلية</h3>';
        echo '<div id="interactiveTests">جاري تحميل الاختبارات التفاعلية...</div>';
        echo '</div>';

        // Calculate score
        $successRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;
        
        echo '<div class="test-section">';
        echo '<h3>📊 نتائج الاختبار</h3>';
        echo '<div class="result ' . ($successRate >= 80 ? 'pass' : ($successRate >= 60 ? 'warning' : 'fail')) . '">';
        echo ($successRate >= 80 ? '🎉' : ($successRate >= 60 ? '⚠️' : '❌')) . ' معدل النجاح: ' . round($successRate, 1) . '% (' . $passedTests . '/' . $totalTests . ')';
        echo '</div>';
        
        foreach ($testResults as $testName => $result) {
            $statusIcon = $result['passed'] ? '✅' : '❌';
            $statusText = $result['passed'] ? 'نجح' : 'فشل';
            $resultClass = $result['passed'] ? 'pass' : 'fail';
            
            echo '<div class="result ' . $resultClass . '">';
            echo $statusIcon . ' ' . $testName . ': ' . $statusText;
            if (!empty($result['message'])) {
                echo ' - ' . $result['message'];
            }
            echo '</div>';
        }
        
        echo '<h4>🛠️ أدوات الإصلاح:</h4>';
        echo '<a href="fix-openai-integration.php" class="test-button">🔧 إصلاح تكامل OpenAI</a>';
        echo '<a href="fix-ai-settings-errors.php" class="test-button">🤖 إصلاح AI Settings</a>';
        
        echo '<h4>🧪 اختبار الوظائف:</h4>';
        echo '<a href="index.html" class="test-button">🏠 فتح لوحة التحكم</a>';
        echo '<a href="ai-settings.html" class="test-button">🤖 اختبار AI Settings</a>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        let testStep = 0;
        const totalSteps = 6;

        function updateProgress(step, message) {
            const percentage = (step / totalSteps) * 100;
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = message;
        }

        // Comprehensive interactive testing
        async function runInteractiveTests() {
            const resultsDiv = document.getElementById('interactiveTests');
            let results = '';

            // Test 1: Get Config API
            updateProgress(++testStep, 'اختبار API للحصول على الإعدادات...');
            try {
                const response = await fetch('../php/api/ai.php?action=get_config');
                const data = await response.json();
                
                if (data.success && data.data.openai) {
                    results += '<div class="test-step">';
                    results += '<div class="step-title"><span class="step-icon">✅</span>اختبار Get Config API</div>';
                    results += '<div class="result pass">✅ تم جلب إعدادات OpenAI بنجاح</div>';
                    results += '<div class="data-display">البيانات المُستلمة: ' + JSON.stringify(data.data.openai, null, 2) + '</div>';
                    results += '</div>';
                } else {
                    results += '<div class="test-step">';
                    results += '<div class="step-title"><span class="step-icon">❌</span>اختبار Get Config API</div>';
                    results += '<div class="result fail">❌ فشل في جلب إعدادات OpenAI</div>';
                    results += '</div>';
                }
            } catch (error) {
                results += '<div class="test-step">';
                results += '<div class="step-title"><span class="step-icon">❌</span>اختبار Get Config API</div>';
                results += '<div class="result fail">❌ خطأ: ' + error.message + '</div>';
                results += '</div>';
            }

            // Test 2: Save Settings
            updateProgress(++testStep, 'اختبار حفظ الإعدادات...');
            const testSettings = {
                provider: 'openai',
                api_key: 'sk-test-key-for-persistence-testing',
                model: 'gpt-4',
                max_tokens: 2000,
                temperature: 0.8,
                is_active: true
            };

            try {
                const response = await fetch('../php/api/ai.php?action=update_config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testSettings)
                });
                const data = await response.json();
                
                if (data.success) {
                    results += '<div class="test-step">';
                    results += '<div class="step-title"><span class="step-icon">✅</span>اختبار حفظ الإعدادات</div>';
                    results += '<div class="result pass">✅ تم حفظ إعدادات OpenAI بنجاح</div>';
                    results += '<div class="data-display">الإعدادات المحفوظة: ' + JSON.stringify(testSettings, null, 2) + '</div>';
                    results += '</div>';
                } else {
                    results += '<div class="test-step">';
                    results += '<div class="step-title"><span class="step-icon">❌</span>اختبار حفظ الإعدادات</div>';
                    results += '<div class="result fail">❌ فشل في حفظ الإعدادات: ' + (data.message || 'خطأ غير معروف') + '</div>';
                    results += '</div>';
                }
            } catch (error) {
                results += '<div class="test-step">';
                results += '<div class="step-title"><span class="step-icon">❌</span>اختبار حفظ الإعدادات</div>';
                results += '<div class="result fail">❌ خطأ: ' + error.message + '</div>';
                results += '</div>';
            }

            // Test 3: Verify Persistence
            updateProgress(++testStep, 'اختبار استمرارية البيانات...');
            try {
                const response = await fetch('../php/api/ai.php?action=get_config');
                const data = await response.json();
                
                if (data.success && data.data.openai) {
                    const savedSettings = data.data.openai;
                    const isPersistent = savedSettings.model === testSettings.model && 
                                       savedSettings.max_tokens === testSettings.max_tokens &&
                                       savedSettings.temperature === testSettings.temperature;
                    
                    if (isPersistent) {
                        results += '<div class="test-step">';
                        results += '<div class="step-title"><span class="step-icon">✅</span>اختبار استمرارية البيانات</div>';
                        results += '<div class="result pass">✅ الإعدادات محفوظة ومستمرة بنجاح</div>';
                        results += '<div class="data-display">الإعدادات المُستلمة: ' + JSON.stringify(savedSettings, null, 2) + '</div>';
                        results += '</div>';
                    } else {
                        results += '<div class="test-step">';
                        results += '<div class="step-title"><span class="step-icon">❌</span>اختبار استمرارية البيانات</div>';
                        results += '<div class="result fail">❌ الإعدادات لم تُحفظ بشكل صحيح</div>';
                        results += '</div>';
                    }
                }
            } catch (error) {
                results += '<div class="test-step">';
                results += '<div class="step-title"><span class="step-icon">❌</span>اختبار استمرارية البيانات</div>';
                results += '<div class="result fail">❌ خطأ: ' + error.message + '</div>';
                results += '</div>';
            }

            // Test 4: Activation Status
            updateProgress(++testStep, 'اختبار حالة التفعيل...');
            try {
                const response = await fetch('../php/api/ai.php?action=get_config');
                const data = await response.json();
                
                if (data.success && data.data.openai) {
                    const isActive = data.data.openai.is_active;
                    
                    results += '<div class="test-step">';
                    results += '<div class="step-title"><span class="step-icon">' + (isActive ? '✅' : '❌') + '</span>اختبار حالة التفعيل</div>';
                    results += '<div class="result ' + (isActive ? 'pass' : 'warning') + '">';
                    results += (isActive ? '✅' : '⚠️') + ' OpenAI ' + (isActive ? 'مُفعل' : 'غير مُفعل');
                    results += '</div>';
                    results += '</div>';
                }
            } catch (error) {
                results += '<div class="test-step">';
                results += '<div class="step-title"><span class="step-icon">❌</span>اختبار حالة التفعيل</div>';
                results += '<div class="result fail">❌ خطأ: ' + error.message + '</div>';
                results += '</div>';
            }

            updateProgress(totalSteps, 'تم الانتهاء من جميع الاختبارات!');
            resultsDiv.innerHTML = results;
        }

        // Run tests after page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runInteractiveTests, 1000);
        });
    </script>
</body>
</html>
