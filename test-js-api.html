<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API JavaScript</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .result {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007cba;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>اختبار API صفحات الهبوط</h1>
    
    <button onclick="testAPI()">اختبار API</button>
    <button onclick="clearResults()">مسح النتائج</button>
    
    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            console.log(message);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testAPI() {
            log('🚀 بدء اختبار API...', 'info');
            
            try {
                log('📡 إرسال طلب إلى: php/api/landing-pages.php', 'info');
                
                const response = await fetch('php/api/landing-pages.php', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`📥 حالة الاستجابة: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const text = await response.text();
                log(`📄 نص الاستجابة الخام: <pre>${text}</pre>`, 'info');
                
                if (!text.trim()) {
                    log('⚠️ الاستجابة فارغة!', 'error');
                    return;
                }
                
                let data;
                try {
                    data = JSON.parse(text);
                    log('✅ تم تحليل JSON بنجاح', 'success');
                } catch (parseError) {
                    log(`❌ خطأ في تحليل JSON: ${parseError.message}`, 'error');
                    return;
                }
                
                log(`📊 البيانات المحللة: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                
                if (data.success) {
                    const pages = data.data || [];
                    log(`✅ تم العثور على ${pages.length} صفحة هبوط`, 'success');
                    
                    if (pages.length > 0) {
                        pages.forEach((page, index) => {
                            log(`📄 صفحة ${index + 1}: ${page.titre} (المنتج: ${page.product_title})`, 'info');
                        });
                    }
                } else {
                    log(`❌ فشل API: ${data.message || 'خطأ غير معروف'}`, 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في الطلب: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            log('🔄 تحميل الصفحة مكتمل، بدء الاختبار التلقائي...', 'info');
            testAPI();
        });
    </script>
</body>
</html>
