<?php
require_once __DIR__ . '/../php/config.php';

try {
    // We will create a generic electronics product for this landing page model
    // In a real scenario, you would fetch a specific product type like 'phone', 'headphone', etc.
    $product_title = 'أحدث الأجهزة الإلكترونية';

    // For the model, we'll insert a placeholder product if none exists
    // This part is for demonstration; ideally, you'd link to an existing electronics product
    $product_stmt = $conn->query("SELECT id FROM produits WHERE type = 'laptop' LIMIT 1"); // Using laptop as a proxy for electronics
    $product = $product_stmt->fetch(PDO::FETCH_ASSOC);
    $product_id = $product ? $product['id'] : 1; // Fallback to 1 if no laptop found

    // Create a landing page for Electronics
    $stmt = $conn->prepare("
        INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $contenu_droit = '
    <h3>🔌 اكتشف عالم التكنولوجيا الحديثة</h3>
    <ul>
        <li><strong>أداء فائق:</strong> معالجات سريعة وذاكرة كبيرة لتجربة استخدام سلسة.</li>
        <li><strong>تصميم مبتكر:</strong> أناقة وجودة في التصنيع تلفت الأنظار.</li>
        <li><strong>بطارية تدوم طويلاً:</strong> استخدم أجهزتك طوال اليوم دون قلق.</li>
        <li><strong>شاشات عالية الدقة:</strong> ألوان واقعية وتفاصيل مذهلة للمشاهدة والعمل.</li>
    </ul>
    
    <h3>💡 أبرز الميزات:</h3>
    <p>• <strong>اتصال لاسلكي سريع:</strong> واي فاي 6 وبلوتوث 5.2.<br>
    • <strong>صوت غامر:</strong> سماعات ستيريو عالية الجودة.<br>
    • <strong>أمان متقدم:</strong> بصمة الإصبع والتعرف على الوجه.<br>
    • <strong>نظام تشغيل حديث:</strong> آخر التحديثات والميزات الأمنية.</p>
    ';
    
    $contenu_gauche = '
    <h3>🚀 مستقبل التكنولوجيا بين يديك</h3>
    <p>نقدم لكم أحدث الابتكارات في عالم الإلكترونيات، مصممة لتسهيل حياتكم وزيادة إنتاجيتكم. كل جهاز هو تحفة فنية تجمع بين القوة والجمال.</p>
    
    <h3>🌟 شهادات الخبراء</h3>
    <blockquote style="border-right: 3px solid #667eea; padding-right: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">
        "جودة تصنيع ممتازة وأداء لا يصدق. هذه الأجهزة تضع معياراً جديداً في السوق."
        <cite style="display: block; margin-top: 10px; font-weight: bold;">- تقني، مراجع أجهزة</cite>
    </blockquote>
    
    <h3>🎁 عرض الإطلاق الحصري</h3>
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
        <h4 style="margin: 0 0 10px 0;">هدية مجانية مع كل طلب!</h4>
        <p style="font-size: 1.5em; font-weight: bold; margin: 0;">اطلب الآن واحصل على شاحن لاسلكي مجاناً!</p>
    </div>
    ';
    
    $stmt->execute([
        $product_id,
        'صفحة هبوط للإلكترونيات: ' . $product_title,
        $contenu_droit,
        $contenu_gauche,
        '/landing-page-template.php?id='
    ]);
    
    $landing_page_id = $conn->lastInsertId();
    
    // Add sample images for the electronics landing page
    $images = [
        'https://images.unsplash.com/photo-1525547719571-a2d4ac8945e2?w=800&h=600&fit=crop', // Various gadgets
        'https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?w=800&h=600&fit=crop', // Laptop and phone
        'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=800&h=600&fit=crop'  // Headphones
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO landing_page_images (landing_page_id, image_url, ordre) 
        VALUES (?, ?, ?)
    ");
    
    foreach ($images as $index => $image_url) {
        $stmt->execute([$landing_page_id, $image_url, $index]);
    }
    
    // Update the lien_url with the actual landing page ID
    $stmt = $conn->prepare("UPDATE landing_pages SET lien_url = ? WHERE id = ?");
    $stmt->execute(['/landing-page-template.php?id=' . $landing_page_id, $landing_page_id]);
    
    echo "✅ Landing page pour 'Électroniques' créée avec succès!<br>";
    echo "ID de la landing page: $landing_page_id<br>";
    echo "URL: <a href='/landing-page-template.php?id=$landing_page_id' target='_blank' style='color: #667eea; font-weight: bold;'>🚀 Voir la landing page</a><br>";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors de la création de la landing page pour les électroniques: " . $e->getMessage();
}
?>