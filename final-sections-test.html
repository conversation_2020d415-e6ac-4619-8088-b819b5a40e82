<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي لأقسام لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .admin-frame {
            width: 100%;
            height: 700px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .section-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #6c757d;
        }
        .section-status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .section-status.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .section-status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .test-button {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-check-double"></i> اختبار نهائي لأقسام لوحة التحكم</h1>
        <p class="lead">اختبار شامل للتأكد من أن جميع الإصلاحات تعمل بشكل صحيح</p>
        
        <div class="alert alert-info">
            <h4><i class="fas fa-info-circle"></i> الإصلاحات المطبقة</h4>
            <ul>
                <li>✅ إنشاء الوظائف المفقودة لتحميل المحتوى</li>
                <li>✅ تحسين نظام التنقل بين الأقسام</li>
                <li>✅ إضافة معالجات الأحداث المحسنة</li>
                <li>✅ إنشاء API الطلبات المفقود</li>
                <li>✅ إصلاح مشاكل عرض المحتوى</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-play"></i> اختبارات سريعة</h2>
            
            <button class="btn btn-primary test-button" onclick="runQuickTest()">
                <i class="fas fa-bolt"></i> اختبار سريع
            </button>
            
            <button class="btn btn-success test-button" onclick="runFullTest()">
                <i class="fas fa-test-tube"></i> اختبار شامل
            </button>
            
            <button class="btn btn-info test-button" onclick="openAdminInNewTab()">
                <i class="fas fa-external-link-alt"></i> فتح لوحة التحكم
            </button>
            
            <button class="btn btn-warning test-button" onclick="clearResults()">
                <i class="fas fa-eraser"></i> مسح النتائج
            </button>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-chart-line"></i> حالة الأقسام</h2>
            <div id="sectionsStatus">
                <div class="section-status">
                    <span><i class="fas fa-home"></i> الرئيسية</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status">
                    <span><i class="fas fa-box"></i> إدارة المنتجات</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status">
                    <span><i class="fas fa-shopping-cart"></i> الطلبات</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status">
                    <span><i class="fas fa-bullhorn"></i> صفحات هبوط</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status">
                    <span><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status">
                    <span><i class="fas fa-cog"></i> إعدادات النظام</span>
                    <span>جاري الفحص...</span>
                </div>
                <div class="section-status">
                    <span><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</span>
                    <span>جاري الفحص...</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-terminal"></i> نتائج الاختبار</h2>
            <div id="testResults" class="test-results">
                <div style="color: #28a745;">🚀 جاهز لبدء الاختبار...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-browser"></i> معاينة مباشرة</h2>
            <iframe id="adminFrame" class="admin-frame" src="/admin/"></iframe>
            <div class="mt-3">
                <button class="btn btn-secondary" onclick="reloadAdminFrame()">
                    <i class="fas fa-redo"></i> إعادة تحميل
                </button>
                <button class="btn btn-info" onclick="testFrameSections()">
                    <i class="fas fa-mouse-pointer"></i> اختبار الأقسام في الإطار
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Test results logging
        function logResult(message, type = "info") {
            const resultsDiv = document.getElementById("testResults");
            const timestamp = new Date().toLocaleTimeString("ar-DZ");
            const icon = type === "error" ? "❌" : type === "success" ? "✅" : type === "warning" ? "⚠️" : "ℹ️";
            const color = type === "error" ? "#dc3545" : type === "success" ? "#28a745" : type === "warning" ? "#ffc107" : "#6c757d";
            
            const logEntry = document.createElement("div");
            logEntry.style.color = color;
            logEntry.style.marginBottom = "5px";
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Update section status
        function updateSectionStatus(sectionName, status, message) {
            const statusElements = document.querySelectorAll("#sectionsStatus .section-status");
            statusElements.forEach(element => {
                if (element.textContent.includes(sectionName)) {
                    element.className = `section-status ${status}`;
                    element.querySelector("span:last-child").textContent = message;
                }
            });
        }
        
        // Quick test
        function runQuickTest() {
            logResult("بدء الاختبار السريع...");
            
            // Test if admin-sections-fix.js is loaded
            const frame = document.getElementById('adminFrame');
            try {
                const frameWindow = frame.contentWindow;
                const frameDoc = frame.contentDocument || frameWindow.document;
                
                // Check if our fix script is loaded
                const fixScript = frameDoc.querySelector('script[src*="admin-sections-fix"]');
                if (fixScript) {
                    logResult("✅ سكريبت الإصلاح محمل بشكل صحيح", "success");
                    updateSectionStatus("الرئيسية", "success", "السكريبت محمل");
                } else {
                    logResult("⚠️ سكريبت الإصلاح غير محمل", "warning");
                }
                
                // Check if load functions exist
                const loadFunctions = [
                    'loadDashboardContent',
                    'loadProductsContent', 
                    'loadOrdersContent',
                    'loadLandingPagesContent',
                    'loadSettingsContent'
                ];
                
                let functionsFound = 0;
                loadFunctions.forEach(func => {
                    if (typeof frameWindow[func] === 'function') {
                        functionsFound++;
                        logResult(`✅ ${func} موجود`, "success");
                    } else {
                        logResult(`❌ ${func} غير موجود`, "error");
                    }
                });
                
                const functionsRate = (functionsFound / loadFunctions.length) * 100;
                logResult(`📊 نسبة الوظائف الموجودة: ${functionsRate.toFixed(1)}%`);
                
                if (functionsRate >= 80) {
                    logResult("🎉 الاختبار السريع نجح!", "success");
                } else {
                    logResult("⚠️ الاختبار السريع يحتاج تحسين", "warning");
                }
                
            } catch (error) {
                logResult(`❌ خطأ في الاختبار السريع: ${error.message}`, "error");
            }
        }
        
        // Full test
        function runFullTest() {
            logResult("بدء الاختبار الشامل...");
            
            const sections = [
                { name: "الرئيسية", id: "dashboard", selector: '[data-section="dashboard"]' },
                { name: "إدارة المنتجات", id: "products", selector: '[data-section="products"]' },
                { name: "الطلبات", id: "orders", selector: '[data-section="orders"]' },
                { name: "صفحات هبوط", id: "landingPages", selector: '[data-section="landingPages"]' },
                { name: "التقارير والإحصائيات", id: "reports", selector: '[data-section="reports"]' },
                { name: "إعدادات النظام", id: "settings", selector: '[data-section="settings"]' },
                { name: "تسجيل الخروج", id: "logout", selector: '[data-action="logout"]' }
            ];
            
            const frame = document.getElementById('adminFrame');
            
            sections.forEach((section, index) => {
                setTimeout(() => {
                    testSection(section, frame);
                }, index * 1000);
            });
        }
        
        // Test individual section
        function testSection(section, frame) {
            logResult(`🔍 اختبار قسم: ${section.name}`);
            
            try {
                const frameDoc = frame.contentDocument || frame.contentWindow.document;
                const sectionElement = frameDoc.querySelector(section.selector);
                
                if (sectionElement) {
                    logResult(`✅ ${section.name}: العنصر موجود`, "success");
                    
                    // Test click
                    try {
                        sectionElement.click();
                        
                        setTimeout(() => {
                            const contentElement = frameDoc.getElementById(section.id + 'Content') || 
                                                 frameDoc.getElementById(section.id);
                            
                            if (contentElement && contentElement.style.display !== 'none') {
                                logResult(`✅ ${section.name}: المحتوى يظهر بشكل صحيح`, "success");
                                updateSectionStatus(section.name, "success", "يعمل بشكل صحيح");
                            } else {
                                logResult(`⚠️ ${section.name}: النقر يعمل لكن المحتوى لا يظهر`, "warning");
                                updateSectionStatus(section.name, "warning", "المحتوى لا يظهر");
                            }
                        }, 500);
                        
                    } catch (clickError) {
                        logResult(`❌ ${section.name}: خطأ في النقر - ${clickError.message}`, "error");
                        updateSectionStatus(section.name, "error", "خطأ في النقر");
                    }
                } else {
                    logResult(`❌ ${section.name}: العنصر غير موجود`, "error");
                    updateSectionStatus(section.name, "error", "غير موجود");
                }
                
            } catch (error) {
                logResult(`❌ ${section.name}: خطأ في الاختبار - ${error.message}`, "error");
                updateSectionStatus(section.name, "error", "خطأ في الاختبار");
            }
        }
        
        // Test frame sections
        function testFrameSections() {
            logResult("اختبار الأقسام في الإطار...");
            runFullTest();
        }
        
        // Open admin in new tab
        function openAdminInNewTab() {
            logResult("فتح لوحة التحكم في نافذة جديدة...");
            window.open('/admin/', '_blank');
        }
        
        // Reload admin frame
        function reloadAdminFrame() {
            logResult("إعادة تحميل إطار لوحة التحكم...");
            const frame = document.getElementById('adminFrame');
            frame.src = frame.src;
        }
        
        // Clear results
        function clearResults() {
            document.getElementById("testResults").innerHTML = "<div style='color: #28a745;'>🧹 تم مسح النتائج</div>";
            
            // Reset section statuses
            const statusElements = document.querySelectorAll("#sectionsStatus .section-status");
            statusElements.forEach(element => {
                element.className = "section-status";
                element.querySelector("span:last-child").textContent = "جاري الفحص...";
            });
        }
        
        // Auto-run quick test when page loads
        document.addEventListener("DOMContentLoaded", function() {
            logResult("تم تحميل صفحة الاختبار النهائي", "success");
            
            // Wait for admin frame to load, then run quick test
            setTimeout(() => {
                logResult("بدء الاختبار التلقائي...");
                runQuickTest();
            }, 3000);
        });
    </script>
</body>
</html>
