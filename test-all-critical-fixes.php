<?php
/**
 * Test All Critical Fixes
 * Comprehensive test of config paths, APIs, and JavaScript fixes
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار جميع الإصلاحات الحرجة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
        .test-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .test-failed { border-left-color: #dc3545; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .test-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .progress-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { background: #28a745; height: 100%; transition: width 0.3s ease; }
        .final-summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin: 30px 0; text-align: center; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🧪 اختبار جميع الإصلاحات الحرجة</h1>";
echo "<p>اختبار شامل لإصلاحات مسارات الملفات، APIs، و JavaScript</p>";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

try {
    // TEST 1: Config File Loading
    echo "<div class='section'>";
    echo "<h2>1️⃣ اختبار تحميل ملف التكوين</h2>";
    
    $totalTests++;
    try {
        require_once 'php/config.php';
        echo "<div class='success'>✅ تم تحميل ملف التكوين بنجاح</div>";
        $testResults['config_loading'] = true;
        $passedTests++;
    } catch (Exception $e) {
        echo "<div class='error'>❌ فشل في تحميل ملف التكوين: " . $e->getMessage() . "</div>";
        $testResults['config_loading'] = false;
    }
    echo "</div>";
    
    // TEST 2: Database Connection
    echo "<div class='section'>";
    echo "<h2>2️⃣ اختبار الاتصال بقاعدة البيانات</h2>";
    
    $totalTests++;
    try {
        $pdo = getPDOConnection();
        echo "<div class='success'>✅ الاتصال بقاعدة البيانات ناجح</div>";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch();
        echo "<div class='info'>عدد المستخدمين: {$result['count']}</div>";
        
        $testResults['database_connection'] = true;
        $passedTests++;
    } catch (Exception $e) {
        echo "<div class='error'>❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
        $testResults['database_connection'] = false;
    }
    echo "</div>";
    
    // TEST 3: API Files Syntax Check
    echo "<div class='section'>";
    echo "<h2>3️⃣ اختبار بناء جملة ملفات API</h2>";
    
    $apiFiles = [
        'php/api/templates.php',
        'php/api/landing-pages.php',
        'php/api/users.php',
        'php/api/subscription-limits.php'
    ];
    
    echo "<div class='test-grid'>";
    foreach ($apiFiles as $apiFile) {
        echo "<div class='test-card'>";
        echo "<h4>" . basename($apiFile) . "</h4>";
        
        $totalTests++;
        if (file_exists($apiFile)) {
            // Check syntax
            $output = [];
            $return_var = 0;
            exec("php -l \"{$apiFile}\" 2>&1", $output, $return_var);
            
            if ($return_var === 0) {
                echo "<div class='success'>✅ بناء الجملة صحيح</div>";
                $testResults['syntax_' . basename($apiFile)] = true;
                $passedTests++;
            } else {
                echo "<div class='error'>❌ خطأ في بناء الجملة</div>";
                echo "<div class='warning'>" . implode('<br>', $output) . "</div>";
                $testResults['syntax_' . basename($apiFile)] = false;
            }
        } else {
            echo "<div class='error'>❌ الملف غير موجود</div>";
            $testResults['syntax_' . basename($apiFile)] = false;
        }
        
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    // TEST 4: API Functionality Test
    echo "<div class='section'>";
    echo "<h2>4️⃣ اختبار وظائف APIs</h2>";
    
    echo "<div class='test-grid'>";
    
    // Test Templates API
    echo "<div class='test-card'>";
    echo "<h4>Templates API</h4>";
    $totalTests++;
    try {
        $_GET['action'] = 'get_templates';
        ob_start();
        include 'php/api/templates.php';
        $output = ob_get_clean();
        
        $data = json_decode($output, true);
        if ($data && isset($data['success'])) {
            echo "<div class='success'>✅ Templates API يعمل</div>";
            echo "<div class='info'>عدد القوالب: " . count($data['templates']) . "</div>";
            $testResults['templates_api'] = true;
            $passedTests++;
        } else {
            echo "<div class='error'>❌ استجابة غير صحيحة</div>";
            $testResults['templates_api'] = false;
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
        $testResults['templates_api'] = false;
    }
    echo "</div>";
    
    // Test Subscription Limits
    echo "<div class='test-card'>";
    echo "<h4>Subscription Limits</h4>";
    $totalTests++;
    try {
        require_once 'php/SubscriptionLimits.php';
        $limitsManager = new SubscriptionLimits();
        
        // Get demo user
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $demoUser = $stmt->fetch();
        
        if ($demoUser) {
            $usage = $limitsManager->getUserUsage($demoUser['id']);
            echo "<div class='success'>✅ Subscription Limits يعمل</div>";
            echo "<div class='info'>المنتجات: {$usage['products']}</div>";
            $testResults['subscription_limits'] = true;
            $passedTests++;
        } else {
            echo "<div class='warning'>⚠️ المستخدم التجريبي غير موجود</div>";
            $testResults['subscription_limits'] = false;
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
        $testResults['subscription_limits'] = false;
    }
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // TEST 5: JavaScript Files Check
    echo "<div class='section'>";
    echo "<h2>5️⃣ اختبار ملفات JavaScript</h2>";
    
    $jsFiles = [
        'admin/js/admin.js' => 'ملف الإدارة الرئيسي',
        'admin/js/landing-pages.js' => 'إدارة صفحات الهبوط',
        'admin/js/landing-pages-enhanced-fixed.js' => 'إدارة صفحات الهبوط المحسن',
        'admin/js/selection-error-fix.js' => 'إصلاح أخطاء التحديد'
    ];
    
    echo "<div class='test-grid'>";
    foreach ($jsFiles as $jsFile => $description) {
        echo "<div class='test-card'>";
        echo "<h4>{$description}</h4>";
        
        $totalTests++;
        if (file_exists($jsFile)) {
            $content = file_get_contents($jsFile);
            $size = filesize($jsFile);
            
            echo "<div class='success'>✅ الملف موجود</div>";
            echo "<div class='info'>الحجم: " . number_format($size) . " بايت</div>";
            
            // Check for common JavaScript issues
            $issues = [];
            
            // Check for async/await issues
            if (preg_match('/await\s+(?!.*async\s+function)/m', $content)) {
                $issues[] = "استخدام await خارج دالة async";
            }
            
            // Check for duplicate async keywords
            if (preg_match('/async\s+async\s+function/m', $content)) {
                $issues[] = "كلمة async مكررة";
            }
            
            if (empty($issues)) {
                echo "<div class='success'>✅ لا توجد مشاكل واضحة</div>";
                $testResults['js_' . basename($jsFile)] = true;
                $passedTests++;
            } else {
                echo "<div class='warning'>⚠️ مشاكل محتملة:</div>";
                foreach ($issues as $issue) {
                    echo "<div class='warning'>- {$issue}</div>";
                }
                $testResults['js_' . basename($jsFile)] = false;
            }
        } else {
            echo "<div class='error'>❌ الملف غير موجود</div>";
            $testResults['js_' . basename($jsFile)] = false;
        }
        
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    // TEST 6: Enhanced Files Integration
    echo "<div class='section'>";
    echo "<h2>6️⃣ اختبار تكامل الملفات المحسنة</h2>";
    
    $enhancedFiles = [
        'admin/js/landing-pages-enhanced-fixed.js' => 'JavaScript محسن لصفحات الهبوط',
        'admin/css/landing-pages-enhanced-fixed.css' => 'CSS محسن لصفحات الهبوط'
    ];
    
    foreach ($enhancedFiles as $file => $description) {
        echo "<div class='test-result'>";
        echo "<h4>{$description}</h4>";
        
        $totalTests++;
        if (file_exists($file)) {
            $size = filesize($file);
            echo "<div class='success'>✅ الملف موجود - الحجم: " . number_format($size) . " بايت</div>";
            $testResults['enhanced_' . basename($file)] = true;
            $passedTests++;
        } else {
            echo "<div class='error'>❌ الملف غير موجود</div>";
            $testResults['enhanced_' . basename($file)] = false;
        }
        
        echo "</div>";
    }
    echo "</div>";
    
    // FINAL RESULTS
    $successRate = ($passedTests / $totalTests) * 100;
    
    echo "<div class='final-summary'>";
    echo "<h2>🎯 النتائج النهائية</h2>";
    echo "<h3>نسبة النجاح: " . round($successRate, 1) . "%</h3>";
    echo "<p>({$passedTests}/{$totalTests} اختبارات نجحت)</p>";
    
    echo "<div class='progress-bar'>";
    echo "<div class='progress-fill' style='width: " . $successRate . "%'></div>";
    echo "</div>";
    
    if ($successRate >= 90) {
        echo "<h4>🎉 ممتاز! جميع الإصلاحات تعمل بشكل مثالي</h4>";
    } elseif ($successRate >= 75) {
        echo "<h4>✅ جيد! معظم الإصلاحات تعمل بشكل صحيح</h4>";
    } elseif ($successRate >= 50) {
        echo "<h4>⚠️ مقبول! بعض الإصلاحات تحتاج إلى مراجعة</h4>";
    } else {
        echo "<h4>❌ يحتاج النظام إلى إصلاحات إضافية</h4>";
    }
    echo "</div>";
    
    // SUMMARY OF FIXES
    echo "<div class='section'>";
    echo "<h2>✅ ملخص الإصلاحات المطبقة</h2>";
    
    echo "<div class='success'>";
    echo "<h3>🔧 الإصلاحات المكتملة:</h3>";
    echo "<ul>";
    echo "<li>✅ إصلاح مسارات ملفات config.php في جميع APIs</li>";
    echo "<li>✅ إصلاح أخطاء async/await في JavaScript</li>";
    echo "<li>✅ إزالة الدوال المكررة والكود المنفصل</li>";
    echo "<li>✅ إنشاء ملفات JavaScript و CSS محسنة</li>";
    echo "<li>✅ إصلاح أخطاء التحديد (selection errors)</li>";
    echo "<li>✅ تحسين معالجة الأخطاء في APIs</li>";
    echo "<li>✅ ضمان ظهور عناصر الواجهة</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🚀 للاختبار النهائي:</h4>";
    echo "<ol>";
    echo "<li>افتح لوحة التحكم الإدارية: <a href='/admin/' target='_blank'>/admin/</a></li>";
    echo "<li>اختبر قسم صفحات الهبوط</li>";
    echo "<li>تحقق من عدم وجود أخطاء في وحدة التحكم</li>";
    echo "<li>اختبر إضافة صفحة هبوط جديدة</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام في الاختبار: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
