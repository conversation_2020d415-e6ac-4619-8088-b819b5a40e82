<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - متجر مصعب</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="auth-box">
            <div class="auth-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <h1>متجر مصعب</h1>
                </div>
                <h2>تسجيل الدخول</h2>
                <p>مرحباً بك مرة أخرى! سجل دخولك للمتابعة</p>
            </div>

            <form id="loginForm" class="auth-form">
                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i>
                        البريد الإلكتروني
                    </label>
                    <input type="email" id="email" name="email" required placeholder="أدخل بريدك الإلكتروني">
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="password-input">
                        <input type="password" id="password" name="password" required placeholder="أدخل كلمة المرور">
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                </div>

                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>

                <button type="submit" class="auth-button" id="loginButton">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>

            <div class="auth-divider">
                <span>أو</span>
            </div>

            <div class="auth-links">
                <p>ليس لديك حساب؟ <a href="register.html">إنشاء حساب جديد</a></p>
            </div>

            <div class="demo-account">
                <h4>حساب تجريبي</h4>
                <p>للتجربة السريعة، يمكنك استخدام:</p>
                <div class="demo-credentials">
                    <p><strong>البريد:</strong> <EMAIL></p>
                    <p><strong>كلمة المرور:</strong> demo123</p>
                </div>
                <button type="button" class="demo-login-btn" onclick="fillDemoCredentials()">
                    <i class="fas fa-user-cog"></i>
                    تسجيل دخول تجريبي
                </button>
            </div>

            <div class="back-to-site">
                <a href="index.html">
                    <i class="fas fa-arrow-right"></i>
                    العودة إلى المتجر
                </a>
            </div>
        </div>
    </div>

    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }

        // Fill demo credentials
        function fillDemoCredentials() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'demo123';
        }

        // Handle form submission
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');
            const loginButton = document.getElementById('loginButton');

            // Clear previous messages
            errorDiv.textContent = '';
            successDiv.textContent = '';

            // Validate inputs
            if (!email || !password) {
                errorDiv.textContent = 'جميع الحقول مطلوبة';
                return;
            }

            // Show loading state
            loginButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
            loginButton.disabled = true;

            try {
                const response = await fetch('php/api/user-auth.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });

                const data = await response.json();

                if (data.success) {
                    successDiv.textContent = data.message;
                    
                    // Redirect after successful login
                    setTimeout(() => {
                        const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 'index.html';
                        window.location.href = redirectUrl;
                    }, 1000);
                } else {
                    errorDiv.textContent = data.message || 'حدث خطأ في تسجيل الدخول';
                }
            } catch (error) {
                console.error('Login error:', error);
                errorDiv.textContent = 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';
            } finally {
                // Reset button state
                loginButton.innerHTML = '<i class="fas fa-sign-in-alt"></i> تسجيل الدخول';
                loginButton.disabled = false;
            }
        });

        // Check if user is already logged in
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                const response = await fetch('php/api/user-auth.php?action=check');
                const data = await response.json();
                
                if (data.success && data.logged_in) {
                    // User is already logged in, redirect
                    const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 'index.html';
                    window.location.href = redirectUrl;
                }
            } catch (error) {
                console.error('Auth check error:', error);
            }
        });
    </script>
</body>
</html>
