<?php
class EnvLoader
{
    private static $variables = [];

    public static function load($path = null)
    {
        if ($path === null) {
            $path = dirname(__DIR__) . '/.env';
        }

        if (!file_exists($path)) {
            throw new Exception('.env file not found');
        }

        $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            // <PERSON><PERSON> comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }

            // Parse variable
            if (strpos($line, '=') !== false) {
                list($name, $value) = array_map('trim', explode('=', $line, 2));
                if (!empty($name)) {
                    // Remove quotes if present
                    $value = trim($value, '"\'');
                    self::$variables[$name] = $value;

                    // Also set in environment
                    if (!getenv($name)) {
                        putenv("$name=$value");
                    }
                }
            }
        }

        return self::$variables;
    }

    public static function get($key, $default = null)
    {
        return self::$variables[$key] ?? getenv($key) ?: $default;
    }
}
