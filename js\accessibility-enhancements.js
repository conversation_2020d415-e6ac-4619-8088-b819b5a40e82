/**
 * Accessibility Enhancements for WCAG 2.1 AA Compliance
 * Specialized for Arabic RTL e-commerce site
 */

class AccessibilityEnhancements {
    constructor() {
        this.init();
    }

    init() {
        this.setupKeyboardNavigation();
        this.setupScreenReaderSupport();
        this.setupColorContrastEnhancements();
        this.setupFocusManagement();
        this.setupAriaLabels();
        this.setupLiveRegions();
        this.setupSkipLinks();
        this.setupFormAccessibility();
    }

    /**
     * Setup keyboard navigation for RTL layout
     */
    setupKeyboardNavigation() {
        // Handle arrow key navigation in product grids
        const productGrids = document.querySelectorAll('.books-grid, .products-grid');
        
        productGrids.forEach(grid => {
            const products = grid.querySelectorAll('.book-card, .product-card');
            
            products.forEach((product, index) => {
                product.setAttribute('tabindex', '0');
                product.setAttribute('role', 'button');
                product.setAttribute('aria-label', this.getProductAriaLabel(product));
                
                product.addEventListener('keydown', (e) => {
                    this.handleProductGridNavigation(e, products, index);
                });
            });
        });

        // Handle RTL-aware navigation in admin tables
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            this.setupTableNavigation(table);
        });

        // Setup modal keyboard navigation
        this.setupModalKeyboardNavigation();
    }

    /**
     * Get appropriate ARIA label for product
     */
    getProductAriaLabel(product) {
        const title = product.querySelector('h3')?.textContent || '';
        const price = product.querySelector('.price')?.textContent || '';
        const author = product.querySelector('.author')?.textContent || '';
        
        let label = `منتج: ${title}`;
        if (author) label += `، المؤلف: ${author}`;
        if (price) label += `، السعر: ${price}`;
        label += '. اضغط Enter للعرض أو Space للإضافة إلى السلة';
        
        return label;
    }

    /**
     * Handle keyboard navigation in product grids (RTL-aware)
     */
    handleProductGridNavigation(event, products, currentIndex) {
        const gridContainer = products[0].parentElement;
        const computedStyle = window.getComputedStyle(gridContainer);
        const columns = computedStyle.gridTemplateColumns.split(' ').length;
        
        let newIndex = currentIndex;
        
        switch (event.key) {
            case 'ArrowRight':
                // In RTL, right arrow goes to previous item
                newIndex = currentIndex > 0 ? currentIndex - 1 : products.length - 1;
                break;
            case 'ArrowLeft':
                // In RTL, left arrow goes to next item
                newIndex = currentIndex < products.length - 1 ? currentIndex + 1 : 0;
                break;
            case 'ArrowUp':
                newIndex = currentIndex - columns;
                if (newIndex < 0) newIndex = currentIndex;
                break;
            case 'ArrowDown':
                newIndex = currentIndex + columns;
                if (newIndex >= products.length) newIndex = currentIndex;
                break;
            case 'Home':
                newIndex = 0;
                break;
            case 'End':
                newIndex = products.length - 1;
                break;
            case 'Enter':
            case ' ':
                event.preventDefault();
                products[currentIndex].click();
                return;
        }
        
        if (newIndex !== currentIndex) {
            event.preventDefault();
            products[newIndex].focus();
        }
    }

    /**
     * Setup table navigation for admin panel
     */
    setupTableNavigation(table) {
        const cells = table.querySelectorAll('td, th');
        const rows = table.querySelectorAll('tr');
        
        cells.forEach(cell => {
            cell.setAttribute('tabindex', '0');
            
            cell.addEventListener('keydown', (e) => {
                const currentRow = cell.parentElement;
                const currentRowIndex = Array.from(rows).indexOf(currentRow);
                const currentCellIndex = Array.from(currentRow.children).indexOf(cell);
                
                let targetCell = null;
                
                switch (e.key) {
                    case 'ArrowRight':
                        // RTL: right goes to previous cell
                        targetCell = currentCellIndex > 0 ? 
                            currentRow.children[currentCellIndex - 1] : null;
                        break;
                    case 'ArrowLeft':
                        // RTL: left goes to next cell
                        targetCell = currentCellIndex < currentRow.children.length - 1 ? 
                            currentRow.children[currentCellIndex + 1] : null;
                        break;
                    case 'ArrowUp':
                        if (currentRowIndex > 0) {
                            const targetRow = rows[currentRowIndex - 1];
                            targetCell = targetRow.children[currentCellIndex] || targetRow.lastElementChild;
                        }
                        break;
                    case 'ArrowDown':
                        if (currentRowIndex < rows.length - 1) {
                            const targetRow = rows[currentRowIndex + 1];
                            targetCell = targetRow.children[currentCellIndex] || targetRow.lastElementChild;
                        }
                        break;
                }
                
                if (targetCell) {
                    e.preventDefault();
                    targetCell.focus();
                }
            });
        });
    }

    /**
     * Setup modal keyboard navigation
     */
    setupModalKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            const activeModal = document.querySelector('.modal.active, .modal[style*="display: block"]');
            
            if (activeModal && e.key === 'Tab') {
                this.trapFocusInModal(e, activeModal);
            }
        });
    }

    /**
     * Trap focus within modal
     */
    trapFocusInModal(event, modal) {
        const focusableElements = modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (event.shiftKey) {
            if (document.activeElement === firstElement) {
                event.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                event.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * Setup screen reader support
     */
    setupScreenReaderSupport() {
        // Add proper headings hierarchy
        this.fixHeadingHierarchy();
        
        // Add landmarks
        this.addLandmarks();
        
        // Improve image alt texts
        this.improveImageAltTexts();
        
        // Add descriptions for complex UI elements
        this.addComplexUIDescriptions();
    }

    /**
     * Fix heading hierarchy for screen readers
     */
    fixHeadingHierarchy() {
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        let currentLevel = 1;
        
        headings.forEach(heading => {
            const level = parseInt(heading.tagName.charAt(1));
            
            // Ensure logical heading progression
            if (level > currentLevel + 1) {
                console.warn(`Heading level jump detected: h${currentLevel} to h${level}`);
                // Could automatically fix this, but better to log for manual review
            }
            
            currentLevel = level;
            
            // Add Arabic text direction if not set
            if (!heading.getAttribute('dir')) {
                heading.setAttribute('dir', 'rtl');
            }
        });
    }

    /**
     * Add ARIA landmarks
     */
    addLandmarks() {
        // Main navigation
        const nav = document.querySelector('nav, .nav, .navigation');
        if (nav && !nav.getAttribute('role')) {
            nav.setAttribute('role', 'navigation');
            nav.setAttribute('aria-label', 'التنقل الرئيسي');
        }
        
        // Main content
        const main = document.querySelector('main, .main-content, #main-content');
        if (main && !main.getAttribute('role')) {
            main.setAttribute('role', 'main');
            main.setAttribute('aria-label', 'المحتوى الرئيسي');
        }
        
        // Search
        const search = document.querySelector('.search, [role="search"]');
        if (search && !search.getAttribute('role')) {
            search.setAttribute('role', 'search');
            search.setAttribute('aria-label', 'البحث');
        }
        
        // Footer
        const footer = document.querySelector('footer, .footer');
        if (footer && !footer.getAttribute('role')) {
            footer.setAttribute('role', 'contentinfo');
            footer.setAttribute('aria-label', 'معلومات الموقع');
        }
    }

    /**
     * Improve image alt texts for Arabic content
     */
    improveImageAltTexts() {
        const images = document.querySelectorAll('img');
        
        images.forEach(img => {
            if (!img.getAttribute('alt')) {
                // Try to get alt text from context
                const figcaption = img.closest('figure')?.querySelector('figcaption');
                const title = img.closest('.book-card, .product-card')?.querySelector('h3');
                
                if (figcaption) {
                    img.setAttribute('alt', figcaption.textContent);
                } else if (title) {
                    img.setAttribute('alt', `صورة ${title.textContent}`);
                } else if (img.src.includes('product') || img.src.includes('book')) {
                    img.setAttribute('alt', 'صورة منتج');
                } else {
                    img.setAttribute('alt', 'صورة');
                }
            }
            
            // Mark decorative images
            if (img.getAttribute('alt') === '' || img.closest('.decoration, .bg-image')) {
                img.setAttribute('role', 'presentation');
                img.setAttribute('aria-hidden', 'true');
            }
        });
    }

    /**
     * Add descriptions for complex UI elements
     */
    addComplexUIDescriptions() {
        // Shopping cart
        const cartIcon = document.querySelector('.cart-icon, .cart-button');
        if (cartIcon) {
            cartIcon.setAttribute('aria-label', 'عربة التسوق');
            
            const cartCount = cartIcon.querySelector('.cart-count');
            if (cartCount) {
                cartCount.setAttribute('aria-label', `${cartCount.textContent} عنصر في السلة`);
            }
        }
        
        // Product ratings
        const ratings = document.querySelectorAll('.rating, .stars');
        ratings.forEach(rating => {
            const stars = rating.querySelectorAll('.star, .fa-star');
            const filledStars = rating.querySelectorAll('.star.filled, .fa-star.filled');
            const ratingValue = filledStars.length;
            const totalStars = stars.length;
            
            rating.setAttribute('role', 'img');
            rating.setAttribute('aria-label', `تقييم ${ratingValue} من ${totalStars} نجوم`);
        });
        
        // Price displays
        const prices = document.querySelectorAll('.price, .cost');
        prices.forEach(price => {
            if (!price.getAttribute('aria-label')) {
                price.setAttribute('aria-label', `السعر ${price.textContent}`);
            }
        });
    }

    /**
     * Setup color contrast enhancements
     */
    setupColorContrastEnhancements() {
        // Check and improve color contrast ratios
        this.checkColorContrast();
        
        // Add high contrast mode support
        this.addHighContrastSupport();
    }

    /**
     * Check color contrast ratios
     */
    checkColorContrast() {
        const elementsToCheck = document.querySelectorAll('p, span, a, button, h1, h2, h3, h4, h5, h6');
        
        elementsToCheck.forEach(element => {
            const styles = window.getComputedStyle(element);
            const color = styles.color;
            const backgroundColor = styles.backgroundColor;
            
            // Simple contrast check (would need more sophisticated implementation for production)
            if (this.isLowContrast(color, backgroundColor)) {
                console.warn('Low contrast detected:', element, { color, backgroundColor });
                // Could automatically adjust colors here
            }
        });
    }

    /**
     * Simple contrast check (placeholder implementation)
     */
    isLowContrast(color, backgroundColor) {
        // This is a simplified check - real implementation would calculate actual contrast ratios
        return false; // Placeholder
    }

    /**
     * Add high contrast mode support
     */
    addHighContrastSupport() {
        // Detect if user prefers high contrast
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            document.body.classList.add('high-contrast');
        }
        
        // Add toggle for high contrast mode
        const contrastToggle = document.createElement('button');
        contrastToggle.textContent = 'تبديل التباين العالي';
        contrastToggle.className = 'contrast-toggle';
        contrastToggle.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 9999;
            background: #000;
            color: #fff;
            border: 2px solid #fff;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
        `;
        
        contrastToggle.addEventListener('click', () => {
            document.body.classList.toggle('high-contrast');
            const isHighContrast = document.body.classList.contains('high-contrast');
            localStorage.setItem('highContrast', isHighContrast);
        });
        
        // Restore saved preference
        if (localStorage.getItem('highContrast') === 'true') {
            document.body.classList.add('high-contrast');
        }
        
        document.body.appendChild(contrastToggle);
    }

    /**
     * Setup focus management
     */
    setupFocusManagement() {
        // Improve focus indicators
        const style = document.createElement('style');
        style.textContent = `
            *:focus {
                outline: 3px solid #0066cc !important;
                outline-offset: 2px !important;
            }
            
            .high-contrast *:focus {
                outline: 4px solid #ffff00 !important;
                outline-offset: 3px !important;
            }
            
            /* Skip to content link */
            .skip-link {
                position: absolute;
                top: -40px;
                left: 6px;
                background: #000;
                color: #fff;
                padding: 8px;
                text-decoration: none;
                border-radius: 4px;
                z-index: 9999;
                transition: top 0.3s ease;
            }
            
            .skip-link:focus {
                top: 6px;
            }
        `;
        document.head.appendChild(style);
        
        // Manage focus for dynamic content
        this.setupDynamicFocusManagement();
    }

    /**
     * Setup dynamic focus management
     */
    setupDynamicFocusManagement() {
        // Focus management for AJAX content updates
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Focus first interactive element in new content
                            const firstInteractive = node.querySelector('button, a, input, select, textarea, [tabindex]');
                            if (firstInteractive) {
                                firstInteractive.focus();
                            }
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Setup comprehensive ARIA labels
     */
    setupAriaLabels() {
        // Form controls
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(control => {
            if (!control.getAttribute('aria-label') && !control.getAttribute('aria-labelledby')) {
                const label = control.closest('.form-group')?.querySelector('label');
                if (label) {
                    const labelId = 'label-' + Math.random().toString(36).substr(2, 9);
                    label.id = labelId;
                    control.setAttribute('aria-labelledby', labelId);
                }
            }
        });
        
        // Buttons without text
        const buttons = document.querySelectorAll('button:not([aria-label])');
        buttons.forEach(button => {
            if (!button.textContent.trim()) {
                const icon = button.querySelector('i, svg');
                if (icon) {
                    button.setAttribute('aria-label', this.getButtonAriaLabel(icon));
                }
            }
        });
    }

    /**
     * Get appropriate ARIA label for button based on icon
     */
    getButtonAriaLabel(icon) {
        const iconClass = icon.className.toLowerCase();
        
        if (iconClass.includes('cart')) return 'إضافة إلى السلة';
        if (iconClass.includes('heart') || iconClass.includes('favorite')) return 'إضافة إلى المفضلة';
        if (iconClass.includes('share')) return 'مشاركة';
        if (iconClass.includes('edit')) return 'تعديل';
        if (iconClass.includes('delete') || iconClass.includes('trash')) return 'حذف';
        if (iconClass.includes('close') || iconClass.includes('times')) return 'إغلاق';
        if (iconClass.includes('menu')) return 'القائمة';
        if (iconClass.includes('search')) return 'بحث';
        
        return 'زر';
    }

    /**
     * Setup live regions for dynamic content
     */
    setupLiveRegions() {
        // Create live region for announcements
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        liveRegion.id = 'live-region';
        liveRegion.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(liveRegion);
        
        // Create alert region for urgent messages
        const alertRegion = document.createElement('div');
        alertRegion.setAttribute('aria-live', 'assertive');
        alertRegion.setAttribute('aria-atomic', 'true');
        alertRegion.className = 'sr-only';
        alertRegion.id = 'alert-region';
        alertRegion.style.cssText = liveRegion.style.cssText;
        document.body.appendChild(alertRegion);
        
        // Setup cart update announcements
        this.setupCartAnnouncements();
    }

    /**
     * Setup cart update announcements
     */
    setupCartAnnouncements() {
        // Monitor cart changes
        const cartObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.target.classList.contains('cart-count')) {
                    const count = mutation.target.textContent;
                    this.announce(`تم تحديث السلة. العدد الحالي: ${count} عنصر`);
                }
            });
        });
        
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            cartObserver.observe(cartCount, {
                childList: true,
                characterData: true,
                subtree: true
            });
        }
    }

    /**
     * Announce message to screen readers
     */
    announce(message, isUrgent = false) {
        const regionId = isUrgent ? 'alert-region' : 'live-region';
        const region = document.getElementById(regionId);
        
        if (region) {
            region.textContent = message;
            
            // Clear after announcement
            setTimeout(() => {
                region.textContent = '';
            }, 1000);
        }
    }

    /**
     * Setup skip links
     */
    setupSkipLinks() {
        const skipLinks = [
            { href: '#main-content', text: 'تخطي إلى المحتوى الرئيسي' },
            { href: '#navigation', text: 'تخطي إلى التنقل' },
            { href: '#search', text: 'تخطي إلى البحث' }
        ];
        
        const skipContainer = document.createElement('div');
        skipContainer.className = 'skip-links';
        
        skipLinks.forEach(link => {
            const skipLink = document.createElement('a');
            skipLink.href = link.href;
            skipLink.textContent = link.text;
            skipLink.className = 'skip-link';
            skipContainer.appendChild(skipLink);
        });
        
        document.body.insertBefore(skipContainer, document.body.firstChild);
    }

    /**
     * Setup form accessibility
     */
    setupFormAccessibility() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            // Add form description
            if (!form.getAttribute('aria-describedby')) {
                const description = document.createElement('div');
                description.id = 'form-description-' + Math.random().toString(36).substr(2, 9);
                description.className = 'sr-only';
                description.textContent = 'استخدم Tab للتنقل بين الحقول، Enter للإرسال';
                form.insertBefore(description, form.firstChild);
                form.setAttribute('aria-describedby', description.id);
            }
            
            // Improve error handling
            this.setupFormErrorHandling(form);
        });
    }

    /**
     * Setup accessible form error handling
     */
    setupFormErrorHandling(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            input.addEventListener('invalid', (e) => {
                e.preventDefault();
                
                // Create error message
                const errorId = 'error-' + Math.random().toString(36).substr(2, 9);
                const errorDiv = document.createElement('div');
                errorDiv.id = errorId;
                errorDiv.className = 'error-message';
                errorDiv.setAttribute('role', 'alert');
                errorDiv.style.color = '#d32f2f';
                errorDiv.textContent = input.validationMessage || 'هذا الحقل مطلوب';
                
                // Remove existing error
                const existingError = input.parentNode.querySelector('.error-message');
                if (existingError) {
                    existingError.remove();
                }
                
                // Add new error
                input.parentNode.insertBefore(errorDiv, input.nextSibling);
                input.setAttribute('aria-describedby', errorId);
                input.setAttribute('aria-invalid', 'true');
                
                // Announce error
                this.announce(`خطأ في ${input.getAttribute('aria-label') || 'الحقل'}: ${errorDiv.textContent}`, true);
                
                // Remove error on valid input
                input.addEventListener('input', () => {
                    if (input.validity.valid) {
                        errorDiv.remove();
                        input.removeAttribute('aria-describedby');
                        input.setAttribute('aria-invalid', 'false');
                    }
                }, { once: true });
            });
        });
    }
}

// Initialize accessibility enhancements
document.addEventListener('DOMContentLoaded', () => {
    new AccessibilityEnhancements();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AccessibilityEnhancements;
}
