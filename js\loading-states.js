/**
 * Loading States Manager
 * Provides spinners, skeleton screens, and loading indicators for better UX
 * Optimized for Arabic RTL layout
 */

class LoadingStatesManager {
    constructor() {
        this.activeLoaders = new Set();
        this.init();
    }

    init() {
        this.createLoadingStyles();
        this.setupAPILoadingStates();
        this.setupFormLoadingStates();
        this.setupImageLoadingStates();
        this.setupSkeletonScreens();
    }

    /**
     * Create CSS styles for loading states
     */
    createLoadingStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* Loading Spinner Styles */
            .loading-spinner {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 2px solid #f3f3f3;
                border-top: 2px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-left: 8px;
            }
            
            .loading-spinner.large {
                width: 40px;
                height: 40px;
                border-width: 4px;
            }
            
            .loading-spinner.rtl {
                margin-left: 0;
                margin-right: 8px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            /* Loading Overlay */
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                backdrop-filter: blur(2px);
            }
            
            .loading-overlay .loading-content {
                text-align: center;
                padding: 2rem;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                direction: rtl;
            }
            
            .loading-overlay .loading-spinner {
                width: 50px;
                height: 50px;
                border-width: 4px;
                margin: 0 auto 1rem;
            }
            
            .loading-overlay .loading-text {
                font-size: 1.1rem;
                color: #2c3e50;
                margin: 0;
            }
            
            /* Skeleton Screen Styles */
            .skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading 1.5s infinite;
                border-radius: 4px;
            }
            
            @keyframes loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }
            
            .skeleton-text {
                height: 1rem;
                margin-bottom: 0.5rem;
            }
            
            .skeleton-text.title {
                height: 1.5rem;
                width: 70%;
            }
            
            .skeleton-text.subtitle {
                height: 1rem;
                width: 50%;
            }
            
            .skeleton-text.line {
                height: 0.875rem;
                width: 90%;
            }
            
            .skeleton-image {
                width: 100%;
                height: 200px;
                margin-bottom: 1rem;
            }
            
            .skeleton-button {
                height: 2.5rem;
                width: 120px;
                margin: 0.5rem 0;
            }
            
            /* Product Card Skeleton */
            .skeleton-product-card {
                background: white;
                border-radius: 8px;
                padding: 1rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                margin-bottom: 1rem;
            }
            
            /* Table Loading */
            .table-loading {
                position: relative;
            }
            
            .table-loading::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            /* Button Loading States */
            .btn-loading {
                position: relative;
                pointer-events: none;
                opacity: 0.7;
            }
            
            .btn-loading .btn-text {
                opacity: 0;
            }
            
            .btn-loading::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 16px;
                height: 16px;
                border: 2px solid transparent;
                border-top: 2px solid currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            /* Form Loading */
            .form-loading {
                position: relative;
                pointer-events: none;
                opacity: 0.6;
            }
            
            .form-loading::after {
                content: 'جاري التحميل...';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 255, 255, 0.95);
                padding: 1rem 2rem;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                font-weight: 600;
                color: #2c3e50;
                direction: rtl;
            }
            
            /* Image Loading */
            .image-loading {
                position: relative;
                background: #f8f9fa;
            }
            
            .image-loading::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 30px;
                height: 30px;
                border: 3px solid #e0e0e0;
                border-top: 3px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                z-index: 1;
            }
            
            .image-loading img {
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            .image-loaded img {
                opacity: 1;
            }
            
            /* Progress Bar */
            .progress-bar {
                width: 100%;
                height: 4px;
                background: #e0e0e0;
                border-radius: 2px;
                overflow: hidden;
                margin: 1rem 0;
            }
            
            .progress-bar-fill {
                height: 100%;
                background: linear-gradient(90deg, #3498db, #2ecc71);
                border-radius: 2px;
                transition: width 0.3s ease;
                animation: progress-shimmer 2s infinite;
            }
            
            @keyframes progress-shimmer {
                0% { background-position: -200px 0; }
                100% { background-position: calc(200px + 100%) 0; }
            }
            
            /* Pulse Animation for Loading Elements */
            .pulse {
                animation: pulse 2s infinite;
            }
            
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }
            
            /* RTL Specific Adjustments */
            [dir="rtl"] .loading-spinner {
                margin-left: 0;
                margin-right: 8px;
            }
            
            [dir="rtl"] .btn-loading::after {
                left: auto;
                right: 50%;
                transform: translate(50%, -50%);
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Show loading spinner
     */
    showSpinner(element, size = 'normal', text = 'جاري التحميل...') {
        const loaderId = 'loader-' + Math.random().toString(36).substr(2, 9);
        this.activeLoaders.add(loaderId);

        const spinner = document.createElement('div');
        spinner.className = `loading-spinner ${size} rtl`;
        spinner.setAttribute('data-loader-id', loaderId);
        spinner.setAttribute('aria-label', text);
        spinner.setAttribute('role', 'status');

        if (element) {
            element.appendChild(spinner);
        }

        return loaderId;
    }

    /**
     * Hide loading spinner
     */
    hideSpinner(loaderId) {
        const spinner = document.querySelector(`[data-loader-id="${loaderId}"]`);
        if (spinner) {
            spinner.remove();
            this.activeLoaders.delete(loaderId);
        }
    }

    /**
     * Show full page loading overlay
     */
    showOverlay(text = 'جاري التحميل...') {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.id = 'global-loading-overlay';
        overlay.setAttribute('aria-label', text);
        overlay.setAttribute('role', 'status');

        overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner large"></div>
                <p class="loading-text">${text}</p>
            </div>
        `;

        document.body.appendChild(overlay);
        document.body.style.overflow = 'hidden';

        return 'global-loading-overlay';
    }

    /**
     * Hide loading overlay
     */
    hideOverlay() {
        const overlay = document.getElementById('global-loading-overlay');
        if (overlay) {
            overlay.remove();
            document.body.style.overflow = '';
        }
    }

    /**
     * Create skeleton screen for product grid
     */
    createProductSkeleton(container, count = 6) {
        const skeletonGrid = document.createElement('div');
        skeletonGrid.className = 'books-grid skeleton-grid';
        skeletonGrid.setAttribute('aria-label', 'جاري تحميل المنتجات');

        for (let i = 0; i < count; i++) {
            const skeletonCard = document.createElement('div');
            skeletonCard.className = 'skeleton-product-card';
            skeletonCard.innerHTML = `
                <div class="skeleton skeleton-image"></div>
                <div class="skeleton skeleton-text title"></div>
                <div class="skeleton skeleton-text subtitle"></div>
                <div class="skeleton skeleton-text line"></div>
                <div class="skeleton skeleton-button"></div>
            `;
            skeletonGrid.appendChild(skeletonCard);
        }

        container.appendChild(skeletonGrid);
        return skeletonGrid;
    }

    /**
     * Create skeleton screen for table
     */
    createTableSkeleton(table, rows = 5, cols = 4) {
        const tbody = table.querySelector('tbody') || table;
        tbody.innerHTML = '';

        for (let i = 0; i < rows; i++) {
            const row = document.createElement('tr');
            for (let j = 0; j < cols; j++) {
                const cell = document.createElement('td');
                cell.innerHTML = '<div class="skeleton skeleton-text"></div>';
                row.appendChild(cell);
            }
            tbody.appendChild(row);
        }
    }

    /**
     * Setup API loading states
     */
    setupAPILoadingStates() {
        // Intercept fetch requests to show loading states
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            const url = args[0];
            let loadingElement = null;
            
            // Determine which loading state to show based on URL
            if (url.includes('/api/products')) {
                loadingElement = document.querySelector('.books-grid');
                if (loadingElement) {
                    this.showProductsLoading(loadingElement);
                }
            } else if (url.includes('/api/')) {
                // Show general loading for other API calls
                this.showOverlay('جاري تحميل البيانات...');
            }
            
            try {
                const response = await originalFetch(...args);
                
                // Hide loading states
                if (url.includes('/api/products') && loadingElement) {
                    this.hideProductsLoading(loadingElement);
                } else if (url.includes('/api/')) {
                    this.hideOverlay();
                }
                
                return response;
            } catch (error) {
                // Hide loading states on error
                if (url.includes('/api/products') && loadingElement) {
                    this.hideProductsLoading(loadingElement);
                } else if (url.includes('/api/')) {
                    this.hideOverlay();
                }
                throw error;
            }
        };
    }

    /**
     * Show products loading state
     */
    showProductsLoading(container) {
        const existingContent = container.innerHTML;
        container.setAttribute('data-original-content', existingContent);
        container.innerHTML = '';
        this.createProductSkeleton(container);
    }

    /**
     * Hide products loading state
     */
    hideProductsLoading(container) {
        const skeletonGrid = container.querySelector('.skeleton-grid');
        if (skeletonGrid) {
            skeletonGrid.remove();
        }
    }

    /**
     * Setup form loading states
     */
    setupFormLoadingStates() {
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.tagName === 'FORM') {
                this.showFormLoading(form);
                
                // Auto-hide after 10 seconds to prevent permanent loading
                setTimeout(() => {
                    this.hideFormLoading(form);
                }, 10000);
            }
        });
    }

    /**
     * Show form loading state
     */
    showFormLoading(form) {
        form.classList.add('form-loading');
        
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitButton) {
            this.showButtonLoading(submitButton);
        }
    }

    /**
     * Hide form loading state
     */
    hideFormLoading(form) {
        form.classList.remove('form-loading');
        
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitButton) {
            this.hideButtonLoading(submitButton);
        }
    }

    /**
     * Show button loading state
     */
    showButtonLoading(button) {
        button.classList.add('btn-loading');
        button.disabled = true;
        
        const text = button.querySelector('.btn-text') || button;
        text.setAttribute('data-original-text', text.textContent);
        text.textContent = 'جاري التحميل...';
    }

    /**
     * Hide button loading state
     */
    hideButtonLoading(button) {
        button.classList.remove('btn-loading');
        button.disabled = false;
        
        const text = button.querySelector('.btn-text') || button;
        const originalText = text.getAttribute('data-original-text');
        if (originalText) {
            text.textContent = originalText;
        }
    }

    /**
     * Setup image loading states
     */
    setupImageLoadingStates() {
        const images = document.querySelectorAll('img');
        
        images.forEach(img => {
            if (!img.complete) {
                this.showImageLoading(img);
                
                img.addEventListener('load', () => {
                    this.hideImageLoading(img);
                });
                
                img.addEventListener('error', () => {
                    this.hideImageLoading(img);
                });
            }
        });
        
        // Handle dynamically added images
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                        images.forEach(img => {
                            if (!img.complete) {
                                this.showImageLoading(img);
                                
                                img.addEventListener('load', () => {
                                    this.hideImageLoading(img);
                                });
                            }
                        });
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Show image loading state
     */
    showImageLoading(img) {
        const container = img.parentElement;
        container.classList.add('image-loading');
    }

    /**
     * Hide image loading state
     */
    hideImageLoading(img) {
        const container = img.parentElement;
        container.classList.remove('image-loading');
        container.classList.add('image-loaded');
    }

    /**
     * Setup skeleton screens
     */
    setupSkeletonScreens() {
        // Auto-create skeletons for empty containers
        const containers = document.querySelectorAll('[data-skeleton]');
        
        containers.forEach(container => {
            if (container.children.length === 0) {
                const skeletonType = container.getAttribute('data-skeleton');
                
                switch (skeletonType) {
                    case 'products':
                        this.createProductSkeleton(container);
                        break;
                    case 'table':
                        this.createTableSkeleton(container);
                        break;
                }
            }
        });
    }

    /**
     * Show progress bar
     */
    showProgress(container, progress = 0) {
        let progressBar = container.querySelector('.progress-bar');
        
        if (!progressBar) {
            progressBar = document.createElement('div');
            progressBar.className = 'progress-bar';
            progressBar.innerHTML = '<div class="progress-bar-fill"></div>';
            container.appendChild(progressBar);
        }
        
        const fill = progressBar.querySelector('.progress-bar-fill');
        fill.style.width = `${progress}%`;
        
        return progressBar;
    }

    /**
     * Update progress
     */
    updateProgress(container, progress) {
        const progressBar = container.querySelector('.progress-bar');
        if (progressBar) {
            const fill = progressBar.querySelector('.progress-bar-fill');
            fill.style.width = `${progress}%`;
        }
    }

    /**
     * Hide progress bar
     */
    hideProgress(container) {
        const progressBar = container.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.remove();
        }
    }

    /**
     * Clean up all active loaders
     */
    cleanup() {
        this.activeLoaders.forEach(loaderId => {
            this.hideSpinner(loaderId);
        });
        this.hideOverlay();
    }
}

// Initialize loading states manager
const loadingManager = new LoadingStatesManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LoadingStatesManager;
}

// Make available globally
window.LoadingStatesManager = LoadingStatesManager;
window.loadingManager = loadingManager;
