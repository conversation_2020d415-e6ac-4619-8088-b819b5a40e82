/**
 * Authentication JavaScript
 * Handles user authentication state and UI updates
 */

// Global user state
let currentUser = null;

/**
 * Initialize authentication on page load
 */
document.addEventListener('DOMContentLoaded', function() {
    checkAuthStatus();
});

/**
 * Check current authentication status
 */
async function checkAuthStatus() {
    try {
        const response = await fetch('php/api/user-auth.php?action=check');
        const data = await response.json();
        
        if (data.success && data.logged_in) {
            currentUser = data.user;
            updateUIForLoggedInUser();
        } else {
            currentUser = null;
            updateUIForLoggedOutUser();
        }
    } catch (error) {
        console.error('Auth check error:', error);
        updateUIForLoggedOutUser();
    }
}

/**
 * Update UI for logged in user
 */
function updateUIForLoggedInUser() {
    const userMenu = document.getElementById('userMenu');
    const authButtons = document.getElementById('authButtons');
    const userName = document.getElementById('userName');
    
    if (userMenu && authButtons && userName) {
        // Show user menu, hide auth buttons
        userMenu.style.display = 'block';
        authButtons.style.display = 'none';
        
        // Update user name
        userName.textContent = `مرحباً، ${currentUser.name}`;
    }
}

/**
 * Update UI for logged out user
 */
function updateUIForLoggedOutUser() {
    const userMenu = document.getElementById('userMenu');
    const authButtons = document.getElementById('authButtons');
    
    if (userMenu && authButtons) {
        // Hide user menu, show auth buttons
        userMenu.style.display = 'none';
        authButtons.style.display = 'flex';
    }
}

/**
 * Logout user
 */
async function logout() {
    try {
        const response = await fetch('php/api/user-auth.php?action=logout', {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.success) {
            currentUser = null;
            updateUIForLoggedOutUser();
            
            // Show success message
            showNotification('تم تسجيل الخروج بنجاح', 'success');
            
            // Reload page to reset any user-specific content
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification('حدث خطأ في تسجيل الخروج', 'error');
        }
    } catch (error) {
        console.error('Logout error:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    }
}

/**
 * Show user profile (placeholder)
 */
function showProfile() {
    showNotification('صفحة الملف الشخصي قيد التطوير', 'info');
}

/**
 * Show user orders (placeholder)
 */
function showOrders() {
    showNotification('صفحة الطلبات قيد التطوير', 'info');
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="closeNotification(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        closeNotification(notification.querySelector('.notification-close'));
    }, 5000);
}

/**
 * Get notification icon based on type
 */
function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        default: return 'fa-info-circle';
    }
}

/**
 * Close notification
 */
function closeNotification(button) {
    const notification = button.closest('.notification');
    if (notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return currentUser !== null;
}

/**
 * Get current user
 */
function getCurrentUser() {
    return currentUser;
}

/**
 * Require authentication for certain actions
 */
function requireAuth(callback) {
    if (isLoggedIn()) {
        callback();
    } else {
        showNotification('يجب تسجيل الدخول أولاً', 'warning');
        setTimeout(() => {
            window.location.href = 'login.html?redirect=' + encodeURIComponent(window.location.href);
        }, 1500);
    }
}

// Add CSS for notifications if not already present
if (!document.querySelector('#notification-styles')) {
    const style = document.createElement('style');
    style.id = 'notification-styles';
    style.textContent = `
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 300px;
            z-index: 10000;
            transform: translateX(400px);
            opacity: 0;
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .notification-success {
            border-left-color: #28a745;
        }
        
        .notification-error {
            border-left-color: #dc3545;
        }
        
        .notification-warning {
            border-left-color: #ffc107;
        }
        
        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .notification-content i {
            font-size: 1.2rem;
        }
        
        .notification-success .notification-content i {
            color: #28a745;
        }
        
        .notification-error .notification-content i {
            color: #dc3545;
        }
        
        .notification-warning .notification-content i {
            color: #ffc107;
        }
        
        .notification-info .notification-content i {
            color: #007bff;
        }
        
        .notification-close {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
            transition: color 0.3s ease;
        }
        
        .notification-close:hover {
            color: #495057;
        }
        
        .user-menu {
            position: relative;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
        }
        
        .user-info:hover {
            background-color: rgba(255,255,255,0.1);
        }
        
        .user-info i {
            font-size: 1.5rem;
            color: #667eea;
        }
        
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 10px 0;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .user-menu:hover .user-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-dropdown a {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: #2c3e50;
            text-decoration: none;
            transition: background-color 0.3s ease;
        }
        
        .user-dropdown a:hover {
            background-color: #f8f9fa;
        }
        
        .user-dropdown a i {
            width: 16px;
            color: #667eea;
        }
        
        .nav-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .login-btn, .register-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .login-btn {
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .login-btn:hover {
            background-color: #667eea;
            color: white;
        }
        
        .register-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: 2px solid transparent;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
    `;
    document.head.appendChild(style);
}
