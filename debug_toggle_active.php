<?php
/**
 * Debug script for toggle-active functionality
 */

require_once 'php/config.php';

echo "=== Debug Toggle Active Functionality ===\n";

// Test 1: Check if produits table has actif column
echo "\n1. Checking produits table structure...\n";
try {
    $stmt = $conn->query("DESCRIBE produits");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasActifColumn = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'actif') {
            $hasActifColumn = true;
            echo "✓ Found 'actif' column: {$column['Type']}, Default: {$column['Default']}\n";
            break;
        }
    }
    
    if (!$hasActifColumn) {
        echo "✗ ERROR: 'actif' column not found in produits table!\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error checking table structure: " . $e->getMessage() . "\n";
}

// Test 2: Check current products and their actif status
echo "\n2. Checking current products...\n";
try {
    $stmt = $conn->query("SELECT id, titre, type, actif FROM produits LIMIT 5");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($products as $product) {
        $status = $product['actif'] ? 'Active' : 'Inactive';
        echo "  - ID: {$product['id']}, Title: {$product['titre']}, Type: {$product['type']}, Status: {$status}\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error fetching products: " . $e->getMessage() . "\n";
}

// Test 3: Simulate toggle-active API call
echo "\n3. Testing toggle-active functionality...\n";
try {
    // Get first product
    $stmt = $conn->query("SELECT id, actif FROM produits LIMIT 1");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($product) {
        $productId = $product['id'];
        $currentStatus = (bool)$product['actif'];
        $newStatus = !$currentStatus;
        
        echo "  - Product ID: {$productId}\n";
        echo "  - Current Status: " . ($currentStatus ? 'Active' : 'Inactive') . "\n";
        echo "  - New Status: " . ($newStatus ? 'Active' : 'Inactive') . "\n";
        
        // Simulate the API call
        $stmt = $conn->prepare("UPDATE produits SET actif = ? WHERE id = ?");
        $result = $stmt->execute([$newStatus, $productId]);
        
        if ($result && $stmt->rowCount() > 0) {
            echo "  ✓ Update successful\n";
            
            // Verify the update
            $stmt = $conn->prepare("SELECT actif FROM produits WHERE id = ?");
            $stmt->execute([$productId]);
            $updatedStatus = (bool)$stmt->fetchColumn();
            
            if ($updatedStatus === $newStatus) {
                echo "  ✓ Status verified: " . ($updatedStatus ? 'Active' : 'Inactive') . "\n";
            } else {
                echo "  ✗ Status verification failed\n";
            }
            
            // Revert the change
            $stmt = $conn->prepare("UPDATE produits SET actif = ? WHERE id = ?");
            $stmt->execute([$currentStatus, $productId]);
            echo "  ✓ Reverted to original status\n";
            
        } else {
            echo "  ✗ Update failed or no rows affected\n";
        }
        
    } else {
        echo "  ✗ No products found to test\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error testing toggle functionality: " . $e->getMessage() . "\n";
}

// Test 4: Check if API endpoint is accessible
echo "\n4. Testing API endpoint structure...\n";

// Simulate the exact conditions of the API call
$_SERVER['REQUEST_METHOD'] = 'POST';
$_GET['action'] = 'toggle-active';
$_POST['productId'] = '1';
$_POST['active'] = '1';

try {
    // Test the handleToggleActive function logic
    $id = $_POST['productId'] ?? null;
    $active = isset($_POST['active']) ? (bool)$_POST['active'] : null;
    
    echo "  - Product ID: " . ($id ?: 'NULL') . "\n";
    echo "  - Active Status: " . ($active !== null ? ($active ? 'true' : 'false') : 'NULL') . "\n";
    
    if (!$id || $active === null) {
        echo "  ✗ Validation would fail: Missing required parameters\n";
    } else {
        echo "  ✓ Parameters validation would pass\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error testing API logic: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Complete ===\n";
?>
