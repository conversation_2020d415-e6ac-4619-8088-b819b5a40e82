<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/security.php';

class AIManager
{
    private static $instance = null;
    private $config;
    private $providerStatus = [];

    private function __construct()
    {
        $this->config = Config::getAIConfig();
        $this->validateAndUpdateProviders();
    }

    private function validateAndUpdateProviders()
    {
        $providers = ['openai', 'anthropic', 'gemini'];

        foreach ($providers as $provider) {
            $apiKey = $this->config[$provider]['key'] ?? null;
            $status = Security::checkAIProviderStatus($provider, $apiKey);

            // Auto-enable if API key is valid
            $this->config[$provider]['enabled'] = $status['enabled'];
            $this->providerStatus[$provider] = $status;
        }
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get an instance of the legacy AIConfig for backward compatibility
     */
    public static function getLegacyConfig()
    {
        require_once __DIR__ . '/../php/ai-config.php';
        return AIConfig::getInstance();
    }

    public function isProviderEnabled($provider)
    {
        return $this->config[$provider]['enabled'] ?? false;
    }

    public function getApiKey($provider)
    {
        if (!$this->isProviderEnabled($provider)) {
            throw new Exception($this->providerStatus[$provider]['message'] ?? "AI provider '$provider' is not enabled or configured");
        }
        return $this->config[$provider]['key'];
    }

    public function getProviderStatus($provider = null)
    {
        if ($provider !== null) {
            return $this->providerStatus[$provider] ?? [
                'enabled' => false,
                'message' => "Unknown provider: $provider",
                'status' => 'error'
            ];
        }
        return $this->providerStatus;
    }

    public function validateConnection($provider)
    {
        if (!$this->isProviderEnabled($provider)) {
            return [
                'success' => false,
                'error' => "AI provider '$provider' is not enabled"
            ];
        }

        try {
            switch ($provider) {
                case 'openai':
                    return $this->validateOpenAI();
                case 'anthropic':
                    return $this->validateAnthropic();
                case 'gemini':
                    return $this->validateGemini();
                default:
                    return [
                        'success' => false,
                        'error' => "Unknown AI provider: $provider"
                    ];
            }
        } catch (Exception $e) {
            error_log("AI Connection Error ($provider): " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    private function validateOpenAI()
    {
        $apiKey = $this->getApiKey('openai');

        $ch = curl_init('https://api.openai.com/v1/models');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            return ['success' => true];
        } else {
            $error = json_decode($response, true);
            throw new Exception($error['error']['message'] ?? 'OpenAI API connection failed');
        }
    }

    private function validateAnthropic()
    {
        $apiKey = $this->getApiKey('anthropic');

        $ch = curl_init('https://api.anthropic.com/v1/messages');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'x-api-key: ' . $apiKey,
            'anthropic-version: 2023-06-01',
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            return ['success' => true];
        } else {
            $error = json_decode($response, true);
            throw new Exception($error['error']['message'] ?? 'Anthropic API connection failed');
        }
    }

    private function validateGemini()
    {
        $apiKey = $this->getApiKey('gemini');

        $ch = curl_init('https://generativelanguage.googleapis.com/v1beta/models?key=' . $apiKey);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            return ['success' => true];
        } else {
            $error = json_decode($response, true);
            throw new Exception($error['error']['message'] ?? 'Gemini API connection failed');
        }
    }
}

// Example usage in AI connection test endpoint:
if (basename($_SERVER['SCRIPT_NAME']) === 'ai.php' && isset($_GET['action']) && $_GET['action'] === 'test_connection') {
    header('Content-Type: application/json');

    try {
        $aiConfig = AIConfig::getInstance();
        $provider = $_GET['provider'] ?? 'openai';

        $result = $aiConfig->validateConnection($provider);
        echo json_encode($result);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
