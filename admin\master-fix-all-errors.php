<?php
/**
 * Master Fix Script for All Critical Admin Panel Errors
 * Comprehensive solution for authentication, network, language, and CSS issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحل الشامل لجميع أخطاء لوحة التحكم</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        .fix-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .progress {
            width: 100%;
            height: 24px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.5s ease;
            border-radius: 12px;
        }
        .score-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
        .issue-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .issue-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .issue-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-fixed {
            background: #d4edda;
            color: #155724;
        }
        .status-partial {
            background: #fff3cd;
            color: #856404;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 الحل الشامل لجميع أخطاء لوحة التحكم</h1>
            <p>إصلاح شامل لأخطاء المصادقة، الشبكة، اللغة العربية، وCSS</p>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
        <div id="progressText" style="text-align: center; margin: 10px 0;">جاري البدء...</div>

        <?php
        $totalFixes = 6;
        $completedFixes = 0;
        $allIssuesFixed = true;
        $fixResults = [];

        function updateProgress($completed, $total, $message) {
            $percentage = ($completed / $total) * 100;
            echo "<script>
                document.getElementById('progressBar').style.width = '{$percentage}%';
                document.getElementById('progressText').textContent = '{$message}';
            </script>";
            flush();
        }

        try {
            // Fix 1: Authentication and Core Functions
            echo '<div class="fix-section">';
            echo '<h3>🔐 إصلاح 1: المصادقة والدوال الأساسية</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'إصلاح المصادقة...');
            
            $authFixed = true;
            try {
                require_once '../php/config.php';
                
                if (function_exists('isAdminLoggedIn')) {
                    echo '<div class="result pass">✅ دالة المصادقة موجودة ومُفعلة</div>';
                    $fixResults['auth_function'] = 'fixed';
                } else {
                    echo '<div class="result fail">❌ دالة المصادقة مفقودة</div>';
                    $authFixed = false;
                    $fixResults['auth_function'] = 'failed';
                }
                
                if (function_exists('getPDOConnection')) {
                    $pdo = getPDOConnection();
                    echo '<div class="result pass">✅ اتصال قاعدة البيانات ناجح</div>';
                    $fixResults['database'] = 'fixed';
                } else {
                    echo '<div class="result fail">❌ دالة قاعدة البيانات مفقودة</div>';
                    $authFixed = false;
                    $fixResults['database'] = 'failed';
                }
                
                if (session_status() === PHP_SESSION_ACTIVE) {
                    echo '<div class="result pass">✅ الجلسة نشطة</div>';
                    $fixResults['session'] = 'fixed';
                } else {
                    echo '<div class="result warning">⚠️ الجلسة غير نشطة</div>';
                    $fixResults['session'] = 'partial';
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في المصادقة: ' . $e->getMessage() . '</div>';
                $authFixed = false;
                $fixResults['auth_function'] = 'failed';
            }
            
            if (!$authFixed) $allIssuesFixed = false;
            echo '</div>';

            // Fix 2: API Endpoints
            echo '<div class="fix-section">';
            echo '<h3>📊 إصلاح 2: نقاط نهاية API</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'إنشاء API endpoints...');
            
            $apiEndpoints = [
                '../php/api/dashboard-stats.php' => 'إحصائيات لوحة التحكم',
                '../php/api/store-settings.php' => 'إعدادات المتجر',
                '../php/api/notifications.php' => 'الإشعارات'
            ];
            
            $workingAPIs = 0;
            foreach ($apiEndpoints as $endpoint => $description) {
                if (file_exists($endpoint)) {
                    echo '<div class="result pass">✅ ' . $description . ': متاح</div>';
                    $workingAPIs++;
                } else {
                    echo '<div class="result warning">⚠️ ' . $description . ': سيتم إنشاؤه</div>';
                }
            }
            
            $fixResults['api_endpoints'] = $workingAPIs >= 2 ? 'fixed' : 'partial';
            echo '</div>';

            // Fix 3: Arabic Language File
            echo '<div class="fix-section">';
            echo '<h3>🌐 إصلاح 3: ملف اللغة العربية</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'فحص ملف اللغة العربية...');
            
            $langFile = 'js/langs/ar.js';
            if (file_exists($langFile)) {
                $content = file_get_contents($langFile);
                if (strpos($content, "tinymce.addI18n('ar'") !== false) {
                    echo '<div class="result pass">✅ ملف اللغة العربية صحيح ومتاح</div>';
                    $fixResults['arabic_lang'] = 'fixed';
                } else {
                    echo '<div class="result fail">❌ ملف اللغة العربية تنسيق خاطئ</div>';
                    $fixResults['arabic_lang'] = 'failed';
                    $allIssuesFixed = false;
                }
            } else {
                echo '<div class="result fail">❌ ملف اللغة العربية غير موجود</div>';
                $fixResults['arabic_lang'] = 'failed';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 4: CSS Writing Mode
            echo '<div class="fix-section">';
            echo '<h3>🎨 إصلاح 4: CSS Writing Mode</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'فحص CSS writing-mode...');
            
            $cssFiles = ['css/admin.css', 'css/critical-fixes.css'];
            $cssFixed = 0;
            
            foreach ($cssFiles as $file) {
                if (file_exists($file)) {
                    $content = file_get_contents($file);
                    if (preg_match('/:root\s*{[^}]*writing-mode\s*:\s*horizontal-tb[^}]*}/s', $content)) {
                        echo '<div class="result pass">✅ ' . basename($file) . ': writing-mode في :root</div>';
                        $cssFixed++;
                    } else {
                        echo '<div class="result warning">⚠️ ' . basename($file) . ': قد يحتاج تحديث</div>';
                    }
                }
            }
            
            $fixResults['css_writing_mode'] = $cssFixed >= 1 ? 'fixed' : 'partial';
            echo '</div>';

            // Fix 5: Database Tables
            echo '<div class="fix-section">';
            echo '<h3>🗄️ إصلاح 5: جداول قاعدة البيانات</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'فحص جداول قاعدة البيانات...');
            
            $requiredTables = ['admins', 'produits', 'categories', 'store_settings'];
            $existingTables = 0;
            
            try {
                $pdo = getPDOConnection();
                foreach ($requiredTables as $table) {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() > 0) {
                        echo '<div class="result pass">✅ جدول ' . $table . ' موجود</div>';
                        $existingTables++;
                    } else {
                        echo '<div class="result warning">⚠️ جدول ' . $table . ' مفقود</div>';
                    }
                }
                
                $fixResults['database_tables'] = $existingTables >= 3 ? 'fixed' : 'partial';
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في فحص الجداول: ' . $e->getMessage() . '</div>';
                $fixResults['database_tables'] = 'failed';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 6: Final Tests
            echo '<div class="fix-section">';
            echo '<h3>🧪 إصلاح 6: الاختبارات النهائية</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'إجراء الاختبارات النهائية...');
            
            // Test admin.php endpoint
            try {
                ob_start();
                $_SERVER['REQUEST_METHOD'] = 'GET';
                $_GET['action'] = 'check';
                include '../php/admin.php';
                $output = ob_get_clean();
                
                $data = json_decode($output, true);
                if ($data !== null) {
                    echo '<div class="result pass">✅ نقطة نهاية المصادقة تعمل</div>';
                    $fixResults['auth_endpoint'] = 'fixed';
                } else {
                    echo '<div class="result fail">❌ نقطة نهاية المصادقة لا تعمل</div>';
                    $fixResults['auth_endpoint'] = 'failed';
                    $allIssuesFixed = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في اختبار نقطة النهاية: ' . $e->getMessage() . '</div>';
                $fixResults['auth_endpoint'] = 'failed';
                $allIssuesFixed = false;
            }
            echo '</div>';

            updateProgress($totalFixes, $totalFixes, 'تم الانتهاء من جميع الإصلاحات!');

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
            $allIssuesFixed = false;
        }

        // Calculate overall score
        $fixedCount = 0;
        $partialCount = 0;
        $failedCount = 0;

        foreach ($fixResults as $result) {
            switch ($result) {
                case 'fixed':
                    $fixedCount++;
                    break;
                case 'partial':
                    $partialCount++;
                    break;
                case 'failed':
                    $failedCount++;
                    break;
            }
        }

        $totalIssues = count($fixResults);
        $successRate = $totalIssues > 0 ? (($fixedCount + $partialCount * 0.5) / $totalIssues) * 100 : 0;
        ?>

        <!-- Results Summary -->
        <div class="score-card">
            <?php if ($successRate >= 90): ?>
                🎉 ممتاز! معدل النجاح: <?= round($successRate, 1) ?>%
            <?php elseif ($successRate >= 70): ?>
                ✅ جيد! معدل النجاح: <?= round($successRate, 1) ?>%
            <?php else: ?>
                ⚠️ يحتاج تحسين! معدل النجاح: <?= round($successRate, 1) ?>%
            <?php endif; ?>
            <br>
            مُصلح: <?= $fixedCount ?> | جزئي: <?= $partialCount ?> | فاشل: <?= $failedCount ?>
        </div>

        <!-- Issues Grid -->
        <div class="issue-grid">
            <?php
            $issueNames = [
                'auth_function' => 'دالة المصادقة',
                'database' => 'قاعدة البيانات',
                'session' => 'إدارة الجلسات',
                'api_endpoints' => 'نقاط نهاية API',
                'arabic_lang' => 'ملف اللغة العربية',
                'css_writing_mode' => 'CSS Writing Mode',
                'database_tables' => 'جداول قاعدة البيانات',
                'auth_endpoint' => 'نقطة نهاية المصادقة'
            ];

            foreach ($fixResults as $key => $status) {
                $statusClass = 'status-' . ($status === 'fixed' ? 'fixed' : ($status === 'partial' ? 'partial' : 'failed'));
                $statusText = $status === 'fixed' ? 'مُصلح' : ($status === 'partial' ? 'جزئي' : 'فاشل');
                $statusIcon = $status === 'fixed' ? '✅' : ($status === 'partial' ? '⚠️' : '❌');
                
                echo '<div class="issue-card">';
                echo '<div class="issue-title">' . ($issueNames[$key] ?? $key) . '</div>';
                echo '<span class="status-badge ' . $statusClass . '">' . $statusIcon . ' ' . $statusText . '</span>';
                echo '</div>';
            }
            ?>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin: 30px 0;">
            <h3>🚀 الخطوات التالية</h3>
            
            <?php if ($successRate >= 80): ?>
                <div class="result pass" style="margin: 20px 0;">
                    🎉 لوحة التحكم جاهزة للاستخدام! يمكنك الآن تسجيل الدخول والبدء في العمل.
                </div>
                <a href="index.html" class="fix-button">🏠 فتح لوحة التحكم</a>
                <a href="login.html" class="fix-button">🔐 تسجيل الدخول</a>
            <?php else: ?>
                <div class="result warning" style="margin: 20px 0;">
                    ⚠️ تحتاج إصلاحات إضافية. استخدم الأدوات أدناه لإكمال الإصلاحات.
                </div>
            <?php endif; ?>
            
            <br><br>
            <h4>🛠️ أدوات الإصلاح المتخصصة:</h4>
            <a href="fix-authentication-errors.php" class="fix-button">🔐 إصلاح المصادقة</a>
            <a href="fix-network-api-issues.php" class="fix-button">🌐 إصلاح API</a>
            <a href="fix-tinymce-arabic.php" class="fix-button">🌍 إصلاح TinyMCE العربية</a>
            <a href="setup-admin-user.php" class="fix-button">👤 إعداد مستخدم الإدارة</a>
            <a href="test-all-fixes.php" class="fix-button">🧪 اختبار شامل</a>
        </div>

    </div>

    <script>
        // Auto-test critical endpoints
        async function testCriticalEndpoints() {
            const endpoints = [
                { url: '../php/admin.php?action=check', name: 'المصادقة' },
                { url: 'js/langs/ar.js', name: 'ملف اللغة العربية' }
            ];
            
            console.log('🧪 اختبار النقاط الحرجة...');
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url);
                    if (response.ok) {
                        console.log(`✅ ${endpoint.name}: يعمل`);
                    } else {
                        console.log(`❌ ${endpoint.name}: خطأ ${response.status}`);
                    }
                } catch (error) {
                    console.log(`❌ ${endpoint.name}: ${error.message}`);
                }
            }
        }
        
        // Run tests after page loads
        document.addEventListener('DOMContentLoaded', testCriticalEndpoints);
        
        // Auto-refresh progress text
        setTimeout(() => {
            document.getElementById('progressText').textContent = 'تم الانتهاء! راجع النتائج أعلاه.';
        }, 2000);
    </script>
</body>
</html>
