<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Landing Pages Manager Fix</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Landing Pages Manager Fix</h1>
        <p>هذا الاختبار يتحقق من إصلاح مشكلة <code>landingPagesManager is not defined</code></p>

        <div class="test-section">
            <h2>اختبارات التحقق من التهيئة</h2>
            <button class="test-button" onclick="testLandingPagesManagerExists()">
                1. تحقق من وجود landingPagesManager
            </button>
            <button class="test-button" onclick="testSafeFunctionsExist()">
                2. تحقق من وجود الدوال الآمنة
            </button>
            <button class="test-button" onclick="testInitialization()">
                3. تحقق من التهيئة
            </button>
        </div>

        <div class="test-section">
            <h2>اختبارات الدوال الآمنة</h2>
            <button class="test-button" onclick="testSafeLandingPagesCall()">
                4. اختبار safeLandingPagesCall
            </button>
            <button class="test-button" onclick="testSafeCloseModal()">
                5. اختبار safeLandingPagesCloseModal
            </button>
            <button class="test-button" onclick="testOpenLandingPagesModal()">
                6. اختبار openLandingPagesModal
            </button>
        </div>

        <div class="test-section">
            <h2>اختبار شامل</h2>
            <button class="test-button" onclick="runAllTests()">
                🚀 تشغيل جميع الاختبارات
            </button>
            <button class="test-button" onclick="clearResults()">
                🧹 مسح النتائج
            </button>
        </div>

        <div id="test-results"></div>
        <div id="console-output"></div>
    </div>

    <!-- Include the same scripts as admin panel -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
    <script src="admin/js/tinymce-config.js"></script>
    <script src="admin/js/landing-pages.js"></script>
    <script src="admin/js/admin.js"></script>

    <script>
        // Capture console output
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        let consoleOutput = '';

        function captureConsole(type, ...args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput += `[${type.toUpperCase()}] ${new Date().toLocaleTimeString()}: ${message}\n`;
            updateConsoleOutput();
        }

        console.log = (...args) => {
            originalConsoleLog(...args);
            captureConsole('log', ...args);
        };

        console.error = (...args) => {
            originalConsoleError(...args);
            captureConsole('error', ...args);
        };

        console.warn = (...args) => {
            originalConsoleWarn(...args);
            captureConsole('warn', ...args);
        };

        function updateConsoleOutput() {
            const output = document.getElementById('console-output');
            if (output) {
                output.textContent = consoleOutput;
                output.scrollTop = output.scrollHeight;
            }
        }

        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function testLandingPagesManagerExists() {
            if (typeof landingPagesManager !== 'undefined') {
                addTestResult('✅ landingPagesManager موجود', 'success');
                console.log('landingPagesManager object:', landingPagesManager);
            } else {
                addTestResult('❌ landingPagesManager غير موجود', 'error');
            }
        }

        function testSafeFunctionsExist() {
            const safeFunctions = [
                'safeLandingPagesCall',
                'safeLandingPagesCloseModal',
                'safeLandingPagesNextStep',
                'safeLandingPagesPreviousStep',
                'safeLandingPagesClearAllImages',
                'safeLandingPagesOpenModal',
                'openLandingPagesModal'
            ];

            let allExist = true;
            safeFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addTestResult(`✅ ${funcName} موجودة`, 'success');
                } else {
                    addTestResult(`❌ ${funcName} غير موجودة`, 'error');
                    allExist = false;
                }
            });

            if (allExist) {
                addTestResult('✅ جميع الدوال الآمنة موجودة', 'success');
            }
        }

        function testInitialization() {
            if (typeof landingPagesManager !== 'undefined') {
                addTestResult(`🔧 حالة التهيئة: ${landingPagesManager.initialized ? 'مهيأ' : 'غير مهيأ'}`, 
                    landingPagesManager.initialized ? 'success' : 'info');
                
                if (!landingPagesManager.initialized) {
                    addTestResult('🔄 محاولة التهيئة...', 'info');
                    try {
                        landingPagesManager.init();
                        setTimeout(() => {
                            addTestResult(`✅ التهيئة مكتملة: ${landingPagesManager.initialized}`, 
                                landingPagesManager.initialized ? 'success' : 'error');
                        }, 100);
                    } catch (error) {
                        addTestResult(`❌ فشل في التهيئة: ${error.message}`, 'error');
                    }
                }
            } else {
                addTestResult('❌ لا يمكن اختبار التهيئة - landingPagesManager غير موجود', 'error');
            }
        }

        function testSafeLandingPagesCall() {
            if (typeof window.safeLandingPagesCall === 'function') {
                try {
                    // Test with a non-existent method
                    window.safeLandingPagesCall('nonExistentMethod');
                    addTestResult('✅ safeLandingPagesCall تعمل بشكل صحيح (لا توجد أخطاء)', 'success');
                } catch (error) {
                    addTestResult(`❌ خطأ في safeLandingPagesCall: ${error.message}`, 'error');
                }
            } else {
                addTestResult('❌ safeLandingPagesCall غير موجودة', 'error');
            }
        }

        function testSafeCloseModal() {
            if (typeof window.safeLandingPagesCloseModal === 'function') {
                try {
                    window.safeLandingPagesCloseModal();
                    addTestResult('✅ safeLandingPagesCloseModal تعمل بشكل صحيح', 'success');
                } catch (error) {
                    addTestResult(`❌ خطأ في safeLandingPagesCloseModal: ${error.message}`, 'error');
                }
            } else {
                addTestResult('❌ safeLandingPagesCloseModal غير موجودة', 'error');
            }
        }

        function testOpenLandingPagesModal() {
            if (typeof window.openLandingPagesModal === 'function') {
                try {
                    // This might show an error about missing DOM elements, which is expected
                    window.openLandingPagesModal();
                    addTestResult('✅ openLandingPagesModal تعمل بشكل صحيح', 'success');
                } catch (error) {
                    addTestResult(`❌ خطأ في openLandingPagesModal: ${error.message}`, 'error');
                }
            } else {
                addTestResult('❌ openLandingPagesModal غير موجودة', 'error');
            }
        }

        function runAllTests() {
            clearResults();
            addTestResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            
            setTimeout(() => testLandingPagesManagerExists(), 100);
            setTimeout(() => testSafeFunctionsExist(), 200);
            setTimeout(() => testInitialization(), 300);
            setTimeout(() => testSafeLandingPagesCall(), 400);
            setTimeout(() => testSafeCloseModal(), 500);
            setTimeout(() => testOpenLandingPagesModal(), 600);
            
            setTimeout(() => {
                addTestResult('🎉 انتهاء جميع الاختبارات', 'info');
            }, 700);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            consoleOutput = '';
            updateConsoleOutput();
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('📋 تشغيل الاختبارات الأساسية تلقائياً...', 'info');
                testLandingPagesManagerExists();
                testSafeFunctionsExist();
            }, 1000);
        });
    </script>
</body>
</html>
