<?php
/**
 * Image Optimization Utility for Mossaab Landing Page
 * Handles WebP conversion, compression, and responsive image variants
 */

class ImageOptimizer
{
    private $uploadDir;
    private $webpSupported;
    private $quality;
    private $sizes;

    public function __construct($uploadDir = 'uploads/products/', $quality = 85)
    {
        $this->uploadDir = rtrim($uploadDir, '/') . '/';
        $this->quality = $quality;
        $this->webpSupported = function_exists('imagewebp');
        
        // Define responsive image sizes
        $this->sizes = [
            'thumbnail' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 400, 'height' => 400],
            'large' => ['width' => 800, 'height' => 800],
            'original' => null // Keep original size
        ];

        // Create upload directory if it doesn't exist
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }

        // Create size-specific directories
        foreach (array_keys($this->sizes) as $size) {
            if ($size !== 'original') {
                $sizeDir = $this->uploadDir . $size . '/';
                if (!is_dir($sizeDir)) {
                    mkdir($sizeDir, 0755, true);
                }
            }
        }
    }

    /**
     * Process uploaded image with optimization and multiple formats
     */
    public function processUploadedImage($uploadedFile, $filename = null)
    {
        try {
            // Validate uploaded file
            $validation = $this->validateImage($uploadedFile);
            if (!$validation['valid']) {
                throw new Exception($validation['error']);
            }

            // Generate secure filename
            if (!$filename) {
                $filename = $this->generateSecureFilename($uploadedFile['name']);
            }

            // Get image info
            $imageInfo = getimagesize($uploadedFile['tmp_name']);
            $originalWidth = $imageInfo[0];
            $originalHeight = $imageInfo[1];
            $mimeType = $imageInfo['mime'];

            // Create image resource from uploaded file
            $sourceImage = $this->createImageFromFile($uploadedFile['tmp_name'], $mimeType);
            if (!$sourceImage) {
                throw new Exception('فشل في قراءة الصورة');
            }

            $results = [];

            // Process each size variant
            foreach ($this->sizes as $sizeName => $dimensions) {
                if ($sizeName === 'original') {
                    // Save original with compression
                    $originalPath = $this->uploadDir . $filename;
                    $results[$sizeName] = $this->saveOptimizedImage(
                        $sourceImage, 
                        $originalPath, 
                        $originalWidth, 
                        $originalHeight,
                        $mimeType
                    );
                } else {
                    // Create resized variant
                    $resizedImage = $this->resizeImage(
                        $sourceImage, 
                        $originalWidth, 
                        $originalHeight,
                        $dimensions['width'], 
                        $dimensions['height']
                    );

                    if ($resizedImage) {
                        $sizePath = $this->uploadDir . $sizeName . '/' . $filename;
                        $results[$sizeName] = $this->saveOptimizedImage(
                            $resizedImage, 
                            $sizePath, 
                            $dimensions['width'], 
                            $dimensions['height'],
                            $mimeType
                        );
                        imagedestroy($resizedImage);
                    }
                }
            }

            // Clean up
            imagedestroy($sourceImage);

            return [
                'success' => true,
                'filename' => $filename,
                'variants' => $results,
                'webp_supported' => $this->webpSupported
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate uploaded image file
     */
    private function validateImage($file)
    {
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return ['valid' => false, 'error' => 'ملف غير صحيح'];
        }

        // Check file size (5MB max)
        if ($file['size'] > 5242880) {
            return ['valid' => false, 'error' => 'حجم الملف كبير جداً (الحد الأقصى 5 ميجابايت)'];
        }

        // Check file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $allowedTypes)) {
            return ['valid' => false, 'error' => 'نوع الملف غير مسموح (JPEG, PNG, GIF فقط)'];
        }

        // Check image dimensions
        $imageInfo = getimagesize($file['tmp_name']);
        if (!$imageInfo) {
            return ['valid' => false, 'error' => 'ملف الصورة تالف'];
        }

        // Minimum dimensions check
        if ($imageInfo[0] < 100 || $imageInfo[1] < 100) {
            return ['valid' => false, 'error' => 'أبعاد الصورة صغيرة جداً (الحد الأدنى 100x100)'];
        }

        return ['valid' => true];
    }

    /**
     * Create image resource from file
     */
    private function createImageFromFile($filePath, $mimeType)
    {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagecreatefromjpeg($filePath);
            case 'image/png':
                return imagecreatefrompng($filePath);
            case 'image/gif':
                return imagecreatefromgif($filePath);
            default:
                return false;
        }
    }

    /**
     * Resize image maintaining aspect ratio
     */
    private function resizeImage($sourceImage, $sourceWidth, $sourceHeight, $targetWidth, $targetHeight)
    {
        // Calculate aspect ratio
        $sourceRatio = $sourceWidth / $sourceHeight;
        $targetRatio = $targetWidth / $targetHeight;

        // Determine new dimensions
        if ($sourceRatio > $targetRatio) {
            // Source is wider
            $newWidth = $targetWidth;
            $newHeight = $targetWidth / $sourceRatio;
        } else {
            // Source is taller
            $newWidth = $targetHeight * $sourceRatio;
            $newHeight = $targetHeight;
        }

        // Create new image
        $resizedImage = imagecreatetruecolor($newWidth, $newHeight);

        // Preserve transparency for PNG and GIF
        imagealphablending($resizedImage, false);
        imagesavealpha($resizedImage, true);
        $transparent = imagecolorallocatealpha($resizedImage, 255, 255, 255, 127);
        imagefill($resizedImage, 0, 0, $transparent);

        // Resize
        imagecopyresampled(
            $resizedImage, $sourceImage,
            0, 0, 0, 0,
            $newWidth, $newHeight,
            $sourceWidth, $sourceHeight
        );

        return $resizedImage;
    }

    /**
     * Save optimized image in multiple formats
     */
    private function saveOptimizedImage($image, $path, $width, $height, $originalMimeType)
    {
        $results = [];
        $pathInfo = pathinfo($path);
        $basePath = $pathInfo['dirname'] . '/' . $pathInfo['filename'];

        // Save original format with compression
        $originalPath = $basePath . '.' . $pathInfo['extension'];
        switch ($originalMimeType) {
            case 'image/jpeg':
                imagejpeg($image, $originalPath, $this->quality);
                break;
            case 'image/png':
                imagepng($image, $originalPath, 9 - round(($this->quality / 100) * 9));
                break;
            case 'image/gif':
                imagegif($image, $originalPath);
                break;
        }

        $results['original'] = [
            'path' => $originalPath,
            'size' => filesize($originalPath),
            'format' => $originalMimeType
        ];

        // Save WebP version if supported
        if ($this->webpSupported) {
            $webpPath = $basePath . '.webp';
            imagewebp($image, $webpPath, $this->quality);
            
            $results['webp'] = [
                'path' => $webpPath,
                'size' => filesize($webpPath),
                'format' => 'image/webp'
            ];
        }

        return $results;
    }

    /**
     * Generate secure filename
     */
    private function generateSecureFilename($originalName)
    {
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        $timestamp = date('Y-m-d_H-i-s');
        $random = bin2hex(random_bytes(8));
        return "product_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Get optimized image URL for display
     */
    public function getOptimizedImageUrl($filename, $size = 'medium', $preferWebP = true)
    {
        if (!$filename) {
            return $this->getPlaceholderUrl($size);
        }

        $pathInfo = pathinfo($filename);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'];

        // Determine size directory
        $sizeDir = ($size === 'original') ? '' : $size . '/';
        
        // Check for WebP version first if preferred and supported
        if ($preferWebP && $this->webpSupported) {
            $webpPath = $this->uploadDir . $sizeDir . $baseName . '.webp';
            if (file_exists($webpPath)) {
                return '/' . $webpPath;
            }
        }

        // Fallback to original format
        $originalPath = $this->uploadDir . $sizeDir . $filename;
        if (file_exists($originalPath)) {
            return '/' . $originalPath;
        }

        // Return placeholder if file not found
        return $this->getPlaceholderUrl($size);
    }

    /**
     * Get placeholder image URL
     */
    private function getPlaceholderUrl($size)
    {
        $dimensions = $this->sizes[$size] ?? $this->sizes['medium'];
        $width = $dimensions['width'] ?? 400;
        $height = $dimensions['height'] ?? 400;
        
        return "https://via.placeholder.com/{$width}x{$height}/f8f9fa/6c757d?text=صورة+المنتج";
    }

    /**
     * Delete all variants of an image
     */
    public function deleteImage($filename)
    {
        if (!$filename) return false;

        $pathInfo = pathinfo($filename);
        $baseName = $pathInfo['filename'];
        $deleted = 0;

        foreach (array_keys($this->sizes) as $size) {
            $sizeDir = ($size === 'original') ? '' : $size . '/';
            
            // Delete original format
            $originalPath = $this->uploadDir . $sizeDir . $filename;
            if (file_exists($originalPath)) {
                unlink($originalPath);
                $deleted++;
            }

            // Delete WebP version
            $webpPath = $this->uploadDir . $sizeDir . $baseName . '.webp';
            if (file_exists($webpPath)) {
                unlink($webpPath);
                $deleted++;
            }
        }

        return $deleted > 0;
    }

    /**
     * Get image optimization statistics
     */
    public function getOptimizationStats($filename)
    {
        if (!$filename) return null;

        $pathInfo = pathinfo($filename);
        $baseName = $pathInfo['filename'];
        $stats = [];

        foreach (array_keys($this->sizes) as $size) {
            $sizeDir = ($size === 'original') ? '' : $size . '/';
            
            $originalPath = $this->uploadDir . $sizeDir . $filename;
            $webpPath = $this->uploadDir . $sizeDir . $baseName . '.webp';

            if (file_exists($originalPath)) {
                $originalSize = filesize($originalPath);
                $webpSize = file_exists($webpPath) ? filesize($webpPath) : 0;
                
                $stats[$size] = [
                    'original_size' => $originalSize,
                    'webp_size' => $webpSize,
                    'savings' => $webpSize > 0 ? round((($originalSize - $webpSize) / $originalSize) * 100, 1) : 0
                ];
            }
        }

        return $stats;
    }
}
?>
