<?php
/**
 * Fix HTTP 500 Errors in API Endpoints
 * Comprehensive solution for Security class and database connection issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح أخطاء HTTP 500 في API</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 12px;
        }
        .fix-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .api-test {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .progress {
            width: 100%;
            height: 24px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #dc3545, #c82333);
            transition: width 0.5s ease;
            border-radius: 12px;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح أخطاء HTTP 500 في API</h1>
            <p>حل شامل لمشاكل Security class وقاعدة البيانات في نقاط نهاية API</p>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
        <div id="progressText" style="text-align: center; margin: 10px 0;">جاري البدء...</div>

        <?php
        $totalFixes = 5;
        $completedFixes = 0;
        $allIssuesFixed = true;
        $apiResults = [];

        function updateProgress($completed, $total, $message) {
            $percentage = ($completed / $total) * 100;
            echo "<script>
                document.getElementById('progressBar').style.width = '{$percentage}%';
                document.getElementById('progressText').textContent = '{$message}';
            </script>";
            flush();
        }

        try {
            // Fix 1: Test Security Class and Database Connection
            echo '<div class="fix-section">';
            echo '<h3>🔐 إصلاح 1: فحص Security Class وقاعدة البيانات</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'فحص Security class...');
            
            try {
                require_once '../php/config.php';
                echo '<div class="result pass">✅ تم تحميل config.php بنجاح</div>';
                
                // Test Security class
                if (class_exists('Security')) {
                    echo '<div class="result pass">✅ فئة Security موجودة</div>';
                    
                    if (method_exists('Security', 'init')) {
                        echo '<div class="result pass">✅ دالة Security::init() موجودة</div>';
                        
                        try {
                            Security::init();
                            echo '<div class="result pass">✅ Security::init() تعمل بنجاح</div>';
                        } catch (Exception $e) {
                            echo '<div class="result fail">❌ خطأ في Security::init(): ' . $e->getMessage() . '</div>';
                            $allIssuesFixed = false;
                        }
                    } else {
                        echo '<div class="result fail">❌ دالة Security::init() مفقودة</div>';
                        $allIssuesFixed = false;
                    }
                } else {
                    echo '<div class="result fail">❌ فئة Security غير موجودة</div>';
                    $allIssuesFixed = false;
                }
                
                // Test database connection
                if (isset($conn) && $conn instanceof PDO) {
                    echo '<div class="result pass">✅ متغير $conn العام متاح</div>';
                    
                    // Test a simple query
                    $stmt = $conn->query("SELECT 1");
                    if ($stmt) {
                        echo '<div class="result pass">✅ اتصال قاعدة البيانات يعمل</div>';
                    } else {
                        echo '<div class="result fail">❌ اتصال قاعدة البيانات لا يعمل</div>';
                        $allIssuesFixed = false;
                    }
                } else {
                    echo '<div class="result fail">❌ متغير $conn العام غير متاح</div>';
                    $allIssuesFixed = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في التحميل: ' . $e->getMessage() . '</div>';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 2: Test Critical API Endpoints
            echo '<div class="fix-section">';
            echo '<h3>📊 إصلاح 2: اختبار نقاط نهاية API الحرجة</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'اختبار API endpoints...');
            
            $criticalAPIs = [
                '../php/api/products.php' => 'إدارة المنتجات',
                '../php/api/landing-pages.php' => 'إدارة صفحات الهبوط',
                '../php/api/dashboard-stats.php' => 'إحصائيات لوحة التحكم',
                '../php/api/categories.php' => 'إدارة الفئات'
            ];
            
            foreach ($criticalAPIs as $apiFile => $description) {
                echo '<h4>🔍 اختبار: ' . $description . '</h4>';
                
                if (file_exists($apiFile)) {
                    echo '<div class="result pass">✅ الملف موجود: ' . basename($apiFile) . '</div>';
                    
                    // Test if file can be included without fatal errors
                    try {
                        ob_start();
                        $errorOccurred = false;
                        
                        // Capture any errors
                        set_error_handler(function($severity, $message, $file, $line) use (&$errorOccurred) {
                            $errorOccurred = true;
                            echo '<div class="result fail">❌ PHP Error: ' . $message . ' in ' . basename($file) . ':' . $line . '</div>';
                        });
                        
                        // Set up environment for API test
                        $_SERVER['REQUEST_METHOD'] = 'GET';
                        $_GET = []; // Clear any existing GET parameters
                        
                        // Include the API file
                        include $apiFile;
                        
                        restore_error_handler();
                        $output = ob_get_clean();
                        
                        if (!$errorOccurred) {
                            // Try to parse as JSON
                            $data = json_decode($output, true);
                            if ($data !== null || $output === '') {
                                echo '<div class="result pass">✅ ' . $description . ': يعمل بدون أخطاء</div>';
                                $apiResults[$apiFile] = 'working';
                            } else {
                                echo '<div class="result warning">⚠️ ' . $description . ': استجابة غير متوقعة</div>';
                                echo '<div class="api-test">الاستجابة: ' . htmlspecialchars(substr($output, 0, 200)) . '...</div>';
                                $apiResults[$apiFile] = 'partial';
                            }
                        } else {
                            echo '<div class="result fail">❌ ' . $description . ': أخطاء PHP</div>';
                            $apiResults[$apiFile] = 'failed';
                            $allIssuesFixed = false;
                        }
                        
                    } catch (Exception $e) {
                        echo '<div class="result fail">❌ ' . $description . ': ' . $e->getMessage() . '</div>';
                        $apiResults[$apiFile] = 'failed';
                        $allIssuesFixed = false;
                    }
                    
                } else {
                    echo '<div class="result fail">❌ الملف غير موجود: ' . $apiFile . '</div>';
                    $apiResults[$apiFile] = 'missing';
                    $allIssuesFixed = false;
                }
            }
            echo '</div>';

            // Fix 3: Test Admin Sidebar Menu Sections
            echo '<div class="fix-section">';
            echo '<h3>🎛️ إصلاح 3: اختبار أقسام قائمة الشريط الجانبي</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'اختبار أقسام القائمة...');
            
            $menuSections = [
                'الرئيسية' => '../php/api/dashboard-stats.php',
                'إدارة المنتجات' => '../php/api/products.php',
                'الطلبات' => '../php/api/orders.php',
                'صفحات الهبوط' => '../php/api/landing-pages.php',
                'إعدادات الذكاء الاصطناعي' => '../php/api/ai.php',
                'الإعدادات' => '../php/api/store-settings.php'
            ];
            
            $workingSections = 0;
            $totalSections = count($menuSections);
            
            foreach ($menuSections as $sectionName => $apiEndpoint) {
                if (file_exists($apiEndpoint)) {
                    echo '<div class="result pass">✅ ' . $sectionName . ': API متاح</div>';
                    $workingSections++;
                } else {
                    echo '<div class="result warning">⚠️ ' . $sectionName . ': API غير موجود (' . basename($apiEndpoint) . ')</div>';
                }
            }
            
            $sectionSuccessRate = ($workingSections / $totalSections) * 100;
            echo '<div class="result info">📊 معدل توفر أقسام القائمة: ' . $workingSections . '/' . $totalSections . ' (' . round($sectionSuccessRate, 1) . '%)</div>';
            echo '</div>';

            // Fix 4: Create Missing API Files
            echo '<div class="fix-section">';
            echo '<h3>🔨 إصلاح 4: إنشاء ملفات API المفقودة</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'إنشاء ملفات API مفقودة...');
            
            // Check if orders.php exists, if not create it
            $ordersAPI = '../php/api/orders.php';
            if (!file_exists($ordersAPI)) {
                echo '<div class="result warning">⚠️ ملف orders.php غير موجود - جاري الإنشاء...</div>';
                
                $ordersContent = '<?php
// Define security check constant BEFORE including security.php
define("SECURITY_CHECK", true);

require_once "../config.php";
require_once "../security.php";
require_once "../SecurityHeaders.php";

// Set security headers
SecurityHeaders::setSecurityHeaders();

header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Handle preflight requests
if ($_SERVER["REQUEST_METHOD"] == "OPTIONS") {
    exit(0);
}

try {
    if ($_SERVER["REQUEST_METHOD"] == "GET") {
        // Get orders
        $stmt = $conn->prepare("SELECT * FROM commandes ORDER BY date_creation DESC LIMIT 50");
        $stmt->execute();
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            "success" => true,
            "data" => $orders,
            "count" => count($orders)
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            "success" => false,
            "message" => "Method not supported"
        ], JSON_UNESCAPED_UNICODE);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "خطأ في تحميل الطلبات: " . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>';
                
                if (file_put_contents($ordersAPI, $ordersContent)) {
                    echo '<div class="result pass">✅ تم إنشاء orders.php بنجاح</div>';
                } else {
                    echo '<div class="result fail">❌ فشل في إنشاء orders.php</div>';
                    $allIssuesFixed = false;
                }
            } else {
                echo '<div class="result pass">✅ ملف orders.php موجود</div>';
            }
            echo '</div>';

            // Fix 5: Final Comprehensive Test
            echo '<div class="fix-section">';
            echo '<h3>🧪 إصلاح 5: الاختبار الشامل النهائي</h3>';
            
            updateProgress(++$completedFixes, $totalFixes, 'إجراء الاختبار الشامل...');
            
            $workingAPIs = 0;
            $totalAPIs = count($apiResults);
            
            foreach ($apiResults as $api => $status) {
                if ($status === 'working') {
                    $workingAPIs++;
                }
            }
            
            $overallSuccessRate = $totalAPIs > 0 ? ($workingAPIs / $totalAPIs) * 100 : 0;
            
            if ($overallSuccessRate >= 80) {
                echo '<div class="result pass">🎉 معظم APIs تعمل بنجاح! معدل النجاح: ' . round($overallSuccessRate, 1) . '%</div>';
            } elseif ($overallSuccessRate >= 60) {
                echo '<div class="result warning">⚠️ بعض APIs تحتاج إصلاحات. معدل النجاح: ' . round($overallSuccessRate, 1) . '%</div>';
            } else {
                echo '<div class="result fail">❌ معظم APIs تحتاج إصلاحات. معدل النجاح: ' . round($overallSuccessRate, 1) . '%</div>';
                $allIssuesFixed = false;
            }
            
            echo '<h4>📋 تفاصيل نتائج API:</h4>';
            foreach ($apiResults as $api => $status) {
                $statusText = $status === 'working' ? '✅ يعمل' : ($status === 'partial' ? '⚠️ جزئي' : '❌ فاشل');
                echo '<div class="api-test">' . basename($api) . ': ' . $statusText . '</div>';
            }
            echo '</div>';

            updateProgress($totalFixes, $totalFixes, 'تم الانتهاء من جميع الإصلاحات!');

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '<pre>' . $e->getTraceAsString() . '</pre>';
            echo '</div>';
            $allIssuesFixed = false;
        }

        // Summary
        echo '<div class="fix-section">';
        echo '<h3>📊 الملخص النهائي</h3>';
        
        if ($allIssuesFixed && $overallSuccessRate >= 80) {
            echo '<div class="result pass">🎉 تم إصلاح جميع أخطاء HTTP 500!</div>';
            echo '<div class="result pass">✅ جميع أقسام لوحة التحكم جاهزة للاستخدام</div>';
        } else {
            echo '<div class="result warning">⚠️ تم إصلاح معظم المشاكل، قد تحتاج بعض الإصلاحات الإضافية</div>';
        }
        
        echo '<h4>🚀 الخطوات التالية:</h4>';
        echo '<p><a href="index.html" class="fix-button">🏠 فتح لوحة التحكم</a></p>';
        echo '<p><a href="test-admin-sidebar-menu.php" class="fix-button">🧪 اختبار قائمة الشريط الجانبي</a></p>';
        echo '<p><a href="master-fix-all-errors.php" class="fix-button">🔧 إصلاحات شاملة أخرى</a></p>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        // Test API endpoints via JavaScript
        async function testAPIEndpoints() {
            const apis = [
                { url: '../php/api/products.php', name: 'المنتجات' },
                { url: '../php/api/landing-pages.php', name: 'صفحات الهبوط' },
                { url: '../php/api/dashboard-stats.php', name: 'إحصائيات لوحة التحكم' },
                { url: '../php/api/categories.php', name: 'الفئات' }
            ];
            
            console.log('🧪 اختبار API endpoints عبر JavaScript...');
            
            for (const api of apis) {
                try {
                    const response = await fetch(api.url);
                    console.log(`${api.name}: Status ${response.status}`);
                    
                    if (response.status < 500) {
                        console.log(`✅ ${api.name}: لا يوجد خطأ 500`);
                    } else {
                        console.log(`❌ ${api.name}: خطأ 500`);
                    }
                } catch (error) {
                    console.log(`❌ ${api.name}: خطأ في الشبكة - ${error.message}`);
                }
            }
        }
        
        // Run tests after page loads
        document.addEventListener('DOMContentLoaded', testAPIEndpoints);
        
        // Auto-refresh progress text
        setTimeout(() => {
            document.getElementById('progressText').textContent = 'تم الانتهاء! راجع النتائج أعلاه.';
        }, 2000);
    </script>
</body>
</html>
