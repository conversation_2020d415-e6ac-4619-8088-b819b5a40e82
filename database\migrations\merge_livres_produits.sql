-- Désactiver les contraintes de clé étrangère temporairement
SET FOREIGN_KEY_CHECKS = 0;

-- Transférer les données de la table livres vers produits
INSERT INTO produits (type, titre, description, prix, stock, image_url, auteur, has_landing_page, landing_page_enabled, slug)
SELECT 'book', titre, description, prix, stock, image_url, auteur, has_landing_page, landing_page_enabled, slug
FROM livres;

-- Mettre à jour les références dans la table product_images
UPDATE product_images pi
JOIN livres l ON pi.product_id = l.id
JOIN produits p ON p.titre = l.titre AND p.auteur = l.auteur
SET pi.product_id = p.id;

-- Supprimer l'ancienne contrainte de clé étrangère
ALTER TABLE product_images
DROP FOREIGN KEY product_images_ibfk_1;

-- Ajouter la nouvelle contrainte de clé étrangère
ALTER TABLE product_images
ADD CONSTRAINT product_images_ibfk_1
FOREIGN KEY (product_id) REFERENCES produits(id) ON DELETE CASCADE;

-- Supprimer la table livres
DROP TABLE livres;

-- Réactiver les contraintes de clé étrangère
SET FOREIGN_KEY_CHECKS = 1;