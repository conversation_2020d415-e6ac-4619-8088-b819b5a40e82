/**
 * Intelligent Order Form with Shipping Integration
 * Handles geographic selection, shipping calculation, and order processing
 */

class OrderFormManager {
    constructor() {
        this.productData = {
            id: null,
            title: 'منتج تجريبي',
            price: 2500,
            image: 'https://via.placeholder.com/300x150?text=Product+Image',
            weight: 1.0
        };

        this.shippingData = {
            cost: 0,
            zone: null,
            deliveryTime: null,
            wilayaCode: null,
            communeCode: null
        };

        this.init();
    }

    init() {
        this.loadProductData();
        this.loadWilayas();
        this.setupEventListeners();
        this.updateOrderSummary();
    }

    loadProductData() {
        // Get product data from URL parameters or localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('product_id');
        const productTitle = urlParams.get('title');
        const productPrice = urlParams.get('price');
        const productImage = urlParams.get('image');

        if (productId) {
            this.productData.id = productId;
        }
        if (productTitle) {
            this.productData.title = decodeURIComponent(productTitle);
        }
        if (productPrice) {
            this.productData.price = parseFloat(productPrice);
        }
        if (productImage) {
            this.productData.image = decodeURIComponent(productImage);
        }

        // Update UI
        document.getElementById('productTitle').textContent = this.productData.title;
        document.getElementById('productPrice').textContent = this.formatPrice(this.productData.price);
        document.getElementById('productImage').src = this.productData.image;
        document.getElementById('summaryProductPrice').textContent = this.formatPrice(this.productData.price);
    }

    async loadWilayas() {
        const wilayaSelect = document.getElementById('wilayaSelect');
        const loading = document.getElementById('wilayaLoading');

        try {
            loading.classList.add('show');
            wilayaSelect.disabled = true;

            const response = await fetch('php/api/geographic-data.php?action=wilayas');

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.wilayas) {
                wilayaSelect.innerHTML = '<option value="">-- اختر الولاية --</option>';

                data.wilayas.forEach(wilaya => {
                    const option = document.createElement('option');
                    option.value = wilaya.wilaya_code;
                    option.textContent = `${wilaya.wilaya_name_ar} (${wilaya.wilaya_code})`;
                    option.dataset.zone = wilaya.zone_number;
                    wilayaSelect.appendChild(option);
                });

                console.log(`Loaded ${data.wilayas.length} wilayas`);
            } else {
                throw new Error(data.message || 'فشل في تحميل الولايات');
            }
        } catch (error) {
            console.error('Error loading wilayas:', error);
            this.showError('wilayaError', 'فشل في تحميل قائمة الولايات. يرجى إعادة تحميل الصفحة.');
        } finally {
            loading.classList.remove('show');
            wilayaSelect.disabled = false;
        }
    }

    async loadCommunes(wilayaCode) {
        const communeSelect = document.getElementById('communeSelect');
        const loading = document.getElementById('communeLoading');

        try {
            loading.classList.add('show');
            communeSelect.disabled = true;
            communeSelect.innerHTML = '<option value="">-- اختر البلدية --</option>';

            const response = await fetch(`php/api/geographic-data.php?action=communes&wilaya_code=${wilayaCode}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.communes) {
                data.communes.forEach(commune => {
                    const option = document.createElement('option');
                    option.value = commune.commune_code;
                    option.textContent = commune.commune_name_ar;
                    communeSelect.appendChild(option);
                });

                console.log(`Loaded ${data.communes.length} communes for wilaya ${wilayaCode}`);
            } else {
                throw new Error(data.message || 'فشل في تحميل البلديات');
            }
        } catch (error) {
            console.error('Error loading communes:', error);
            this.showError('communeError', 'فشل في تحميل قائمة البلديات.');
        } finally {
            loading.classList.remove('show');
            communeSelect.disabled = false;
        }
    }

    async calculateShipping(wilayaCode) {
        const shippingInfo = document.getElementById('shippingInfo');
        const shippingDetails = document.getElementById('shippingDetails');

        try {
            // Show calculating state
            shippingInfo.style.display = 'block';
            shippingInfo.className = 'shipping-info calculating';
            shippingDetails.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري حساب تكلفة الشحن...';

            // Check for shipping overrides first
            const overrideResponse = await fetch(`php/api/geographic-data.php?action=shipping_override&location_type=wilaya&location_code=${wilayaCode}`);
            const overrideData = await overrideResponse.json();

            let shippingCost, deliveryTime, zoneName;

            if (overrideData.success && overrideData.has_override) {
                // Use override pricing
                shippingCost = parseFloat(overrideData.override.shipping_cost);
                deliveryTime = overrideData.override.delivery_days;
                zoneName = 'منطقة مخصصة';
            } else {
                // Use standard Yalidine Express pricing
                const response = await fetch(`php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilayaCode}&weight=${this.productData.weight}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success) {
                    shippingCost = data.cost_breakdown.total_cost;
                    deliveryTime = data.zone_info.delivery_time;
                    zoneName = data.zone_info.zone_name;
                } else {
                    throw new Error(data.message || 'فشل في حساب تكلفة الشحن');
                }
            }

            // Update shipping data
            this.shippingData = {
                cost: shippingCost,
                zone: zoneName,
                deliveryTime: deliveryTime,
                wilayaCode: wilayaCode,
                communeCode: null
            };

            // Show success state
            shippingInfo.className = 'shipping-info';
            shippingDetails.innerHTML = `
                <div><strong>المنطقة:</strong> <span class="zone-badge">${zoneName}</span></div>
                <div><strong>تكلفة الشحن:</strong> ${this.formatPrice(shippingCost)}</div>
                <div class="delivery-estimate"><strong>مدة التوصيل:</strong> ${deliveryTime}</div>
            `;

            // Update order summary
            this.updateOrderSummary();

            console.log('Shipping calculated:', this.shippingData);

        } catch (error) {
            console.error('Error calculating shipping:', error);

            // Show error state
            shippingInfo.className = 'shipping-info error';
            shippingDetails.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                خطأ في حساب تكلفة الشحن: ${error.message}
            `;

            // Reset shipping data
            this.shippingData.cost = 0;
            this.updateOrderSummary();
        }
    }

    setupEventListeners() {
        // Wilaya selection
        document.getElementById('wilayaSelect').addEventListener('change', (e) => {
            const wilayaCode = e.target.value;
            const communeSelect = document.getElementById('communeSelect');

            // Reset commune selection
            communeSelect.innerHTML = '<option value="">-- اختر البلدية --</option>';
            communeSelect.disabled = true;

            // Hide shipping info
            document.getElementById('shippingInfo').style.display = 'none';
            this.shippingData.cost = 0;
            this.updateOrderSummary();

            if (wilayaCode) {
                this.loadCommunes(wilayaCode);
                this.calculateShipping(wilayaCode);
            }

            this.validateForm();
        });

        // Commune selection
        document.getElementById('communeSelect').addEventListener('change', (e) => {
            this.shippingData.communeCode = e.target.value;
            this.validateForm();
        });

        // Payment method selection
        document.getElementById('paymentMethod').addEventListener('change', (e) => {
            const ccpInfo = document.getElementById('ccpInfo');
            if (e.target.value === 'ccp') {
                ccpInfo.style.display = 'block';
            } else {
                ccpInfo.style.display = 'none';
            }
            this.validateForm();
        });

        // Form validation on input
        const requiredFields = ['fullName', 'phone', 'wilayaSelect', 'communeSelect', 'address', 'paymentMethod'];
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', () => this.validateForm());
                field.addEventListener('change', () => this.validateForm());
            }
        });

        // Form submission
        document.getElementById('orderForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitOrder();
        });
    }

    validateForm() {
        const requiredFields = [
            { id: 'fullName', message: 'الاسم الكامل مطلوب' },
            { id: 'phone', message: 'رقم الهاتف مطلوب' },
            { id: 'wilayaSelect', message: 'يرجى اختيار الولاية' },
            { id: 'communeSelect', message: 'يرجى اختيار البلدية' },
            { id: 'address', message: 'العنوان التفصيلي مطلوب' },
            { id: 'paymentMethod', message: 'يرجى اختيار طريقة الدفع' }
        ];

        let isValid = true;

        requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            const errorElement = document.getElementById(field.id.replace('Select', '') + 'Error');

            if (!element.value.trim()) {
                this.showError(errorElement.id, field.message);
                isValid = false;
            } else {
                this.hideError(errorElement.id);
            }
        });

        // Validate phone number format
        const phone = document.getElementById('phone').value;
        if (phone && !/^(05|06|07)\d{8}$/.test(phone.replace(/\s/g, ''))) {
            this.showError('phoneError', 'رقم الهاتف غير صحيح (مثال: 0555123456)');
            isValid = false;
        }

        // Validate email if provided
        const email = document.getElementById('email').value;
        if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            this.showError('emailError', 'البريد الإلكتروني غير صحيح');
            isValid = false;
        }

        // Check if shipping is calculated
        if (this.shippingData.cost === 0 && document.getElementById('wilayaSelect').value) {
            isValid = false;
        }

        document.getElementById('submitBtn').disabled = !isValid;
        return isValid;
    }

    async submitOrder() {
        if (!this.validateForm()) {
            return;
        }

        const submitBtn = document.getElementById('submitBtn');
        const originalText = submitBtn.innerHTML;

        try {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إرسال الطلب...';

            // Collect form data
            const formData = new FormData(document.getElementById('orderForm'));
            const orderData = {
                product: this.productData,
                shipping: this.shippingData,
                customer: {
                    fullName: formData.get('fullName'),
                    phone: formData.get('phone'),
                    email: formData.get('email'),
                    wilaya: formData.get('wilaya'),
                    commune: formData.get('commune'),
                    address: formData.get('address'),
                    paymentMethod: formData.get('paymentMethod'),
                    ccpNumber: formData.get('ccpNumber'),
                    notes: formData.get('notes')
                },
                total: this.productData.price + this.shippingData.cost,
                timestamp: new Date().toISOString()
            };

            console.log('Submitting order:', orderData);

            // Here you would send the order to your backend
            // const response = await fetch('php/api/orders.php', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(orderData)
            // });

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Show success message
            alert('تم إرسال طلبك بنجاح! سنتواصل معك قريباً لتأكيد الطلب.');

            // Optionally redirect to success page
            // window.location.href = 'order-success.html';

        } catch (error) {
            console.error('Error submitting order:', error);
            alert('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    }

    updateOrderSummary() {
        const total = this.productData.price + this.shippingData.cost;

        document.getElementById('summaryShippingCost').textContent =
            this.shippingData.cost > 0 ? this.formatPrice(this.shippingData.cost) : '-- دج';

        document.getElementById('summaryTotal').textContent = this.formatPrice(total);
    }

    formatPrice(price) {
        return new Intl.NumberFormat('ar-DZ', {
            style: 'decimal',
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(price) + ' دج';
    }

    showError(elementId, message) {
        const errorElement = document.getElementById(elementId);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    hideError(elementId) {
        const errorElement = document.getElementById(elementId);
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }
}

// Initialize the order form when the page loads
document.addEventListener('DOMContentLoaded', () => {
    console.log('🛒 Order form initialized');
    new OrderFormManager();
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { OrderFormManager };
}
