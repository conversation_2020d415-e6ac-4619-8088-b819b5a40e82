<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error-log {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success-log {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning-log {
            background: #fff3cd;
            color: #856404;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .admin-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-loading {
            background: #ffc107;
            animation: pulse 1s infinite;
        }
        .status-success {
            background: #28a745;
        }
        .status-error {
            background: #dc3545;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-cogs"></i> اختبار تحميل لوحة التحكم</h1>
        <p>اختبار شامل لحل مشكلة "جاري التحميل..." في لوحة التحكم</p>
        
        <div class="test-section">
            <h2><i class="fas fa-play"></i> اختبارات التحميل</h2>
            
            <button class="btn btn-primary" onclick="testAdminPanelLoading()">
                <i class="fas fa-test-tube"></i> اختبار تحميل لوحة التحكم
            </button>
            
            <button class="btn btn-success" onclick="openAdminPanel()">
                <i class="fas fa-external-link-alt"></i> فتح لوحة التحكم في نافذة جديدة
            </button>
            
            <button class="btn btn-info" onclick="checkAdminFiles()">
                <i class="fas fa-file-code"></i> فحص ملفات لوحة التحكم
            </button>
            
            <button class="btn btn-warning" onclick="clearConsole()">
                <i class="fas fa-eraser"></i> مسح وحدة التحكم
            </button>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-chart-line"></i> حالة التحميل</h2>
            <div id="loadingStatus">
                <div class="alert alert-info">
                    <span class="status-indicator status-loading"></span>
                    جاري فحص حالة لوحة التحكم...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-terminal"></i> سجل الاختبارات</h2>
            <div id="consoleOutput" class="console-output">
                <div class="success-log">🚀 جاهز لاختبار تحميل لوحة التحكم...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-browser"></i> معاينة لوحة التحكم</h2>
            <p>معاينة مباشرة لحالة تحميل لوحة التحكم:</p>
            <iframe id="adminFrame" class="admin-frame" src="about:blank"></iframe>
            <div class="mt-3">
                <button class="btn btn-primary" onclick="loadAdminInFrame()">
                    <i class="fas fa-reload"></i> تحميل لوحة التحكم في الإطار
                </button>
                <button class="btn btn-secondary" onclick="reloadAdminFrame()">
                    <i class="fas fa-redo"></i> إعادة تحميل الإطار
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-clipboard-check"></i> نتائج الاختبار</h2>
            <div id="testResults">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> جاري فحص النظام...
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Console logging function
        function logToConsole(message, type = "info") {
            const console = document.getElementById("consoleOutput");
            const timestamp = new Date().toLocaleTimeString("ar-DZ");
            const icon = type === "error" ? "❌" : type === "success" ? "✅" : type === "warning" ? "⚠️" : "ℹ️";
            
            const logEntry = document.createElement("div");
            logEntry.className = type === "error" ? "error-log" : type === "success" ? "success-log" : type === "warning" ? "warning-log" : "";
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            console.appendChild(logEntry);
            console.scrollTop = console.scrollHeight;
        }
        
        // Test admin panel loading
        function testAdminPanelLoading() {
            logToConsole("بدء اختبار تحميل لوحة التحكم...");
            
            updateLoadingStatus("loading", "جاري اختبار تحميل لوحة التحكم...");
            
            // Test if admin panel loads without getting stuck
            const testFrame = document.createElement('iframe');
            testFrame.style.display = 'none';
            testFrame.src = '/admin/';
            
            let loadingTimeout;
            let loaded = false;
            
            testFrame.onload = function() {
                if (loaded) return;
                loaded = true;
                
                clearTimeout(loadingTimeout);
                
                try {
                    const frameDoc = testFrame.contentDocument || testFrame.contentWindow.document;
                    const loadingIndicator = frameDoc.getElementById('loading-indicator');
                    const body = frameDoc.body;
                    
                    if (loadingIndicator) {
                        const isLoadingVisible = window.getComputedStyle(loadingIndicator).display !== 'none';
                        
                        if (isLoadingVisible) {
                            logToConsole("⚠️ لوحة التحكم لا تزال تظهر 'جاري التحميل...'", "warning");
                            updateLoadingStatus("error", "لوحة التحكم عالقة في حالة التحميل");
                            
                            // Try to force load
                            setTimeout(() => {
                                testForceLoad(frameDoc);
                            }, 2000);
                        } else {
                            logToConsole("✅ لوحة التحكم تم تحميلها بنجاح!", "success");
                            updateLoadingStatus("success", "لوحة التحكم تعمل بشكل صحيح");
                        }
                    } else {
                        logToConsole("⚠️ لم يتم العثور على مؤشر التحميل", "warning");
                    }
                    
                    // Check if content is visible
                    const hasContentLoaded = body.classList.contains('content-loaded');
                    if (hasContentLoaded) {
                        logToConsole("✅ تم تطبيق فئة content-loaded", "success");
                    } else {
                        logToConsole("⚠️ فئة content-loaded غير مطبقة", "warning");
                    }
                    
                } catch (error) {
                    logToConsole(`❌ خطأ في الوصول إلى محتوى الإطار: ${error.message}`, "error");
                    updateLoadingStatus("error", "خطأ في الوصول إلى لوحة التحكم");
                }
                
                document.body.removeChild(testFrame);
            };
            
            testFrame.onerror = function() {
                if (loaded) return;
                loaded = true;
                
                clearTimeout(loadingTimeout);
                logToConsole("❌ فشل في تحميل لوحة التحكم", "error");
                updateLoadingStatus("error", "فشل في تحميل لوحة التحكم");
                document.body.removeChild(testFrame);
            };
            
            // Timeout after 10 seconds
            loadingTimeout = setTimeout(() => {
                if (loaded) return;
                loaded = true;
                
                logToConsole("⏰ انتهت مهلة اختبار التحميل (10 ثوانٍ)", "warning");
                updateLoadingStatus("error", "انتهت مهلة التحميل");
                document.body.removeChild(testFrame);
            }, 10000);
            
            document.body.appendChild(testFrame);
        }
        
        // Test force load functionality
        function testForceLoad(frameDoc) {
            logToConsole("🔧 اختبار آلية الإجبار على التحميل...");
            
            try {
                // Simulate emergency loading fix
                frameDoc.body.classList.add('content-loaded');
                
                const loadingIndicator = frameDoc.getElementById('loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
                
                frameDoc.body.style.visibility = 'visible';
                
                logToConsole("✅ تم تطبيق إصلاح التحميل الطارئ", "success");
                updateLoadingStatus("success", "تم إصلاح التحميل بنجاح");
                
            } catch (error) {
                logToConsole(`❌ فشل في تطبيق إصلاح التحميل: ${error.message}`, "error");
            }
        }
        
        // Update loading status
        function updateLoadingStatus(status, message) {
            const statusDiv = document.getElementById("loadingStatus");
            let alertClass, iconClass, statusClass;
            
            switch (status) {
                case "loading":
                    alertClass = "alert-warning";
                    iconClass = "fas fa-spinner fa-spin";
                    statusClass = "status-loading";
                    break;
                case "success":
                    alertClass = "alert-success";
                    iconClass = "fas fa-check-circle";
                    statusClass = "status-success";
                    break;
                case "error":
                    alertClass = "alert-danger";
                    iconClass = "fas fa-times-circle";
                    statusClass = "status-error";
                    break;
                default:
                    alertClass = "alert-info";
                    iconClass = "fas fa-info-circle";
                    statusClass = "status-loading";
            }
            
            statusDiv.innerHTML = `
                <div class="alert ${alertClass}">
                    <span class="status-indicator ${statusClass}"></span>
                    <i class="${iconClass}"></i> ${message}
                </div>
            `;
        }
        
        // Open admin panel in new window
        function openAdminPanel() {
            logToConsole("فتح لوحة التحكم في نافذة جديدة...");
            window.open('/admin/', '_blank');
        }
        
        // Load admin in frame
        function loadAdminInFrame() {
            logToConsole("تحميل لوحة التحكم في الإطار...");
            const frame = document.getElementById('adminFrame');
            frame.src = '/admin/';
        }
        
        // Reload admin frame
        function reloadAdminFrame() {
            logToConsole("إعادة تحميل إطار لوحة التحكم...");
            const frame = document.getElementById('adminFrame');
            frame.src = frame.src;
        }
        
        // Check admin files
        function checkAdminFiles() {
            logToConsole("فحص ملفات لوحة التحكم...");
            
            const filesToCheck = [
                '/admin/js/emergency-loading-fix.js',
                '/admin/css/ultimate-visibility-fix.css',
                '/admin/css/admin-loading-fix.css',
                '/admin/js/admin.js',
                '/admin/index.html'
            ];
            
            filesToCheck.forEach(file => {
                fetch(file, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            logToConsole(`✅ ${file}: موجود`, "success");
                        } else {
                            logToConsole(`❌ ${file}: غير موجود (${response.status})`, "error");
                        }
                    })
                    .catch(error => {
                        logToConsole(`❌ ${file}: خطأ في الفحص`, "error");
                    });
            });
        }
        
        // Clear console
        function clearConsole() {
            document.getElementById("consoleOutput").innerHTML = "<div class='success-log'>🧹 تم مسح سجل الاختبارات</div>";
        }
        
        // System status check
        function checkSystemStatus() {
            const statusDiv = document.getElementById("testResults");
            let statusHTML = "";
            
            const checks = [
                {
                    name: "Emergency Loading Fix",
                    test: () => fetch('/admin/js/emergency-loading-fix.js', { method: 'HEAD' }).then(r => r.ok),
                    details: "سكريبت إصلاح التحميل الطارئ"
                },
                {
                    name: "Ultimate Visibility CSS",
                    test: () => fetch('/admin/css/ultimate-visibility-fix.css', { method: 'HEAD' }).then(r => r.ok),
                    details: "CSS إصلاح الظهور"
                },
                {
                    name: "Admin Loading Fix CSS",
                    test: () => fetch('/admin/css/admin-loading-fix.css', { method: 'HEAD' }).then(r => r.ok),
                    details: "CSS إصلاح تحميل لوحة التحكم"
                }
            ];
            
            Promise.all(checks.map(check => 
                check.test().then(result => ({ ...check, status: result })).catch(() => ({ ...check, status: false }))
            )).then(results => {
                results.forEach(check => {
                    const alertClass = check.status ? "alert-success" : "alert-danger";
                    const icon = check.status ? "fas fa-check-circle" : "fas fa-times-circle";
                    
                    statusHTML += `
                        <div class="alert ${alertClass}">
                            <i class="${icon}"></i> 
                            <strong>${check.name}:</strong> ${check.details} - ${check.status ? 'يعمل' : 'لا يعمل'}
                        </div>
                    `;
                });
                
                statusDiv.innerHTML = statusHTML;
            });
        }
        
        // Initialize when page loads
        document.addEventListener("DOMContentLoaded", function() {
            logToConsole("تم تحميل صفحة الاختبار", "success");
            
            // Check system status
            setTimeout(checkSystemStatus, 1000);
            
            // Auto-test after 2 seconds
            setTimeout(() => {
                logToConsole("بدء الاختبار التلقائي...", "info");
                testAdminPanelLoading();
            }, 2000);
        });
    </script>
</body>
</html>
