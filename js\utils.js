/**
 * Shared JavaScript Utilities for Mossaab Landing Page
 * Consolidates duplicate code across admin.js, landing-pages.js, and other JS files
 */

// ===== API UTILITIES =====

/**
 * Safe API call with standardized error handling
 * Consolidates duplicate API calling patterns from admin.js and landing-pages.js
 */
async function safeApiCall(url, options = {}) {
    try {
        console.log(`🌐 API Call: ${url}`);

        // Add default headers
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        const mergedOptions = { ...defaultOptions, ...options };
        if (mergedOptions.headers && options.headers) {
            mergedOptions.headers = { ...defaultOptions.headers, ...options.headers };
        }

        const response = await fetch(url, mergedOptions);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();

        // Check if response is empty
        if (!text.trim()) {
            console.warn('Empty response from API:', url);
            return { success: false, message: 'Empty response from server' };
        }

        // Try to parse JSON
        try {
            const data = JSON.parse(text);
            console.log(`✅ API Success: ${url}`, data);
            return data;
        } catch (parseError) {
            console.error('JSON parse error for response:', text.substring(0, 200));
            throw new Error('Invalid JSON response from server');
        }
    } catch (error) {
        console.error(`❌ API call failed: ${url}`, error);
        throw error;
    }
}

/**
 * Standardized API response handler
 * Consolidates response handling patterns from multiple files
 */
function handleApiResponse(response, successCallback, errorCallback) {
    if (response.success) {
        if (typeof successCallback === 'function') {
            successCallback(response.data || response);
        }
    } else {
        const errorMessage = response.message || response.error || 'حدث خطأ غير معروف';
        console.error('API Error:', errorMessage);
        if (typeof errorCallback === 'function') {
            errorCallback(errorMessage);
        } else {
            showNotification(errorMessage, 'error');
        }
    }
}

// ===== NOTIFICATION UTILITIES =====

/**
 * Unified notification system
 * Consolidates notification patterns from admin.js and other files
 */
const NotificationManager = {
    init() {
        // Create notification container if it doesn't exist
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
    },

    show(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notification-container');
        if (!container) {
            console.log(`${type.toUpperCase()}: ${message}`);
            return;
        }

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            background: ${this.getTypeColor(type)};
            color: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            animation: slideIn 0.3s ease-out;
            direction: rtl;
            text-align: right;
            font-family: 'Noto Sans Arabic', sans-serif;
        `;
        notification.textContent = message;

        // Add close button
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '&times;';
        closeBtn.style.cssText = `
            float: left;
            cursor: pointer;
            font-size: 20px;
            margin-left: 10px;
        `;
        closeBtn.onclick = () => this.remove(notification);
        notification.appendChild(closeBtn);

        container.appendChild(notification);

        // Auto remove after duration
        if (duration > 0) {
            setTimeout(() => this.remove(notification), duration);
        }
    },

    getTypeColor(type) {
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        return colors[type] || colors.info;
    },

    remove(notification) {
        if (notification && notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    },

    showSuccess(message) { this.show(message, 'success'); },
    showError(message) { this.show(message, 'error'); },
    showWarning(message) { this.show(message, 'warning'); },
    showInfo(message) { this.show(message, 'info'); }
};

// Global notification function for backward compatibility
function showNotification(message, type = 'info') {
    NotificationManager.show(message, type);
}

// ===== DOM UTILITIES =====

/**
 * Safe DOM element selection with error handling
 * Consolidates DOM manipulation patterns
 */
function safeGetElement(selector, context = document) {
    try {
        const element = context.querySelector(selector);
        if (!element) {
            console.warn(`Element not found: ${selector}`);
        }
        return element;
    } catch (error) {
        console.error(`Error selecting element: ${selector}`, error);
        return null;
    }
}

/**
 * Safe event listener attachment
 */
function safeAddEventListener(selector, event, handler, context = document) {
    const element = safeGetElement(selector, context);
    if (element && typeof handler === 'function') {
        element.addEventListener(event, handler);
        return true;
    }
    return false;
}

/**
 * Update element content safely
 */
function safeUpdateContent(selector, content, context = document) {
    const element = safeGetElement(selector, context);
    if (element) {
        element.textContent = content;
        return true;
    }
    return false;
}

// ===== VALIDATION UTILITIES =====

/**
 * Input validation utilities
 * Consolidates validation patterns from forms
 */
const ValidationUtils = {
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    isValidPhone(phone) {
        // Algerian phone number validation
        const phoneRegex = /^(0|\+213)[5-7][0-9]{8}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    },

    isValidPrice(price) {
        const priceRegex = /^\d+(\.\d{1,2})?$/;
        return priceRegex.test(price) && parseFloat(price) > 0;
    },

    sanitizeInput(input) {
        if (typeof input !== 'string') return input;
        return input.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    },

    validateRequired(value, fieldName) {
        if (!value || value.trim() === '') {
            throw new Error(`${fieldName} مطلوب`);
        }
        return true;
    }
};

// ===== ERROR HANDLING UTILITIES =====

/**
 * Global error handlers
 * Consolidates error handling patterns from admin.js and other files
 */
const ErrorHandler = {
    init() {
        // Handle selection-related errors
        window.addEventListener('error', (event) => {
            if (event.error && event.error.message && (
                event.error.message.includes('rangeCount') ||
                event.error.message.includes('selection is null') ||
                event.error.message.includes('mozInputSource')
            )) {
                event.preventDefault();
                console.debug('Selection error prevented:', event.error.message);
                return true;
            }

            // Handle JSON parsing errors
            if (event.error instanceof SyntaxError && event.error.message.includes('JSON')) {
                event.preventDefault();
                console.warn('JSON parsing error prevented:', event.error.message);
                return true;
            }
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            event.preventDefault();
        });
    },

    logError(error, context = '') {
        const errorInfo = {
            message: error.message,
            stack: error.stack,
            context: context,
            timestamp: new Date().toISOString(),
            url: window.location.href
        };
        console.error('Error logged:', errorInfo);
        // In production, send to error tracking service
    }
};

// ===== LOADING UTILITIES =====

/**
 * Loading state management
 * Consolidates loading patterns from multiple files
 */
const LoadingManager = {
    show(element, message = 'جاري التحميل...') {
        if (typeof element === 'string') {
            element = safeGetElement(element);
        }
        if (element) {
            element.style.opacity = '0.5';
            element.style.pointerEvents = 'none';

            // Add loading indicator
            const loader = document.createElement('div');
            loader.className = 'loading-indicator';
            loader.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 1000;
            `;
            loader.textContent = message;

            if (element.style.position !== 'relative' && element.style.position !== 'absolute') {
                element.style.position = 'relative';
            }
            element.appendChild(loader);
        }
    },

    hide(element) {
        if (typeof element === 'string') {
            element = safeGetElement(element);
        }
        if (element) {
            element.style.opacity = '1';
            element.style.pointerEvents = 'auto';

            const loader = element.querySelector('.loading-indicator');
            if (loader) {
                loader.remove();
            }
        }
    }
};

// ===== LAZY LOADING UTILITIES =====

/**
 * Lazy Loading Manager with Intersection Observer
 * Optimizes image loading for better performance
 */
const LazyLoadManager = {
    observer: null,

    init() {
        // Check if Intersection Observer is supported
        if (!('IntersectionObserver' in window)) {
            console.warn('Intersection Observer not supported, falling back to immediate loading');
            this.loadAllImages();
            return;
        }

        // Create intersection observer
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    this.observer.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: '50px 0px', // Start loading 50px before image comes into view
            threshold: 0.1
        });

        // Observe all lazy images
        this.observeImages();
    },

    observeImages() {
        const lazyImages = document.querySelectorAll('img[data-src], img[data-srcset]');
        lazyImages.forEach(img => {
            this.observer.observe(img);
        });
    },

    loadImage(img) {
        // Create placeholder while loading
        this.showImagePlaceholder(img);

        // Load the actual image
        const actualImg = new Image();

        actualImg.onload = () => {
            // Image loaded successfully
            if (img.dataset.srcset) {
                img.srcset = img.dataset.srcset;
            }
            if (img.dataset.src) {
                img.src = img.dataset.src;
            }

            // Add fade-in animation
            img.style.opacity = '0';
            img.style.transition = 'opacity 0.3s ease-in-out';

            // Remove placeholder and show image
            this.hideImagePlaceholder(img);

            setTimeout(() => {
                img.style.opacity = '1';
            }, 50);

            // Clean up data attributes
            delete img.dataset.src;
            delete img.dataset.srcset;

            img.classList.add('lazy-loaded');
        };

        actualImg.onerror = () => {
            // Handle loading error
            this.showImageError(img);
        };

        // Start loading
        if (img.dataset.srcset) {
            actualImg.srcset = img.dataset.srcset;
        }
        if (img.dataset.src) {
            actualImg.src = img.dataset.src;
        }
    },

    showImagePlaceholder(img) {
        if (!img.classList.contains('lazy-placeholder')) {
            img.classList.add('lazy-placeholder');
            img.style.backgroundColor = '#f8f9fa';
            img.style.backgroundImage = `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23dee2e6'%3E%3Cpath d='M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z'/%3E%3C/svg%3E")`;
            img.style.backgroundRepeat = 'no-repeat';
            img.style.backgroundPosition = 'center';
            img.style.backgroundSize = '48px 48px';
        }
    },

    hideImagePlaceholder(img) {
        img.classList.remove('lazy-placeholder');
        img.style.backgroundColor = '';
        img.style.backgroundImage = '';
        img.style.backgroundRepeat = '';
        img.style.backgroundPosition = '';
        img.style.backgroundSize = '';
    },

    showImageError(img) {
        img.classList.add('lazy-error');
        img.alt = 'فشل في تحميل الصورة';
        img.style.backgroundColor = '#f8d7da';
        img.style.backgroundImage = `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23721c24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E")`;
        img.style.backgroundRepeat = 'no-repeat';
        img.style.backgroundPosition = 'center';
        img.style.backgroundSize = '32px 32px';
    },

    loadAllImages() {
        // Fallback for browsers without Intersection Observer
        const lazyImages = document.querySelectorAll('img[data-src], img[data-srcset]');
        lazyImages.forEach(img => {
            if (img.dataset.srcset) {
                img.srcset = img.dataset.srcset;
            }
            if (img.dataset.src) {
                img.src = img.dataset.src;
            }
            delete img.dataset.src;
            delete img.dataset.srcset;
        });
    },

    // Method to add new images to lazy loading
    observeNewImages(container) {
        if (!this.observer) return;

        const newImages = container.querySelectorAll('img[data-src], img[data-srcset]');
        newImages.forEach(img => {
            this.observer.observe(img);
        });
    }
};

// ===== INITIALIZATION =====

// Initialize utilities when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    NotificationManager.init();
    ErrorHandler.init();
    LazyLoadManager.init();
    console.log('✅ Shared utilities initialized');
});

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Export for module systems (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        safeApiCall,
        handleApiResponse,
        NotificationManager,
        ValidationUtils,
        ErrorHandler,
        LoadingManager,
        safeGetElement,
        safeAddEventListener,
        safeUpdateContent
    };
}
