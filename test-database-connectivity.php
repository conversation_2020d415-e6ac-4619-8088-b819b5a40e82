<?php
/**
 * Test Database Connectivity
 * Test de connectivité à la base de données pour les APIs
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <title>Test de Connectivité Base de Données</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-passed { border: 3px solid #28a745; background: #d4edda; }
        .test-failed { border: 3px solid #dc3545; background: #f8d7da; }
        h1, h2, h3 { color: #333; }
        .test-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .test-failed-result { border-left-color: #dc3545; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; white-space: pre-wrap; }
        .api-test { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .api-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .api-card.success { border-left-color: #28a745; background: #d4edda; }
        .api-card.error { border-left-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔍 Test de Connectivité Base de Données</h1>";
echo "<p>Vérification de la connectivité des APIs avec la base de données</p>";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

try {
    // TEST 1: Configuration Database
    echo "<div class='section test-passed'>";
    echo "<h2>✅ Test 1: Configuration Base de Données</h2>";
    
    $totalTests++;
    try {
        require_once 'php/config.php';
        echo "<div class='success'>✅ Configuration chargée avec succès</div>";
        
        $pdo = getPDOConnection();
        if ($pdo) {
            echo "<div class='success'>✅ Connexion PDO établie</div>";
            $passedTests++;
            $testResults[] = "Database connection: PASSED";
        } else {
            echo "<div class='error'>❌ Échec de la connexion PDO</div>";
            $testResults[] = "Database connection: FAILED";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Erreur de configuration: " . $e->getMessage() . "</div>";
        $testResults[] = "Database connection: ERROR";
    }
    
    echo "</div>";
    
    // TEST 2: Tables Existence
    echo "<div class='section test-passed'>";
    echo "<h2>✅ Test 2: Existence des Tables</h2>";
    
    $totalTests++;
    $tablesFound = 0;
    $expectedTables = ['produits', 'products', 'books', 'commandes', 'orders', 'landing_pages', 'users'];
    
    foreach ($expectedTables as $table) {
        try {
            $stmt = $pdo->prepare("SELECT 1 FROM $table LIMIT 1");
            $stmt->execute();
            echo "<div class='success'>✅ Table '$table' existe</div>";
            $tablesFound++;
        } catch (PDOException $e) {
            echo "<div class='warning'>⚠️ Table '$table' n'existe pas</div>";
        }
    }
    
    if ($tablesFound >= 3) {
        echo "<div class='success'>✅ Suffisamment de tables trouvées ($tablesFound)</div>";
        $passedTests++;
        $testResults[] = "Tables existence: PASSED";
    } else {
        echo "<div class='error'>❌ Pas assez de tables trouvées ($tablesFound)</div>";
        $testResults[] = "Tables existence: FAILED";
    }
    
    echo "</div>";
    
    // TEST 3: Dashboard Stats API
    echo "<div class='section test-passed'>";
    echo "<h2>✅ Test 3: API Dashboard Statistics</h2>";
    
    $totalTests++;
    try {
        // Capture output from dashboard-stats.php
        ob_start();
        $_SERVER['REQUEST_METHOD'] = 'GET';
        include 'php/api/dashboard-stats.php';
        $output = ob_get_clean();
        
        // Parse JSON response
        $response = json_decode($output, true);
        
        if ($response && $response['success']) {
            echo "<div class='success'>✅ API Dashboard Stats fonctionne</div>";
            echo "<div class='info'>📊 Données récupérées:</div>";
            echo "<div class='code-block'>";
            echo "• Total Produits: " . $response['data']['totalBooks'] . "\n";
            echo "• Nouvelles Commandes: " . $response['data']['newOrders'] . "\n";
            echo "• Total Ventes: " . $response['data']['totalSales'] . " DZD\n";
            echo "• Landing Pages: " . $response['data']['totalLandingPages'] . "\n";
            echo "• Utilisateurs Actifs: " . $response['data']['activeUsers'] . "\n";
            echo "</div>";
            $passedTests++;
            $testResults[] = "Dashboard Stats API: PASSED";
        } else {
            echo "<div class='error'>❌ API Dashboard Stats échoue</div>";
            echo "<div class='code-block'>Réponse: " . $output . "</div>";
            $testResults[] = "Dashboard Stats API: FAILED";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Erreur API Dashboard Stats: " . $e->getMessage() . "</div>";
        $testResults[] = "Dashboard Stats API: ERROR";
    }
    
    echo "</div>";
    
    // TEST 4: Products Data
    echo "<div class='section test-passed'>";
    echo "<h2>✅ Test 4: Données Produits</h2>";
    
    $totalTests++;
    try {
        // Test direct database query for products
        $productsTable = 'produits';
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM produits");
            $stmt->execute();
            $productsCount = $stmt->fetch()['count'];
        } catch (PDOException $e) {
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products");
            $stmt->execute();
            $productsCount = $stmt->fetch()['count'];
            $productsTable = 'products';
        }
        
        echo "<div class='success'>✅ Table produits '$productsTable' accessible</div>";
        echo "<div class='info'>📦 Nombre de produits: $productsCount</div>";
        
        if ($productsCount > 0) {
            // Get sample products
            $stmt = $pdo->prepare("SELECT titre, prix FROM $productsTable LIMIT 3");
            $stmt->execute();
            $sampleProducts = $stmt->fetchAll();
            
            echo "<div class='info'>📋 Exemples de produits:</div>";
            echo "<div class='code-block'>";
            foreach ($sampleProducts as $product) {
                echo "• " . $product['titre'] . " - " . $product['prix'] . " DZD\n";
            }
            echo "</div>";
            
            $passedTests++;
            $testResults[] = "Products data: PASSED";
        } else {
            echo "<div class='warning'>⚠️ Aucun produit trouvé dans la base</div>";
            $testResults[] = "Products data: EMPTY";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Erreur données produits: " . $e->getMessage() . "</div>";
        $testResults[] = "Products data: ERROR";
    }
    
    echo "</div>";
    
    // TEST 5: Orders Data
    echo "<div class='section test-passed'>";
    echo "<h2>✅ Test 5: Données Commandes</h2>";
    
    $totalTests++;
    try {
        $ordersTable = 'commandes';
        $ordersCount = 0;
        
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM commandes");
            $stmt->execute();
            $ordersCount = $stmt->fetch()['count'];
        } catch (PDOException $e) {
            try {
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders");
                $stmt->execute();
                $ordersCount = $stmt->fetch()['count'];
                $ordersTable = 'orders';
            } catch (PDOException $e2) {
                echo "<div class='warning'>⚠️ Aucune table de commandes trouvée</div>";
            }
        }
        
        if ($ordersCount >= 0) {
            echo "<div class='success'>✅ Table commandes '$ordersTable' accessible</div>";
            echo "<div class='info'>🛒 Nombre de commandes: $ordersCount</div>";
            $passedTests++;
            $testResults[] = "Orders data: PASSED";
        } else {
            echo "<div class='warning'>⚠️ Table commandes non accessible</div>";
            $testResults[] = "Orders data: FAILED";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Erreur données commandes: " . $e->getMessage() . "</div>";
        $testResults[] = "Orders data: ERROR";
    }
    
    echo "</div>";
    
    // FINAL RESULTS
    $successRate = ($passedTests / $totalTests) * 100;
    
    echo "<div class='section'>";
    echo "<h2>📊 Résultats Finaux</h2>";
    
    if ($successRate >= 80) {
        echo "<div class='success'>";
        echo "<h3>🎉 Excellent! La connectivité fonctionne bien ($passedTests/$totalTests)</h3>";
        echo "<p>Taux de réussite: " . round($successRate, 1) . "%</p>";
        echo "</div>";
    } elseif ($successRate >= 60) {
        echo "<div class='warning'>";
        echo "<h3>⚠️ Bon! Quelques problèmes mineurs ($passedTests/$totalTests)</h3>";
        echo "<p>Taux de réussite: " . round($successRate, 1) . "%</p>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ Problèmes de connectivité détectés ($passedTests/$totalTests)</h3>";
        echo "<p>Taux de réussite: " . round($successRate, 1) . "%</p>";
        echo "</div>";
    }
    
    echo "<div class='api-test'>";
    foreach ($testResults as $result) {
        $status = strpos($result, 'PASSED') !== false ? 'success' : (strpos($result, 'ERROR') !== false ? 'error' : 'warning');
        $icon = $status === 'success' ? '✅' : ($status === 'error' ? '❌' : '⚠️');
        echo "<div class='api-card $status'>";
        echo "<h4>$icon $result</h4>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>🔗 Tests Recommandés:</h3>";
    echo "<ul>";
    echo "<li><a href='/admin/' target='_blank'>Tester le Dashboard Admin</a></li>";
    echo "<li><a href='/test-dashboard-only.html' target='_blank'>Tester l'affichage Dashboard</a></li>";
    echo "<li><a href='/php/api/dashboard-stats.php' target='_blank'>Tester API Dashboard Stats</a></li>";
    echo "</ul>";
    echo "</div>";
    
    if ($successRate >= 80) {
        echo "<div class='success'>";
        echo "<h3>✅ Problèmes Résolus:</h3>";
        echo "<ul>";
        echo "<li>✅ Connectivité base de données établie</li>";
        echo "<li>✅ API Dashboard Stats fonctionnelle</li>";
        echo "<li>✅ Données produits accessibles</li>";
        echo "<li>✅ Tables de base présentes</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur générale: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
