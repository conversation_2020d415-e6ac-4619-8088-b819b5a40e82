<?php
require_once __DIR__ . '/../php/config.php';

try {
    // For the model, we will create a generic clothing product
    // In a real application, you would fetch a specific clothing item
    $product_title = 'أحدث تشكيلات الملابس العصرية';

    // We'll associate this with the first available product as a placeholder
    $product_stmt = $conn->query("SELECT id FROM produits WHERE actif = 1 LIMIT 1");
    $product = $product_stmt->fetch(PDO::FETCH_ASSOC);
    $product_id = $product ? $product['id'] : 1; // Fallback to product ID 1

    // Create a landing page for Clothing/Fashion
    $stmt = $conn->prepare("
        INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $contenu_droit = '
    <h3>👕 أطلق أسلوبك الخاص</h3>
    <ul>
        <li><strong>تصاميم فريدة:</strong> كن مميزاً مع أحدث صيحات الموضة العالمية.</li>
        <li><strong>أقمشة فاخرة:</strong> راحة فائقة وجودة تلاحظها من اللمسة الأولى.</li>
        <li><strong>قصات متقنة:</strong> تناسب جميع الأجسام وتبرز جمالك الطبيعي.</li>
        <li><strong>ألوان متنوعة:</strong> تشكيلة واسعة من الألوان التي تناسب كل الأذواق.</li>
    </ul>
    
    <h3>✨ تفاصيل الجودة:</h3>
    <p>• <strong>الخامات:</strong> قطن مصري، حرير، كتان طبيعي.<br>
    • <strong>العناية:</strong> سهلة الغسيل وتحافظ على شكلها ولونها.<br>
    • <strong>المقاسات:</strong> متوفرة من S إلى XXL.<br>
    • <strong>الموسم:</strong> تصاميم مناسبة لجميع فصول السنة.</p>
    ';
    
    $contenu_gauche = '
    <h3>💃 لكل مناسبة إطلالة</h3>
    <p>سواء كنت تبحث عن ملابس رسمية للعمل، أو إطلالة كاجوال لعطلة نهاية الأسبوع، ستجد لدينا ما يلبي احتياجاتك ويعكس شخصيتك.</p>
    
    <h3>🌟 آراء عشاق الموضة</h3>
    <blockquote style="border-right: 3px solid #667eea; padding-right: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">
        "الجودة لا تضاهى والتصاميم دائماً على الموضة. متجري المفضل بلا منازع!"
        <cite style="display: block; margin-top: 10px; font-weight: bold;">- نورة، مدونة موضة</cite>
    </blockquote>
    
    <h3>🎁 تخفيضات الموسم</h3>
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
        <h4 style="margin: 0 0 10px 0;">خصم يصل إلى 50%</h4>
        <p style="font-size: 1.5em; font-weight: bold; margin: 0;">تسوق الآن وجدد خزانتك بأفضل الأسعار!</p>
    </div>
    ';
    
    $stmt->execute([
        $product_id,
        'صفحة هبوط للملابس: ' . $product_title,
        $contenu_droit,
        $contenu_gauche,
        '/landing-page-template.php?id='
    ]);
    
    $landing_page_id = $conn->lastInsertId();
    
    // Add sample images for the clothing landing page
    $images = [
        'https://images.unsplash.com/photo-1489987707025-afc232f7ea0f?w=800&h=600&fit=crop', // Man in t-shirt
        'https://images.unsplash.com/photo-1529139574466-a303027c1d8b?w=800&h=600&fit=crop', // Woman in stylish outfit
        'https://images.unsplash.com/photo-1523381294911-8d3cead13475?w=800&h=600&fit=crop'  // Folded clothes
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO landing_page_images (landing_page_id, image_url, ordre) 
        VALUES (?, ?, ?)
    ");
    
    foreach ($images as $index => $image_url) {
        $stmt->execute([$landing_page_id, $image_url, $index]);
    }
    
    // Update the lien_url with the actual landing page ID
    $stmt = $conn->prepare("UPDATE landing_pages SET lien_url = ? WHERE id = ?");
    $stmt->execute(['/landing-page-template.php?id=' . $landing_page_id, $landing_page_id]);
    
    echo "✅ Landing page pour 'Vêtements' créée avec succès!<br>";
    echo "ID de la landing page: $landing_page_id<br>";
    echo "URL: <a href='/landing-page-template.php?id=$landing_page_id' target='_blank' style='color: #667eea; font-weight: bold;'>🚀 Voir la landing page</a><br>";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors de la création de la landing page pour les vêtements: " . $e->getMessage();
}
?>