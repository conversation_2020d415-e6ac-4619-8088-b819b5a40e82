<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 Testing Products API</h1>\n";

// Test 1: Direct database query
echo "<h2>Test 1: Direct Database Query</h2>\n";
try {
    require_once 'php/config.php';
    
    $stmt = $conn->prepare("SELECT id, type, titre, prix, stock, actif FROM produits WHERE actif = 1");
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>✅ Found " . count($products) . " active products:</p>\n";
    foreach ($products as $product) {
        echo "<li>ID: {$product['id']} - {$product['titre']} ({$product['type']}) - {$product['prix']} DZD</li>\n";
    }
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>\n";
}

// Test 2: Include the API file directly
echo "<h2>Test 2: API File Inclusion Test</h2>\n";
try {
    // Capture output
    ob_start();
    $_SERVER['REQUEST_METHOD'] = 'GET';
    include 'php/api/products.php';
    $api_output = ob_get_clean();
    
    echo "<p>✅ API included successfully</p>\n";
    echo "<h3>API Output:</h3>\n";
    echo "<pre>" . htmlspecialchars($api_output) . "</pre>\n";
} catch (Exception $e) {
    ob_end_clean();
    echo "<p>❌ API inclusion error: " . $e->getMessage() . "</p>\n";
}

// Test 3: Check if API returns valid JSON
echo "<h2>Test 3: JSON Validation</h2>\n";
if (isset($api_output)) {
    $json_data = json_decode($api_output, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p>✅ Valid JSON response</p>\n";
        echo "<p>Response structure: " . print_r(array_keys($json_data), true) . "</p>\n";
    } else {
        echo "<p>❌ Invalid JSON: " . json_last_error_msg() . "</p>\n";
    }
}
?>
