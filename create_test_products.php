<?php
/**
 * <PERSON><PERSON>t to create 5 diverse test products for landing page testing
 */

require_once 'php/config.php';

// Test products data
$products = [
    [
        'type' => 'book',
        'titre' => 'فن اللامبالاة - كتاب تطوير الذات',
        'description' => '<p><strong>كتاب فن اللامبالاة</strong> هو دليلك الشامل لتعلم كيفية التركيز على ما يهم حقاً في الحياة.</p><p>يعلمك هذا الكتاب:</p><ul><li>كيفية اختيار معاركك بحكمة</li><li>التخلص من القلق غير المبرر</li><li>بناء الثقة بالنفس</li><li>تحقيق السعادة الحقيقية</li></ul><p>مؤلف: مارك مانسون</p>',
        'prix' => 2500.00,
        'stock' => 50,
        'auteur' => 'مارك مانسون',
        'actif' => 1
    ],
    [
        'type' => 'laptop',
        'titre' => 'لابتوب Dell Inspiron 15 - للطلاب والمهنيين',
        'description' => '<p><strong>لابتوب Dell Inspiron 15</strong> - الخيار المثالي للطلاب والمهنيين</p><p>المواصفات:</p><ul><li>معالج Intel Core i5 الجيل الحادي عشر</li><li>ذاكرة عشوائية 8GB DDR4</li><li>قرص صلب SSD 256GB</li><li>شاشة 15.6 بوصة Full HD</li><li>كرت رسوميات Intel Iris Xe</li><li>نظام Windows 11</li></ul><p>ضمان سنتين</p>',
        'prix' => 85000.00,
        'stock' => 15,
        'materiel' => 'Intel Core i5, 8GB RAM, 256GB SSD',
        'actif' => 1
    ],
    [
        'type' => 'bag',
        'titre' => 'حقيبة ظهر رياضية مقاومة للماء',
        'description' => '<p><strong>حقيبة ظهر رياضية عملية</strong> مصممة للاستخدام اليومي والرياضي</p><p>المميزات:</p><ul><li>مقاومة للماء بنسبة 100%</li><li>جيوب متعددة للتنظيم</li><li>حمالات مبطنة مريحة</li><li>جيب خاص للحاسوب المحمول</li><li>تصميم عصري وأنيق</li><li>سعة 30 لتر</li></ul><p>مثالية للمدرسة، الجامعة، والرياضة</p>',
        'prix' => 4500.00,
        'stock' => 30,
        'materiel' => 'نايلون مقاوم للماء، سحابات معدنية',
        'actif' => 1
    ],
    [
        'type' => 'clothing',
        'titre' => 'قميص قطني كلاسيكي للرجال',
        'description' => '<p><strong>قميص قطني فاخر</strong> مصنوع من أجود أنواع القطن</p><p>المميزات:</p><ul><li>قطن 100% عالي الجودة</li><li>تصميم كلاسيكي أنيق</li><li>مقاسات متنوعة (S-XXL)</li><li>ألوان متعددة</li><li>سهل العناية والغسيل</li><li>مناسب للعمل والمناسبات</li></ul><p>متوفر بالألوان: أبيض، أزرق، رمادي</p>',
        'prix' => 3200.00,
        'stock' => 40,
        'materiel' => 'قطن 100%',
        'actif' => 1
    ],
    [
        'type' => 'home',
        'titre' => 'خلاط كهربائي متعدد الاستخدامات',
        'description' => '<p><strong>خلاط كهربائي قوي</strong> لجميع احتياجات المطبخ</p><p>المميزات:</p><ul><li>قوة 1000 واط</li><li>5 سرعات مختلفة</li><li>وعاء زجاجي سعة 1.5 لتر</li><li>شفرات من الستانلس ستيل</li><li>قاعدة مانعة للانزلاق</li><li>سهل التنظيف</li></ul><p>مثالي لتحضير العصائر، الشوربات، والصلصات</p><p>ضمان سنة كاملة</p>',
        'prix' => 12000.00,
        'stock' => 25,
        'materiel' => 'بلاستيك عالي الجودة، شفرات ستانلس ستيل',
        'actif' => 1
    ]
];

try {
    $pdo = getPDOConnection();
    
    echo "<h2>🎯 Creating 5 Diverse Test Products</h2>\n";
    echo "<pre>\n";
    
    foreach ($products as $index => $product) {
        echo "Creating Product " . ($index + 1) . ": " . $product['titre'] . "\n";
        
        $sql = "INSERT INTO produits (type, titre, description, prix, stock, auteur, materiel, actif, date_creation) 
                VALUES (:type, :titre, :description, :prix, :stock, :auteur, :materiel, :actif, NOW())";
        
        $stmt = $pdo->prepare($sql);
        
        $result = $stmt->execute([
            ':type' => $product['type'],
            ':titre' => $product['titre'],
            ':description' => $product['description'],
            ':prix' => $product['prix'],
            ':stock' => $product['stock'],
            ':auteur' => $product['auteur'] ?? null,
            ':materiel' => $product['materiel'] ?? null,
            ':actif' => $product['actif']
        ]);
        
        if ($result) {
            $productId = $pdo->lastInsertId();
            echo "✅ Product created successfully with ID: $productId\n";
        } else {
            echo "❌ Failed to create product\n";
        }
        
        echo "---\n";
    }
    
    echo "\n🎉 All products created successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Go to admin panel: http://localhost:8000/admin/\n";
    echo "2. Navigate to 'إدارة المنتجات' section\n";
    echo "3. Verify all 5 products are listed and active\n";
    echo "4. Create landing pages for each product\n";
    echo "</pre>\n";
    
} catch (Exception $e) {
    echo "<h3>❌ Error creating products:</h3>\n";
    echo "<pre>" . $e->getMessage() . "</pre>\n";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء المنتجات التجريبية</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; }
        h2 { color: #333; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <h1>🛍️ إنشاء المنتجات التجريبية</h1>
    <p>هذا السكريبت ينشئ 5 منتجات متنوعة لاختبار نظام صفحات الهبوط:</p>
    <ol>
        <li><strong>كتاب:</strong> فن اللامبالاة</li>
        <li><strong>لابتوب:</strong> Dell Inspiron 15</li>
        <li><strong>حقيبة:</strong> حقيبة ظهر رياضية</li>
        <li><strong>ملابس:</strong> قميص قطني كلاسيكي</li>
        <li><strong>أجهزة منزلية:</strong> خلاط كهربائي</li>
    </ol>
    
    <a href="admin/" style="display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 20px;">
        🚀 الذهاب إلى لوحة التحكم
    </a>
</body>
</html>
