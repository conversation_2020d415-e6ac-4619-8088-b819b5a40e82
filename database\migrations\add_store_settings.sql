CREATE TABLE IF NOT EXISTS store_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(50) NOT NULL UNIQUE,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT IGNORE INTO store_settings (setting_key, setting_value) VALUES
('store_name', 'Mossaab Store'),
('store_description', 'Your one-stop shop for books and electronics'),
('contact_email', '<EMAIL>'),
('phone_number', '+213 000000000'),
('address', 'Algeria'),
('shipping_policy', 'Standard shipping within 3-5 business days'),
('return_policy', '30-day return policy for unused items');