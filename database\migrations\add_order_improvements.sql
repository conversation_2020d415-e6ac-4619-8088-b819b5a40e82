-- Ajout d'un index sur la date de commande pour améliorer les performances des filtres
ALTER TABLE `commandes` ADD INDEX `idx_date_commande` (`date_commande`);

-- Ajout d'un index sur le statut pour améliorer les performances des filtres
ALTER TABLE `commandes` ADD INDEX `idx_statut` (`statut`);

-- Ajout d'une colonne pour le nombre total d'articles
ALTER TABLE `commandes` ADD COLUMN `nombre_articles` INT DEFAULT 0 AFTER `montant_total`;

-- Ajout d'une colonne pour le numéro de suivi
ALTER TABLE `commandes` ADD COLUMN `numero_suivi` VARCHAR(50) DEFAULT NULL AFTER `statut`;

-- Ajout d'une colonne pour les notes administratives
ALTER TABLE `commandes` ADD COLUMN `notes_admin` TEXT DEFAULT NULL AFTER `numero_suivi`;

-- Création de la table des notifications
CREATE TABLE IF NOT EXISTS `notifications` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `type` VARCHAR(50) NOT NULL,
    `message` TEXT NOT NULL,
    `reference_id` VARCHAR(50) DEFAULT NULL,
    `is_read` TINYINT(1) DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_type` (`type`),
    INDEX `idx_is_read` (`is_read`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Mise à jour du nombre d'articles pour les commandes existantes
UPDATE `commandes` c
SET c.nombre_articles = (
    SELECT COUNT(*)
    FROM `details_commande` dc
    WHERE dc.commande_id = c.id
);