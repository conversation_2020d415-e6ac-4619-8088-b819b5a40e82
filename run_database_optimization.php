<?php
/**
 * Database Optimization Runner
 * Executes database optimization script and measures performance improvements
 */

require_once 'php/config.php';

echo "<h1>🚀 Database Optimization Runner</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }
    .step { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #007bff; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .performance-metric { background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0; }
</style>\n";

try {
    // Step 1: Backup current performance metrics
    echo "<div class='step'>\n";
    echo "<h2>📊 Step 1: Measuring Current Performance</h2>\n";
    
    $beforeMetrics = [];
    
    // Measure query execution times before optimization
    $queries = [
        'products_all' => "SELECT COUNT(*) FROM produits",
        'products_with_landing' => "SELECT p.*, CASE WHEN lp.id IS NOT NULL THEN 1 ELSE 0 END as has_landing FROM produits p LEFT JOIN landing_pages lp ON p.id = lp.produit_id",
        'landing_pages_all' => "SELECT COUNT(*) FROM landing_pages",
        'categories_active' => "SELECT COUNT(*) FROM categories WHERE actif = 1",
        'orders_recent' => "SELECT COUNT(*) FROM commandes WHERE date_commande >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
    ];
    
    foreach ($queries as $name => $sql) {
        $start = microtime(true);
        $stmt = $conn->query($sql);
        $result = $stmt->fetch();
        $end = microtime(true);
        $beforeMetrics[$name] = ($end - $start) * 1000; // Convert to milliseconds
        echo "<p class='info'>Query '$name': " . number_format($beforeMetrics[$name], 2) . " ms</p>\n";
    }
    echo "</div>\n";
    
    // Step 2: Check current indexes
    echo "<div class='step'>\n";
    echo "<h2>🔍 Step 2: Current Index Analysis</h2>\n";
    
    $indexQuery = "
        SELECT 
            TABLE_NAME,
            INDEX_NAME,
            COLUMN_NAME,
            CARDINALITY
        FROM INFORMATION_SCHEMA.STATISTICS 
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME IN ('produits', 'landing_pages', 'commandes', 'categories')
        ORDER BY TABLE_NAME, INDEX_NAME
    ";
    
    $stmt = $conn->query($indexQuery);
    $currentIndexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p class='info'>Current indexes found: " . count($currentIndexes) . "</p>\n";
    echo "<table>\n";
    echo "<tr><th>Table</th><th>Index</th><th>Column</th><th>Cardinality</th></tr>\n";
    foreach ($currentIndexes as $index) {
        echo "<tr>";
        echo "<td>{$index['TABLE_NAME']}</td>";
        echo "<td>{$index['INDEX_NAME']}</td>";
        echo "<td>{$index['COLUMN_NAME']}</td>";
        echo "<td>{$index['CARDINALITY']}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    echo "</div>\n";
    
    // Step 3: Execute optimization script
    echo "<div class='step'>\n";
    echo "<h2>⚡ Step 3: Executing Database Optimization</h2>\n";
    
    $optimizationScript = file_get_contents('database/optimize_database.sql');
    if (!$optimizationScript) {
        throw new Exception("Could not read optimization script");
    }
    
    // Split script into individual statements
    $statements = array_filter(array_map('trim', explode(';', $optimizationScript)));
    $executedCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // Skip empty lines and comments
        }
        
        try {
            $conn->exec($statement);
            $executedCount++;
            echo "<p class='success'>✅ Executed: " . substr($statement, 0, 50) . "...</p>\n";
        } catch (PDOException $e) {
            $errorCount++;
            echo "<p class='warning'>⚠️ Warning: " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<p class='info'><strong>Optimization Summary:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Statements executed: $executedCount</li>\n";
    echo "<li>Warnings/Errors: $errorCount</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    // Step 4: Measure performance after optimization
    echo "<div class='step'>\n";
    echo "<h2>📈 Step 4: Performance Comparison</h2>\n";
    
    $afterMetrics = [];
    
    foreach ($queries as $name => $sql) {
        $start = microtime(true);
        $stmt = $conn->query($sql);
        $result = $stmt->fetch();
        $end = microtime(true);
        $afterMetrics[$name] = ($end - $start) * 1000; // Convert to milliseconds
    }
    
    echo "<table>\n";
    echo "<tr><th>Query</th><th>Before (ms)</th><th>After (ms)</th><th>Improvement</th><th>Status</th></tr>\n";
    
    $totalImprovement = 0;
    $queryCount = 0;
    
    foreach ($beforeMetrics as $name => $beforeTime) {
        $afterTime = $afterMetrics[$name];
        $improvement = (($beforeTime - $afterTime) / $beforeTime) * 100;
        $totalImprovement += $improvement;
        $queryCount++;
        
        $status = $improvement > 0 ? '🚀 Faster' : ($improvement < -10 ? '⚠️ Slower' : '➡️ Similar');
        $improvementText = number_format($improvement, 1) . '%';
        
        echo "<tr>";
        echo "<td>$name</td>";
        echo "<td>" . number_format($beforeTime, 2) . "</td>";
        echo "<td>" . number_format($afterTime, 2) . "</td>";
        echo "<td>$improvementText</td>";
        echo "<td>$status</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    $avgImprovement = $totalImprovement / $queryCount;
    echo "<div class='performance-metric'>\n";
    echo "<h3>🎯 Overall Performance Improvement: " . number_format($avgImprovement, 1) . "%</h3>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    // Step 5: Verify new indexes
    echo "<div class='step'>\n";
    echo "<h2>✅ Step 5: Index Verification</h2>\n";
    
    $stmt = $conn->query($indexQuery);
    $newIndexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $indexesAdded = count($newIndexes) - count($currentIndexes);
    echo "<p class='success'>New indexes added: $indexesAdded</p>\n";
    echo "<p class='info'>Total indexes now: " . count($newIndexes) . "</p>\n";
    
    // Show new indexes
    if ($indexesAdded > 0) {
        echo "<h4>🆕 Newly Added Indexes:</h4>\n";
        echo "<ul>\n";
        $currentIndexNames = array_column($currentIndexes, 'INDEX_NAME');
        foreach ($newIndexes as $index) {
            if (!in_array($index['INDEX_NAME'], $currentIndexNames)) {
                echo "<li>{$index['TABLE_NAME']}.{$index['INDEX_NAME']} on {$index['COLUMN_NAME']}</li>\n";
            }
        }
        echo "</ul>\n";
    }
    echo "</div>\n";
    
    // Step 6: Recommendations
    echo "<div class='step'>\n";
    echo "<h2>💡 Step 6: Optimization Recommendations</h2>\n";
    
    echo "<h4>✅ Completed Optimizations:</h4>\n";
    echo "<ul>\n";
    echo "<li>Added performance indexes for frequently queried columns</li>\n";
    echo "<li>Created composite indexes for complex queries</li>\n";
    echo "<li>Standardized storage engines to InnoDB</li>\n";
    echo "<li>Ensured consistent UTF-8 collation</li>\n";
    echo "<li>Added proper foreign key constraints</li>\n";
    echo "</ul>\n";
    
    echo "<h4>🔄 Ongoing Maintenance:</h4>\n";
    echo "<ul>\n";
    echo "<li>Run ANALYZE TABLE monthly to update index statistics</li>\n";
    echo "<li>Monitor query performance with EXPLAIN statements</li>\n";
    echo "<li>Consider query caching for frequently accessed data</li>\n";
    echo "<li>Regular database backups before major changes</li>\n";
    echo "</ul>\n";
    
    if ($avgImprovement > 20) {
        echo "<p class='success'>🎉 Excellent! Database performance improved significantly.</p>\n";
    } elseif ($avgImprovement > 10) {
        echo "<p class='info'>👍 Good improvement in database performance.</p>\n";
    } else {
        echo "<p class='warning'>⚠️ Minimal improvement. Consider additional optimizations.</p>\n";
    }
    echo "</div>\n";
    
    // Success summary
    echo "<div class='step' style='border-left-color: #28a745;'>\n";
    echo "<h2>🎉 Database Optimization Complete!</h2>\n";
    echo "<p class='success'>Your database has been successfully optimized for better performance.</p>\n";
    echo "<p><strong>Next Steps:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>Test your application to ensure everything works correctly</li>\n";
    echo "<li>Monitor query performance in production</li>\n";
    echo "<li>Consider implementing query caching for frequently accessed data</li>\n";
    echo "<li>Schedule regular maintenance tasks</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='step' style='border-left-color: #dc3545;'>\n";
    echo "<h2>❌ Error During Optimization</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<p class='error'>Stack trace:</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    echo "</div>\n";
}
?>

<script>
console.log('🚀 Database optimization completed');
</script>
