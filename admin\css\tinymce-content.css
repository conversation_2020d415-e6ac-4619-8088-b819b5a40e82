/* TinyMCE Content Styles */
body {
    font-family: 'Noto Sans Arabic', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    padding: 1rem;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Noto Sans Arabic', sans-serif;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: #2c3e50;
}

h1 { font-size: 2em; }
h2 { font-size: 1.75em; }
h3 { font-size: 1.5em; }
h4 { font-size: 1.25em; }
h5 { font-size: 1.1em; }
h6 { font-size: 1em; }

p {
    margin-bottom: 1rem;
}

ul, ol {
    margin: 0 1.5rem 1rem;
    padding: 0;
}

li {
    margin-bottom: 0.5rem;
}

a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

blockquote {
    border-right: 4px solid #e5e7eb;
    margin: 1.5rem 0;
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    font-style: italic;
}

code {
    font-family: monospace;
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.9em;
}

pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    margin-bottom: 1rem;
}

table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1rem;
}

th, td {
    border: 1px solid #e5e7eb;
    padding: 0.75rem;
    text-align: right;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
}

img {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
}

hr {
    border: none;
    border-top: 1px solid #e5e7eb;
    margin: 2rem 0;
}