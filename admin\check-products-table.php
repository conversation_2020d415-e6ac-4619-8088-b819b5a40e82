<?php
require_once __DIR__ . '/../php/config.php';

try {
    $pdo = getPDOConnection();
    
    echo "📋 Products Table Schema:\n";
    echo "=" . str_repeat("=", 30) . "\n";
    
    $stmt = $pdo->query('DESCRIBE produits');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo "  - {$column['Field']} ({$column['Type']})\n";
    }
    
    echo "\n📊 Sample Products:\n";
    echo "=" . str_repeat("=", 20) . "\n";
    
    $stmt = $pdo->query('SELECT * FROM produits LIMIT 3');
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "No products found.\n";
    } else {
        foreach ($products as $index => $product) {
            echo "Product " . ($index + 1) . ":\n";
            foreach ($product as $key => $value) {
                echo "  {$key}: {$value}\n";
            }
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
