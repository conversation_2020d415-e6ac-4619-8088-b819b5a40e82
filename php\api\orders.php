<?php
/**
 * Orders API
 * API لإدارة الطلبات
 */

header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Handle preflight requests
if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit();
}

try {
    // Auto-detect config path
    if (file_exists("../config.php")) {
        require_once "../config.php";
    } elseif (file_exists(__DIR__ . "/../config.php")) {
        require_once __DIR__ . "/../config.php";
    } else {
        throw new Exception("Config file not found");
    }
    
    $method = $_SERVER["REQUEST_METHOD"];
    
    switch ($method) {
        case "GET":
            handleGetOrders();
            break;
        case "POST":
            handleCreateOrder();
            break;
        case "PUT":
            handleUpdateOrder();
            break;
        case "DELETE":
            handleDeleteOrder();
            break;
        default:
            http_response_code(405);
            echo json_encode(["success" => false, "message" => "Method not allowed"]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Server error: " . $e->getMessage()
    ]);
}

function handleGetOrders() {
    try {
        $pdo = getPDOConnection();
        
        // Sample orders data
        $orders = [
            [
                "id" => 1,
                "customer_name" => "أحمد محمد",
                "total_amount" => 150.00,
                "status" => "pending",
                "created_at" => date("Y-m-d H:i:s", strtotime("-2 days"))
            ],
            [
                "id" => 2,
                "customer_name" => "فاطمة علي",
                "total_amount" => 275.50,
                "status" => "completed",
                "created_at" => date("Y-m-d H:i:s", strtotime("-1 day"))
            ],
            [
                "id" => 3,
                "customer_name" => "محمد حسن",
                "total_amount" => 89.99,
                "status" => "processing",
                "created_at" => date("Y-m-d H:i:s")
            ]
        ];
        
        echo json_encode([
            "success" => true,
            "data" => $orders,
            "total" => count($orders),
            "message" => "Orders loaded successfully"
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Error loading orders: " . $e->getMessage()
        ]);
    }
}

function handleCreateOrder() {
    echo json_encode([
        "success" => true,
        "message" => "Order creation functionality will be implemented"
    ]);
}

function handleUpdateOrder() {
    echo json_encode([
        "success" => true,
        "message" => "Order update functionality will be implemented"
    ]);
}

function handleDeleteOrder() {
    echo json_encode([
        "success" => true,
        "message" => "Order deletion functionality will be implemented"
    ]);
}
?>