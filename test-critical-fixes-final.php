<?php
/**
 * Test Critical Fixes - Final Validation
 * Comprehensive testing of all critical fixes
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار الإصلاحات الحرجة - التحقق النهائي</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-passed { border: 3px solid #28a745; background: #d4edda; }
        .test-failed { border: 3px solid #dc3545; background: #f8d7da; }
        h1, h2, h3 { color: #333; }
        .test-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .test-result.failed { border-left-color: #dc3545; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .test-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .test-card.passed { border-left-color: #28a745; }
        .test-card.failed { border-left-color: #dc3545; }
        .progress { background: #e9ecef; height: 25px; border-radius: 12px; overflow: hidden; margin: 15px 0; }
        .progress-bar { background: #28a745; height: 100%; transition: width 0.3s ease; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; }
        .final-summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin: 30px 0; text-align: center; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🧪 اختبار الإصلاحات الحرجة - التحقق النهائي</h1>";
echo "<p>اختبار شامل لجميع الإصلاحات المطبقة على النظام</p>";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

try {
    // TEST 1: Config File Loading
    echo "<div class='section'>";
    echo "<h2>1️⃣ اختبار تحميل ملف التكوين</h2>";
    
    $totalTests++;
    try {
        require_once 'php/config.php';
        echo "<div class='success'>✅ تم تحميل ملف التكوين بنجاح</div>";
        $testResults['config_loading'] = true;
        $passedTests++;
    } catch (Exception $e) {
        echo "<div class='error'>❌ فشل في تحميل ملف التكوين: " . $e->getMessage() . "</div>";
        $testResults['config_loading'] = false;
    }
    echo "</div>";
    
    // TEST 2: API Endpoints Testing
    echo "<div class='section'>";
    echo "<h2>2️⃣ اختبار APIs</h2>";
    
    $apiTests = [
        'templates' => ['file' => 'php/api/templates.php', 'params' => '?action=get_templates'],
        'landing-pages' => ['file' => 'php/api/landing-pages.php', 'params' => ''],
        'users' => ['file' => 'php/api/users.php', 'params' => '?action=list'],
        'subscription-limits' => ['file' => 'php/api/subscription-limits.php', 'params' => '?action=limits']
    ];
    
    echo "<div class='test-grid'>";
    foreach ($apiTests as $apiName => $apiInfo) {
        echo "<div class='test-card'>";
        echo "<h4>API: {$apiName}</h4>";
        
        $totalTests++;
        $apiFile = $apiInfo['file'];
        
        if (file_exists($apiFile)) {
            // Test syntax first
            $output = [];
            $return_var = 0;
            exec("php -l \"{$apiFile}\" 2>&1", $output, $return_var);
            
            if ($return_var === 0) {
                echo "<div class='success'>✅ بناء الجملة صحيح</div>";
                
                // Test include capability
                try {
                    ob_start();
                    $_GET['action'] = 'get_templates'; // Set default action
                    include $apiFile;
                    $apiOutput = ob_get_clean();
                    
                    if (!empty($apiOutput)) {
                        $jsonData = json_decode($apiOutput, true);
                        if ($jsonData) {
                            echo "<div class='success'>✅ يعيد JSON صحيح</div>";
                            $testResults['api_' . $apiName] = true;
                            $passedTests++;
                        } else {
                            echo "<div class='warning'>⚠️ الاستجابة ليست JSON صحيح</div>";
                            $testResults['api_' . $apiName] = false;
                        }
                    } else {
                        echo "<div class='warning'>⚠️ لا توجد استجابة</div>";
                        $testResults['api_' . $apiName] = false;
                    }
                } catch (Exception $e) {
                    echo "<div class='error'>❌ خطأ في التنفيذ: " . $e->getMessage() . "</div>";
                    $testResults['api_' . $apiName] = false;
                }
            } else {
                echo "<div class='error'>❌ خطأ في بناء الجملة</div>";
                $testResults['api_' . $apiName] = false;
            }
        } else {
            echo "<div class='error'>❌ الملف غير موجود</div>";
            $testResults['api_' . $apiName] = false;
        }
        
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    // TEST 3: JavaScript Files Validation
    echo "<div class='section'>";
    echo "<h2>3️⃣ اختبار ملفات JavaScript</h2>";
    
    $jsFiles = [
        'js/utils.js' => 'أدوات مساعدة',
        'js/main.js' => 'ملف رئيسي',
        'js/auth.js' => 'مصادقة',
        'admin/js/admin.js' => 'إدارة النظام',
        'admin/js/landing-pages-enhanced-fixed.js' => 'صفحات الهبوط المحسن'
    ];
    
    echo "<div class='test-grid'>";
    foreach ($jsFiles as $jsFile => $description) {
        echo "<div class='test-card'>";
        echo "<h4>{$description}</h4>";
        echo "<p><code>{$jsFile}</code></p>";
        
        $totalTests++;
        if (file_exists($jsFile)) {
            $content = file_get_contents($jsFile);
            $issues = [];
            
            // Check for HTML content
            if (preg_match('/<!DOCTYPE|<html|<head|<body/i', $content)) {
                $issues[] = "محتوى HTML";
            }
            
            // Check for PHP content
            if (preg_match('/<\?php|\?>/i', $content)) {
                $issues[] = "محتوى PHP";
            }
            
            // Check for async/await issues
            if (preg_match('/async\s+async\s+function/m', $content)) {
                $issues[] = "async مكررة";
            }
            
            if (empty($issues)) {
                echo "<div class='success'>✅ الملف نظيف</div>";
                $testResults['js_' . basename($jsFile)] = true;
                $passedTests++;
            } else {
                echo "<div class='warning'>⚠️ مشاكل: " . implode(', ', $issues) . "</div>";
                $testResults['js_' . basename($jsFile)] = false;
            }
        } else {
            echo "<div class='error'>❌ غير موجود</div>";
            $testResults['js_' . basename($jsFile)] = false;
        }
        
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    // TEST 4: CSS Files and UI Fixes
    echo "<div class='section'>";
    echo "<h2>4️⃣ اختبار ملفات CSS وإصلاحات الواجهة</h2>";
    
    $cssFiles = [
        'admin/css/critical-fixes.css' => 'إصلاحات حرجة',
        'admin/css/landing-pages-enhanced-fixed.css' => 'صفحات الهبوط المحسنة',
        'css/style.css' => 'أنماط رئيسية'
    ];
    
    foreach ($cssFiles as $cssFile => $description) {
        echo "<div class='test-result'>";
        echo "<h4>{$description}</h4>";
        
        $totalTests++;
        if (file_exists($cssFile)) {
            $size = filesize($cssFile);
            echo "<div class='success'>✅ موجود - الحجم: " . number_format($size) . " بايت</div>";
            $testResults['css_' . basename($cssFile)] = true;
            $passedTests++;
        } else {
            echo "<div class='error'>❌ غير موجود: {$cssFile}</div>";
            $testResults['css_' . basename($cssFile)] = false;
        }
        
        echo "</div>";
    }
    echo "</div>";
    
    // TEST 5: Database Connection and SubscriptionLimits
    echo "<div class='section'>";
    echo "<h2>5️⃣ اختبار قاعدة البيانات و SubscriptionLimits</h2>";
    
    echo "<div class='test-result'>";
    echo "<h4>اختبار قاعدة البيانات</h4>";
    
    $totalTests++;
    try {
        $pdo = getPDOConnection();
        echo "<div class='success'>✅ الاتصال بقاعدة البيانات ناجح</div>";
        
        // Test SubscriptionLimits
        require_once 'php/SubscriptionLimits.php';
        $limitsManager = new SubscriptionLimits();
        
        // Get demo user
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $demoUser = $stmt->fetch();
        
        if ($demoUser) {
            $usage = $limitsManager->getUserUsage($demoUser['id']);
            echo "<div class='success'>✅ SubscriptionLimits يعمل بشكل صحيح</div>";
            echo "<div class='info'>الاستخدام: {$usage['products']} منتجات، {$usage['landing_pages']} صفحات هبوط</div>";
            $testResults['subscription_limits'] = true;
            $passedTests++;
        } else {
            echo "<div class='warning'>⚠️ المستخدم التجريبي غير موجود</div>";
            $testResults['subscription_limits'] = false;
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
        $testResults['subscription_limits'] = false;
    }
    
    echo "</div>";
    echo "</div>";
    
    // FINAL RESULTS
    $successRate = ($passedTests / $totalTests) * 100;
    
    echo "<div class='final-summary'>";
    echo "<h2>🎯 النتائج النهائية</h2>";
    echo "<h3>نسبة النجاح: " . round($successRate, 1) . "%</h3>";
    echo "<p>({$passedTests}/{$totalTests} اختبارات نجحت)</p>";
    
    echo "<div class='progress'>";
    echo "<div class='progress-bar' style='width: {$successRate}%'>" . round($successRate, 1) . "%</div>";
    echo "</div>";
    
    if ($successRate >= 90) {
        echo "<h4>🎉 ممتاز! جميع الإصلاحات تعمل بشكل مثالي</h4>";
        echo "<p>النظام جاهز للاستخدام الكامل</p>";
    } elseif ($successRate >= 75) {
        echo "<h4>✅ جيد! معظم الإصلاحات تعمل بشكل صحيح</h4>";
        echo "<p>بعض التحسينات الطفيفة قد تكون مطلوبة</p>";
    } elseif ($successRate >= 50) {
        echo "<h4>⚠️ مقبول! بعض الإصلاحات تحتاج إلى مراجعة</h4>";
        echo "<p>يرجى مراجعة الاختبارات الفاشلة</p>";
    } else {
        echo "<h4>❌ يحتاج النظام إلى إصلاحات إضافية</h4>";
        echo "<p>يرجى تشغيل سكريبت الإصلاح مرة أخرى</p>";
    }
    echo "</div>";
    
    // DETAILED RESULTS
    echo "<div class='section'>";
    echo "<h2>📋 تفاصيل النتائج</h2>";
    
    $passedCount = 0;
    $failedCount = 0;
    
    echo "<div class='test-grid'>";
    foreach ($testResults as $testName => $result) {
        $cardClass = $result ? 'passed' : 'failed';
        $icon = $result ? '✅' : '❌';
        $status = $result ? 'نجح' : 'فشل';
        
        if ($result) $passedCount++;
        else $failedCount++;
        
        echo "<div class='test-card {$cardClass}'>";
        echo "<h4>{$icon} {$testName}</h4>";
        echo "<p>الحالة: {$status}</p>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>📊 الإحصائيات التفصيلية:</h4>";
    echo "<ul>";
    echo "<li>الاختبارات الناجحة: {$passedCount}</li>";
    echo "<li>الاختبارات الفاشلة: {$failedCount}</li>";
    echo "<li>إجمالي الاختبارات: {$totalTests}</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام في الاختبار: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
