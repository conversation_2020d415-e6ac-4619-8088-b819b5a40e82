/* Admin Panel Loading Fix CSS */

/* Force body to be visible after 3 seconds */
body {
    animation: forceVisible 3s forwards;
}

@keyframes forceVisible {
    0% { visibility: hidden; }
    99% { visibility: hidden; }
    100% { visibility: visible !important; }
}

/* Emergency visibility rules */
.emergency-visible {
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
}

/* Force content to show */
.content-loaded,
body.content-loaded {
    visibility: visible !important;
    opacity: 1 !important;
}

/* Hide loading indicator when content is loaded */
body.content-loaded #loading-indicator {
    display: none !important;
}

/* Force main areas to be visible */
.main-content,
.sidebar,
.admin-container {
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
}

/* Force all admin sections to be visible */
.admin-section,
.content-section {
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
}

/* Ensure active sections are visible */
.content-section.active {
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
}

/* Force buttons and interactive elements to be visible */
.btn,
.action-button,
button {
    visibility: visible !important;
    opacity: 1 !important;
    display: inline-block !important;
}

/* Emergency fallback - show everything after 5 seconds */
body {
    animation: emergencyShow 5s forwards;
}

@keyframes emergencyShow {
    0% { }
    99% { }
    100% { 
        visibility: visible !important;
    }
}

/* Force specific admin elements */
#landingPagesContent,
#userManagementContent,
#storesManagementContent,
#dashboardContent {
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .main-content,
    .sidebar {
        visibility: visible !important;
        display: block !important;
    }
}