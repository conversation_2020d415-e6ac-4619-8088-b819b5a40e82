<?php
// Simple test for AI Settings API
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Testing AI Settings API ===\n\n";

// Test 1: Get AI Settings
echo "1. Testing get-ai-settings.php...\n";
$url = 'http://localhost:8000/api/get-ai-settings.php';
$response = file_get_contents($url);

if ($response === false) {
    echo "❌ Failed to fetch get-ai-settings.php\n";
} else {
    echo "✅ Response received\n";
    echo "Raw response: " . substr($response, 0, 200) . "...\n";
    
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ Valid JSON\n";
        echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        if ($data['success']) {
            echo "Providers found: " . implode(', ', array_keys($data['data'])) . "\n";
            foreach ($data['data'] as $provider => $config) {
                echo "  $provider: enabled=" . ($config['enabled'] ? 'true' : 'false') . 
                     ", has_key=" . (!empty($config['api_key']) ? 'true' : 'false') . "\n";
            }
        }
    } else {
        echo "❌ Invalid JSON: " . json_last_error_msg() . "\n";
    }
}

echo "\n";

// Test 2: Save AI Settings
echo "2. Testing save-ai-settings.php...\n";
$testData = [
    'openai' => [
        'key' => 'test-key-123',
        'model' => 'gpt-3.5-turbo',
        'enabled' => true
    ],
    'anthropic' => [
        'key' => '',
        'model' => 'claude-3-sonnet',
        'enabled' => false
    ]
];

$postData = json_encode($testData);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $postData
    ]
]);

$saveUrl = 'http://localhost:8000/api/save-ai-settings.php';
$saveResponse = file_get_contents($saveUrl, false, $context);

if ($saveResponse === false) {
    echo "❌ Failed to save AI settings\n";
} else {
    echo "✅ Save response received\n";
    echo "Raw response: " . substr($saveResponse, 0, 200) . "...\n";
    
    $saveData = json_decode($saveResponse, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ Valid JSON\n";
        echo "Success: " . ($saveData['success'] ? 'true' : 'false') . "\n";
        if ($saveData['success']) {
            echo "Message: " . $saveData['message'] . "\n";
            echo "Saved count: " . $saveData['saved_count'] . "\n";
        } else {
            echo "Error: " . $saveData['error'] . "\n";
        }
    } else {
        echo "❌ Invalid JSON: " . json_last_error_msg() . "\n";
    }
}

echo "\n";

// Test 3: Get AI Settings again to verify persistence
echo "3. Testing persistence - getting settings again...\n";
$response2 = file_get_contents($url);

if ($response2 !== false) {
    $data2 = json_decode($response2, true);
    if (json_last_error() === JSON_ERROR_NONE && $data2['success']) {
        echo "✅ Settings retrieved after save\n";
        foreach ($data2['data'] as $provider => $config) {
            echo "  $provider: enabled=" . ($config['enabled'] ? 'true' : 'false') . 
                 ", has_key=" . (!empty($config['api_key']) ? 'true' : 'false') . "\n";
        }
        
        // Check if OpenAI is enabled (should be true from our test)
        if ($data2['data']['openai']['enabled']) {
            echo "✅ OpenAI enabled state persisted correctly\n";
        } else {
            echo "❌ OpenAI enabled state not persisted\n";
        }
    }
}

echo "\n=== Test Complete ===\n";
