<?php
/**
 * Test Categories API
 * This script tests the categories API to identify loading issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../php/config.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API الفئات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .test-result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API الفئات</h1>
        <p>هذا السكريبت يختبر API الفئات لتحديد مشاكل التحميل.</p>

        <?php
        try {
            // Test 1: Database Connection
            echo '<div class="test-section">';
            echo '<h3>🗄️ اختبار الاتصال بقاعدة البيانات</h3>';
            
            $pdo = getPDOConnection();
            if ($pdo) {
                echo '<div class="test-result pass">✅ الاتصال بقاعدة البيانات ناجح</div>';
            } else {
                echo '<div class="test-result fail">❌ فشل الاتصال بقاعدة البيانات</div>';
                exit;
            }
            echo '</div>';

            // Test 2: Check Categories Table Structure
            echo '<div class="test-section">';
            echo '<h3>📋 فحص هيكل جدول الفئات</h3>';
            
            $stmt = $pdo->query("DESCRIBE categories");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo '<table>';
            echo '<tr><th>اسم العمود</th><th>النوع</th><th>القيمة الافتراضية</th><th>NULL</th></tr>';
            foreach ($columns as $column) {
                echo '<tr>';
                echo '<td>' . $column['Field'] . '</td>';
                echo '<td>' . $column['Type'] . '</td>';
                echo '<td>' . ($column['Default'] ?? 'NULL') . '</td>';
                echo '<td>' . $column['Null'] . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            // Check for required columns
            $requiredColumns = ['id', 'nom_ar', 'actif', 'ordre'];
            $missingColumns = [];
            $existingColumns = array_column($columns, 'Field');
            
            foreach ($requiredColumns as $col) {
                if (!in_array($col, $existingColumns)) {
                    $missingColumns[] = $col;
                }
            }
            
            if (empty($missingColumns)) {
                echo '<div class="test-result pass">✅ جميع الأعمدة المطلوبة موجودة</div>';
            } else {
                echo '<div class="test-result fail">❌ أعمدة مفقودة: ' . implode(', ', $missingColumns) . '</div>';
            }
            echo '</div>';

            // Test 3: Check Categories Data
            echo '<div class="test-section">';
            echo '<h3>📊 فحص بيانات الفئات</h3>';
            
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM categories");
            $totalCategories = $stmt->fetch()['total'];
            
            $stmt = $pdo->query("SELECT COUNT(*) as active FROM categories WHERE actif = 1");
            $activeCategories = $stmt->fetch()['active'];
            
            echo '<div class="test-result pass">📈 إجمالي الفئات: ' . $totalCategories . '</div>';
            echo '<div class="test-result pass">✅ الفئات النشطة: ' . $activeCategories . '</div>';
            
            if ($totalCategories > 0) {
                $stmt = $pdo->query("SELECT * FROM categories ORDER BY ordre ASC, nom_ar ASC LIMIT 10");
                $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo '<h4>عينة من الفئات:</h4>';
                echo '<table>';
                echo '<tr><th>ID</th><th>الاسم العربي</th><th>الاسم الإنجليزي</th><th>الترتيب</th><th>نشط</th></tr>';
                foreach ($categories as $cat) {
                    echo '<tr>';
                    echo '<td>' . $cat['id'] . '</td>';
                    echo '<td>' . ($cat['nom_ar'] ?? 'غير محدد') . '</td>';
                    echo '<td>' . ($cat['nom_en'] ?? 'غير محدد') . '</td>';
                    echo '<td>' . ($cat['ordre'] ?? 'غير محدد') . '</td>';
                    echo '<td>' . ($cat['actif'] ? 'نعم' : 'لا') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            echo '</div>';

            // Test 4: Test Categories API Directly
            echo '<div class="test-section">';
            echo '<h3>🔌 اختبار API الفئات مباشرة</h3>';
            
            // Simulate API call
            ob_start();
            $_SERVER['REQUEST_METHOD'] = 'GET';
            include '../php/api/categories.php';
            $apiOutput = ob_get_clean();
            
            $apiData = json_decode($apiOutput, true);
            
            if ($apiData && isset($apiData['success']) && $apiData['success']) {
                echo '<div class="test-result pass">✅ API الفئات يعمل بنجاح</div>';
                echo '<div class="test-result pass">📊 عدد الفئات المُرجعة: ' . count($apiData['categories']) . '</div>';
                
                echo '<h4>استجابة API:</h4>';
                echo '<pre>' . json_encode($apiData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            } else {
                echo '<div class="test-result fail">❌ فشل API الفئات</div>';
                echo '<h4>استجابة API:</h4>';
                echo '<pre>' . htmlspecialchars($apiOutput) . '</pre>';
            }
            echo '</div>';

            // Test 5: Test JavaScript Loading
            echo '<div class="test-section">';
            echo '<h3>📜 اختبار تحميل JavaScript</h3>';
            
            echo '<div id="categoriesTestResult">جاري الاختبار...</div>';
            
            echo '<script>
                async function testCategoriesAPI() {
                    const resultDiv = document.getElementById("categoriesTestResult");
                    
                    try {
                        const response = await fetch("../php/api/categories.php");
                        const data = await response.json();
                        
                        if (data.success) {
                            resultDiv.innerHTML = `
                                <div class="test-result pass">✅ تحميل JavaScript ناجح</div>
                                <div class="test-result pass">📊 عدد الفئات: ${data.categories.length}</div>
                                <div class="test-result pass">🔗 API متاح عبر AJAX</div>
                            `;
                        } else {
                            resultDiv.innerHTML = `<div class="test-result fail">❌ فشل في تحميل الفئات: ${data.message || "خطأ غير معروف"}</div>`;
                        }
                    } catch (error) {
                        resultDiv.innerHTML = `<div class="test-result fail">❌ خطأ في JavaScript: ${error.message}</div>`;
                    }
                }
                
                // Run test after page loads
                document.addEventListener("DOMContentLoaded", testCategoriesAPI);
            </script>';
            echo '</div>';

            // Test 6: Recommendations
            echo '<div class="test-section">';
            echo '<h3>💡 التوصيات</h3>';
            
            if ($totalCategories == 0) {
                echo '<div class="test-result warning">⚠️ لا توجد فئات في قاعدة البيانات. يرجى إضافة بعض الفئات للاختبار.</div>';
            }
            
            if ($activeCategories == 0) {
                echo '<div class="test-result warning">⚠️ لا توجد فئات نشطة. يرجى تفعيل بعض الفئات.</div>';
            }
            
            echo '<div class="test-result pass">✅ يمكنك الآن العودة إلى لوحة التحكم واختبار تحميل الفئات.</div>';
            echo '<p><a href="index.html" style="background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">العودة إلى لوحة التحكم</a></p>';
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="test-result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
        }
        ?>

    </div>
</body>
</html>
