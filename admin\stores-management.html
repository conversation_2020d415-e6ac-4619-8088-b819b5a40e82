<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المتاجر - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Stores Management Content -->
    <div class="stores-management-content">
        <!-- Header Section -->
        <div class="stores-management-header">
            <div class="section-title-wrapper">
                <div class="section-icon">
                    <i class="fas fa-store-alt"></i>
                </div>
                <div class="section-title-content">
                    <h3 class="section-title">إدارة المتاجر</h3>
                    <p class="section-subtitle">مراقبة وإدارة متاجر المستخدمين</p>
                </div>
            </div>
            <div class="settings-summary">
                <div class="summary-item">
                    <span class="summary-label">إجمالي المتاجر:</span>
                    <span class="summary-value" id="totalStores">--</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">المتاجر النشطة:</span>
                    <span class="summary-value" id="activeStores">--</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">المتاجر المحظورة:</span>
                    <span class="summary-value" id="blockedStores">--</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">إجمالي الإيرادات:</span>
                    <span class="summary-value" id="totalRevenue">--</span>
                </div>
            </div>
        </div>

        <!-- Stores Management Tools -->
        <div class="settings-section">
            <div class="section-header">
                <h4 class="section-title">
                    <i class="fas fa-tools"></i>
                    أدوات إدارة المتاجر
                </h4>
                <div class="tools-actions">
                    <button type="button" class="action-button" onclick="refreshStores()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <button type="button" class="action-button" onclick="exportStores()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="search-filter-section">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="storeSearch" placeholder="البحث في المتاجر..." onkeyup="filterStores()">
                </div>
                <div class="filter-controls">
                    <select id="statusFilter" onchange="filterStores()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="blocked">محظور</option>
                        <option value="pending">قيد المراجعة</option>
                        <option value="suspended">معلق</option>
                    </select>
                    <select id="sortBy" onchange="sortStores()">
                        <option value="created_at">تاريخ الإنشاء</option>
                        <option value="store_name">اسم المتجر</option>
                        <option value="total_revenue">الإيرادات</option>
                        <option value="total_orders">عدد الطلبات</option>
                    </select>
                </div>
            </div>

            <!-- Stores Table -->
            <div class="table-responsive">
                <table class="enhanced-table" id="storesTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAllStores" onchange="toggleSelectAll()">
                            </th>
                            <th>المتجر</th>
                            <th>المالك</th>
                            <th>الحالة</th>
                            <th>المنتجات</th>
                            <th>الطلبات</th>
                            <th>الإيرادات</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="storesTableBody">
                        <!-- Stores will be loaded here -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <div class="pagination-info">
                    <span id="paginationInfo">عرض 0 من 0 متجر</span>
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" onclick="changePage(-1)" id="prevPage">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <span id="currentPage">1</span>
                    <button class="pagination-btn" onclick="changePage(1)" id="nextPage">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="bulk-actions" id="bulkActions" style="display: none;">
                <span class="selected-count">تم تحديد <span id="selectedCount">0</span> متجر</span>
                <div class="bulk-buttons">
                    <button class="bulk-btn activate" onclick="bulkAction('activate')">
                        <i class="fas fa-check"></i> تفعيل
                    </button>
                    <button class="bulk-btn block" onclick="bulkAction('block')">
                        <i class="fas fa-ban"></i> حظر
                    </button>
                    <button class="bulk-btn delete" onclick="bulkAction('delete')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Store Details Modal -->
    <div class="enhanced-modal" id="storeDetailsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="storeDetailsTitle">تفاصيل المتجر</h3>
                <button type="button" class="modal-close" onclick="closeStoreDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="storeDetailsContent">
                <!-- Store details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeStoreDetailsModal()">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="accessAsOwner()">الدخول كمالك</button>
            </div>
        </div>
    </div>

    <!-- Store Status Change Modal -->
    <div class="enhanced-modal" id="statusChangeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تغيير حالة المتجر</h3>
                <button type="button" class="modal-close" onclick="closeStatusChangeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="newStatus" class="enhanced-label">
                        <i class="fas fa-toggle-on"></i>
                        الحالة الجديدة
                    </label>
                    <select id="newStatus" class="enhanced-select">
                        <option value="active">نشط</option>
                        <option value="blocked">محظور</option>
                        <option value="pending">قيد المراجعة</option>
                        <option value="suspended">معلق</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="statusReason" class="enhanced-label">
                        <i class="fas fa-comment"></i>
                        سبب التغيير (اختياري)
                    </label>
                    <textarea id="statusReason" class="enhanced-input" rows="3" placeholder="اكتب سبب تغيير الحالة..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeStatusChangeModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="confirmStatusChange()">تأكيد التغيير</button>
            </div>
        </div>
    </div>

    <script src="js/stores-management.js"></script>
</body>
</html>
