<?php
/**
 * FINAL OpenAI Validation and Error Resolution
 * Complete verification of OpenAI API key and JavaScript error fixes
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق النهائي من OpenAI وإصلاح الأخطاء</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .api-key-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success-banner {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
        .error-banner {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
    <!-- Load context menu fix -->
    <script src="js/context-menu-fix.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔑 التحقق النهائي من OpenAI وإصلاح الأخطاء</h1>
            <p>اختبار شامل لمفتاح OpenAI API وحل جميع أخطاء JavaScript</p>
        </div>

        <?php
        $allTestsPassed = true;
        $testResults = [];
        $apiKey = '';

        try {
            require_once '../php/config.php';
            
            // Test 1: OpenAI API Key Validation
            echo '<div class="test-section">';
            echo '<h3>🔑 اختبار 1: التحقق من مفتاح OpenAI API</h3>';
            
            // Load API key from .env
            $envFile = '../.env';
            if (file_exists($envFile)) {
                $envContent = file_get_contents($envFile);
                if (preg_match('/OPENAI_API_KEY=(.*)/', $envContent, $matches)) {
                    $apiKey = trim($matches[1]);
                    $maskedKey = substr($apiKey, 0, 20) . '...' . substr($apiKey, -10);
                    echo '<div class="result pass">✅ مفتاح API موجود في .env</div>';
                    echo '<div class="api-key-display">مفتاح API: ' . $maskedKey . '</div>';
                    echo '<div class="result info">📏 طول المفتاح: ' . strlen($apiKey) . ' حرف</div>';
                    
                    if (strpos($apiKey, 'sk-proj-') === 0 || strpos($apiKey, 'sk-') === 0) {
                        echo '<div class="result pass">✅ تنسيق مفتاح API صحيح</div>';
                        $testResults['api_key_format'] = true;
                    } else {
                        echo '<div class="result fail">❌ تنسيق مفتاح API غير صحيح</div>';
                        $testResults['api_key_format'] = false;
                        $allTestsPassed = false;
                    }
                } else {
                    echo '<div class="result fail">❌ مفتاح API غير موجود في .env</div>';
                    $testResults['api_key_exists'] = false;
                    $allTestsPassed = false;
                }
            } else {
                echo '<div class="result fail">❌ ملف .env غير موجود</div>';
                $testResults['env_file'] = false;
                $allTestsPassed = false;
            }
            echo '</div>';

            // Test 2: Direct OpenAI API Test
            if (!empty($apiKey)) {
                echo '<div class="test-section">';
                echo '<h3>🌐 اختبار 2: اختبار مباشر لـ OpenAI API</h3>';
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/models');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json'
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                if ($error) {
                    echo '<div class="result fail">❌ خطأ في الاتصال: ' . $error . '</div>';
                    $testResults['direct_api'] = false;
                    $allTestsPassed = false;
                } else {
                    echo '<div class="result info">📡 رمز الاستجابة: ' . $httpCode . '</div>';
                    
                    if ($httpCode === 200) {
                        $data = json_decode($response, true);
                        if ($data && isset($data['data'])) {
                            echo '<div class="result pass">✅ مفتاح API صالح وفعال!</div>';
                            echo '<div class="result info">📊 عدد النماذج: ' . count($data['data']) . '</div>';
                            $testResults['direct_api'] = true;
                        } else {
                            echo '<div class="result fail">❌ استجابة غير صالحة</div>';
                            $testResults['direct_api'] = false;
                            $allTestsPassed = false;
                        }
                    } elseif ($httpCode === 401) {
                        echo '<div class="result fail">❌ مفتاح API غير صالح</div>';
                        $testResults['direct_api'] = false;
                        $allTestsPassed = false;
                    } else {
                        echo '<div class="result warning">⚠️ رمز HTTP غير متوقع: ' . $httpCode . '</div>';
                        $testResults['direct_api'] = false;
                        $allTestsPassed = false;
                    }
                }
                echo '</div>';
            }

            // Test 3: AI API Endpoint Test
            echo '<div class="test-section">';
            echo '<h3>🔗 اختبار 3: اختبار نقطة نهاية AI API</h3>';
            
            $aiAPIFile = '../php/api/ai.php';
            if (file_exists($aiAPIFile)) {
                echo '<div class="result pass">✅ ملف AI API موجود</div>';
                
                try {
                    // Clean output buffer
                    if (ob_get_level()) {
                        ob_clean();
                    }
                    
                    ob_start();
                    $_SERVER['REQUEST_METHOD'] = 'GET';
                    $_GET = ['action' => 'test_connection', 'provider' => 'openai'];
                    
                    include $aiAPIFile;
                    $output = ob_get_clean();
                    
                    // Try to parse JSON
                    $data = json_decode($output, true);
                    if ($data !== null) {
                        echo '<div class="result pass">✅ AI API يعطي JSON صالح</div>';
                        echo '<div class="json-display">' . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</div>';
                        
                        if (isset($data['success'])) {
                            if ($data['success']) {
                                echo '<div class="result pass">✅ اختبار OpenAI نجح عبر AI API</div>';
                                $testResults['ai_api'] = true;
                            } else {
                                echo '<div class="result warning">⚠️ اختبار OpenAI فشل: ' . ($data['error'] ?? 'خطأ غير معروف') . '</div>';
                                $testResults['ai_api'] = false;
                            }
                        }
                    } else {
                        echo '<div class="result fail">❌ AI API لا يعطي JSON صالح</div>';
                        echo '<div class="result warning">⚠️ الاستجابة الخام:</div>';
                        echo '<div class="json-display">' . htmlspecialchars(substr($output, 0, 500)) . '</div>';
                        $testResults['ai_api'] = false;
                        $allTestsPassed = false;
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="result fail">❌ خطأ في AI API: ' . $e->getMessage() . '</div>';
                    $testResults['ai_api'] = false;
                    $allTestsPassed = false;
                }
            } else {
                echo '<div class="result fail">❌ ملف AI API غير موجود</div>';
                $testResults['ai_api'] = false;
                $allTestsPassed = false;
            }
            echo '</div>';

            // Test 4: JavaScript Error Handling
            echo '<div class="test-section">';
            echo '<h3>🌐 اختبار 4: معالجة أخطاء JavaScript</h3>';
            echo '<div id="jsTestResults">جاري اختبار JavaScript...</div>';
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
            $allTestsPassed = false;
        }

        // Final Results
        if ($allTestsPassed) {
            echo '<div class="success-banner">';
            echo '🎉 جميع الاختبارات نجحت! OpenAI API يعمل بشكل مثالي';
            echo '<br>✅ مفتاح API صالح ✅ JSON responses تعمل ✅ JavaScript errors مُصلحة';
            echo '</div>';
        } else {
            echo '<div class="error-banner">';
            echo '⚠️ بعض الاختبارات فشلت - راجع التفاصيل أعلاه';
            echo '</div>';
        }

        echo '<div class="test-section">';
        echo '<h3>🚀 الخطوات التالية</h3>';
        echo '<button onclick="testOpenAIConnection()" class="test-button">🧪 اختبار OpenAI عبر JavaScript</button>';
        echo '<button onclick="testContextMenuFix()" class="test-button">🛡️ اختبار إصلاح Context Menu</button>';
        echo '<a href="ai-settings.html" class="test-button">🤖 فتح إعدادات AI</a>';
        echo '<a href="index.html" class="test-button">🏠 لوحة التحكم</a>';
        echo '</div>';
        ?>

    </div>

    <script>
        // Test OpenAI connection via JavaScript
        async function testOpenAIConnection() {
            const resultsDiv = document.getElementById('jsTestResults');
            resultsDiv.innerHTML = '<div class="result info">🔄 جاري اختبار OpenAI عبر JavaScript...</div>';
            
            try {
                const response = await fetch('../php/api/ai.php?action=test_connection&provider=openai');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const text = await response.text();
                console.log('Raw response:', text);
                
                const data = JSON.parse(text);
                console.log('Parsed JSON:', data);
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="result pass">✅ اختبار OpenAI عبر JavaScript نجح!</div>
                        <div class="result info">📊 مصدر API Key: ${data.data.api_key_source}</div>
                        <div class="result info">🔑 طول API Key: ${data.data.api_key_length}</div>
                        <div class="json-display">${JSON.stringify(data, null, 2)}</div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="result fail">❌ اختبار OpenAI فشل: ${data.error || 'خطأ غير معروف'}</div>
                        <div class="json-display">${JSON.stringify(data, null, 2)}</div>
                    `;
                }
                
            } catch (error) {
                console.error('JavaScript test error:', error);
                resultsDiv.innerHTML = `
                    <div class="result fail">❌ خطأ في اختبار JavaScript: ${error.message}</div>
                    <div class="result info">🔍 تحقق من وحدة تحكم المطور للمزيد من التفاصيل</div>
                `;
            }
        }

        // Test context menu fix
        function testContextMenuFix() {
            const resultsDiv = document.getElementById('jsTestResults');
            
            try {
                console.log('🧪 Testing context menu fix...');
                
                // Test getSelection
                const selection = window.getSelection();
                console.log('✅ getSelection works:', !!selection);
                console.log('✅ rangeCount:', selection.rangeCount);
                
                // Test context menu event
                const event = new Event('contextmenu');
                document.dispatchEvent(event);
                console.log('✅ Context menu event handled');
                
                // Test context menu fix if available
                if (window.contextMenuFix && window.contextMenuFix.test) {
                    const testResult = window.contextMenuFix.test();
                    console.log('✅ Context menu fix test:', testResult);
                }
                
                resultsDiv.innerHTML = `
                    <div class="result pass">✅ اختبار Context Menu نجح!</div>
                    <div class="result info">🛡️ getSelection يعمل بأمان</div>
                    <div class="result info">📊 rangeCount: ${selection.rangeCount}</div>
                    <div class="result info">🎯 لا توجد أخطاء JavaScript</div>
                `;
                
            } catch (error) {
                console.error('Context menu test error:', error);
                resultsDiv.innerHTML = `
                    <div class="result fail">❌ خطأ في اختبار Context Menu: ${error.message}</div>
                `;
            }
        }

        // Auto-run tests after page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testOpenAIConnection, 2000);
        });
    </script>
</body>
</html>
