/* Product View More and Social Share Styles */
.view-more-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    margin: 10px 0;
    background-color: #4a90e2;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    font-weight: 500;
}

.view-more-link:hover {
    background-color: #357abd;
}

.view-more-link i {
    margin-left: 8px;
}

.social-share {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.share-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: transform 0.2s ease;
}

.share-button:hover {
    transform: scale(1.1);
}

.facebook-share {
    background-color: #1877f2;
}

.whatsapp-share {
    background-color: #25d366;
}

.share-button i {
    font-size: 18px;
}

/* RTL Specific Adjustments */
.product-card {
    text-align: right;
}

.social-share {
    justify-content: flex-end;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .product-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .view-more-link {
        width: 100%;
        margin: 5px 0;
    }

    .social-share {
        justify-content: center;
    }
}