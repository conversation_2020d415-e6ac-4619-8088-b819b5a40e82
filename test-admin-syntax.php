<?php
/**
 * Test Admin JavaScript Syntax
 */

header('Content-Type: text/html');

echo "<h1>Test Admin JavaScript Syntax</h1>";

// Check if the admin.js file exists and is readable
$adminJsFile = 'admin/js/admin.js';
if (file_exists($adminJsFile)) {
    echo "<p>✅ admin.js file exists</p>";
    
    // Check file size
    $fileSize = filesize($adminJsFile);
    echo "<p>File size: " . number_format($fileSize) . " bytes</p>";
    
    // Try to read the file
    $content = file_get_contents($adminJsFile);
    if ($content !== false) {
        echo "<p>✅ File is readable</p>";
        
        // Check for basic syntax issues
        $lines = explode("\n", $content);
        echo "<p>Total lines: " . count($lines) . "</p>";
        
        // Look for the loadUserManagementContent function
        $functionFound = false;
        $functionLine = 0;
        foreach ($lines as $lineNum => $line) {
            if (strpos($line, 'function loadUserManagementContent') !== false) {
                $functionFound = true;
                $functionLine = $lineNum + 1;
                break;
            }
        }
        
        if ($functionFound) {
            echo "<p>✅ loadUserManagementContent function found at line $functionLine</p>";
        } else {
            echo "<p>❌ loadUserManagementContent function not found</p>";
        }
        
        // Check for async function
        if (strpos($content, 'async function loadUserManagementContent') !== false) {
            echo "<p>✅ Function is properly marked as async</p>";
        } else {
            echo "<p>❌ Function is not marked as async</p>";
        }
        
    } else {
        echo "<p>❌ Cannot read file</p>";
    }
} else {
    echo "<p>❌ admin.js file does not exist</p>";
}

echo "<h2>Test User Management HTML</h2>";

// Check if user-management.html exists
$userMgmtFile = 'admin/user-management.html';
if (file_exists($userMgmtFile)) {
    echo "<p>✅ user-management.html file exists</p>";
    
    $content = file_get_contents($userMgmtFile);
    if ($content !== false) {
        echo "<p>✅ File is readable</p>";
        
        // Check for required elements
        $requiredElements = [
            'usersTableBody',
            'userSearch', 
            'roleFilter',
            'statusFilter',
            'usersCount'
        ];
        
        foreach ($requiredElements as $element) {
            if (strpos($content, 'id="' . $element . '"') !== false) {
                echo "<p>✅ Element '$element' found</p>";
            } else {
                echo "<p>❌ Element '$element' not found</p>";
            }
        }
    }
} else {
    echo "<p>❌ user-management.html file does not exist</p>";
}

echo "<h2>Recommendations</h2>";
echo "<ol>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "<li>Verify all required DOM elements exist in user-management.html</li>";
echo "<li>Test the API endpoints directly</li>";
echo "<li>Check network requests in browser dev tools</li>";
echo "</ol>";
?>
