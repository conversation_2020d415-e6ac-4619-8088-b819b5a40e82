<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار واجهة إدارة المتاجر</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #5a6fd8;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }
        .output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-pending { background: #ffc107; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .iframe-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            height: 600px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار واجهة إدارة المتاجر</h1>

        <div class="info">
            <h3>📋 الاختبارات المطلوبة:</h3>
            <ul>
                <li>✅ التحقق من API المتاجر</li>
                <li>✅ اختبار تحميل واجهة إدارة المتاجر</li>
                <li>✅ التحقق من عدم ظهور رسالة "جاري تحميل المتاجر..." بشكل دائم</li>
                <li>✅ التحقق من عرض بيانات المتاجر في الجدول</li>
                <li>✅ اختبار التفاعل مع واجهة المتاجر</li>
            </ul>
        </div>

        <div class="test-section">
            <h3><span class="status-indicator status-pending" id="test1Status"></span>اختبار 1: API المتاجر</h3>
            <button class="button" onclick="runTest1()" id="test1Btn">تشغيل الاختبار</button>
            <div id="test1Output"></div>
        </div>

        <div class="test-section">
            <h3><span class="status-indicator status-pending" id="test2Status"></span>اختبار 2: تحميل JavaScript</h3>
            <button class="button" onclick="runTest2()" id="test2Btn">تشغيل الاختبار</button>
            <div id="test2Output"></div>
        </div>

        <div class="test-section">
            <h3><span class="status-indicator status-pending" id="test3Status"></span>اختبار 3: واجهة إدارة المتاجر (مدمجة)</h3>
            <button class="button" onclick="runTest3()" id="test3Btn">تحميل الواجهة</button>
            <div id="test3Output"></div>
            <div class="iframe-container" id="iframeContainer" style="display: none;">
                <iframe id="storesIframe" src=""></iframe>
            </div>
        </div>

        <div class="test-section">
            <h3><span class="status-indicator status-pending" id="test4Status"></span>اختبار 4: الوصول المباشر للوحة التحكم</h3>
            <button class="button" onclick="runTest4()" id="test4Btn">فتح لوحة التحكم</button>
            <div id="test4Output"></div>
        </div>

        <div class="test-section">
            <h3>📊 ملخص النتائج</h3>
            <div id="summaryOutput">
                <div class="info">انقر على الاختبارات أعلاه لبدء التحقق من النظام</div>
            </div>
        </div>
    </div>

    <script>
        function setStatus(testId, status) {
            const indicator = document.getElementById(testId + 'Status');
            indicator.className = `status-indicator status-${status}`;
        }

        async function runTest1() {
            const btn = document.getElementById('test1Btn');
            const output = document.getElementById('test1Output');
            
            btn.disabled = true;
            btn.textContent = '⏳ جاري الاختبار...';
            setStatus('test1', 'pending');
            
            try {
                const response = await fetch('../php/api/stores.php');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    output.innerHTML = `
                        <div class="success">✅ API المتاجر يعمل بشكل صحيح!</div>
                        <div class="info">📊 HTTP Status: ${response.status}</div>
                        <div class="info">📊 عدد المتاجر: ${data.total}</div>
                        <div class="info">📊 الرسالة: ${data.message}</div>
                        <div class="output">${JSON.stringify(data, null, 2)}</div>
                    `;
                    setStatus('test1', 'success');
                } else {
                    output.innerHTML = `
                        <div class="error">❌ خطأ في API المتاجر</div>
                        <div class="output">${JSON.stringify(data, null, 2)}</div>
                    `;
                    setStatus('test1', 'error');
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في الاتصال: ${error.message}</div>`;
                setStatus('test1', 'error');
            }
            
            btn.disabled = false;
            btn.textContent = 'إعادة الاختبار';
        }

        async function runTest2() {
            const btn = document.getElementById('test2Btn');
            const output = document.getElementById('test2Output');
            
            btn.disabled = true;
            btn.textContent = '⏳ جاري الاختبار...';
            setStatus('test2', 'pending');
            
            try {
                // Test JavaScript files
                const jsFiles = [
                    'js/stores-management.js',
                    'stores-management.html'
                ];
                
                let allFilesOk = true;
                let results = [];
                
                for (const file of jsFiles) {
                    try {
                        const response = await fetch(file);
                        if (response.ok) {
                            results.push(`✅ ${file} - متاح`);
                        } else {
                            results.push(`❌ ${file} - غير متاح (${response.status})`);
                            allFilesOk = false;
                        }
                    } catch (error) {
                        results.push(`❌ ${file} - خطأ: ${error.message}`);
                        allFilesOk = false;
                    }
                }
                
                if (allFilesOk) {
                    output.innerHTML = `
                        <div class="success">✅ جميع الملفات متاحة!</div>
                        <div class="output">${results.join('\n')}</div>
                    `;
                    setStatus('test2', 'success');
                } else {
                    output.innerHTML = `
                        <div class="error">❌ بعض الملفات غير متاحة</div>
                        <div class="output">${results.join('\n')}</div>
                    `;
                    setStatus('test2', 'error');
                }
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في الاختبار: ${error.message}</div>`;
                setStatus('test2', 'error');
            }
            
            btn.disabled = false;
            btn.textContent = 'إعادة الاختبار';
        }

        async function runTest3() {
            const btn = document.getElementById('test3Btn');
            const output = document.getElementById('test3Output');
            const iframeContainer = document.getElementById('iframeContainer');
            const iframe = document.getElementById('storesIframe');
            
            btn.disabled = true;
            btn.textContent = '⏳ جاري التحميل...';
            setStatus('test3', 'pending');
            
            try {
                // Load the stores management interface
                iframe.src = 'stores-management.html';
                iframeContainer.style.display = 'block';
                
                // Wait for iframe to load
                iframe.onload = function() {
                    setTimeout(() => {
                        try {
                            // Check if the iframe loaded successfully
                            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                            if (iframeDoc && iframeDoc.body) {
                                output.innerHTML = `
                                    <div class="success">✅ تم تحميل واجهة إدارة المتاجر بنجاح!</div>
                                    <div class="info">📋 يمكنك الآن التفاعل مع الواجهة أدناه</div>
                                    <div class="info">🔍 تحقق من عدم ظهور رسالة "جاري تحميل المتاجر..." بشكل دائم</div>
                                `;
                                setStatus('test3', 'success');
                            } else {
                                output.innerHTML = `<div class="error">❌ فشل في تحميل محتوى الواجهة</div>`;
                                setStatus('test3', 'error');
                            }
                        } catch (error) {
                            output.innerHTML = `
                                <div class="success">✅ تم تحميل الواجهة (محدود بسبب CORS)</div>
                                <div class="info">📋 يمكنك التفاعل مع الواجهة أدناه</div>
                            `;
                            setStatus('test3', 'success');
                        }
                        
                        btn.disabled = false;
                        btn.textContent = 'إعادة التحميل';
                    }, 2000);
                };
                
                iframe.onerror = function() {
                    output.innerHTML = `<div class="error">❌ فشل في تحميل الواجهة</div>`;
                    setStatus('test3', 'error');
                    btn.disabled = false;
                    btn.textContent = 'إعادة المحاولة';
                };
                
            } catch (error) {
                output.innerHTML = `<div class="error">❌ خطأ في تحميل الواجهة: ${error.message}</div>`;
                setStatus('test3', 'error');
                btn.disabled = false;
                btn.textContent = 'إعادة المحاولة';
            }
        }

        function runTest4() {
            const btn = document.getElementById('test4Btn');
            const output = document.getElementById('test4Output');
            
            output.innerHTML = `
                <div class="info">🔗 فتح لوحة التحكم في نافذة جديدة...</div>
                <div class="info">📋 انتقل إلى قسم "إدارة المتاجر" وتحقق من:</div>
                <ul>
                    <li>عدم ظهور رسالة "جاري تحميل المتاجر..." بشكل دائم</li>
                    <li>عرض بيانات المتاجر في الجدول</li>
                    <li>إمكانية التفاعل مع الواجهة</li>
                </ul>
            `;
            
            // Open admin panel in new window
            window.open('index.html#storesManagement', '_blank');
            setStatus('test4', 'success');
        }

        // Auto-run first two tests
        window.addEventListener('load', () => {
            setTimeout(() => {
                runTest1();
                setTimeout(() => runTest2(), 2000);
            }, 1000);
        });
    </script>
</body>
</html>
