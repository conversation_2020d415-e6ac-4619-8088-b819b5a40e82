<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر التعديل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .result {
            background: #fff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a87; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار زر التعديل - صفحات الهبوط</h1>
    
    <div class="test-section">
        <h2>1. اختبار API للحصول على صفحة محددة</h2>
        <button onclick="testGetPage(2)">اختبار GET صفحة ID=2</button>
        <button onclick="testGetPage(4)">اختبار GET صفحة ID=4</button>
        <div id="apiResults"></div>
    </div>

    <div class="test-section">
        <h2>2. اختبار وظيفة editPage مباشرة</h2>
        <button onclick="testEditFunction(2)">اختبار editPage(2)</button>
        <button onclick="testEditFunction(4)">اختبار editPage(4)</button>
        <div id="editResults"></div>
    </div>

    <div class="test-section">
        <h2>3. محاكاة النقر على زر التعديل</h2>
        <button onclick="simulateEditClick(2)">محاكاة النقر على زر تعديل ID=2</button>
        <div id="clickResults"></div>
    </div>

    <script>
        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(div);
            console.log(message);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // Test 1: API direct
        async function testGetPage(pageId) {
            clearResults('apiResults');
            
            try {
                log('apiResults', `🔍 اختبار API للصفحة ID=${pageId}...`, 'info');
                
                const response = await fetch(`php/api/landing-pages.php?id=${pageId}`);
                log('apiResults', `📡 حالة الاستجابة: ${response.status}`, response.ok ? 'success' : 'error');
                
                const text = await response.text();
                log('apiResults', `📄 نص الاستجابة: <pre>${text.substring(0, 500)}...</pre>`, 'info');
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(text);
                        if (data.success && data.data) {
                            log('apiResults', `✅ تم العثور على الصفحة: ${data.data.titre}`, 'success');
                            log('apiResults', `📦 المنتج: ${data.data.product_title}`, 'info');
                        } else {
                            log('apiResults', '❌ البيانات غير صحيحة', 'error');
                        }
                    } catch (parseError) {
                        log('apiResults', `❌ خطأ في تحليل JSON: ${parseError.message}`, 'error');
                    }
                } else {
                    log('apiResults', '❌ فشل في الطلب', 'error');
                }
                
            } catch (error) {
                log('apiResults', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        // Test 2: Function direct (simulate landingPagesManager.editPage)
        async function testEditFunction(pageId) {
            clearResults('editResults');
            
            try {
                log('editResults', `🖊️ اختبار وظيفة editPage للصفحة ID=${pageId}...`, 'info');
                
                // Simulate the safeApiCall function
                const response = await fetch(`php/api/landing-pages.php?id=${pageId}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const text = await response.text();
                
                if (!text.trim()) {
                    log('editResults', '⚠️ الاستجابة فارغة!', 'error');
                    return;
                }

                let result;
                try {
                    result = JSON.parse(text);
                } catch (parseError) {
                    log('editResults', `❌ خطأ في تحليل JSON: ${parseError.message}`, 'error');
                    return;
                }

                log('editResults', '✅ تم استلام البيانات بنجاح', 'success');
                
                let pageData;
                if (result.success && result.data) {
                    pageData = Array.isArray(result.data) ? result.data[0] : result.data;
                } else if (Array.isArray(result)) {
                    pageData = result.find(page => page.id == pageId);
                } else {
                    throw new Error('لم يتم العثور على بيانات الصفحة');
                }

                if (!pageData) {
                    throw new Error('لم يتم العثور على الصفحة المطلوبة');
                }

                log('editResults', `✅ بيانات الصفحة: ${pageData.titre}`, 'success');
                log('editResults', `📦 المنتج: ${pageData.product_title}`, 'info');
                log('editResults', `🖼️ عدد الصور: ${pageData.images ? pageData.images.length : 0}`, 'info');
                
                // Test if modal elements exist
                const modal = document.getElementById('landingPageModal');
                const form = document.getElementById('landingPageForm');
                
                log('editResults', `🪟 المودال موجود: ${modal ? 'نعم' : 'لا'}`, modal ? 'success' : 'error');
                log('editResults', `📝 النموذج موجود: ${form ? 'نعم' : 'لا'}`, form ? 'success' : 'error');
                
            } catch (error) {
                log('editResults', `❌ خطأ في وظيفة التعديل: ${error.message}`, 'error');
            }
        }

        // Test 3: Simulate button click
        function simulateEditClick(pageId) {
            clearResults('clickResults');
            
            log('clickResults', `🖱️ محاكاة النقر على زر التعديل للصفحة ID=${pageId}...`, 'info');
            
            // Check if landingPagesManager exists
            if (typeof window.landingPagesManager !== 'undefined') {
                log('clickResults', '✅ landingPagesManager موجود', 'success');
                
                if (typeof window.landingPagesManager.editPage === 'function') {
                    log('clickResults', '✅ وظيفة editPage موجودة', 'success');
                    
                    try {
                        window.landingPagesManager.editPage(pageId);
                        log('clickResults', '✅ تم استدعاء وظيفة التعديل', 'success');
                    } catch (error) {
                        log('clickResults', `❌ خطأ في استدعاء وظيفة التعديل: ${error.message}`, 'error');
                    }
                } else {
                    log('clickResults', '❌ وظيفة editPage غير موجودة', 'error');
                }
            } else {
                log('clickResults', '❌ landingPagesManager غير موجود', 'error');
                log('clickResults', '💡 يجب فتح هذا الاختبار من داخل صفحة الإدارة', 'info');
            }
        }

        // Auto-run basic test
        window.addEventListener('load', () => {
            console.log('🚀 بدء الاختبارات التلقائية...');
            testGetPage(2);
        });
    </script>
</body>
</html>
