/**
 * Test Suite for Order Form System
 * Comprehensive testing for geographic data, shipping integration, and order processing
 */

class OrderFormTestSuite {
    constructor() {
        this.stats = {
            testsRun: 0,
            testsPass: 0,
            testsFail: 0,
            apiCalls: 0
        };
        
        this.init();
    }

    init() {
        this.loadTestWilayas();
        this.setupEventListeners();
        this.updateStats();
    }

    async loadTestWilayas() {
        try {
            const response = await fetch('php/api/geographic-data.php?action=wilayas');
            const data = await response.json();
            
            if (data.success && data.wilayas) {
                const select = document.getElementById('testWilayaSelect');
                select.innerHTML = '<option value="">-- اختر الولاية --</option>';
                
                data.wilayas.forEach(wilaya => {
                    const option = document.createElement('option');
                    option.value = wilaya.wilaya_code;
                    option.textContent = `${wilaya.wilaya_name_ar} (المنطقة ${wilaya.zone_number})`;
                    select.appendChild(option);
                });
                
                this.logToConsole(`✅ Loaded ${data.wilayas.length} wilayas for testing`);
            }
        } catch (error) {
            this.logToConsole(`❌ Error loading test wilayas: ${error.message}`);
        }
    }

    setupEventListeners() {
        // Wilaya selection for testing
        const wilayaSelect = document.getElementById('testWilayaSelect');
        if (wilayaSelect) {
            wilayaSelect.addEventListener('change', async (e) => {
                const wilayaCode = e.target.value;
                const communeSelect = document.getElementById('testCommuneSelect');
                
                communeSelect.innerHTML = '<option value="">-- اختر البلدية --</option>';
                communeSelect.disabled = true;
                
                if (wilayaCode) {
                    await this.loadTestCommunes(wilayaCode);
                }
            });
        }
    }

    async loadTestCommunes(wilayaCode) {
        try {
            const response = await fetch(`php/api/geographic-data.php?action=communes&wilaya_code=${wilayaCode}`);
            const data = await response.json();
            
            if (data.success && data.communes) {
                const select = document.getElementById('testCommuneSelect');
                
                data.communes.forEach(commune => {
                    const option = document.createElement('option');
                    option.value = commune.commune_code;
                    option.textContent = commune.commune_name_ar;
                    select.appendChild(option);
                });
                
                select.disabled = false;
                this.logToConsole(`✅ Loaded ${data.communes.length} communes for wilaya ${wilayaCode}`);
            }
        } catch (error) {
            this.logToConsole(`❌ Error loading communes: ${error.message}`);
        }
    }

    // Test Functions
    async testDatabaseTables() {
        this.addTestResult('🗄️ اختبار جداول قاعدة البيانات...', 'info');
        this.stats.testsRun++;
        
        try {
            // Test wilayas table
            const wilayasResponse = await fetch('php/api/geographic-data.php?action=wilayas');
            const wilayasData = await wilayasResponse.json();
            this.stats.apiCalls++;
            
            if (!wilayasData.success) {
                throw new Error('Wilayas table test failed');
            }
            
            // Test shipping zones table
            const zonesResponse = await fetch('php/api/payment-settings.php?module=shipping&action=zones');
            const zonesData = await zonesResponse.json();
            this.stats.apiCalls++;
            
            if (!zonesData.success) {
                throw new Error('Shipping zones table test failed');
            }
            
            this.addTestResult(`✅ جداول قاعدة البيانات تعمل بشكل صحيح (${wilayasData.wilayas.length} ولاية، ${zonesData.zones.length} منطقة)`, 'success');
            this.stats.testsPass++;
            
        } catch (error) {
            this.addTestResult(`❌ فشل اختبار جداول قاعدة البيانات: ${error.message}`, 'error');
            this.stats.testsFail++;
        }
        
        this.updateStats();
    }

    async testGeographicAPI() {
        this.addTestResult('🗺️ اختبار API البيانات الجغرافية...', 'info');
        this.stats.testsRun++;
        
        try {
            // Test wilayas endpoint
            const wilayasResponse = await fetch('php/api/geographic-data.php?action=wilayas');
            const wilayasData = await wilayasResponse.json();
            this.stats.apiCalls++;
            
            if (!wilayasData.success || !wilayasData.wilayas || wilayasData.wilayas.length === 0) {
                throw new Error('Wilayas API returned invalid data');
            }
            
            // Test communes endpoint with a sample wilaya
            const sampleWilaya = wilayasData.wilayas[0].wilaya_code;
            const communesResponse = await fetch(`php/api/geographic-data.php?action=communes&wilaya_code=${sampleWilaya}`);
            const communesData = await communesResponse.json();
            this.stats.apiCalls++;
            
            if (!communesData.success) {
                throw new Error('Communes API test failed');
            }
            
            this.addTestResult(`✅ API البيانات الجغرافية يعمل بشكل صحيح (${wilayasData.wilayas.length} ولاية، ${communesData.communes.length} بلدية للولاية ${sampleWilaya})`, 'success');
            this.stats.testsPass++;
            
        } catch (error) {
            this.addTestResult(`❌ فشل اختبار API البيانات الجغرافية: ${error.message}`, 'error');
            this.stats.testsFail++;
        }
        
        this.updateStats();
    }

    async testShippingIntegration() {
        this.addTestResult('🚚 اختبار تكامل الشحن...', 'info');
        this.stats.testsRun++;
        
        try {
            const testWilayas = ['09', '15', '25', '31', '11']; // Different zones
            const results = [];
            
            for (const wilayaCode of testWilayas) {
                const response = await fetch(`php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilayaCode}&weight=1.0`);
                const data = await response.json();
                this.stats.apiCalls++;
                
                if (data.success) {
                    results.push({
                        wilaya: data.zone_info.wilaya_name,
                        zone: data.zone_info.zone_name,
                        cost: data.cost_breakdown.total_cost,
                        delivery: data.zone_info.delivery_time
                    });
                }
            }
            
            if (results.length === testWilayas.length) {
                this.addTestResult(`✅ تكامل الشحن يعمل بشكل صحيح لجميع المناطق (${results.length} ولايات مختبرة)`, 'success');
                this.logToConsole('Shipping test results:');
                results.forEach(result => {
                    this.logToConsole(`  ${result.wilaya} (${result.zone}): ${result.cost} DA, ${result.delivery}`);
                });
                this.stats.testsPass++;
            } else {
                throw new Error(`Only ${results.length}/${testWilayas.length} shipping calculations succeeded`);
            }
            
        } catch (error) {
            this.addTestResult(`❌ فشل اختبار تكامل الشحن: ${error.message}`, 'error');
            this.stats.testsFail++;
        }
        
        this.updateStats();
    }

    async testShippingManagement() {
        this.addTestResult('⚙️ اختبار إدارة الشحن...', 'info');
        this.stats.testsRun++;
        
        try {
            // Test custom rates endpoint
            const customRatesResponse = await fetch('php/api/payment-settings.php?module=shipping&action=custom_rates');
            const customRatesData = await customRatesResponse.json();
            this.stats.apiCalls++;
            
            if (!customRatesData.success) {
                throw new Error('Custom rates API test failed');
            }
            
            // Test shipping override endpoint
            const overrideResponse = await fetch('php/api/geographic-data.php?action=shipping_override&location_type=wilaya&location_code=09');
            const overrideData = await overrideResponse.json();
            this.stats.apiCalls++;
            
            if (!overrideData.success) {
                throw new Error('Shipping override API test failed');
            }
            
            this.addTestResult(`✅ إدارة الشحن تعمل بشكل صحيح (${customRatesData.custom_rates.length} رسوم مخصصة)`, 'success');
            this.stats.testsPass++;
            
        } catch (error) {
            this.addTestResult(`❌ فشل اختبار إدارة الشحن: ${error.message}`, 'error');
            this.stats.testsFail++;
        }
        
        this.updateStats();
    }

    async testCascadingDropdowns() {
        this.addTestResult('📋 اختبار القوائم المتتالية...', 'info');
        this.stats.testsRun++;
        
        try {
            const wilayaSelect = document.getElementById('testWilayaSelect');
            const communeSelect = document.getElementById('testCommuneSelect');
            
            if (!wilayaSelect.value) {
                throw new Error('يرجى اختيار ولاية أولاً');
            }
            
            // Simulate wilaya selection
            const wilayaCode = wilayaSelect.value;
            await this.loadTestCommunes(wilayaCode);
            
            // Check if communes were loaded
            if (communeSelect.options.length <= 1) {
                throw new Error('لم يتم تحميل البلديات');
            }
            
            this.addTestResult(`✅ القوائم المتتالية تعمل بشكل صحيح (${communeSelect.options.length - 1} بلدية)`, 'success');
            this.stats.testsPass++;
            
        } catch (error) {
            this.addTestResult(`❌ فشل اختبار القوائم المتتالية: ${error.message}`, 'error');
            this.stats.testsFail++;
        }
        
        this.updateStats();
    }

    async testShippingCalculation() {
        this.addTestResult('💰 اختبار حساب الشحن...', 'info');
        this.stats.testsRun++;
        
        try {
            const wilayaSelect = document.getElementById('testWilayaSelect');
            
            if (!wilayaSelect.value) {
                throw new Error('يرجى اختيار ولاية أولاً');
            }
            
            const wilayaCode = wilayaSelect.value;
            const response = await fetch(`php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilayaCode}&weight=2.5`);
            const data = await response.json();
            this.stats.apiCalls++;
            
            if (!data.success) {
                throw new Error(data.message || 'فشل في حساب الشحن');
            }
            
            // Display result
            const resultDiv = document.getElementById('shippingTestResult');
            resultDiv.innerHTML = `
                <h5>نتيجة حساب الشحن:</h5>
                <p><strong>الولاية:</strong> ${data.zone_info.wilaya_name}</p>
                <p><strong>المنطقة:</strong> ${data.zone_info.zone_name}</p>
                <p><strong>التكلفة:</strong> ${data.cost_breakdown.total_cost} دج</p>
                <p><strong>مدة التوصيل:</strong> ${data.zone_info.delivery_time}</p>
                <p><strong>الوزن:</strong> 2.5 كغ</p>
            `;
            resultDiv.style.display = 'block';
            
            this.addTestResult(`✅ حساب الشحن يعمل بشكل صحيح (${data.cost_breakdown.total_cost} دج)`, 'success');
            this.stats.testsPass++;
            
        } catch (error) {
            this.addTestResult(`❌ فشل اختبار حساب الشحن: ${error.message}`, 'error');
            this.stats.testsFail++;
        }
        
        this.updateStats();
    }

    // Utility Functions
    addTestResult(message, type = 'info') {
        const resultsDiv = document.getElementById('test-results');
        const resultDiv = document.createElement('div');
        resultDiv.className = `test-result ${type}`;
        resultDiv.textContent = message;
        resultsDiv.appendChild(resultDiv);
        
        // Auto-scroll to latest result
        resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    logToConsole(message) {
        const output = document.getElementById('console-output');
        const timestamp = new Date().toLocaleTimeString();
        output.textContent += `[${timestamp}] ${message}\n`;
        output.scrollTop = output.scrollHeight;
    }

    updateStats() {
        document.getElementById('testsRun').textContent = this.stats.testsRun;
        document.getElementById('testsPass').textContent = this.stats.testsPass;
        document.getElementById('testsFail').textContent = this.stats.testsFail;
        document.getElementById('apiCalls').textContent = this.stats.apiCalls;
    }

    clearResults() {
        document.getElementById('test-results').innerHTML = '';
        document.getElementById('console-output').textContent = '';
        document.getElementById('shippingTestResult').style.display = 'none';
        
        // Reset stats
        this.stats = { testsRun: 0, testsPass: 0, testsFail: 0, apiCalls: 0 };
        this.updateStats();
    }

    async runAllTests() {
        this.clearResults();
        this.addTestResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
        
        const tests = [
            () => this.testDatabaseTables(),
            () => this.testGeographicAPI(),
            () => this.testShippingIntegration(),
            () => this.testShippingManagement()
        ];
        
        for (const test of tests) {
            await test();
            await new Promise(resolve => setTimeout(resolve, 1000)); // Delay between tests
        }
        
        this.addTestResult(`🎉 انتهاء جميع الاختبارات (${this.stats.testsPass}/${this.stats.testsRun} نجح)`, 'info');
    }

    generateTestReport() {
        const report = {
            timestamp: new Date().toISOString(),
            stats: this.stats,
            success_rate: this.stats.testsRun > 0 ? ((this.stats.testsPass / this.stats.testsRun) * 100).toFixed(2) : 0,
            status: this.stats.testsFail === 0 ? 'PASS' : 'FAIL'
        };
        
        this.logToConsole('=== TEST REPORT ===');
        this.logToConsole(`Status: ${report.status}`);
        this.logToConsole(`Success Rate: ${report.success_rate}%`);
        this.logToConsole(`Tests Run: ${report.stats.testsRun}`);
        this.logToConsole(`Tests Passed: ${report.stats.testsPass}`);
        this.logToConsole(`Tests Failed: ${report.stats.testsFail}`);
        this.logToConsole(`API Calls: ${report.stats.apiCalls}`);
        this.logToConsole(`Timestamp: ${report.timestamp}`);
        
        // Download report as JSON
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `order-form-test-report-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// Global functions for HTML buttons
function testDatabaseTables() { testSuite.testDatabaseTables(); }
function testGeographicAPI() { testSuite.testGeographicAPI(); }
function testShippingIntegration() { testSuite.testShippingIntegration(); }
function testShippingManagement() { testSuite.testShippingManagement(); }
function testCascadingDropdowns() { testSuite.testCascadingDropdowns(); }
function testShippingCalculation() { testSuite.testShippingCalculation(); }
function clearResults() { testSuite.clearResults(); }
function runAllTests() { testSuite.runAllTests(); }
function generateTestReport() { testSuite.generateTestReport(); }

function openOrderForm(productId, title, price, image) {
    const params = new URLSearchParams({
        product_id: productId,
        title: encodeURIComponent(title),
        price: price,
        image: encodeURIComponent(image)
    });
    
    window.open(`order-form.html?${params.toString()}`, '_blank');
}

function testCustomRates() {
    testSuite.addTestResult('⚠️ اختبار الرسوم المخصصة - يتطلب صلاحيات الإدارة', 'warning');
}

function testZoneManagement() {
    testSuite.addTestResult('⚠️ اختبار إدارة المناطق - يتطلب صلاحيات الإدارة', 'warning');
}

function testFreeShipping() {
    testSuite.addTestResult('⚠️ اختبار الشحن المجاني - يتطلب صلاحيات الإدارة', 'warning');
}

function openAdminPanel() {
    window.open('admin/payment_settings.html', '_blank');
}

// Initialize test suite
let testSuite;
document.addEventListener('DOMContentLoaded', () => {
    console.log('🧪 Order Form Test Suite initialized');
    testSuite = new OrderFormTestSuite();
});
