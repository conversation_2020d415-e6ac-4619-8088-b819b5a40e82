<?php
require_once 'php/config.php';

// Get landing page ID from URL - support both direct ID and URL-based lookup
$landing_page_id = 0;

if (isset($_GET['id'])) {
    // Direct ID access: /landing-page-template.php?id=123
    $landing_page_id = (int)$_GET['id'];
} elseif (isset($_GET['url'])) {
    // URL-based access: /landing/product-20-68717bb83b7a9 -> ?url=20-68717bb83b7a9
    $url_param = $_GET['url'];

    // Look up landing page by lien_url
    try {
        $stmt = $conn->prepare("SELECT id FROM landing_pages WHERE lien_url = ?");
        $stmt->execute(['/landing/product-' . $url_param]);
        $result = $stmt->fetch();

        if ($result) {
            $landing_page_id = (int)$result['id'];
        }
    } catch (PDOException $e) {
        error_log("Error looking up landing page by URL: " . $e->getMessage());
    }
}

if (!$landing_page_id) {
    header('HTTP/1.0 404 Not Found');
    exit('Landing page not found');
}

try {
    // Get landing page data with product info
    $stmt = $conn->prepare("
        SELECT lp.*, p.titre as product_title, p.description as product_description,
               p.prix, p.type, p.auteur, p.materiel, p.capacite, p.processeur, p.ram, p.stockage, p.image_url as product_image
        FROM landing_pages lp
        JOIN produits p ON lp.produit_id = p.id
        WHERE lp.id = ?
    ");
    $stmt->execute([$landing_page_id]);
    $landing_page = $stmt->fetch();

    if (!$landing_page) {
        header('HTTP/1.0 404 Not Found');
        exit('Landing page not found');
    }

    // Get landing page images
    $stmt = $conn->prepare("
        SELECT image_url
        FROM landing_page_images
        WHERE landing_page_id = ?
        ORDER BY ordre
    ");
    $stmt->execute([$landing_page_id]);
    $images = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    error_log("Error loading landing page: " . $e->getMessage());
    header('HTTP/1.0 500 Internal Server Error');
    exit('Error loading landing page');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($landing_page['titre']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header Section */
        .header {
            text-align: center;
            padding: 40px 0;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: fadeInDown 1s ease-out;
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        /* Main Content */
        .main-content {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 40px 0;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        /* Image Carousel */
        .image-carousel {
            position: relative;
            height: 400px;
            overflow: hidden;
        }

        .carousel-container {
            display: flex;
            transition: transform 0.5s ease-in-out;
            height: 100%;
        }

        .carousel-slide {
            min-width: 100%;
            height: 100%;
            position: relative;
        }

        .carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            padding: 15px;
            cursor: pointer;
            font-size: 1.5rem;
            transition: background 0.3s;
        }

        .carousel-nav:hover {
            background: rgba(0, 0, 0, 0.8);
        }

        .carousel-prev {
            right: 20px;
        }

        .carousel-next {
            left: 20px;
        }

        .carousel-indicators {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: background 0.3s;
        }

        .indicator.active {
            background: white;
        }

        /* Content Sections */
        .content-section {
            padding: 40px;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: start;
        }

        .content-block {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
            animation: slideInRight 0.8s ease-out;
        }

        .content-block h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .content-block p {
            color: #666;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        /* Product Info */
        .product-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .product-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .product-price {
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
            margin: 20px 0;
        }

        .product-specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .spec-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .spec-label {
            font-weight: bold;
            margin-bottom: 5px;
        }

        /* Product Description Styles */
        .product-description {
            line-height: 1.6;
            margin: 20px 0;
        }

        .product-description h3 {
            color: #2c3e50;
            margin: 20px 0 10px 0;
            font-size: 1.3em;
        }

        .product-description ul {
            margin: 15px 0;
            padding-right: 20px;
        }

        .product-description li {
            margin: 8px 0;
            line-height: 1.5;
        }

        .product-description p {
            margin: 15px 0;
            text-align: justify;
        }

        .product-description strong {
            color: #2c3e50;
            font-weight: 600;
        }

        /* CTA Section */
        .cta-section {
            background: #f8f9fa;
            padding: 60px 40px;
            text-align: center;
        }

        .cta-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.3rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .two-column {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .product-specs {
                grid-template-columns: 1fr;
            }

            .carousel-nav {
                padding: 10px;
                font-size: 1.2rem;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><?php echo htmlspecialchars($landing_page['titre']); ?></h1>
            <p class="subtitle">اكتشف أفضل العروض والمنتجات المميزة</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Image Carousel -->
            <?php if (!empty($images)): ?>
                <div class="image-carousel">
                    <div class="carousel-container" id="carouselContainer">
                        <?php foreach ($images as $index => $image): ?>
                            <div class="carousel-slide">
                                <img src="<?php echo htmlspecialchars($image); ?>" alt="صورة المنتج <?php echo $index + 1; ?>">
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <?php if (count($images) > 1): ?>
                        <button class="carousel-nav carousel-prev" onclick="previousSlide()">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="carousel-nav carousel-next" onclick="nextSlide()">
                            <i class="fas fa-chevron-left"></i>
                        </button>

                        <div class="carousel-indicators">
                            <?php foreach ($images as $index => $image): ?>
                                <div class="indicator <?php echo $index === 0 ? 'active' : ''; ?>" onclick="goToSlide(<?php echo $index; ?>)"></div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Product Info -->
            <div class="product-info">
                <h2 class="product-title"><?php echo htmlspecialchars($landing_page['product_title']); ?></h2>
                <div class="product-price"><?php echo number_format($landing_page['prix'], 2); ?> دج</div>

                <?php if ($landing_page['product_description']): ?>
                    <div class="product-description"><?php echo $landing_page['product_description']; ?></div>
                <?php endif; ?>

                <!-- Product Specifications -->
                <div class="product-specs">
                    <?php if ($landing_page['type'] === 'book' && $landing_page['auteur']): ?>
                        <div class="spec-item">
                            <div class="spec-label">المؤلف</div>
                            <div><?php echo htmlspecialchars($landing_page['auteur']); ?></div>
                        </div>
                    <?php endif; ?>

                    <?php if ($landing_page['type'] === 'laptop'): ?>
                        <?php if ($landing_page['processeur']): ?>
                            <div class="spec-item">
                                <div class="spec-label">المعالج</div>
                                <div><?php echo htmlspecialchars($landing_page['processeur']); ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($landing_page['ram']): ?>
                            <div class="spec-item">
                                <div class="spec-label">الذاكرة</div>
                                <div><?php echo htmlspecialchars($landing_page['ram']); ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($landing_page['stockage']): ?>
                            <div class="spec-item">
                                <div class="spec-label">التخزين</div>
                                <div><?php echo htmlspecialchars($landing_page['stockage']); ?></div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if ($landing_page['type'] === 'bag'): ?>
                        <?php if ($landing_page['materiel']): ?>
                            <div class="spec-item">
                                <div class="spec-label">المادة</div>
                                <div><?php echo htmlspecialchars($landing_page['materiel']); ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($landing_page['capacite']): ?>
                            <div class="spec-item">
                                <div class="spec-label">السعة</div>
                                <div><?php echo htmlspecialchars($landing_page['capacite']); ?></div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Content Sections -->
            <div class="content-section">
                <div class="two-column">
                    <!-- Right Content -->
                    <?php if ($landing_page['contenu_droit']): ?>
                        <div class="content-block">
                            <h2>مميزات المنتج</h2>
                            <div><?php echo $landing_page['contenu_droit']; ?></div>
                        </div>
                    <?php endif; ?>

                    <!-- Left Content -->
                    <?php if ($landing_page['contenu_gauche']): ?>
                        <div class="content-block">
                            <h2>تفاصيل إضافية</h2>
                            <div><?php echo $landing_page['contenu_gauche']; ?></div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- CTA Section -->
            <div class="cta-section">
                <h2>احصل على هذا المنتج الآن!</h2>
                <p>عرض محدود - لا تفوت الفرصة</p>
                <a href="#order" class="cta-button">
                    <i class="fas fa-shopping-cart"></i>
                    اطلب الآن
                </a>
                <a href="tel:+213123456789" class="cta-button">
                    <i class="fas fa-phone"></i>
                    اتصل بنا
                </a>
            </div>
        </div>
    </div>

    <script>
        // Carousel functionality
        let currentSlide = 0;
        const slides = document.querySelectorAll('.carousel-slide');
        const indicators = document.querySelectorAll('.indicator');
        const totalSlides = slides.length;

        function updateCarousel() {
            const container = document.getElementById('carouselContainer');
            container.style.transform = `translateX(-${currentSlide * 100}%)`;

            // Update indicators
            indicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === currentSlide);
            });
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            updateCarousel();
        }

        function previousSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            updateCarousel();
        }

        function goToSlide(index) {
            currentSlide = index;
            updateCarousel();
        }

        // Auto-play carousel
        if (totalSlides > 1) {
            setInterval(nextSlide, 5000);
        }

        // Smooth scrolling for CTA
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>

</html>
