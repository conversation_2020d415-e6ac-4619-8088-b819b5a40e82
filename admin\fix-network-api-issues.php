<?php
/**
 * Fix Network and API Connectivity Issues
 * Creates missing API endpoints and ensures proper responses
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل الشبكة وAPI</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .fix-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .fix-button:hover {
            background: #0056b3;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح مشاكل الشبكة وAPI</h1>
        <p>هذا السكريبت ينشئ نقاط نهاية API مفقودة ويضمن الاستجابات الصحيحة.</p>

        <?php
        try {
            require_once '../php/config.php';
            
            // Fix 1: Create Dashboard Stats API
            echo '<div class="fix-section">';
            echo '<h3>📊 إصلاح 1: إنشاء Dashboard Stats API</h3>';
            
            $dashboardStatsFile = '../php/api/dashboard-stats.php';
            if (!file_exists($dashboardStatsFile)) {
                echo '<div class="result warning">⚠️ ملف dashboard-stats.php غير موجود - جاري الإنشاء...</div>';
                
                $dashboardStatsContent = '<?php
/**
 * Dashboard Statistics API
 * Returns statistics for the admin dashboard
 */

header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Handle preflight requests
if ($_SERVER["REQUEST_METHOD"] == "OPTIONS") {
    exit(0);
}

try {
    require_once "../config.php";
    
    $pdo = getPDOConnection();
    
    // Get product count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits WHERE actif = 1");
    $productCount = $stmt->fetch()[\'count\'];
    
    // Get category count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE actif = 1");
    $categoryCount = $stmt->fetch()[\'count\'];
    
    // Get total sales (if orders table exists)
    $totalSales = 0;
    try {
        $stmt = $pdo->query("SELECT SUM(total) as total FROM commandes WHERE statut = \'completed\'");
        $result = $stmt->fetch();
        $totalSales = $result[\'total\'] ?? 0;
    } catch (Exception $e) {
        // Orders table might not exist yet
        $totalSales = 0;
    }
    
    // Get recent activity count
    $recentActivity = 5; // Default value
    
    $stats = [
        "success" => true,
        "data" => [
            "products" => (int)$productCount,
            "categories" => (int)$categoryCount,
            "total_sales" => (float)$totalSales,
            "recent_activity" => (int)$recentActivity,
            "currency" => "DZD"
        ]
    ];
    
    echo json_encode($stats, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "خطأ في تحميل الإحصائيات: " . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>';
                
                if (file_put_contents($dashboardStatsFile, $dashboardStatsContent)) {
                    echo '<div class="result pass">✅ تم إنشاء dashboard-stats.php بنجاح</div>';
                } else {
                    echo '<div class="result fail">❌ فشل في إنشاء dashboard-stats.php</div>';
                }
            } else {
                echo '<div class="result pass">✅ ملف dashboard-stats.php موجود</div>';
            }
            
            // Test the API
            try {
                ob_start();
                include $dashboardStatsFile;
                $output = ob_get_clean();
                
                $data = json_decode($output, true);
                if ($data && $data['success']) {
                    echo '<div class="result pass">✅ Dashboard Stats API يعمل بنجاح</div>';
                    echo '<div class="result info">📊 المنتجات: ' . $data['data']['products'] . ', الفئات: ' . $data['data']['categories'] . '</div>';
                } else {
                    echo '<div class="result fail">❌ Dashboard Stats API لا يعمل بشكل صحيح</div>';
                    echo '<pre>' . htmlspecialchars($output) . '</pre>';
                }
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في اختبار Dashboard Stats API: ' . $e->getMessage() . '</div>';
            }
            echo '</div>';

            // Fix 2: Create Store Settings API
            echo '<div class="fix-section">';
            echo '<h3>🏪 إصلاح 2: إنشاء Store Settings API</h3>';
            
            $storeSettingsFile = '../php/api/store-settings.php';
            if (!file_exists($storeSettingsFile)) {
                echo '<div class="result warning">⚠️ ملف store-settings.php غير موجود - جاري الإنشاء...</div>';
                
                $storeSettingsContent = '<?php
/**
 * Store Settings API
 * Manages store configuration settings
 */

header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Handle preflight requests
if ($_SERVER["REQUEST_METHOD"] == "OPTIONS") {
    exit(0);
}

try {
    require_once "../config.php";
    
    $pdo = getPDOConnection();
    
    // Ensure store_settings table exists
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS store_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        setting_type ENUM(\'string\', \'integer\', \'float\', \'boolean\', \'json\') DEFAULT \'string\',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createTableSQL);
    
    if ($_SERVER["REQUEST_METHOD"] == "GET") {
        // Get all settings
        $stmt = $pdo->query("SELECT setting_key, setting_value, setting_type FROM store_settings WHERE is_active = 1");
        $settings = [];
        
        while ($row = $stmt->fetch()) {
            $value = $row[\'setting_value\'];
            
            // Convert value based on type
            switch ($row[\'setting_type\']) {
                case \'integer\':
                    $value = (int)$value;
                    break;
                case \'float\':
                    $value = (float)$value;
                    break;
                case \'boolean\':
                    $value = (bool)$value;
                    break;
                case \'json\':
                    $value = json_decode($value, true);
                    break;
            }
            
            $settings[$row[\'setting_key\']] = $value;
        }
        
        // Add default settings if none exist
        if (empty($settings)) {
            $defaultSettings = [
                [\'store_name\', \'متجر مصعب\', \'string\'],
                [\'store_description\', \'متجر إلكتروني للكتب والمنتجات\', \'string\'],
                [\'store_email\', \'<EMAIL>\', \'string\'],
                [\'currency\', \'DZD\', \'string\'],
                [\'tax_rate\', \'19\', \'float\'],
                [\'shipping_cost\', \'500\', \'float\']
            ];
            
            $insertStmt = $pdo->prepare("INSERT IGNORE INTO store_settings (setting_key, setting_value, setting_type) VALUES (?, ?, ?)");
            foreach ($defaultSettings as $setting) {
                $insertStmt->execute($setting);
                $settings[$setting[0]] = $setting[1];
            }
        }
        
        echo json_encode([
            "success" => true,
            "data" => $settings
        ], JSON_UNESCAPED_UNICODE);
        
    } else {
        echo json_encode([
            "success" => false,
            "message" => "Method not supported"
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "خطأ في إعدادات المتجر: " . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>';
                
                if (file_put_contents($storeSettingsFile, $storeSettingsContent)) {
                    echo '<div class="result pass">✅ تم إنشاء store-settings.php بنجاح</div>';
                } else {
                    echo '<div class="result fail">❌ فشل في إنشاء store-settings.php</div>';
                }
            } else {
                echo '<div class="result pass">✅ ملف store-settings.php موجود</div>';
            }
            
            // Test the API
            try {
                ob_start();
                $_SERVER['REQUEST_METHOD'] = 'GET';
                include $storeSettingsFile;
                $output = ob_get_clean();
                
                $data = json_decode($output, true);
                if ($data && $data['success']) {
                    echo '<div class="result pass">✅ Store Settings API يعمل بنجاح</div>';
                    echo '<div class="result info">📊 عدد الإعدادات: ' . count($data['data']) . '</div>';
                } else {
                    echo '<div class="result fail">❌ Store Settings API لا يعمل بشكل صحيح</div>';
                    echo '<pre>' . htmlspecialchars($output) . '</pre>';
                }
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في اختبار Store Settings API: ' . $e->getMessage() . '</div>';
            }
            echo '</div>';

            // Fix 3: Create Notifications API
            echo '<div class="fix-section">';
            echo '<h3>🔔 إصلاح 3: إنشاء Notifications API</h3>';
            
            $notificationsFile = '../php/api/notifications.php';
            if (!file_exists($notificationsFile)) {
                echo '<div class="result warning">⚠️ ملف notifications.php غير موجود - جاري الإنشاء...</div>';
                
                $notificationsContent = '<?php
/**
 * Notifications API
 * Manages system notifications
 */

header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Handle preflight requests
if ($_SERVER["REQUEST_METHOD"] == "OPTIONS") {
    exit(0);
}

try {
    require_once "../config.php";
    
    if ($_SERVER["REQUEST_METHOD"] == "GET") {
        // Return sample notifications for now
        $notifications = [
            [
                "id" => 1,
                "title" => "مرحباً بك في لوحة التحكم",
                "message" => "تم تحميل النظام بنجاح",
                "type" => "success",
                "timestamp" => date("Y-m-d H:i:s"),
                "read" => false
            ],
            [
                "id" => 2,
                "title" => "تحديث النظام",
                "message" => "تم إصلاح مشاكل API بنجاح",
                "type" => "info",
                "timestamp" => date("Y-m-d H:i:s", strtotime("-1 hour")),
                "read" => false
            ]
        ];
        
        echo json_encode([
            "success" => true,
            "data" => $notifications,
            "count" => count($notifications)
        ], JSON_UNESCAPED_UNICODE);
        
    } else {
        echo json_encode([
            "success" => false,
            "message" => "Method not supported"
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "خطأ في الإشعارات: " . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>';
                
                if (file_put_contents($notificationsFile, $notificationsContent)) {
                    echo '<div class="result pass">✅ تم إنشاء notifications.php بنجاح</div>';
                } else {
                    echo '<div class="result fail">❌ فشل في إنشاء notifications.php</div>';
                }
            } else {
                echo '<div class="result pass">✅ ملف notifications.php موجود</div>';
            }
            echo '</div>';

            // Fix 4: Test All API Endpoints
            echo '<div class="fix-section">';
            echo '<h3>🧪 إصلاح 4: اختبار جميع نقاط نهاية API</h3>';
            
            $apiEndpoints = [
                '../php/admin.php?action=check' => 'المصادقة',
                '../php/api/dashboard-stats.php' => 'إحصائيات لوحة التحكم',
                '../php/api/store-settings.php' => 'إعدادات المتجر',
                '../php/api/notifications.php' => 'الإشعارات',
                '../php/api/products.php' => 'المنتجات',
                '../php/api/categories.php' => 'الفئات'
            ];
            
            $workingEndpoints = 0;
            $totalEndpoints = count($apiEndpoints);
            
            foreach ($apiEndpoints as $endpoint => $description) {
                try {
                    if (strpos($endpoint, '?') !== false) {
                        // Handle endpoints with query parameters
                        $parts = explode('?', $endpoint);
                        $file = $parts[0];
                        parse_str($parts[1], $params);
                        
                        if (file_exists($file)) {
                            ob_start();
                            foreach ($params as $key => $value) {
                                $_GET[$key] = $value;
                            }
                            include $file;
                            $output = ob_get_clean();
                        } else {
                            throw new Exception("File not found: $file");
                        }
                    } else {
                        if (file_exists($endpoint)) {
                            ob_start();
                            include $endpoint;
                            $output = ob_get_clean();
                        } else {
                            throw new Exception("File not found: $endpoint");
                        }
                    }
                    
                    $data = json_decode($output, true);
                    if ($data !== null) {
                        echo '<div class="result pass">✅ ' . $description . ': يعمل بنجاح</div>';
                        $workingEndpoints++;
                    } else {
                        echo '<div class="result fail">❌ ' . $description . ': استجابة غير صالحة</div>';
                        echo '<pre>' . htmlspecialchars(substr($output, 0, 200)) . '...</pre>';
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="result fail">❌ ' . $description . ': ' . $e->getMessage() . '</div>';
                }
            }
            
            $successRate = ($workingEndpoints / $totalEndpoints) * 100;
            echo '<div class="result info">📊 معدل النجاح: ' . $workingEndpoints . '/' . $totalEndpoints . ' (' . round($successRate, 1) . '%)</div>';
            echo '</div>';

            // Summary
            echo '<div class="fix-section">';
            echo '<h3>📋 الملخص</h3>';
            
            if ($successRate >= 80) {
                echo '<div class="result pass">🎉 معظم APIs تعمل بنجاح!</div>';
                echo '<div class="result pass">✅ مشاكل الشبكة والاتصال تم حلها</div>';
            } else {
                echo '<div class="result warning">⚠️ بعض APIs تحتاج إصلاحات إضافية</div>';
            }
            
            echo '<h4>🚀 الخطوات التالية:</h4>';
            echo '<p><a href="fix-authentication-errors.php" class="fix-button">إصلاح أخطاء المصادقة</a></p>';
            echo '<p><a href="test-all-fixes.php" class="fix-button">اختبار شامل</a></p>';
            echo '<p><a href="index.html" class="fix-button">فتح لوحة التحكم</a></p>';
            
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
        }
        ?>

    </div>

    <script>
        // Test all API endpoints via JavaScript
        async function testAllAPIs() {
            const apis = [
                { url: '../php/admin.php?action=check', name: 'المصادقة' },
                { url: '../php/api/dashboard-stats.php', name: 'إحصائيات لوحة التحكم' },
                { url: '../php/api/store-settings.php', name: 'إعدادات المتجر' },
                { url: '../php/api/notifications.php', name: 'الإشعارات' }
            ];
            
            console.log('Testing APIs via JavaScript...');
            
            for (const api of apis) {
                try {
                    const response = await fetch(api.url);
                    const data = await response.json();
                    
                    if (data.success !== false) {
                        console.log(`✅ ${api.name}: يعمل`);
                    } else {
                        console.log(`❌ ${api.name}: فشل - ${data.message}`);
                    }
                } catch (error) {
                    console.log(`❌ ${api.name}: خطأ - ${error.message}`);
                }
            }
        }
        
        // Run tests after page loads
        document.addEventListener('DOMContentLoaded', testAllAPIs);
    </script>
</body>
</html>
