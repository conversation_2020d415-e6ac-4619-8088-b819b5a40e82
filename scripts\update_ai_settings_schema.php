<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

try {
    $db = Database::getInstance();

    // Check if enabled column exists
    $result = $db->query("SHOW COLUMNS FROM ai_settings LIKE 'enabled'");
    if ($result->rowCount() === 0) {
        // Add enabled column if it doesn't exist
        $db->exec("ALTER TABLE ai_settings ADD COLUMN enabled BOOLEAN DEFAULT FALSE AFTER model");
        echo "Added enabled column to ai_settings table\n";
    }

    // Check if status_message column exists
    $result = $db->query("SHOW COLUMNS FROM ai_settings LIKE 'status_message'");
    if ($result->rowCount() === 0) {
        // Add status_message column if it doesn't exist
        $db->exec("ALTER TABLE ai_settings ADD COLUMN status_message VARCHAR(255) DEFAULT NULL AFTER enabled");
        echo "Added status_message column to ai_settings table\n";
    }

    // Update indexes
    $db->exec("ALTER TABLE ai_settings DROP INDEX IF EXISTS idx_provider");
    $db->exec("ALTER TABLE ai_settings ADD UNIQUE INDEX idx_provider (provider)");
    echo "Updated indexes on ai_settings table\n";

    // Initial data migration
    $providers = ['openai', 'anthropic', 'gemini'];
    foreach ($providers as $provider) {
        $envKey = strtoupper($provider) . '_API_KEY';
        $apiKey = Config::get($envKey);

        if ($apiKey) {
            $stmt = $db->prepare("
                INSERT INTO ai_settings (provider, api_key, model, enabled, updated_at)
                VALUES (:provider, :api_key, :model, TRUE, NOW())
                ON DUPLICATE KEY UPDATE
                api_key = VALUES(api_key),
                model = COALESCE(model, VALUES(model)),
                enabled = TRUE,
                updated_at = NOW()
            ");

            $stmt->execute([
                'provider' => $provider,
                'api_key' => $apiKey,
                'model' => Config::get(strtoupper($provider) . '_MODEL', 'default')
            ]);

            echo "Updated settings for $provider\n";
        }
    }

    echo "Migration completed successfully\n";
} catch (Exception $e) {
    echo "Error during migration: " . $e->getMessage() . "\n";
    exit(1);
}
