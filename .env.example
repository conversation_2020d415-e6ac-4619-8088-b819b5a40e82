# ===========================================
# MOSSAAB LANDING PAGE - ENVIRONMENT CONFIGURATION
# ===========================================
# Copy this file to .env and update the values according to your setup

# ===========================================
# APPLICATION SETTINGS
# ===========================================
APP_NAME="Mossaab Landing Page"
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost:8000

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
# MariaDB/MySQL Database Settings
DB_HOST=localhost
DB_PORT=3307
DB_USERNAME=root
DB_PASSWORD=
DB_DATABASE=mossab-landing-page

# ===========================================
# AI SERVICES CONFIGURATION
# ===========================================
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# Anthropic Claude API Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=1000
ANTHROPIC_TEMPERATURE=0.7

# Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=1000
GEMINI_TEMPERATURE=0.7

# ===========================================
# EMAIL CONFIGURATION
# ===========================================
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Mossaab Store"

# ===========================================
# PAYMENT CONFIGURATION
# ===========================================
# Payment Gateway Settings
PAYMENT_GATEWAY=cod
PAYMENT_CURRENCY=DZD
PAYMENT_TAX_RATE=0.19

# Cash on Delivery Settings
COD_ENABLED=true
COD_FEE=0
COD_MIN_ORDER=1000

# Bank Transfer Settings
BANK_ENABLED=false
BANK_NAME="Your Bank Name"
BANK_ACCOUNT="Your Account Number"
BANK_HOLDER="Account Holder Name"
BANK_SWIFT="SWIFT Code"

# CCP Settings
CCP_ENABLED=false
CCP_NUMBER="Your CCP Number"
CCP_KEY="Your CCP Key"
CCP_HOLDER="Account Holder Name"

# Mobile Payment Settings
MOBILE_ENABLED=false
MOBILE_NUMBER="Your Mobile Number"
MOBILE_APP=mobilis-money
MOBILE_HOLDER="Account Holder Name"

# ===========================================
# SHIPPING CONFIGURATION
# ===========================================
# Default Shipping Settings
SHIPPING_ENABLED=true
SHIPPING_DEFAULT_COST=500
SHIPPING_FREE_THRESHOLD=5000
SHIPPING_WEIGHT_LIMIT=5.0
SHIPPING_SURCHARGE_PER_KG=100

# Yalidine Express Integration
YALIDINE_ENABLED=false
YALIDINE_API_KEY=your_yalidine_api_key
YALIDINE_API_URL=https://api.yalidine.app

# ===========================================
# SECURITY SETTINGS
# ===========================================
# Session Configuration
SESSION_LIFETIME=30
SESSION_SECURE=false
SESSION_HTTP_ONLY=true

# Password Policy
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=false

# Login Security
LOGIN_MAX_ATTEMPTS=5
LOGIN_LOCKOUT_DURATION=15
LOGIN_2FA_ENABLED=false

# ===========================================
# FILE UPLOAD SETTINGS
# ===========================================
UPLOAD_MAX_SIZE=********
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,webp,pdf
UPLOAD_PATH=uploads/

# Image Optimization
IMAGE_OPTIMIZATION_ENABLED=true
IMAGE_WEBP_ENABLED=true
IMAGE_QUALITY=85
IMAGE_MAX_WIDTH=1920
IMAGE_MAX_HEIGHT=1080

# ===========================================
# CACHE CONFIGURATION
# ===========================================
CACHE_ENABLED=true
CACHE_DRIVER=file
CACHE_TTL=3600
CACHE_PATH=php/cache/

# ===========================================
# LOGGING CONFIGURATION
# ===========================================
LOG_LEVEL=error
LOG_PATH=logs/
LOG_MAX_FILES=30

# ===========================================
# SOCIAL MEDIA INTEGRATION
# ===========================================
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret

INSTAGRAM_ACCESS_TOKEN=your_instagram_access_token

# ===========================================
# ANALYTICS CONFIGURATION
# ===========================================
GOOGLE_ANALYTICS_ID=your_google_analytics_id
FACEBOOK_PIXEL_ID=your_facebook_pixel_id

# ===========================================
# BACKUP CONFIGURATION
# ===========================================
BACKUP_ENABLED=false
BACKUP_FREQUENCY=weekly
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=backups/

# ===========================================
# DEVELOPMENT SETTINGS
# ===========================================
# Only for development environment
DEV_TOOLBAR_ENABLED=false
DEV_PROFILER_ENABLED=false
DEV_MOCK_PAYMENTS=false

# ===========================================
# CUSTOM SETTINGS
# ===========================================
# Add your custom environment variables here
STORE_NAME="متجر مصعب"
STORE_DESCRIPTION="متجر إلكتروني متخصص في بيع الكتب والمنتجات التعليمية"
STORE_PHONE="+213-XXX-XXX-XXX"
STORE_EMAIL="<EMAIL>"
STORE_ADDRESS="الجزائر العاصمة، الجزائر"

# Business Hours
BUSINESS_HOURS_START=09:00
BUSINESS_HOURS_END=18:00
BUSINESS_DAYS="السبت-الخميس"

# Support Settings
SUPPORT_EMAIL="<EMAIL>"
SUPPORT_PHONE="+213-XXX-XXX-XXX"
SUPPORT_WHATSAPP="+213-XXX-XXX-XXX"

# ===========================================
# NOTES
# ===========================================
# 1. Never commit the .env file to version control
# 2. Keep sensitive information secure
# 3. Use strong passwords and API keys
# 4. Regularly rotate API keys and passwords
# 5. Enable 2FA where possible
# 6. Monitor logs for security issues
# 7. Keep backups of your configuration
# ===========================================
