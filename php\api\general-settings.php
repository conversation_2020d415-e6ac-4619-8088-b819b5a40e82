<?php

/**
 * General Settings API
 * Handles general system settings operations
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../config.php';

// Get PDO connection using the existing config
try {
    $pdo = getPDOConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'فشل في الاتصال بقاعدة البيانات: ' . $e->getMessage()
    ]);
    exit();
}

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? 'get';

    switch ($action) {
        case 'get':
            getGeneralSettings();
            break;

        case 'update':
            updateGeneralSettings();
            break;

        case 'test':
            testConnection();
            break;

        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}

/**
 * Get general settings
 */
function getGeneralSettings()
{
    global $pdo;

    try {
        // Check if settings table exists, if not create it
        $pdo->exec("CREATE TABLE IF NOT EXISTS general_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(255) UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // Insert default settings if they don't exist
        $defaultSettings = [
            ['site_name', 'Mossaab Store', 'اسم الموقع'],
            ['site_description', 'متجر إلكتروني متميز', 'وصف الموقع'],
            ['admin_email', '<EMAIL>', 'بريد المدير الإلكتروني'],
            ['timezone', 'Africa/Algiers', 'المنطقة الزمنية'],
            ['language', 'ar', 'لغة النظام'],
            ['currency', 'DZD', 'العملة'],
            ['maintenance_mode', '0', 'وضع الصيانة'],
            ['max_upload_size', '10', 'الحد الأقصى لحجم الرفع (MB)']
        ];

        foreach ($defaultSettings as $setting) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO general_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
        }

        // Get all settings
        $stmt = $pdo->query("SELECT * FROM general_settings ORDER BY setting_key");
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Convert to key-value format
        $settingsData = [];
        foreach ($settings as $setting) {
            $settingsData[$setting['setting_key']] = [
                'value' => $setting['setting_value'],
                'description' => $setting['description'],
                'updated_at' => $setting['updated_at']
            ];
        }

        echo json_encode([
            'success' => true,
            'data' => $settingsData,
            'message' => 'تم تحميل الإعدادات العامة بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل الإعدادات العامة: ' . $e->getMessage());
    }
}

/**
 * Update general settings
 */
function updateGeneralSettings()
{
    global $pdo;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['settings'])) {
            throw new Exception('بيانات غير صحيحة');
        }

        $pdo->beginTransaction();

        foreach ($input['settings'] as $key => $value) {
            $stmt = $pdo->prepare("UPDATE general_settings SET setting_value = ? WHERE setting_key = ?");
            $stmt->execute([$value, $key]);
        }

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الإعدادات العامة بنجاح'
        ]);
    } catch (Exception $e) {
        $pdo->rollBack();
        throw new Exception('فشل في تحديث الإعدادات العامة: ' . $e->getMessage());
    }
}

/**
 * Test database connection
 */
function testConnection()
{
    global $pdo;

    try {
        $stmt = $pdo->query("SELECT 1");
        echo json_encode([
            'success' => true,
            'message' => 'اتصال قاعدة البيانات ناجح',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في اختبار الاتصال: ' . $e->getMessage());
    }
}
