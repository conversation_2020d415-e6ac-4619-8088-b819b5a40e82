<?php
/**
 * Test User Management API Fix
 * Verifies that the user management API is working correctly
 */

require_once 'php/config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار إصلاح API إدارة المستخدمين</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2 { color: #333; }
        .user-card { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .json-output { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: right; border: 1px solid #ddd; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 اختبار إصلاح API إدارة المستخدمين</h1>";

try {
    $pdo = getPDOConnection();
    
    echo "<div class='section'>";
    echo "<h2>1️⃣ اختبار الاتصال بقاعدة البيانات</h2>";
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2️⃣ اختبار استعلام المستخدمين المباشر</h2>";
    
    $stmt = $pdo->prepare("
        SELECT
            u.*,
            ur.display_name_ar as role_name,
            ur.level as role_level,
            sp.display_name_ar as subscription_name,
            sp.max_products,
            sp.max_landing_pages,
            sp.max_storage_mb,
            s.store_name,
            s.store_slug
        FROM users u
        LEFT JOIN user_roles ur ON u.role_id = ur.id
        LEFT JOIN subscription_plans sp ON u.subscription_id = sp.id
        LEFT JOIN stores s ON u.store_id = s.id
        ORDER BY u.created_at DESC
    ");
    
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='info'>📊 تم العثور على " . count($users) . " مستخدم</div>";
    
    if (count($users) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد الإلكتروني</th><th>الدور</th><th>الاشتراك</th><th>المتجر</th><th>الحالة</th></tr>";
        
        foreach ($users as $user) {
            $name = trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''));
            $role = $user['role_name'] ?? 'غير محدد';
            $subscription = $user['subscription_name'] ?? 'غير محدد';
            $store = $user['store_name'] ?? 'لا يوجد';
            
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$name}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$role}</td>";
            echo "<td>{$subscription}</td>";
            echo "<td>{$store}</td>";
            echo "<td>{$user['status']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<div class='error'>❌ لم يتم العثور على أي مستخدمين</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3️⃣ اختبار API المستخدمين عبر HTTP</h2>";
    
    // Test the API endpoint
    $apiUrl = 'http://localhost:8000/php/api/users.php?action=list';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<div class='info'>🌐 رمز الاستجابة HTTP: {$httpCode}</div>";
    
    if ($error) {
        echo "<div class='error'>❌ خطأ في cURL: {$error}</div>";
    } elseif ($httpCode === 200) {
        echo "<div class='success'>✅ تم استدعاء API بنجاح</div>";
        
        $apiData = json_decode($response, true);
        
        if ($apiData && isset($apiData['success']) && $apiData['success']) {
            echo "<div class='success'>✅ API يعمل بشكل صحيح</div>";
            echo "<div class='info'>📊 عدد المستخدمين من API: " . count($apiData['users']) . "</div>";
            
            if (isset($apiData['stats'])) {
                echo "<div class='info'>";
                echo "<h4>📈 إحصائيات المستخدمين:</h4>";
                echo "<ul>";
                echo "<li>إجمالي المستخدمين: " . ($apiData['stats']['total_users'] ?? 0) . "</li>";
                echo "<li>المستخدمين النشطين: " . ($apiData['stats']['active_users'] ?? 0) . "</li>";
                echo "<li>التسجيلات الحديثة (30 يوم): " . ($apiData['stats']['recent_registrations'] ?? 0) . "</li>";
                echo "</ul>";
                echo "</div>";
            }
            
            // Show first few users
            if (!empty($apiData['users'])) {
                echo "<h4>👥 عينة من المستخدمين:</h4>";
                foreach (array_slice($apiData['users'], 0, 3) as $user) {
                    echo "<div class='user-card'>";
                    echo "<h5>{$user['name']} ({$user['email']})</h5>";
                    echo "<p><strong>الدور:</strong> {$user['role']}</p>";
                    echo "<p><strong>الاشتراك:</strong> {$user['subscription']}</p>";
                    if ($user['store']['name']) {
                        echo "<p><strong>المتجر:</strong> {$user['store']['name']}</p>";
                    }
                    echo "<p><strong>تاريخ التسجيل:</strong> {$user['registeredAt']}</p>";
                    echo "</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ API يعيد خطأ: " . ($apiData['message'] ?? 'خطأ غير معروف') . "</div>";
        }
        
        echo "<h4>📄 استجابة API الكاملة:</h4>";
        echo "<div class='json-output'>" . htmlspecialchars(json_encode($apiData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</div>";
        
    } else {
        echo "<div class='error'>❌ فشل في استدعاء API - رمز الخطأ: {$httpCode}</div>";
        echo "<div class='json-output'>" . htmlspecialchars($response) . "</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4️⃣ اختبار الجداول المطلوبة</h2>";
    
    $requiredTables = ['users', 'user_roles', 'subscription_plans', 'stores'];
    
    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$table}");
            $stmt->execute();
            $count = $stmt->fetchColumn();
            echo "<div class='success'>✅ جدول {$table}: {$count} سجل</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ جدول {$table}: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5️⃣ اختبار المستخدم التجريبي</h2>";
    
    $stmt = $pdo->prepare("
        SELECT u.*, s.store_name, ur.display_name_ar as role_name, sp.display_name_ar as subscription_name
        FROM users u
        LEFT JOIN stores s ON u.store_id = s.id
        LEFT JOIN user_roles ur ON u.role_id = ur.id
        LEFT JOIN subscription_plans sp ON u.subscription_id = sp.id
        WHERE u.email = ?
    ");
    $stmt->execute(['<EMAIL>']);
    $demoUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($demoUser) {
        echo "<div class='success'>✅ تم العثور على المستخدم التجريبي</div>";
        echo "<div class='user-card'>";
        echo "<h4>👤 معلومات المستخدم التجريبي:</h4>";
        echo "<ul>";
        echo "<li>ID: {$demoUser['id']}</li>";
        echo "<li>الاسم: {$demoUser['first_name']} {$demoUser['last_name']}</li>";
        echo "<li>البريد الإلكتروني: {$demoUser['email']}</li>";
        echo "<li>الدور: " . ($demoUser['role_name'] ?? 'غير محدد') . "</li>";
        echo "<li>الاشتراك: " . ($demoUser['subscription_name'] ?? 'غير محدد') . "</li>";
        echo "<li>المتجر: " . ($demoUser['store_name'] ?? 'لا يوجد') . "</li>";
        echo "<li>الحالة: {$demoUser['status']}</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='error'>❌ لم يتم العثور على المستخدم التجريبي</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>✅ ملخص النتائج</h2>";
    
    $allGood = true;
    $issues = [];
    
    if (count($users) === 0) {
        $allGood = false;
        $issues[] = "لا توجد مستخدمين في قاعدة البيانات";
    }
    
    if ($httpCode !== 200) {
        $allGood = false;
        $issues[] = "API لا يعمل بشكل صحيح";
    }
    
    if (!$demoUser) {
        $allGood = false;
        $issues[] = "المستخدم التجريبي غير موجود";
    }
    
    if ($allGood) {
        echo "<div class='success'>";
        echo "<h3>🎉 جميع الاختبارات نجحت!</h3>";
        echo "<p>API إدارة المستخدمين يعمل بشكل صحيح ويمكن استخدامه في لوحة التحكم الإدارية.</p>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h3>⚠️ توجد مشاكل تحتاج إلى إصلاح:</h3>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>{$issue}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div class='info'>";
    echo "<h4>🔗 روابط مفيدة:</h4>";
    echo "<ul>";
    echo "<li><a href='/admin/' target='_blank'>لوحة التحكم الإدارية</a></li>";
    echo "<li><a href='/php/api/users.php?action=list' target='_blank'>API المستخدمين</a></li>";
    echo "<li><a href='/admin/user-management.html' target='_blank'>صفحة إدارة المستخدمين</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
