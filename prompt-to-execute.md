### **Prompt: Fix Critical Errors in `landing-pages.js` and PHP Backend**

---

#### **1. Fix PHP Transaction Error in `landing-pages.php`**

**Issue**: Fatal error `PDOTransaction: There is no active transaction`.
**Steps**:

- **Ensure Transactions Are Properly Handled**:
  - Check all `beginTransaction()`, `commit()`, and `rollBack()` calls.
  - Wrap database operations in `try/catch` blocks:
    ```php
    try {
      $pdo->beginTransaction();
      // Your database queries...
      $pdo->commit();
    } catch (PDOException $e) {
      if ($pdo->inTransaction()) {
        $pdo->rollBack(); // Only rollback if transaction is active
      }
      http_response_code(500);
      echo json_encode(['success' => false, 'message' => $e->getMessage()]);
      exit;
    }
    ```
- **Return Valid JSON on Errors**:
  Replace HTML error outputs with JSON responses:
  ```php
  if (/* some error condition */) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'Page not found']);
    exit;
  }
  ```

---

#### **2. Fix JavaScript Syntax Errors (TypeScript Errors)**

**Issue**: Syntax errors in `landing-pages.js` (e.g., missing semicolons, misplaced colons).
**Steps**:

- **Address Specific Errors**:
  - **Missing Semicolons**: Add `;` at the end of lines with syntax errors (e.g., line 972, 1028).
  - **Incorrect Arrow Functions**: Fix syntax like `param => {}` (e.g., line 1584).
  - **Unclosed Braces**: Ensure all `if/else`, loops, and functions have matching `{}` (e.g., line 1791).
- **Validate Code**:
  Run `landing-pages.js` through a linter (e.g., ESLint) or fix errors one-by-one using the provided TypeScript error logs.

---

#### **3. Fix JSON Parse Errors in JavaScript**

**Issue**: PHP errors return HTML instead of JSON, causing `JSON.parse` failures.
**Steps**:

- **Update `safeApiCall()` to Handle Invalid JSON**:
  ```javascript
  async function safeApiCall(url, options = {}) {
    try {
      const response = await fetch(url, options);
      const text = await response.text();
      if (!text.trim()) {
        throw new Error("Empty response");
      }
      // Validate JSON format before parsing
      if (
        !/^[\],:{}\s\d\-+.eE]+$/g.test(text.replace(/\\["\\\/bfnrtu]/g, "@"))
      ) {
        throw new Error("Invalid JSON structure");
      }
      return JSON.parse(text);
    } catch (error) {
      console.error("API Error:", error);
      throw error;
    }
  }
  ```

---

#### **4. Fix TinyMCE Initialization Issues**

**Issue**: `editors is undefined` or `tinymce.get()` returns `null`.
**Steps**:

- **Ensure TinyMCE is Initialized Before Access**:
  Add explicit initialization in `landingPagesManager.init()`:
  ```javascript
  tinymce.init({
    selector: "#rightContent, #leftContent, #storeAddress, #productDescription",
    directionality: "rtl",
    // ... other config ...
  });
  ```
- **Access Editors Safely**:
  ```javascript
  const editor = tinymce.get("rightContent");
  if (editor) {
    editor.setContent(pageData.contenu_droit);
  } else {
    console.error("TinyMCE editor not initialized");
  }
  ```

---

#### **5. Fix `rangeCount` and `mozInputSource` Warnings**

**Issue**: `TypeError: can't access property "rangeCount"` and deprecated `mozInputSource`.
**Steps**:

- **Null Checks for Selection**:
  ```javascript
  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) {
    return; // Exit early if no selection
  }
  ```
- **Replace `mozInputSource` with `pointerType`**:
  Update event handlers in `contextmenuhlpr.js`:
  ```javascript
  if (e.pointerType === "mouse") {
    // Handle mouse events
  }
  ```

---

#### **6. Fix CSS Writing Mode Warnings**

**Issue**: CSS properties applied to `<html>` instead of `:root`.
**Steps**:

- **Update CSS**:
  ```css
  :root {
    writing-mode: horizontal-tb;
    direction: rtl;
    text-orientation: mixed;
  }
  ```
- **Remove Redundant Styles**: Delete duplicate declarations from `<html>`.

---

#### **7. Fix 404 Image Error (`book3.svg`)**

**Issue**: Missing image file.
**Steps**:

- **Verify Image Path**: Ensure `../images/book3.svg` exists.
- **Fallback Image**: Add a default image in the front-end:
  ```javascript
  document.querySelector(".book-image").src = "/default-book.png";
  ```

---

#### **8. Cleanup and Testing**

- **Test Form Submissions**:
  - Create, edit, and delete landing pages to ensure PHP and JS handle transactions properly.
- **Validate JSON Responses**: Use Postman to test `../php/api/landing-pages.php`.
- **Check Console Logs**: Ensure no errors after fixes.

---

### **Deliverables**

1. **Patched `landing-pages.php`**: Fix transaction logic and return valid JSON.
2. **Updated `landing-pages.js`**: Fix syntax errors, TinyMCE initialization, and JSON handling.
3. **CSS Fixes**: Update styles to resolve writing mode warnings.
4. **Documentation**: Add comments explaining critical fixes.

Let me know if you need specific code snippets for any of these steps!
