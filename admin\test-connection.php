<?php
/**
 * Simple Database Connection Test
 * Tests database connection with current configuration
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار اتصال قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .fix-button:hover {
            background: #0056b3;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 اختبار اتصال قاعدة البيانات</h1>
        <p>اختبار سريع لاتصال قاعدة البيانات باستخدام التكوين الحالي.</p>

        <?php
        try {
            // Load environment variables manually
            $envFile = dirname(__DIR__) . '/.env';
            if (!file_exists($envFile)) {
                throw new Exception('ملف .env غير موجود');
            }

            $envLines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $envVars = [];
            foreach ($envLines as $line) {
                if (strpos(trim($line), '#') === 0) continue;
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $envVars[trim($key)] = trim($value);
                }
            }

            $host = $envVars['DB_HOST'] ?? 'localhost';
            $port = $envVars['DB_PORT'] ?? '3306';
            $database = $envVars['DB_DATABASE'] ?? '';
            $username = $envVars['DB_USERNAME'] ?? '';
            $password = $envVars['DB_PASSWORD'] ?? '';
            $charset = $envVars['DB_CHARSET'] ?? 'utf8mb4';

            echo '<div class="result info">📋 معلومات الاتصال:</div>';
            echo '<div class="result info">🖥️ الخادم: ' . $host . ':' . $port . '</div>';
            echo '<div class="result info">🗄️ قاعدة البيانات: ' . $database . '</div>';
            echo '<div class="result info">👤 المستخدم: ' . $username . '</div>';

            // Test 1: Check if host is reachable
            echo '<h3>🔍 اختبار الوصول للخادم</h3>';
            $connection = @fsockopen($host, $port, $errno, $errstr, 5);
            if ($connection) {
                echo '<div class="result pass">✅ الخادم متاح على المنفذ ' . $port . '</div>';
                fclose($connection);
            } else {
                echo '<div class="result fail">❌ لا يمكن الوصول للخادم: ' . $errstr . ' (كود: ' . $errno . ')</div>';
                echo '<div class="result info">💡 تأكد من تشغيل خدمة MySQL/MariaDB</div>';
                throw new Exception('الخادم غير متاح');
            }

            // Test 2: Try PDO connection without database
            echo '<h3>🔌 اختبار اتصال PDO</h3>';
            $dsn = "mysql:host=$host;port=$port;charset=$charset";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]);
            echo '<div class="result pass">✅ اتصال PDO ناجح</div>';

            // Test 3: Check if database exists
            echo '<h3>🗄️ فحص قاعدة البيانات</h3>';
            $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
            if ($stmt->rowCount() > 0) {
                echo '<div class="result pass">✅ قاعدة البيانات ' . $database . ' موجودة</div>';
                
                // Test 4: Connect to specific database
                $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=$charset";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]);
                echo '<div class="result pass">✅ اتصال ناجح بقاعدة البيانات ' . $database . '</div>';

                // Test 5: Check tables
                echo '<h3>📊 فحص الجداول</h3>';
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo '<div class="result info">📋 عدد الجداول: ' . count($tables) . '</div>';

                if (count($tables) > 0) {
                    $requiredTables = ['categories', 'produits', 'ai_settings', 'payment_settings'];
                    $missingTables = [];
                    
                    foreach ($requiredTables as $table) {
                        if (in_array($table, $tables)) {
                            echo '<div class="result pass">✅ جدول ' . $table . ' موجود</div>';
                        } else {
                            echo '<div class="result fail">❌ جدول ' . $table . ' مفقود</div>';
                            $missingTables[] = $table;
                        }
                    }
                    
                    if (!empty($missingTables)) {
                        echo '<div class="result info">💡 يمكن إنشاء الجداول المفقودة باستخدام سكريبت الإصلاح</div>';
                    }
                } else {
                    echo '<div class="result fail">❌ لا توجد جداول في قاعدة البيانات</div>';
                    echo '<div class="result info">💡 يجب تشغيل سكريبت إنشاء الجداول</div>';
                }

                // Test 6: Test basic operations
                echo '<h3>🧪 اختبار العمليات الأساسية</h3>';
                try {
                    // Test query
                    $stmt = $pdo->query("SELECT DATABASE() as current_db, VERSION() as mysql_version");
                    $result = $stmt->fetch();
                    echo '<div class="result pass">✅ استعلام ناجح</div>';
                    echo '<div class="result info">📋 قاعدة البيانات الحالية: ' . $result['current_db'] . '</div>';
                    echo '<div class="result info">📋 إصدار MySQL: ' . $result['mysql_version'] . '</div>';

                    // Test if we can create a temporary table
                    $pdo->exec("CREATE TEMPORARY TABLE test_temp (id INT)");
                    echo '<div class="result pass">✅ إنشاء جدول مؤقت ناجح</div>';

                    echo '<h3>🎉 جميع الاختبارات نجحت!</h3>';
                    echo '<div class="result pass">✅ قاعدة البيانات جاهزة للاستخدام</div>';
                    echo '<div class="result pass">✅ يمكنك الآن تشغيل لوحة التحكم</div>';

                } catch (PDOException $e) {
                    echo '<div class="result fail">❌ فشل في اختبار العمليات: ' . $e->getMessage() . '</div>';
                }

            } else {
                echo '<div class="result fail">❌ قاعدة البيانات ' . $database . ' غير موجودة</div>';
                echo '<div class="result info">💡 يمكن إنشاؤها باستخدام سكريبت الإنشاء</div>';
            }

        } catch (PDOException $e) {
            echo '<div class="result fail">❌ فشل في الاتصال: ' . $e->getMessage() . '</div>';
            
            $errorMessage = $e->getMessage();
            if (strpos($errorMessage, 'Connection refused') !== false) {
                echo '<div class="result info">💡 خدمة MySQL غير متاحة. تحقق من تشغيل MySQL/MariaDB</div>';
            } elseif (strpos($errorMessage, 'Access denied') !== false) {
                echo '<div class="result info">💡 مشكلة في اسم المستخدم أو كلمة المرور</div>';
            } elseif (strpos($errorMessage, 'Unknown database') !== false) {
                echo '<div class="result info">💡 قاعدة البيانات غير موجودة</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="result fail">❌ خطأ عام: ' . $e->getMessage() . '</div>';
        }
        ?>

        <h3>🛠️ الخطوات التالية</h3>
        <a href="diagnose-database.php" class="fix-button">تشخيص مفصل</a>
        <a href="create-database.php" class="fix-button">إنشاء قاعدة البيانات</a>
        <a href="manual-sql-commands.php" class="fix-button">أوامر SQL اليدوية</a>
        <a href="index.html" class="fix-button">العودة إلى لوحة التحكم</a>

    </div>
</body>
</html>
