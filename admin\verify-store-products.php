<?php
require_once __DIR__ . '/../php/config.php';

try {
    $pdo = getPDOConnection();
    
    echo "🔍 VERIFYING STORE PRODUCTS DISPLAY\n";
    echo "=" . str_repeat("=", 40) . "\n\n";
    
    // Test store page accessibility
    echo "📋 Testing Store Page Accessibility:\n";
    echo "-" . str_repeat("-", 35) . "\n";
    
    $storeUrl = "http://localhost:8000/store/mossaab-store";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $storeUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ cURL Error: {$error}\n";
    } else {
        echo "✅ Store page HTTP status: {$httpCode}\n";
        
        if ($httpCode === 200) {
            echo "✅ Store page is accessible\n";
            
            // Check for specific products in the response
            $productChecks = [
                'كتاب الطبخ الجزائري الأصيل' => 'Algerian Cookbook',
                'Samsung Galaxy A54 5G' => 'Samsung Phone',
                'حقيبة ظهر جلدية فاخرة' => 'Leather Backpack',
                'قندورة رجالية تقليدية' => 'Traditional Qandoura',
                'مصباح LED ذكي' => 'Smart LED Bulb'
            ];
            
            echo "\n📦 Checking for specific products:\n";
            foreach ($productChecks as $productName => $description) {
                if (strpos($response, $productName) !== false) {
                    echo "   ✅ {$description}: Found\n";
                } else {
                    echo "   ❌ {$description}: Not found\n";
                }
            }
            
            // Check for store name
            if (strpos($response, 'متجر مصعب') !== false) {
                echo "   ✅ Store name displayed correctly\n";
            } else {
                echo "   ❌ Store name not found\n";
            }
            
        } else {
            echo "❌ Store page returned HTTP {$httpCode}\n";
        }
    }
    
    echo "\n📊 Database Verification:\n";
    echo "-" . str_repeat("-", 25) . "\n";
    
    // Check products in database
    $stmt = $pdo->prepare("
        SELECT 
            p.id, p.titre, p.prix, p.type, p.stock, p.actif,
            p.store_id
        FROM produits p
        WHERE p.store_id = 1 AND p.actif = 1
        ORDER BY p.created_at DESC
    ");
    $stmt->execute();
    $storeProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✅ Products in database for store ID 1: " . count($storeProducts) . "\n\n";
    
    echo "📋 Product List:\n";
    foreach ($storeProducts as $index => $product) {
        echo "   " . ($index + 1) . ". {$product['titre']}\n";
        echo "      Type: {$product['type']} | Price: {$product['prix']} DZD | Stock: {$product['stock']}\n";
        echo "      Store ID: {$product['store_id']} | Active: {$product['actif']}\n\n";
    }
    
    echo "🎯 STORE VERIFICATION SUMMARY:\n";
    echo "=" . str_repeat("=", 40) . "\n";
    
    if ($httpCode === 200 && count($storeProducts) === 10) {
        echo "✅ Store page is accessible\n";
        echo "✅ All 10 products are in database\n";
        echo "✅ Products are properly linked to store (store_id = 1)\n";
        echo "✅ All products are active (actif = 1)\n\n";
        
        echo "🔗 Store URL: {$storeUrl}\n";
        echo "📦 Products: " . count($storeProducts) . " items\n";
        echo "💰 Price range: 2,500 - 75,000 DZD\n";
        echo "🏷️ Categories: Books, Electronics, Bags, Clothing, Home\n\n";
        
        echo "✅ READY FOR USER PANEL IMPLEMENTATION!\n";
    } else {
        echo "⚠️ Issues detected:\n";
        if ($httpCode !== 200) {
            echo "   - Store page not accessible (HTTP {$httpCode})\n";
        }
        if (count($storeProducts) !== 10) {
            echo "   - Expected 10 products, found " . count($storeProducts) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
