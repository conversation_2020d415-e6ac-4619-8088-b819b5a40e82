<?php
// Security check
if (!defined('SECURITY_CHECK')) {
    die('Direct access not allowed');
}

require_once __DIR__ . '/../config/ai.php';
require_once __DIR__ . '/../config/config.php';

// Legacy AIConfig class - maintains backward compatibility with existing code
class AIConfig
{
    private static function getManager()
    {
        static $instance = null;
        if ($instance === null) {
            $instance = \AIManager::getInstance();
        }
        return $instance;
    }

    public static function getInstance()
    {
        return self::getManager();
    }

    public static function getProvider($provider)
    {
        $manager = self::getManager();
        return [
            'enabled' => $manager->isProviderEnabled($provider),
            'api_key' => $manager->getApiKey($provider),
            'status' => $manager->getProviderStatus($provider)
        ];
    }

    public static function isAvailable()
    {
        $status = self::getManager()->getProviderStatus();
        return !empty(array_filter($status, function ($s) {
            return $s['enabled'] ?? false;
        }));
    }

    public static function getEnabledProviders()
    {
        $status = self::getManager()->getProviderStatus();
        return array_keys(array_filter($status, function ($s) {
            return $s['enabled'] ?? false;
        }));
    }

    public static function getDefaultProvider()
    {
        $manager = self::getManager();
        foreach (['openai', 'anthropic', 'gemini'] as $provider) {
            if ($manager->isProviderEnabled($provider)) {
                return $provider;
            }
        }
        return null;
    }

    public static function getPrompt($type)
    {
        $templates = [
            'product_description' => "قم بإنشاء وصف مقنع للمنتج التالي:\n\n{product}\n\nContext: {context}",
            'landing_page_title' => "قم بإنشاء عنوان مقنع لصفحة هبوط المنتج التالي:\n\n{product}\n\nContext: {context}",
            'landing_page_content' => "قم بإنشاء محتوى جذاب لصفحة هبوط المنتج التالي:\n\n{product}\n\nContext: {context}",
            'meta_description' => "قم بإنشاء وصف تعريفي SEO للمنتج التالي:\n\n{product}\n\nContext: {context}"
        ];
        return $templates[$type] ?? '';
    }

    public static function refreshFromEnv()
    {
        // Force config reload and reload configuration
        Config::get('APP_ENV', null, true); // Force config reload
        return self::getManager()->getProviderStatus();
    }

    public static function save()
    {
        return true;
    }
    public static function get($key, $default = null)
    {
        return Config::get('AI_' . strtoupper($key), $default);
    }
    public static function set($key, $value)
    {
        return false;
    }
}
