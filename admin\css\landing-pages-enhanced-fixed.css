/* Enhanced Landing Pages UI Fixes */

/* Ensure landing pages section is always visible when active */
.landing-pages-section,
#landingPagesContent,
[data-section="landing-pages"] {
    display: block !important;
    visibility: visible !important;
}

/* Ensure add button is always visible */
#addLandingPageBtn,
.add-landing-page-btn,
[data-action="add-landing-page"] {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Fix for hidden content sections */
.content-section {
    min-height: 1px;
}

.content-section.active {
    display: block !important;
    visibility: visible !important;
}

/* Modal fixes */
.modal {
    z-index: 1050 !important;
}

.modal.show {
    display: block !important;
}

.modal-backdrop {
    z-index: 1040 !important;
}

/* Button styling improvements */
.btn {
    cursor: pointer;
    user-select: none;
}

.btn:hover {
    opacity: 0.9;
}

/* Landing pages table improvements */
.landing-pages-table {
    width: 100%;
    margin-top: 20px;
}

/* Form improvements */
.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Arabic RTL improvements */
[dir="rtl"] .modal-header .close {
    margin-left: auto;
    margin-right: -1rem;
}

[dir="rtl"] .form-group label {
    text-align: right;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
}

/* Debug styles for development */
.debug-visible {
    border: 2px solid red !important;
    background-color: rgba(255, 0, 0, 0.1) !important;
}

/* Force visibility for troubleshooting */
.force-visible {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    width: auto !important;
}