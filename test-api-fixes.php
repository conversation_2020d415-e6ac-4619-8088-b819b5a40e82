<?php
// Test API fixes for database errors
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Fixes</title>
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test API Fixes</h1>
        <p>اختبار إصلاحات قاعدة البيانات والـ APIs</p>

        <div class="test-section">
            <h2>اختبارات الـ APIs</h2>
            <button class="test-button" onclick="testStoreSettings()">
                1. اختبار Store Settings API
            </button>
            <button class="test-button" onclick="testLandingPagesGet()">
                2. اختبار Landing Pages GET
            </button>
            <button class="test-button" onclick="testProductsAPI()">
                3. اختبار Products API
            </button>
            <button class="test-button" onclick="testDatabaseConnection()">
                4. اختبار اتصال قاعدة البيانات
            </button>
        </div>

        <div class="test-section">
            <h2>اختبار شامل</h2>
            <button class="test-button" onclick="runAllTests()">
                🚀 تشغيل جميع الاختبارات
            </button>
            <button class="test-button" onclick="clearResults()">
                🧹 مسح النتائج
            </button>
        </div>

        <div id="test-results"></div>
        <div id="console-output"></div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function logToConsole(message) {
            const output = document.getElementById('console-output');
            output.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        async function testStoreSettings() {
            addTestResult('🧪 اختبار Store Settings API...', 'info');
            logToConsole('Testing Store Settings API...');
            
            try {
                const response = await fetch('php/api/store-settings.php');
                logToConsole(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const text = await response.text();
                logToConsole(`Response text: ${text.substring(0, 200)}...`);
                
                const data = JSON.parse(text);
                logToConsole(`Parsed data: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    addTestResult('✅ Store Settings API يعمل بشكل صحيح', 'success');
                    logToConsole('Store Settings API test passed');
                } else {
                    addTestResult('❌ Store Settings API فشل: ' + (data.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في Store Settings API: ' + error.message, 'error');
                logToConsole(`Store Settings API error: ${error.message}`);
            }
        }

        async function testLandingPagesGet() {
            addTestResult('🧪 اختبار Landing Pages GET API...', 'info');
            logToConsole('Testing Landing Pages GET API...');
            
            try {
                const response = await fetch('php/api/landing-pages.php');
                logToConsole(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const text = await response.text();
                logToConsole(`Response text: ${text.substring(0, 200)}...`);
                
                const data = JSON.parse(text);
                logToConsole(`Parsed data: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    addTestResult('✅ Landing Pages GET API يعمل بشكل صحيح', 'success');
                    logToConsole('Landing Pages GET API test passed');
                } else {
                    addTestResult('❌ Landing Pages GET API فشل: ' + (data.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في Landing Pages GET API: ' + error.message, 'error');
                logToConsole(`Landing Pages GET API error: ${error.message}`);
            }
        }

        async function testProductsAPI() {
            addTestResult('🧪 اختبار Products API...', 'info');
            logToConsole('Testing Products API...');
            
            try {
                const response = await fetch('php/api/products.php');
                logToConsole(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const text = await response.text();
                logToConsole(`Response text: ${text.substring(0, 200)}...`);
                
                const data = JSON.parse(text);
                logToConsole(`Products found: ${Array.isArray(data) ? data.length : 'Not an array'}`);
                
                if (Array.isArray(data) && data.length > 0) {
                    addTestResult('✅ Products API يعمل بشكل صحيح', 'success');
                    logToConsole('Products API test passed');
                } else {
                    addTestResult('⚠️ Products API يعمل لكن لا توجد منتجات', 'info');
                }
            } catch (error) {
                addTestResult('❌ خطأ في Products API: ' + error.message, 'error');
                logToConsole(`Products API error: ${error.message}`);
            }
        }

        async function testDatabaseConnection() {
            addTestResult('🧪 اختبار اتصال قاعدة البيانات...', 'info');
            logToConsole('Testing database connection...');
            
            try {
                const response = await fetch('test-db-connection.php');
                logToConsole(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const text = await response.text();
                logToConsole(`Database test result: ${text.substring(0, 500)}...`);
                
                if (text.includes('✅ Connexion réussie')) {
                    addTestResult('✅ اتصال قاعدة البيانات يعمل بشكل صحيح', 'success');
                    logToConsole('Database connection test passed');
                } else {
                    addTestResult('❌ مشكلة في اتصال قاعدة البيانات', 'error');
                }
            } catch (error) {
                addTestResult('❌ خطأ في اختبار قاعدة البيانات: ' + error.message, 'error');
                logToConsole(`Database connection error: ${error.message}`);
            }
        }

        async function runAllTests() {
            clearResults();
            addTestResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            logToConsole('Starting all tests...');
            
            await testDatabaseConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testStoreSettings();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testProductsAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testLandingPagesGet();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            addTestResult('🎉 انتهاء جميع الاختبارات', 'info');
            logToConsole('All tests completed');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('console-output').textContent = '';
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('📋 تشغيل اختبار أساسي تلقائياً...', 'info');
                testDatabaseConnection();
            }, 1000);
        });
    </script>
</body>
</html>
