<?php

/**
 * Debug script to check product database and API issues
 */

require_once 'php/config.php';

echo "<h1>🔍 Product Management Debug Report</h1>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>\n";

try {
    $pdo = getPDOConnection();
    echo "<h2>✅ Database Connection: SUCCESS</h2>\n";

    // Check if produits table exists
    echo "<h2>📋 Table Structure Check</h2>\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'produits'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ Table 'produits' exists</p>\n";

        // Show table structure
        $stmt = $pdo->query("DESCRIBE produits");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<h3>Table Structure:</h3>\n";
        echo "<pre>";
        foreach ($columns as $column) {
            echo sprintf(
                "%-20s %-20s %-10s %-10s\n",
                $column['Field'],
                $column['Type'],
                $column['Null'],
                $column['Key']
            );
        }
        echo "</pre>\n";
    } else {
        echo "<p class='error'>❌ Table 'produits' does not exist!</p>\n";
        exit;
    }

    // Check total products count
    echo "<h2>📊 Products Count</h2>\n";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM produits");
    $total = $stmt->fetch()['total'];
    echo "<p>Total products in database: <strong>$total</strong></p>\n";

    // Check active products count
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM produits WHERE actif = 1");
    $active = $stmt->fetch()['active'];
    echo "<p>Active products: <strong>$active</strong></p>\n";

    // Show all products
    echo "<h2>📦 All Products in Database</h2>\n";
    $stmt = $pdo->query("SELECT id, type, titre, prix, stock, actif, created_at FROM produits ORDER BY id DESC");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($products)) {
        echo "<p class='warning'>⚠️ No products found in database!</p>\n";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse:collapse;width:100%;'>\n";
        echo "<tr style='background:#f0f0f0;'><th>ID</th><th>Type</th><th>Title</th><th>Price</th><th>Stock</th><th>Active</th><th>Created</th></tr>\n";
        foreach ($products as $product) {
            $activeStatus = $product['actif'] ? '✅ Yes' : '❌ No';
            $rowClass = $product['actif'] ? 'success' : 'error';
            echo "<tr class='$rowClass'>";
            echo "<td>{$product['id']}</td>";
            echo "<td>{$product['type']}</td>";
            echo "<td>" . htmlspecialchars($product['titre']) . "</td>";
            echo "<td>{$product['prix']} DZD</td>";
            echo "<td>{$product['stock']}</td>";
            echo "<td>$activeStatus</td>";
            echo "<td>" . ($product['created_at'] ? date('Y-m-d', strtotime($product['created_at'])) : 'N/A') . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }

    // Test the products API endpoint
    echo "<h2>🔌 API Endpoint Test</h2>\n";

    // Simulate API call
    $_SERVER['REQUEST_METHOD'] = 'GET';
    ob_start();

    try {
        include 'php/api/products.php';
        $apiResponse = ob_get_clean();

        echo "<h3>API Response:</h3>\n";
        echo "<pre>" . htmlspecialchars($apiResponse) . "</pre>\n";

        // Try to parse JSON
        $jsonData = json_decode($apiResponse, true);
        if ($jsonData) {
            echo "<p class='success'>✅ API returns valid JSON</p>\n";
            if (isset($jsonData['success']) && $jsonData['success']) {
                echo "<p class='success'>✅ API success flag is true</p>\n";
                if (isset($jsonData['products'])) {
                    $apiProductCount = count($jsonData['products']);
                    echo "<p>API returned <strong>$apiProductCount</strong> products</p>\n";
                } else {
                    echo "<p class='warning'>⚠️ API response missing 'products' field</p>\n";
                }
            } else {
                echo "<p class='error'>❌ API success flag is false</p>\n";
            }
        } else {
            echo "<p class='error'>❌ API response is not valid JSON</p>\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p class='error'>❌ API Error: " . $e->getMessage() . "</p>\n";
    }

    // Check admin authentication
    echo "<h2>🔐 Authentication Check</h2>\n";
    session_start();
    if (isset($_SESSION['admin_id'])) {
        echo "<p class='success'>✅ Admin session active (ID: {$_SESSION['admin_id']})</p>\n";
    } else {
        echo "<p class='warning'>⚠️ No admin session found</p>\n";
        echo "<p>This might affect API responses that require authentication.</p>\n";
    }

    // Recommendations
    echo "<h2>💡 Recommendations</h2>\n";
    if ($total == 0) {
        echo "<p class='warning'>🔧 Run the product creation script: <a href='create_test_products.php'>create_test_products.php</a></p>\n";
    }

    if ($active < $total) {
        echo "<p class='warning'>🔧 Some products are inactive. Check the 'actif' field values.</p>\n";
    }

    echo "<p>🔧 Check the admin panel: <a href='admin/'>Admin Panel</a></p>\n";
    echo "<p>🔧 Test the products API directly: <a href='php/api/products.php'>Products API</a></p>\n";
} catch (Exception $e) {
    echo "<h2 class='error'>❌ Database Error</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
}
?>

<script>
    console.log('🔍 Product Debug Report Generated');
</script>
