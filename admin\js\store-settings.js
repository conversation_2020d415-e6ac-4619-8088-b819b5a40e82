/**
 * Store Settings Management
 * Handles comprehensive store configuration including branding, social media, and business details
 */

let storeSettingsData = {};

/**
 * Load store settings content
 */
async function loadStoreSettingsContent() {
    console.log('Loading store settings content...');
    
    const container = document.getElementById('storeSettingsContent');
    if (!container) {
        console.error('Store settings container not found');
        return;
    }

    try {
        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إعدادات المتجر...</p>
            </div>
        `;

        // Load current store settings
        await loadStoreSettingsData();

        // Render the store settings interface
        renderStoreSettingsInterface();

        console.log('Store settings content loaded successfully');
    } catch (error) {
        console.error('Error loading store settings:', error);
        container.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <p>خطأ في تحميل إعدادات المتجر</p>
                <button onclick="loadStoreSettingsContent()" class="retry-btn">إعادة المحاولة</button>
            </div>
        `;
    }
}

/**
 * Load store settings data from API
 */
async function loadStoreSettingsData() {
    try {
        const response = await fetch('../php/api/store-settings.php?action=get');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            storeSettingsData = data.data || {};
        } else {
            throw new Error(data.message || 'Failed to load store settings');
        }
        
    } catch (error) {
        console.error('Error loading store settings data:', error);
        // Initialize with default values
        storeSettingsData = {
            store_name: '',
            store_description: '',
            store_logo: '',
            store_phone: '',
            store_email: '',
            store_address: '',
            store_city: '',
            store_country: 'الجزائر',
            facebook_url: '',
            instagram_url: '',
            twitter_url: '',
            youtube_url: '',
            whatsapp_number: '',
            business_hours: '',
            currency: 'دج',
            tax_rate: '0',
            shipping_cost: '0',
            free_shipping_threshold: '0'
        };
    }
}

/**
 * Render store settings interface
 */
function renderStoreSettingsInterface() {
    const container = document.getElementById('storeSettingsContent');
    
    container.innerHTML = `
        <div class="store-settings-container">
            <div class="settings-header">
                <h2><i class="fas fa-store"></i> إعدادات المتجر</h2>
                <p>قم بتكوين معلومات متجرك الأساسية والإعدادات التجارية</p>
            </div>

            <form id="storeSettingsForm" class="settings-form">
                <!-- Store Information Section -->
                <div class="settings-section">
                    <h3><i class="fas fa-info-circle"></i> معلومات المتجر الأساسية</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="storeName">اسم المتجر *</label>
                            <input type="text" id="storeName" name="store_name" value="${storeSettingsData.store_name || ''}" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="storePhone">رقم الهاتف</label>
                            <input type="tel" id="storePhone" name="store_phone" value="${storeSettingsData.store_phone || ''}">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="storeEmail">البريد الإلكتروني</label>
                            <input type="email" id="storeEmail" name="store_email" value="${storeSettingsData.store_email || ''}">
                        </div>
                        
                        <div class="form-group">
                            <label for="storeCity">المدينة</label>
                            <input type="text" id="storeCity" name="store_city" value="${storeSettingsData.store_city || ''}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="storeDescription">وصف المتجر</label>
                        <textarea id="storeDescription" name="store_description" rows="3">${storeSettingsData.store_description || ''}</textarea>
                    </div>

                    <div class="form-group">
                        <label for="storeAddress">عنوان المتجر</label>
                        <textarea id="storeAddress" name="store_address" rows="2">${storeSettingsData.store_address || ''}</textarea>
                    </div>
                </div>

                <!-- Branding Section -->
                <div class="settings-section">
                    <h3><i class="fas fa-palette"></i> العلامة التجارية</h3>
                    
                    <div class="form-group">
                        <label for="storeLogo">شعار المتجر</label>
                        <div class="logo-upload-container">
                            <input type="file" id="storeLogo" name="store_logo" accept="image/*" style="display: none;">
                            <div class="logo-preview" id="logoPreview">
                                ${storeSettingsData.store_logo ? 
                                    `<img src="${storeSettingsData.store_logo}" alt="شعار المتجر">` : 
                                    '<i class="fas fa-image"></i><span>لا يوجد شعار</span>'
                                }
                            </div>
                            <button type="button" class="upload-btn" onclick="document.getElementById('storeLogo').click()">
                                <i class="fas fa-upload"></i> رفع شعار
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Social Media Section -->
                <div class="settings-section">
                    <h3><i class="fas fa-share-alt"></i> وسائل التواصل الاجتماعي</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="facebookUrl"><i class="fab fa-facebook"></i> فيسبوك</label>
                            <input type="url" id="facebookUrl" name="facebook_url" value="${storeSettingsData.facebook_url || ''}" placeholder="https://facebook.com/yourstore">
                        </div>
                        
                        <div class="form-group">
                            <label for="instagramUrl"><i class="fab fa-instagram"></i> إنستغرام</label>
                            <input type="url" id="instagramUrl" name="instagram_url" value="${storeSettingsData.instagram_url || ''}" placeholder="https://instagram.com/yourstore">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="twitterUrl"><i class="fab fa-twitter"></i> تويتر</label>
                            <input type="url" id="twitterUrl" name="twitter_url" value="${storeSettingsData.twitter_url || ''}" placeholder="https://twitter.com/yourstore">
                        </div>
                        
                        <div class="form-group">
                            <label for="youtubeUrl"><i class="fab fa-youtube"></i> يوتيوب</label>
                            <input type="url" id="youtubeUrl" name="youtube_url" value="${storeSettingsData.youtube_url || ''}" placeholder="https://youtube.com/yourchannel">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="whatsappNumber"><i class="fab fa-whatsapp"></i> رقم الواتساب</label>
                        <input type="tel" id="whatsappNumber" name="whatsapp_number" value="${storeSettingsData.whatsapp_number || ''}" placeholder="+213xxxxxxxxx">
                    </div>
                </div>

                <!-- Business Settings Section -->
                <div class="settings-section">
                    <h3><i class="fas fa-business-time"></i> الإعدادات التجارية</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="currency">العملة</label>
                            <select id="currency" name="currency">
                                <option value="دج" ${storeSettingsData.currency === 'دج' ? 'selected' : ''}>دينار جزائري (دج)</option>
                                <option value="$" ${storeSettingsData.currency === '$' ? 'selected' : ''}>دولار أمريكي ($)</option>
                                <option value="€" ${storeSettingsData.currency === '€' ? 'selected' : ''}>يورو (€)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="taxRate">معدل الضريبة (%)</label>
                            <input type="number" id="taxRate" name="tax_rate" value="${storeSettingsData.tax_rate || '0'}" min="0" max="100" step="0.01">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="shippingCost">تكلفة الشحن الافتراضية</label>
                            <input type="number" id="shippingCost" name="shipping_cost" value="${storeSettingsData.shipping_cost || '0'}" min="0" step="0.01">
                        </div>
                        
                        <div class="form-group">
                            <label for="freeShippingThreshold">حد الشحن المجاني</label>
                            <input type="number" id="freeShippingThreshold" name="free_shipping_threshold" value="${storeSettingsData.free_shipping_threshold || '0'}" min="0" step="0.01">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="businessHours">ساعات العمل</label>
                        <textarea id="businessHours" name="business_hours" rows="3" placeholder="السبت - الخميس: 9:00 ص - 6:00 م">${storeSettingsData.business_hours || ''}</textarea>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="form-actions">
                    <button type="submit" class="save-btn">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                    <button type="button" class="reset-btn" onclick="resetStoreSettings()">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </form>
        </div>
    `;

    // Initialize form handlers
    initStoreSettingsHandlers();
}

/**
 * Initialize store settings form handlers
 */
function initStoreSettingsHandlers() {
    const form = document.getElementById('storeSettingsForm');
    if (!form) return;

    // Form submission handler
    form.addEventListener('submit', handleStoreSettingsSubmit);

    // Logo upload handler
    const logoInput = document.getElementById('storeLogo');
    if (logoInput) {
        logoInput.addEventListener('change', handleLogoUpload);
    }
}

/**
 * Handle store settings form submission
 */
async function handleStoreSettingsSubmit(e) {
    e.preventDefault();
    
    const submitBtn = e.target.querySelector('.save-btn');
    const originalText = submitBtn.innerHTML;
    
    try {
        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        submitBtn.disabled = true;

        const formData = new FormData(e.target);
        
        const response = await fetch('../php/api/store-settings.php', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            showNotification('تم حفظ إعدادات المتجر بنجاح', 'success');
            // Update local data
            storeSettingsData = { ...storeSettingsData, ...result.data };
        } else {
            throw new Error(result.message || 'فشل في حفظ الإعدادات');
        }

    } catch (error) {
        console.error('Error saving store settings:', error);
        showNotification('خطأ في حفظ إعدادات المتجر: ' + error.message, 'error');
    } finally {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

/**
 * Handle logo upload
 */
function handleLogoUpload(e) {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        showNotification('يرجى اختيار ملف صورة صالح', 'error');
        return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
        showNotification('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت', 'error');
        return;
    }

    // Preview the image
    const reader = new FileReader();
    reader.onload = function(e) {
        const preview = document.getElementById('logoPreview');
        preview.innerHTML = `<img src="${e.target.result}" alt="شعار المتجر">`;
    };
    reader.readAsDataURL(file);
}

/**
 * Reset store settings form
 */
function resetStoreSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        renderStoreSettingsInterface();
        showNotification('تم إعادة تعيين الإعدادات', 'info');
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Use the global notification system if available
    if (typeof notificationManager !== 'undefined') {
        switch(type) {
            case 'success':
                notificationManager.showSuccess(message);
                break;
            case 'error':
                notificationManager.showError(message);
                break;
            default:
                notificationManager.showInfo(message);
        }
    } else {
        // Fallback notification
        alert(message);
    }
}

// Export functions for global access
window.loadStoreSettingsContent = loadStoreSettingsContent;
window.resetStoreSettings = resetStoreSettings;
