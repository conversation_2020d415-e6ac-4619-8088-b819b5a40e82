<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🗺️ Testing Geographic Data System\n\n";

require_once 'php/config.php';

try {
    // Check if tables exist
    echo "1. Checking if geographic tables exist:\n";
    
    $tables = ['wilayas', 'communes'];
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "✅ Table '$table' exists with $count records\n";
        } catch (PDOException $e) {
            echo "❌ Table '$table' does not exist or has error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n2. Testing geographic API endpoints:\n";
    
    // Test wilayas endpoint
    echo "Testing wilayas endpoint...\n";
    $_GET['action'] = 'wilayas';
    ob_start();
    include 'php/api/geographic-data.php';
    $wilayas_response = ob_get_clean();
    
    $wilayas_data = json_decode($wilayas_response, true);
    if ($wilayas_data && $wilayas_data['success']) {
        echo "✅ Wilayas API working - found " . count($wilayas_data['wilayas']) . " wilayas\n";
    } else {
        echo "❌ Wilayas API failed\n";
        echo "Response: " . substr($wilayas_response, 0, 200) . "\n";
    }
    
    // Test communes endpoint
    echo "Testing communes endpoint for wilaya 16 (Algiers)...\n";
    $_GET['action'] = 'communes';
    $_GET['wilaya_code'] = '16';
    ob_start();
    include 'php/api/geographic-data.php';
    $communes_response = ob_get_clean();
    
    $communes_data = json_decode($communes_response, true);
    if ($communes_data && $communes_data['success']) {
        echo "✅ Communes API working - found " . count($communes_data['communes']) . " communes for Algiers\n";
    } else {
        echo "❌ Communes API failed\n";
        echo "Response: " . substr($communes_response, 0, 200) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
