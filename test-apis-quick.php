<?php
/**
 * Quick API Test
 */

echo "Testing APIs quickly...\n\n";

// Test 1: Config loading
echo "1. Testing config loading:\n";
try {
    require_once 'php/config.php';
    echo "✅ Config loaded successfully\n";
} catch (Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Database connection
echo "\n2. Testing database connection:\n";
try {
    $pdo = getPDOConnection();
    echo "✅ Database connection successful\n";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

// Test 3: Templates API
echo "\n3. Testing Templates API:\n";
try {
    $_GET['action'] = 'get_templates';
    ob_start();
    include 'php/api/templates.php';
    $output = ob_get_clean();
    
    if (!empty($output)) {
        $data = json_decode($output, true);
        if ($data && isset($data['success'])) {
            echo "✅ Templates API working - " . count($data['templates']) . " templates\n";
        } else {
            echo "⚠️ Templates API returns non-JSON: " . substr($output, 0, 100) . "...\n";
        }
    } else {
        echo "❌ Templates API returns empty response\n";
    }
} catch (Exception $e) {
    echo "❌ Templates API error: " . $e->getMessage() . "\n";
}

// Test 4: Landing Pages API
echo "\n4. Testing Landing Pages API:\n";
try {
    $_SERVER['REQUEST_METHOD'] = 'GET';
    ob_start();
    include 'php/api/landing-pages.php';
    $output = ob_get_clean();
    
    if (!empty($output)) {
        $data = json_decode($output, true);
        if ($data) {
            echo "✅ Landing Pages API working - JSON response received\n";
        } else {
            echo "⚠️ Landing Pages API returns non-JSON: " . substr($output, 0, 100) . "...\n";
        }
    } else {
        echo "❌ Landing Pages API returns empty response\n";
    }
} catch (Exception $e) {
    echo "❌ Landing Pages API error: " . $e->getMessage() . "\n";
}

echo "\nQuick test completed.\n";
?>
