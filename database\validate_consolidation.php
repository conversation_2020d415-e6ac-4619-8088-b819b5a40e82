<?php
/**
 * Database Consolidation Validation Script
 * This script validates that all database relationships work correctly after consolidation
 */

require_once __DIR__ . '/../php/config.php';

function runValidation() {
    try {
        echo "=== Database Consolidation Validation ===\n\n";
        
        $pdo = getPDOConnection();
        $errors = [];
        $warnings = [];
        
        // Test 1: Check if produits table exists and has correct structure
        echo "1. Validating produits table structure...\n";
        
        $stmt = $pdo->query("DESCRIBE produits");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredColumns = ['id', 'type', 'titre', 'description', 'prix', 'stock', 'image_url', 
                           'auteur', 'materiel', 'capacite', 'processeur', 'ram', 'stockage',
                           'has_landing_page', 'landing_page_enabled', 'slug', 'actif'];
        
        foreach ($requiredColumns as $column) {
            if (!in_array($column, $columns)) {
                $errors[] = "Missing column '{$column}' in produits table";
            }
        }
        
        if (empty($errors)) {
            echo "   ✓ All required columns present\n";
        }
        
        // Test 2: Check foreign key constraints
        echo "\n2. Validating foreign key constraints...\n";
        
        $stmt = $pdo->query("
            SELECT 
                CONSTRAINT_NAME,
                TABLE_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND REFERENCED_TABLE_NAME IS NOT NULL
            ORDER BY TABLE_NAME, CONSTRAINT_NAME
        ");
        $constraints = $stmt->fetchAll();
        
        $expectedConstraints = [
            'details_commande' => 'produits',
            'panier' => 'produits',
            'product_content_blocks' => 'produits',
            'product_images' => 'produits',
            'landing_pages' => 'produits'
        ];
        
        foreach ($constraints as $constraint) {
            if ($constraint['REFERENCED_TABLE_NAME'] === 'livres') {
                $errors[] = "Foreign key {$constraint['CONSTRAINT_NAME']} still references 'livres' table";
            } elseif (isset($expectedConstraints[$constraint['TABLE_NAME']]) && 
                     $constraint['REFERENCED_TABLE_NAME'] === 'produits') {
                echo "   ✓ {$constraint['TABLE_NAME']}.{$constraint['CONSTRAINT_NAME']} -> produits\n";
            }
        }
        
        // Test 3: Check if livres table still exists
        echo "\n3. Checking for legacy livres table...\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM information_schema.tables 
                            WHERE table_schema = DATABASE() AND table_name = 'livres'");
        $livresExists = $stmt->fetch()['count'] > 0;
        
        if ($livresExists) {
            $warnings[] = "Legacy 'livres' table still exists - consider removing after validation";
            echo "   ⚠ Legacy 'livres' table found\n";
        } else {
            echo "   ✓ No legacy 'livres' table found\n";
        }
        
        // Test 4: Validate data integrity
        echo "\n4. Validating data integrity...\n";
        
        // Check for orphaned records in related tables
        $orphanChecks = [
            'details_commande' => 'SELECT COUNT(*) FROM details_commande d LEFT JOIN produits p ON d.livre_id = p.id WHERE p.id IS NULL',
            'panier' => 'SELECT COUNT(*) FROM panier pa LEFT JOIN produits p ON pa.livre_id = p.id WHERE p.id IS NULL',
            'product_content_blocks' => 'SELECT COUNT(*) FROM product_content_blocks pcb LEFT JOIN produits p ON pcb.product_id = p.id WHERE p.id IS NULL',
            'product_images' => 'SELECT COUNT(*) FROM product_images pi LEFT JOIN produits p ON pi.product_id = p.id WHERE p.id IS NULL'
        ];
        
        foreach ($orphanChecks as $table => $query) {
            try {
                $stmt = $pdo->query($query);
                $orphanCount = $stmt->fetchColumn();
                
                if ($orphanCount > 0) {
                    $errors[] = "Found {$orphanCount} orphaned records in {$table} table";
                } else {
                    echo "   ✓ No orphaned records in {$table}\n";
                }
            } catch (Exception $e) {
                $warnings[] = "Could not check {$table} for orphaned records: " . $e->getMessage();
            }
        }
        
        // Test 5: Validate product types
        echo "\n5. Validating product types...\n";
        
        $stmt = $pdo->query("SELECT DISTINCT type FROM produits");
        $types = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $validTypes = ['book', 'bag', 'laptop'];
        
        foreach ($types as $type) {
            if (!in_array($type, $validTypes)) {
                $warnings[] = "Unknown product type found: {$type}";
            } else {
                echo "   ✓ Valid product type: {$type}\n";
            }
        }
        
        // Test 6: Test basic CRUD operations
        echo "\n6. Testing basic CRUD operations...\n";
        
        try {
            // Test INSERT
            $pdo->beginTransaction();
            
            $stmt = $pdo->prepare("INSERT INTO produits (type, titre, prix, stock) VALUES (?, ?, ?, ?)");
            $stmt->execute(['book', 'Test Book - Validation', 99.99, 1]);
            $testId = $pdo->lastInsertId();
            echo "   ✓ INSERT operation successful\n";
            
            // Test SELECT
            $stmt = $pdo->prepare("SELECT * FROM produits WHERE id = ?");
            $stmt->execute([$testId]);
            $testProduct = $stmt->fetch();
            
            if ($testProduct && $testProduct['titre'] === 'Test Book - Validation') {
                echo "   ✓ SELECT operation successful\n";
            } else {
                $errors[] = "SELECT operation failed";
            }
            
            // Test UPDATE
            $stmt = $pdo->prepare("UPDATE produits SET prix = ? WHERE id = ?");
            $stmt->execute([199.99, $testId]);
            echo "   ✓ UPDATE operation successful\n";
            
            // Test DELETE
            $stmt = $pdo->prepare("DELETE FROM produits WHERE id = ?");
            $stmt->execute([$testId]);
            echo "   ✓ DELETE operation successful\n";
            
            $pdo->rollback(); // Rollback test data
            
        } catch (Exception $e) {
            $pdo->rollback();
            $errors[] = "CRUD operations test failed: " . $e->getMessage();
        }
        
        // Summary
        echo "\n=== Validation Summary ===\n";
        
        if (empty($errors)) {
            echo "✅ All validation tests passed!\n";
        } else {
            echo "❌ Validation failed with " . count($errors) . " error(s):\n";
            foreach ($errors as $error) {
                echo "   - {$error}\n";
            }
        }
        
        if (!empty($warnings)) {
            echo "\n⚠️  Warnings (" . count($warnings) . "):\n";
            foreach ($warnings as $warning) {
                echo "   - {$warning}\n";
            }
        }
        
        return empty($errors);
        
    } catch (Exception $e) {
        echo "\n❌ Validation script failed: " . $e->getMessage() . "\n";
        return false;
    }
}

// Run validation
$success = runValidation();
exit($success ? 0 : 1);
?>
