<?php
/**
 * Test AI Settings Navigation Section
 * Comprehensive testing of AI Settings section functionality
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قسم إعدادات الذكاء الاصطناعي</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .api-test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .provider-test {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .provider-header {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .provider-icon {
            margin-left: 10px;
            font-size: 20px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .score-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 اختبار قسم إعدادات الذكاء الاصطناعي</h1>
            <p>اختبار شامل لوظائف قسم AI Settings وحل مشاكل Security::init()</p>
        </div>

        <?php
        $testResults = [];
        $totalTests = 0;
        $passedTests = 0;

        function recordTest($testName, $passed, $message = '') {
            global $testResults, $totalTests, $passedTests;
            $totalTests++;
            if ($passed) $passedTests++;
            $testResults[$testName] = ['passed' => $passed, 'message' => $message];
            return $passed;
        }

        try {
            require_once '../php/config.php';
            
            // Test 1: AI API Endpoint Availability
            echo '<div class="test-section">';
            echo '<h3>🔍 اختبار 1: توفر AI API Endpoint</h3>';
            
            $aiAPIFile = '../php/api/ai.php';
            if (recordTest('ai_api_exists', file_exists($aiAPIFile))) {
                echo '<div class="result pass">✅ ملف AI API موجود</div>';
                
                // Test API loading without errors
                try {
                    ob_start();
                    $errorOccurred = false;
                    
                    set_error_handler(function($severity, $message, $file, $line) use (&$errorOccurred) {
                        $errorOccurred = true;
                    });
                    
                    $_SERVER['REQUEST_METHOD'] = 'GET';
                    $_GET = ['action' => 'get_settings'];
                    
                    include $aiAPIFile;
                    restore_error_handler();
                    
                    $output = ob_get_clean();
                    
                    if (recordTest('ai_api_loads', !$errorOccurred)) {
                        echo '<div class="result pass">✅ AI API يتم تحميله بدون أخطاء Security::init()</div>';
                    } else {
                        echo '<div class="result fail">❌ AI API أخطاء في التحميل</div>';
                    }
                    
                    // Test JSON response
                    if (!empty($output)) {
                        $data = json_decode($output, true);
                        if (recordTest('ai_api_json', $data !== null)) {
                            echo '<div class="result pass">✅ AI API يعطي استجابة JSON صالحة</div>';
                        } else {
                            echo '<div class="result fail">❌ AI API استجابة JSON غير صالحة</div>';
                        }
                    }
                    
                } catch (Exception $e) {
                    recordTest('ai_api_loads', false, $e->getMessage());
                    echo '<div class="result fail">❌ خطأ في AI API: ' . $e->getMessage() . '</div>';
                }
                
            } else {
                echo '<div class="result fail">❌ ملف AI API غير موجود</div>';
            }
            echo '</div>';

            // Test 2: Database Table and Settings
            echo '<div class="test-section">';
            echo '<h3>🗄️ اختبار 2: جدول قاعدة البيانات والإعدادات</h3>';
            
            if (isset($conn) && $conn instanceof PDO) {
                echo '<div class="result pass">✅ اتصال قاعدة البيانات متاح</div>';
                
                // Check ai_settings table
                $stmt = $conn->query("SHOW TABLES LIKE 'ai_settings'");
                if (recordTest('ai_table_exists', $stmt->rowCount() > 0)) {
                    echo '<div class="result pass">✅ جدول ai_settings موجود</div>';
                    
                    // Test settings retrieval
                    try {
                        $stmt = $conn->query("SELECT * FROM ai_settings");
                        $settings = $stmt->fetchAll();
                        
                        if (recordTest('ai_settings_data', count($settings) > 0)) {
                            echo '<div class="result pass">✅ إعدادات AI متاحة (' . count($settings) . ' مزود)</div>';
                        } else {
                            echo '<div class="result warning">⚠️ لا توجد إعدادات AI في قاعدة البيانات</div>';
                        }
                        
                    } catch (Exception $e) {
                        recordTest('ai_settings_data', false, $e->getMessage());
                        echo '<div class="result fail">❌ خطأ في استرجاع إعدادات AI: ' . $e->getMessage() . '</div>';
                    }
                    
                } else {
                    echo '<div class="result fail">❌ جدول ai_settings غير موجود</div>';
                }
                
            } else {
                recordTest('database_connection', false);
                echo '<div class="result fail">❌ اتصال قاعدة البيانات غير متاح</div>';
            }
            echo '</div>';

            // Test 3: AI Provider Settings
            echo '<div class="test-section">';
            echo '<h3>🤖 اختبار 3: إعدادات مزودي الذكاء الاصطناعي</h3>';
            
            $aiProviders = [
                'openai' => ['name' => 'OpenAI', 'icon' => '🧠'],
                'anthropic' => ['name' => 'Anthropic Claude', 'icon' => '🎭'],
                'gemini' => ['name' => 'Google Gemini', 'icon' => '💎']
            ];
            
            echo '<div class="test-grid">';
            
            foreach ($aiProviders as $providerId => $provider) {
                echo '<div class="provider-test">';
                echo '<div class="provider-header">';
                echo '<span class="provider-icon">' . $provider['icon'] . '</span>';
                echo $provider['name'];
                echo '</div>';
                
                try {
                    $stmt = $conn->prepare("SELECT * FROM ai_settings WHERE provider = ?");
                    $stmt->execute([$providerId]);
                    $settings = $stmt->fetch();
                    
                    if (recordTest("provider_$providerId", $settings !== false)) {
                        echo '<div class="result pass">✅ إعدادات متاحة</div>';
                        if ($settings) {
                            echo '<div class="result info">📋 النموذج: ' . ($settings['model'] ?? 'غير محدد') . '</div>';
                            echo '<div class="result info">🔑 API Key: ' . (empty($settings['api_key']) ? 'غير مُعين' : 'مُعين') . '</div>';
                        }
                    } else {
                        echo '<div class="result warning">⚠️ إعدادات غير متاحة</div>';
                    }
                    
                } catch (Exception $e) {
                    recordTest("provider_$providerId", false, $e->getMessage());
                    echo '<div class="result fail">❌ خطأ: ' . $e->getMessage() . '</div>';
                }
                
                echo '</div>';
            }
            
            echo '</div>';
            echo '</div>';

            // Test 4: JavaScript API Calls
            echo '<div class="test-section">';
            echo '<h3>🌐 اختبار 4: استدعاءات JavaScript API</h3>';
            echo '<div id="jsTestResults">جاري تحميل اختبارات JavaScript...</div>';
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام في الاختبار</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
        }

        // Calculate score
        $successRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;
        $scoreClass = 'score-card';
        $scoreIcon = '🎉';
        $scoreText = 'ممتاز';

        if ($successRate < 70) {
            $scoreIcon = '⚠️';
            $scoreText = 'يحتاج تحسين';
        } elseif ($successRate < 90) {
            $scoreIcon = '✅';
            $scoreText = 'جيد';
        }

        echo '<div class="' . $scoreClass . '">';
        echo $scoreIcon . ' نتيجة اختبار AI Settings: ' . round($successRate, 1) . '% (' . $scoreText . ')';
        echo '<br>اختبارات نجحت: ' . $passedTests . '/' . $totalTests;
        echo '</div>';

        // Detailed results
        echo '<div class="test-section">';
        echo '<h3>📊 تفاصيل نتائج الاختبار</h3>';
        
        foreach ($testResults as $testName => $result) {
            $statusIcon = $result['passed'] ? '✅' : '❌';
            $statusText = $result['passed'] ? 'نجح' : 'فشل';
            $resultClass = $result['passed'] ? 'pass' : 'fail';
            
            echo '<div class="result ' . $resultClass . '">';
            echo $statusIcon . ' ' . $testName . ': ' . $statusText;
            if (!empty($result['message'])) {
                echo ' - ' . $result['message'];
            }
            echo '</div>';
        }
        
        // Recommendations
        echo '<h4>💡 التوصيات:</h4>';
        if ($successRate >= 90) {
            echo '<div class="result pass">🎉 ممتاز! قسم AI Settings يعمل بشكل مثالي</div>';
            echo '<div class="result info">💡 يمكنك الآن استخدام جميع ميزات إعدادات الذكاء الاصطناعي</div>';
        } elseif ($successRate >= 70) {
            echo '<div class="result warning">⚠️ قسم AI Settings يعمل مع بعض المشاكل البسيطة</div>';
            echo '<div class="result info">💡 قم بتشغيل سكريبت إصلاح AI Settings لحل المشاكل المتبقية</div>';
        } else {
            echo '<div class="result fail">❌ قسم AI Settings يحتاج إصلاحات</div>';
            echo '<div class="result info">💡 قم بتشغيل سكريبت الإصلاح الشامل أولاً</div>';
        }
        
        echo '<h4>🛠️ أدوات الإصلاح:</h4>';
        echo '<a href="fix-ai-settings-errors.php" class="test-button">🤖 إصلاح AI Settings</a>';
        echo '<a href="MASTER-FIX-HTTP-500-ERRORS.php" class="test-button">🔧 إصلاح شامل</a>';
        
        echo '<h4>🧪 اختبار الوظائف:</h4>';
        echo '<a href="index.html" class="test-button">🏠 فتح لوحة التحكم</a>';
        echo '<a href="ai-settings.html" class="test-button">🤖 اختبار AI Settings مباشرة</a>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        // Comprehensive JavaScript testing for AI Settings
        async function testAISettingsJS() {
            const resultsDiv = document.getElementById('jsTestResults');
            let results = '';
            let jsPassedTests = 0;
            let jsTotalTests = 0;

            // Test 1: AI Settings API Call
            try {
                jsTotalTests++;
                console.log('Testing AI Settings API...');
                const response = await fetch('../php/api/ai.php?action=get_settings');
                
                if (response.status < 500) {
                    results += '<div class="result pass">✅ AI Settings API: لا يوجد خطأ 500 (Status: ' + response.status + ')</div>';
                    jsPassedTests++;
                    
                    try {
                        const data = await response.json();
                        results += '<div class="result pass">✅ AI Settings API: استجابة JSON صالحة</div>';
                        console.log('AI Settings data:', data);
                    } catch (jsonError) {
                        results += '<div class="result warning">⚠️ AI Settings API: استجابة ليست JSON صالحة</div>';
                    }
                } else {
                    results += '<div class="result fail">❌ AI Settings API: خطأ 500</div>';
                }
            } catch (error) {
                results += '<div class="result fail">❌ AI Settings API: خطأ في الشبكة - ' + error.message + '</div>';
            }

            // Test 2: OpenAI Connection Test
            try {
                jsTotalTests++;
                console.log('Testing OpenAI connection...');
                const response = await fetch('../php/api/ai.php?action=test_connection&provider=openai');
                
                if (response.status < 500) {
                    results += '<div class="result pass">✅ OpenAI Test: لا يوجد خطأ 500 (Status: ' + response.status + ')</div>';
                    jsPassedTests++;
                } else {
                    results += '<div class="result fail">❌ OpenAI Test: خطأ 500</div>';
                }
            } catch (error) {
                results += '<div class="result fail">❌ OpenAI Test: خطأ في الشبكة - ' + error.message + '</div>';
            }

            // Test 3: Anthropic Connection Test
            try {
                jsTotalTests++;
                console.log('Testing Anthropic connection...');
                const response = await fetch('../php/api/ai.php?action=test_connection&provider=anthropic');
                
                if (response.status < 500) {
                    results += '<div class="result pass">✅ Anthropic Test: لا يوجد خطأ 500 (Status: ' + response.status + ')</div>';
                    jsPassedTests++;
                } else {
                    results += '<div class="result fail">❌ Anthropic Test: خطأ 500</div>';
                }
            } catch (error) {
                results += '<div class="result fail">❌ Anthropic Test: خطأ في الشبكة - ' + error.message + '</div>';
            }

            // Test 4: Gemini Connection Test
            try {
                jsTotalTests++;
                console.log('Testing Gemini connection...');
                const response = await fetch('../php/api/ai.php?action=test_connection&provider=gemini');
                
                if (response.status < 500) {
                    results += '<div class="result pass">✅ Gemini Test: لا يوجد خطأ 500 (Status: ' + response.status + ')</div>';
                    jsPassedTests++;
                } else {
                    results += '<div class="result fail">❌ Gemini Test: خطأ 500</div>';
                }
            } catch (error) {
                results += '<div class="result fail">❌ Gemini Test: خطأ في الشبكة - ' + error.message + '</div>';
            }

            // Calculate JavaScript success rate
            const jsSuccessRate = jsTotalTests > 0 ? (jsPassedTests / jsTotalTests) * 100 : 0;
            results += '<div class="api-test-result">';
            results += '<strong>نتائج اختبار JavaScript:</strong> ' + jsPassedTests + '/' + jsTotalTests + ' (' + Math.round(jsSuccessRate) + '%)';
            results += '</div>';

            resultsDiv.innerHTML = results;
            
            console.log(`JavaScript AI Settings tests completed: ${jsPassedTests}/${jsTotalTests} (${jsSuccessRate.toFixed(1)}%)`);
        }

        // Run JavaScript tests after page loads
        document.addEventListener('DOMContentLoaded', testAISettingsJS);
    </script>
</body>
</html>
