<?php
/**
 * Test API Call
 */

echo "<h1>Testing Users API</h1>";

// Test the users API by making an internal call
$_GET['action'] = 'list';
$_SERVER['REQUEST_METHOD'] = 'GET';

// Capture output
ob_start();
include 'php/api/users.php';
$output = ob_get_clean();

echo "<h2>API Response:</h2>";
echo "<pre>" . htmlspecialchars($output) . "</pre>";

// Try to decode JSON
$data = json_decode($output, true);
if ($data) {
    echo "<h2>Parsed Data:</h2>";
    echo "<p>Success: " . ($data['success'] ? 'Yes' : 'No') . "</p>";
    if (isset($data['users'])) {
        echo "<p>Users count: " . count($data['users']) . "</p>";
        if (!empty($data['users'])) {
            echo "<h3>Users:</h3>";
            foreach ($data['users'] as $user) {
                echo "<p>- {$user['name']} ({$user['email']}) - {$user['role']}</p>";
            }
        }
    }
    if (isset($data['message'])) {
        echo "<p>Message: " . $data['message'] . "</p>";
    }
} else {
    echo "<p style='color: red;'>Failed to parse JSON response</p>";
}
?>
