<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐛 Debug Landing Page Button</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            padding: 20px;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .debug-section {
            margin: 30px 0;
            padding: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: #f9fafb;
        }
        .debug-section h3 {
            color: #374151;
            margin-bottom: 20px;
            font-size: 1.25rem;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status.success {
            background: #dcfce7;
            color: #166534;
        }
        .status.error {
            background: #fef2f2;
            color: #dc2626;
        }
        .status.warning {
            background: #fef3c7;
            color: #92400e;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .test-item.success {
            border-left-color: #10b981;
        }
        .test-item.error {
            border-left-color: #ef4444;
        }
        .test-item.warning {
            border-left-color: #f59e0b;
        }
        .test-title {
            font-weight: 600;
            margin-bottom: 8px;
        }
        .test-description {
            font-size: 0.9rem;
            color: #6b7280;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .iframe-container {
            margin-top: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .code-snippet {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Debug Landing Page Button Issue</h1>
        
        <div class="debug-section">
            <h3>🔍 Issue Analysis</h3>
            <div class="test-item warning">
                <div class="test-title">Problem: "أضف صفحة هبوط" Button Not Working</div>
                <div class="test-description">
                    The button exists in the HTML but clicking it doesn't open the modal.
                    This could be due to:
                    <ul>
                        <li>JavaScript not loading properly</li>
                        <li>Event listeners not being bound</li>
                        <li>Modal elements missing or misnamed</li>
                        <li>Script loading order conflicts</li>
                        <li>API path issues</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="debug-section">
            <h3>🧪 Debug Tests</h3>
            <button onclick="testButtonExists()">Test Button Exists</button>
            <button onclick="testModalExists()">Test Modal Exists</button>
            <button onclick="testScriptLoaded()">Test Script Loaded</button>
            <button onclick="testEventListeners()">Test Event Listeners</button>
            <button onclick="testManualClick()">Test Manual Click</button>
            <button onclick="openAdminPanel()">Open Admin Panel</button>
            
            <div id="test-results"></div>
            <div class="console-output" id="console-output"></div>
        </div>

        <div class="debug-section">
            <h3>📋 Debug Results</h3>
            <div class="test-item" id="result-button">
                <div class="test-title">🔄 Button Element</div>
                <div class="test-description">Testing...</div>
            </div>
            <div class="test-item" id="result-modal">
                <div class="test-title">🔄 Modal Element</div>
                <div class="test-description">Testing...</div>
            </div>
            <div class="test-item" id="result-script">
                <div class="test-title">🔄 Script Loading</div>
                <div class="test-description">Testing...</div>
            </div>
            <div class="test-item" id="result-events">
                <div class="test-title">🔄 Event Listeners</div>
                <div class="test-description">Testing...</div>
            </div>
        </div>

        <div class="debug-section">
            <h3>🔧 Manual Debug Commands</h3>
            <div class="code-snippet">
// Test in browser console:

// 1. Check if button exists
console.log('Button:', document.getElementById('addLandingPageBtn'));

// 2. Check if modal exists  
console.log('Modal:', document.getElementById('landingPageModal'));

// 3. Check if script loaded
console.log('Landing Pages Manager:', window.landingPagesManager);

// 4. Check initialization status
console.log('Initialized:', window.landingPagesManager?.initialized);

// 5. Manual button click
document.getElementById('addLandingPageBtn')?.click();

// 6. Manual modal open
window.landingPagesManager?.openModal();
            </div>
        </div>

        <div class="iframe-container" style="display: none;" id="iframe-container">
            <iframe id="admin-frame"></iframe>
        </div>
    </div>

    <script>
        const consoleOutput = document.getElementById('console-output');
        const testResults = document.getElementById('test-results');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            consoleOutput.appendChild(logEntry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function updateResult(id, status, message) {
            const item = document.getElementById(id);
            if (item) {
                item.className = `test-item ${status}`;
                const title = item.querySelector('.test-title');
                const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : '⚠️';
                title.innerHTML = title.innerHTML.replace('🔄', icon);
                const desc = item.querySelector('.test-description');
                desc.textContent = message;
            }
        }

        function testButtonExists() {
            log('Testing if button exists...');
            
            const iframe = document.getElementById('admin-frame');
            if (!iframe || !iframe.contentDocument) {
                updateResult('result-button', 'warning', 'Admin panel not loaded yet');
                return;
            }
            
            try {
                const button = iframe.contentDocument.getElementById('addLandingPageBtn');
                if (button) {
                    updateResult('result-button', 'success', `Button found: ${button.textContent.trim()}`);
                    log('✅ Button exists in DOM');
                } else {
                    updateResult('result-button', 'error', 'Button #addLandingPageBtn not found');
                    log('❌ Button not found');
                }
            } catch (error) {
                updateResult('result-button', 'error', 'Error accessing iframe: ' + error.message);
                log('❌ Error: ' + error.message);
            }
        }

        function testModalExists() {
            log('Testing if modal exists...');
            
            const iframe = document.getElementById('admin-frame');
            if (!iframe || !iframe.contentDocument) {
                updateResult('result-modal', 'warning', 'Admin panel not loaded yet');
                return;
            }
            
            try {
                const modal = iframe.contentDocument.getElementById('landingPageModal');
                if (modal) {
                    updateResult('result-modal', 'success', 'Modal found in DOM');
                    log('✅ Modal exists in DOM');
                } else {
                    updateResult('result-modal', 'error', 'Modal #landingPageModal not found');
                    log('❌ Modal not found');
                }
            } catch (error) {
                updateResult('result-modal', 'error', 'Error accessing iframe: ' + error.message);
                log('❌ Error: ' + error.message);
            }
        }

        function testScriptLoaded() {
            log('Testing if script loaded...');
            
            const iframe = document.getElementById('admin-frame');
            if (!iframe || !iframe.contentWindow) {
                updateResult('result-script', 'warning', 'Admin panel not loaded yet');
                return;
            }
            
            try {
                const landingPagesManager = iframe.contentWindow.landingPagesManager;
                if (landingPagesManager) {
                    const initialized = landingPagesManager.initialized;
                    updateResult('result-script', 'success', `Script loaded, initialized: ${initialized}`);
                    log(`✅ Script loaded, initialized: ${initialized}`);
                } else {
                    updateResult('result-script', 'error', 'landingPagesManager not found in window');
                    log('❌ landingPagesManager not found');
                }
            } catch (error) {
                updateResult('result-script', 'error', 'Error accessing iframe: ' + error.message);
                log('❌ Error: ' + error.message);
            }
        }

        function testEventListeners() {
            log('Testing event listeners...');
            
            const iframe = document.getElementById('admin-frame');
            if (!iframe || !iframe.contentDocument) {
                updateResult('result-events', 'warning', 'Admin panel not loaded yet');
                return;
            }
            
            try {
                const button = iframe.contentDocument.getElementById('addLandingPageBtn');
                if (button) {
                    // Check if button has event listeners (this is approximate)
                    const hasListeners = button.onclick || button.addEventListener;
                    updateResult('result-events', hasListeners ? 'success' : 'warning', 
                        hasListeners ? 'Button appears to have event listeners' : 'Cannot detect event listeners');
                    log(hasListeners ? '✅ Button has event handling capability' : '⚠️ Cannot detect event listeners');
                } else {
                    updateResult('result-events', 'error', 'Button not found for event testing');
                    log('❌ Button not found');
                }
            } catch (error) {
                updateResult('result-events', 'error', 'Error testing events: ' + error.message);
                log('❌ Error: ' + error.message);
            }
        }

        function testManualClick() {
            log('Testing manual button click...');
            
            const iframe = document.getElementById('admin-frame');
            if (!iframe || !iframe.contentDocument) {
                log('❌ Admin panel not loaded yet');
                return;
            }
            
            try {
                const button = iframe.contentDocument.getElementById('addLandingPageBtn');
                if (button) {
                    log('🖱️ Simulating button click...');
                    button.click();
                    
                    // Check if modal opened
                    setTimeout(() => {
                        const modal = iframe.contentDocument.getElementById('landingPageModal');
                        if (modal && modal.style.display === 'block') {
                            log('✅ Modal opened successfully!');
                        } else {
                            log('❌ Modal did not open after click');
                        }
                    }, 500);
                } else {
                    log('❌ Button not found for manual click');
                }
            } catch (error) {
                log('❌ Error during manual click: ' + error.message);
            }
        }

        function openAdminPanel() {
            log('Opening admin panel for debugging...');
            const iframe = document.getElementById('admin-frame');
            const container = document.getElementById('iframe-container');
            
            container.style.display = 'block';
            iframe.src = '/admin/index.html?' + Date.now();
            
            iframe.onload = function() {
                log('✅ Admin panel loaded');
                
                // Wait a bit for scripts to load, then run tests
                setTimeout(() => {
                    testButtonExists();
                    testModalExists();
                    testScriptLoaded();
                    testEventListeners();
                }, 2000);
            };
        }

        // Auto-run basic tests
        document.addEventListener('DOMContentLoaded', () => {
            log('🐛 Debug page loaded - ready to test landing page button');
            log('Click "Open Admin Panel" to start debugging');
        });
    </script>
</body>
</html>
