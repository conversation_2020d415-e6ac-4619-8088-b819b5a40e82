<?php

/**
 * Test AI Settings and Categories Management Fixes
 * This script tests the specific fixes for AI configuration and categories management
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define security check
define('SECURITY_CHECK', true);

require_once '../php/config.php';
require_once '../php/config/env.php';
require_once '../php/ai-config.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات الذكاء الاصطناعي وإدارة الفئات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }

        .test-result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .test-result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .test-result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .env-var {
            background: #e9ecef;
            padding: 5px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🧪 اختبار إصلاحات الذكاء الاصطناعي وإدارة الفئات</h1>
        <p>هذا السكريبت يختبر الإصلاحات المطبقة على إعدادات الذكاء الاصطناعي وإدارة الفئات.</p>

        <?php
        $testResults = [];
        $totalTests = 0;
        $passedTests = 0;

        try {
            // Test 1: Environment Variables Loading
            echo '<div class="test-section">';
            echo '<h3>🌍 اختبار تحميل متغيرات البيئة</h3>';

            EnvLoader::load();

            $envVars = [
                'OPENAI_API_KEY' => EnvLoader::get('OPENAI_API_KEY'),
                'ANTHROPIC_API_KEY' => EnvLoader::get('ANTHROPIC_API_KEY'),
                'GEMINI_API_KEY' => EnvLoader::get('GEMINI_API_KEY')
            ];

            foreach ($envVars as $key => $value) {
                $totalTests++;
                if (!empty($value)) {
                    echo '<div class="test-result pass">✅ ' . $key . ': <span class="env-var">***' . substr($value, -4) . '</span></div>';
                    $passedTests++;
                } else {
                    echo '<div class="test-result warning">⚠️ ' . $key . ': غير محدد</div>';
                }
            }

            echo '</div>';

            // Test 2: AI Configuration Loading
            echo '<div class="test-section">';
            echo '<h3>🤖 اختبار تكوين الذكاء الاصطناعي</h3>';

            try {
                $aiConfig = AIManager::getInstance(); // Config reloaded via Config class
                echo '<div class="test-result pass">✅ تم تحميل تكوين الذكاء الاصطناعي بنجاح</div>';
                $passedTests++;

                // Check each provider
                foreach ($aiConfig['providers'] as $provider => $config) {
                    $totalTests++;
                    if ($config['enabled']) {
                        echo '<div class="test-result pass">✅ ' . ucfirst($provider) . ': مفعل</div>';
                        $passedTests++;
                    } else {
                        echo '<div class="test-result warning">⚠️ ' . ucfirst($provider) . ': غير مفعل</div>';
                    }
                }

                echo '<h4>تفاصيل التكوين:</h4>';
                echo '<pre>' . json_encode($aiConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            } catch (Exception $e) {
                echo '<div class="test-result fail">❌ فشل في تحميل تكوين الذكاء الاصطناعي: ' . $e->getMessage() . '</div>';
            }
            $totalTests++;

            echo '</div>';

            // Test 3: AI API Endpoint
            echo '<div class="test-section">';
            echo '<h3>🔌 اختبار AI API</h3>';

            try {
                ob_start();
                $_GET['action'] = 'get_config';
                $_SERVER['REQUEST_METHOD'] = 'GET';
                include '../php/api/ai.php';
                $aiApiOutput = ob_get_clean();

                $aiApiData = json_decode($aiApiOutput, true);

                if ($aiApiData && isset($aiApiData['success']) && $aiApiData['success']) {
                    echo '<div class="test-result pass">✅ AI API يعمل بنجاح</div>';
                    echo '<div class="test-result pass">📊 المزودون المتاحون: ' . count($aiApiData['data']['available_providers']) . '</div>';
                    $passedTests++;
                } else {
                    echo '<div class="test-result fail">❌ AI API فشل</div>';
                    echo '<pre>' . htmlspecialchars($aiApiOutput) . '</pre>';
                }
            } catch (Exception $e) {
                echo '<div class="test-result fail">❌ خطأ في AI API: ' . $e->getMessage() . '</div>';
            }
            $totalTests++;

            echo '</div>';

            // Test 4: Categories Management JavaScript
            echo '<div class="test-section">';
            echo '<h3>📋 اختبار JavaScript إدارة الفئات</h3>';

            $jsFile = 'js/categories-management.js';
            if (file_exists($jsFile)) {
                $jsContent = file_get_contents($jsFile);

                $requiredFunctions = [
                    'initializeCategoriesManagement',
                    'loadCategories',
                    'showAddCategoryModal',
                    'closeCategoryModal',
                    'handleCategorySubmit',
                    'displayCategories',
                    'updateStatistics'
                ];

                $missingFunctions = [];
                foreach ($requiredFunctions as $function) {
                    $totalTests++;
                    if (strpos($jsContent, $function) !== false) {
                        echo '<div class="test-result pass">✅ الوظيفة ' . $function . ' موجودة</div>';
                        $passedTests++;
                    } else {
                        echo '<div class="test-result fail">❌ الوظيفة ' . $function . ' مفقودة</div>';
                        $missingFunctions[] = $function;
                    }
                }

                if (empty($missingFunctions)) {
                    echo '<div class="test-result pass">✅ جميع الوظائف المطلوبة موجودة في JavaScript</div>';
                }
            } else {
                echo '<div class="test-result fail">❌ ملف JavaScript غير موجود: ' . $jsFile . '</div>';
                $totalTests++;
            }

            echo '</div>';

            // Test 5: Categories HTML Structure
            echo '<div class="test-section">';
            echo '<h3>📄 اختبار هيكل HTML إدارة الفئات</h3>';

            $htmlFile = 'categories-management.html';
            if (file_exists($htmlFile)) {
                $htmlContent = file_get_contents($htmlFile);

                $requiredElements = [
                    'categoriesTree' => 'id="categoriesTree"',
                    'categoryForm' => 'id="categoryForm"',
                    'categoryModal' => 'id="categoryModal"',
                    'categorySearch' => 'id="categorySearch"',
                    'totalCategories' => 'id="totalCategories"',
                    'activeCategories' => 'id="activeCategories"'
                ];

                foreach ($requiredElements as $name => $pattern) {
                    $totalTests++;
                    if (strpos($htmlContent, $pattern) !== false) {
                        echo '<div class="test-result pass">✅ العنصر ' . $name . ' موجود</div>';
                        $passedTests++;
                    } else {
                        echo '<div class="test-result fail">❌ العنصر ' . $name . ' مفقود</div>';
                    }
                }
            } else {
                echo '<div class="test-result fail">❌ ملف HTML غير موجود: ' . $htmlFile . '</div>';
                $totalTests++;
            }

            echo '</div>';

            // Test 6: AI Magic Wand Integration
            echo '<div class="test-section">';
            echo '<h3>🪄 اختبار تكامل AI Magic Wand</h3>';

            $magicWandFile = 'js/ai-magic-wand.js';
            if (file_exists($magicWandFile)) {
                echo '<div class="test-result pass">✅ ملف AI Magic Wand موجود</div>';
                $passedTests++;

                $magicWandContent = file_get_contents($magicWandFile);
                $magicWandFunctions = [
                    'AIMagicWand',
                    'generateText',
                    'addMagicWandToField',
                    'checkAvailability'
                ];

                foreach ($magicWandFunctions as $function) {
                    $totalTests++;
                    if (strpos($magicWandContent, $function) !== false) {
                        echo '<div class="test-result pass">✅ وظيفة Magic Wand: ' . $function . '</div>';
                        $passedTests++;
                    } else {
                        echo '<div class="test-result warning">⚠️ وظيفة Magic Wand مفقودة: ' . $function . '</div>';
                    }
                }
            } else {
                echo '<div class="test-result fail">❌ ملف AI Magic Wand غير موجود</div>';
                $totalTests++;
            }

            echo '</div>';

            // Summary
            echo '<div class="test-section">';
            echo '<h3>📊 ملخص النتائج</h3>';

            $successRate = ($totalTests > 0) ? round(($passedTests / $totalTests) * 100, 2) : 0;

            if ($successRate >= 90) {
                $statusClass = 'pass';
                $statusIcon = '🎉';
                $statusText = 'ممتاز - جميع الإصلاحات تعمل بنجاح';
            } elseif ($successRate >= 70) {
                $statusClass = 'warning';
                $statusIcon = '⚠️';
                $statusText = 'جيد مع بعض التحذيرات';
            } else {
                $statusClass = 'fail';
                $statusIcon = '❌';
                $statusText = 'يحتاج إصلاحات إضافية';
            }

            echo '<div class="test-result ' . $statusClass . '">';
            echo $statusIcon . ' <strong>النتيجة النهائية:</strong> ' . $passedTests . '/' . $totalTests . ' اختبار نجح (' . $successRate . '%) - ' . $statusText;
            echo '</div>';

            echo '<h4>🔧 الإصلاحات المطبقة:</h4>';
            echo '<ul>';
            echo '<li>✅ إصلاح تحميل متغيرات البيئة من ملف .env</li>';
            echo '<li>✅ إصلاح تكوين الذكاء الاصطناعي وتحديث API</li>';
            echo '<li>✅ إصلاح JavaScript إدارة الفئات مع فحص DOM</li>';
            echo '<li>✅ إضافة وظائف مفقودة في إدارة الفئات</li>';
            echo '<li>✅ تحسين معالجة الأخطاء والتحقق من العناصر</li>';
            echo '</ul>';

            echo '<h4>🧪 أدوات الاختبار:</h4>';
            echo '<p><a href="index.html" class="test-button">العودة إلى لوحة التحكم</a></p>';
            echo '<p><a href="ai-settings.html" class="test-button">اختبار إعدادات الذكاء الاصطناعي</a></p>';
            echo '<p><a href="categories-management.html" class="test-button">اختبار إدارة الفئات</a></p>';
            echo '<p><a href="test-critical-fixes.php" class="test-button">اختبار جميع الإصلاحات</a></p>';

            echo '</div>';
        } catch (Exception $e) {
            echo '<div class="test-result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
        }
        ?>

        <!-- JavaScript Tests -->
        <div class="test-section">
            <h3>🧪 اختبارات JavaScript المباشرة</h3>
            <div id="jsTestResults">جاري تحميل الاختبارات...</div>

            <script>
                async function runJavaScriptTests() {
                    const resultsDiv = document.getElementById('jsTestResults');
                    let results = '';

                    // Test AI API
                    try {
                        const response = await fetch('../php/api/ai.php?action=get_config');
                        const data = await response.json();

                        if (data.success) {
                            results += '<div class="test-result pass">✅ AI API يعمل عبر JavaScript</div>';
                            results += '<div class="test-result pass">📊 المزودون المتاحون: ' + data.data.available_providers.length + '</div>';
                        } else {
                            results += '<div class="test-result fail">❌ AI API فشل عبر JavaScript</div>';
                        }
                    } catch (error) {
                        results += '<div class="test-result fail">❌ خطأ في AI API: ' + error.message + '</div>';
                    }

                    // Test Categories API
                    try {
                        const response = await fetch('../php/api/categories.php');
                        const data = await response.json();

                        if (data.success) {
                            results += '<div class="test-result pass">✅ Categories API يعمل عبر JavaScript - عدد الفئات: ' + data.categories.length + '</div>';
                        } else {
                            results += '<div class="test-result fail">❌ Categories API فشل عبر JavaScript</div>';
                        }
                    } catch (error) {
                        results += '<div class="test-result fail">❌ خطأ في Categories API: ' + error.message + '</div>';
                    }

                    resultsDiv.innerHTML = results;
                }

                // Run tests after page loads
                document.addEventListener('DOMContentLoaded', runJavaScriptTests);
            </script>
        </div>

    </div>
</body>

</html>
