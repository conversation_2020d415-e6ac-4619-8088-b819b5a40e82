<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الذكاء الاصطناعي - لوحة التحكم</title>
    <link href="css/admin.css" rel="stylesheet">
    <link href="../css/ai-settings.css" rel="stylesheet">
    <style>
        body {
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
</head>
<body>
    <!-- AI Settings Content -->
    <div class="ai-settings-content">
        <!-- Header Section -->
        <div class="ai-settings-header">
            <div class="section-title-wrapper">
                <div class="section-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="section-title-content">
                    <h3 class="section-title">🤖 إعدادات الذكاء الاصطناعي</h3>
                    <p class="section-subtitle">تكوين وإدارة خدمات الذكاء الاصطناعي والنماذج المختلفة</p>
                </div>
            </div>
            <div class="ai-summary">
                <div class="summary-item">
                    <span class="summary-label">النماذج المتاحة:</span>
                    <span class="summary-value" id="availableModels">--</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">الحالة:</span>
                    <span class="summary-value status-active">متصل</span>
                </div>
            </div>
        </div>

                <!-- AI Status Overview -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-info" id="aiStatusAlert">
                            <i class="fas fa-info-circle"></i>
                            <span id="aiStatusText">جاري تحميل حالة الذكاء الاصطناعي...</span>
                        </div>
                    </div>
                </div>

                <!-- AI Providers Configuration -->
                <div class="row">
                    <div class="col-12">
                        <div class="ai-providers-header">
                            <div class="section-title-wrapper">
                                <div class="section-icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div class="section-title-content">
                                    <h3 class="section-title">إعداد مزودي الذكاء الاصطناعي</h3>
                                    <p class="section-subtitle">قم بتكوين مفاتيح API للمزودين المختلفين لتفعيل ميزات الذكاء الاصطناعي المتقدمة</p>
                                </div>
                            </div>
                            <div class="providers-summary">
                                <div class="summary-item">
                                    <span class="summary-label">المزودين المتاحين:</span>
                                    <span class="summary-value">3</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">المزودين المفعلين:</span>
                                    <span class="summary-value" id="enabledProvidersCount">0</span>
                                </div>
                            </div>
                        </div>

                        <div class="providers-grid-container">
                            <div class="providers-grid-header">
                                <h4 class="grid-title">
                                    <i class="fas fa-robot"></i>
                                    مزودي الذكاء الاصطناعي المدعومين
                                </h4>
                                <div class="cost-legend">
                                    <span class="legend-item budget">
                                        <i class="fas fa-circle"></i>
                                        اقتصادي
                                    </span>
                                    <span class="legend-item balanced">
                                        <i class="fas fa-circle"></i>
                                        متوازن
                                    </span>
                                    <span class="legend-item premium">
                                        <i class="fas fa-circle"></i>
                                        متقدم
                                    </span>
                                </div>
                            </div>

                        <!-- OpenAI Configuration -->
                        <div class="ai-provider-card enhanced-card" id="openaiCard">
                            <div class="provider-header">
                                <div class="provider-info">
                                    <div class="provider-logo openai-logo">
                                        <i class="fab fa-openai"></i>
                                    </div>
                                    <div class="provider-details">
                                        <h5 class="provider-name">OpenAI GPT</h5>
                                        <p class="provider-description">أقوى نماذج الذكاء الاصطناعي للنصوص والمحادثات</p>
                                        <div class="provider-features">
                                            <span class="feature-tag">محادثات ذكية</span>
                                            <span class="feature-tag">كتابة إبداعية</span>
                                            <span class="feature-tag">تحليل النصوص</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="provider-status">
                                    <span class="status-badge" id="openaiStatus">غير مفعل</span>
                                    <div class="provider-pricing">
                                        <span class="pricing-info">من $0.0005 لكل 1K رمز</span>
                                    </div>
                                </div>
                            </div>
                            <div class="provider-config-body">
                                <div class="config-section">
                                    <div class="config-row">
                                        <div class="config-item enable-section">
                                            <div class="form-check enhanced-checkbox">
                                                <input class="form-check-input" type="checkbox" id="openaiEnabled" onchange="updateProviderStatus('openai', this.checked)">
                                                <label class="form-check-label" for="openaiEnabled">
                                                    <span class="checkbox-text">تفعيل OpenAI</span>
                                                    <span class="checkbox-description">تمكين استخدام نماذج OpenAI</span>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="config-item api-key-section">
                                            <label for="openaiApiKey" class="form-label enhanced-label">
                                                <i class="fas fa-key"></i>
                                                مفتاح API
                                                <span class="label-hint">احصل عليه من platform.openai.com</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="password" class="form-control enhanced-input" id="openaiApiKey" placeholder="sk-proj-..." dir="ltr">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('openaiApiKey')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="input-hint">يبدأ مفتاح OpenAI عادة بـ "sk-"</div>
                                        </div>
                                    </div>

                                    <div class="config-row">
                                        <div class="config-item model-section">
                                            <label for="openaiModel" class="form-label enhanced-label">
                                                <i class="fas fa-brain"></i>
                                                النموذج
                                                <span class="label-hint">اختر النموذج المناسب لاحتياجاتك</span>
                                            </label>
                                            <select class="form-select enhanced-select" id="openaiModel" onchange="updateModelInfo('openai', this.value)">
                                                <optgroup label="🟢 النماذج الاقتصادية (الأرخص)">
                                                    <option value="gpt-4o-mini" data-cost="budget" data-description="نموذج سريع واقتصادي للمهام البسيطة - الأرخص">GPT-4o Mini (اقتصادي)</option>
                                                    <option value="gpt-3.5-turbo" data-cost="budget" data-description="نموذج متوازن وسريع للاستخدام العام">GPT-3.5 Turbo (اقتصادي)</option>
                                                </optgroup>
                                                <optgroup label="🟡 النماذج المتوازنة">
                                                    <option value="gpt-4-turbo" data-cost="balanced" data-description="نموذج متقدم مع توازن جيد بين الأداء والتكلفة">GPT-4 Turbo (متوازن)</option>
                                                    <option value="gpt-4" data-cost="balanced" data-description="النموذج الأساسي عالي الجودة">GPT-4 (متوازن)</option>
                                                </optgroup>
                                                <optgroup label="🔴 النماذج المتقدمة">
                                                    <option value="gpt-4o" data-cost="premium" data-description="أحدث وأقوى نموذج من OpenAI">GPT-4o (متقدم)</option>
                                                </optgroup>
                                            </select>
                                            <div class="model-info" id="openaiModelInfo">
                                                <div class="model-description">اختر نموذجاً لعرض التفاصيل</div>
                                            </div>
                                        </div>

                                        <div class="config-item test-section">
                                            <button type="button" class="btn btn-test enhanced-test-btn" onclick="testConnection('openai')">
                                                <i class="fas fa-plug"></i>
                                                <span class="btn-text">اختبار الاتصال</span>
                                                <span class="btn-description">تحقق من صحة المفتاح</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Anthropic Configuration -->
                        <div class="ai-provider-card enhanced-card" id="anthropicCard">
                            <div class="provider-header">
                                <div class="provider-info">
                                    <div class="provider-logo anthropic-logo">
                                        <i class="fas fa-robot"></i>
                                    </div>
                                    <div class="provider-details">
                                        <h5 class="provider-name">Anthropic Claude</h5>
                                        <p class="provider-description">نماذج ذكية متقدمة للمحادثات والتحليل المعمق</p>
                                        <div class="provider-features">
                                            <span class="feature-tag">تحليل معمق</span>
                                            <span class="feature-tag">أمان عالي</span>
                                            <span class="feature-tag">فهم السياق</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="provider-status">
                                    <span class="status-badge" id="anthropicStatus">غير مفعل</span>
                                    <div class="provider-pricing">
                                        <span class="pricing-info">من $0.25 لكل 1M رمز</span>
                                    </div>
                                </div>
                            </div>
                            <div class="provider-config-body">
                                <div class="config-section">
                                    <div class="config-row">
                                        <div class="config-item enable-section">
                                            <div class="form-check enhanced-checkbox">
                                                <input class="form-check-input" type="checkbox" id="anthropicEnabled" onchange="updateProviderStatus('anthropic', this.checked)">
                                                <label class="form-check-label" for="anthropicEnabled">
                                                    <span class="checkbox-text">تفعيل Anthropic</span>
                                                    <span class="checkbox-description">تمكين استخدام نماذج Claude</span>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="config-item api-key-section">
                                            <label for="anthropicApiKey" class="form-label enhanced-label">
                                                <i class="fas fa-key"></i>
                                                مفتاح API
                                                <span class="label-hint">احصل عليه من console.anthropic.com</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="password" class="form-control enhanced-input" id="anthropicApiKey" placeholder="sk-ant-..." dir="ltr">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('anthropicApiKey')" title="إظهار/إخفاء كلمة المرور">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="input-hint">يبدأ مفتاح Anthropic عادة بـ "sk-ant-"</div>
                                        </div>
                                    </div>

                                    <div class="config-row">
                                        <div class="config-item model-section">
                                            <label for="anthropicModel" class="form-label enhanced-label">
                                                <i class="fas fa-brain"></i>
                                                النموذج
                                                <span class="label-hint">اختر النموذج المناسب لاحتياجاتك</span>
                                            </label>
                                            <select class="form-select enhanced-select" id="anthropicModel" onchange="updateModelInfo('anthropic', this.value)">
                                                <optgroup label="🟢 النماذج الاقتصادية (الأرخص)">
                                                    <option value="claude-3-haiku-20240307" data-cost="budget" data-description="نموذج سريع واقتصادي للمهام البسيطة - الأرخص">Claude 3 Haiku (اقتصادي)</option>
                                                    <option value="claude-3-5-haiku-20241022" data-cost="budget" data-description="أحدث نموذج اقتصادي مع تحسينات">Claude 3.5 Haiku (اقتصادي)</option>
                                                </optgroup>
                                                <optgroup label="🟡 النماذج المتوازنة">
                                                    <option value="claude-3-sonnet-20240229" data-cost="balanced" data-description="نموذج متوازن للاستخدام العام">Claude 3 Sonnet (متوازن)</option>
                                                    <option value="claude-3-5-sonnet-20241022" data-cost="balanced" data-description="أحدث نموذج متوازن مع أداء محسن">Claude 3.5 Sonnet (متوازن)</option>
                                                </optgroup>
                                                <optgroup label="🔴 النماذج المتقدمة">
                                                    <option value="claude-3-opus-20240229" data-cost="premium" data-description="أقوى نموذج من Anthropic للمهام المعقدة">Claude 3 Opus (متقدم)</option>
                                                </optgroup>
                                            </select>
                                            <div class="model-info" id="anthropicModelInfo">
                                                <div class="model-description">اختر نموذجاً لعرض التفاصيل</div>
                                            </div>
                                        </div>

                                        <div class="config-item test-section">
                                            <button type="button" class="btn btn-test enhanced-test-btn" onclick="testConnection('anthropic')">
                                                <i class="fas fa-plug"></i>
                                                <span class="btn-text">اختبار الاتصال</span>
                                                <span class="btn-description">تحقق من صحة المفتاح</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Google Gemini Configuration -->
                        <div class="ai-provider-card enhanced-card" id="geminiCard">
                            <div class="provider-header">
                                <div class="provider-info">
                                    <div class="provider-logo gemini-logo">
                                        <i class="fab fa-google"></i>
                                    </div>
                                    <div class="provider-details">
                                        <h5 class="provider-name">Google Gemini</h5>
                                        <p class="provider-description">نماذج متعددة الوسائط من Google للنصوص والصور</p>
                                        <div class="provider-features">
                                            <span class="feature-tag">متعدد الوسائط</span>
                                            <span class="feature-tag">سريع</span>
                                            <span class="feature-tag">مجاني جزئياً</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="provider-status">
                                    <span class="status-badge" id="geminiStatus">غير مفعل</span>
                                    <div class="provider-pricing">
                                        <span class="pricing-info">مجاني حتى 15 طلب/دقيقة</span>
                                    </div>
                                </div>
                            </div>
                            <div class="provider-config-body">
                                <div class="config-section">
                                    <div class="config-row">
                                        <div class="config-item enable-section">
                                            <div class="form-check enhanced-checkbox">
                                                <input class="form-check-input" type="checkbox" id="geminiEnabled" onchange="updateProviderStatus('gemini', this.checked)">
                                                <label class="form-check-label" for="geminiEnabled">
                                                    <span class="checkbox-text">تفعيل Google Gemini</span>
                                                    <span class="checkbox-description">تمكين استخدام نماذج Gemini</span>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="config-item api-key-section">
                                            <label for="geminiApiKey" class="form-label enhanced-label">
                                                <i class="fas fa-key"></i>
                                                مفتاح API
                                                <span class="label-hint">احصل عليه من aistudio.google.com</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="password" class="form-control enhanced-input" id="geminiApiKey" placeholder="AIza..." dir="ltr">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('geminiApiKey')" title="إظهار/إخفاء كلمة المرور">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="input-hint">يبدأ مفتاح Google عادة بـ "AIza"</div>
                                        </div>
                                    </div>

                                    <div class="config-row">
                                        <div class="config-item model-section">
                                            <label for="geminiModel" class="form-label enhanced-label">
                                                <i class="fas fa-brain"></i>
                                                النموذج
                                                <span class="label-hint">اختر النموذج المناسب لاحتياجاتك</span>
                                            </label>
                                            <select class="form-select enhanced-select" id="geminiModel" onchange="updateModelInfo('gemini', this.value)">
                                                <optgroup label="🟢 النماذج الاقتصادية (مجانية/رخيصة)">
                                                    <option value="gemini-1.5-flash" data-cost="budget" data-description="نموذج سريع ومجاني للاستخدام المحدود - الأرخص">Gemini 1.5 Flash (مجاني)</option>
                                                    <option value="gemini-1.5-flash-8b" data-cost="budget" data-description="نموذج مضغوط وسريع جداً">Gemini 1.5 Flash 8B (مجاني)</option>
                                                </optgroup>
                                                <optgroup label="🟡 النماذج المتوازنة">
                                                    <option value="gemini-1.5-pro" data-cost="balanced" data-description="نموذج متقدم مع قدرات محسنة">Gemini 1.5 Pro (متوازن)</option>
                                                    <option value="gemini-pro" data-cost="balanced" data-description="النموذج الأساسي للاستخدام العام">Gemini Pro (متوازن)</option>
                                                </optgroup>
                                                <optgroup label="🔴 النماذج المتخصصة">
                                                    <option value="gemini-pro-vision" data-cost="premium" data-description="نموذج متخصص في فهم الصور والنصوص">Gemini Pro Vision (متخصص)</option>
                                                </optgroup>
                                            </select>
                                            <div class="model-info" id="geminiModelInfo">
                                                <div class="model-description">اختر نموذجاً لعرض التفاصيل</div>
                                            </div>
                                        </div>

                                        <div class="config-item test-section">
                                            <button type="button" class="btn btn-test enhanced-test-btn" onclick="testConnection('gemini')">
                                                <i class="fas fa-plug"></i>
                                                <span class="btn-text">اختبار الاتصال</span>
                                                <span class="btn-description">تحقق من صحة المفتاح</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div> <!-- End providers-grid-container -->
                    </div>
                </div>

                <!-- AI Features Section -->
                <div class="row mt-5">
                    <div class="col-12">
                        <h3>✨ ميزات الذكاء الاصطناعي المتاحة</h3>
                        <div class="row">
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="feature-card">
                                    <div class="feature-icon">📝</div>
                                    <h5>إنشاء أوصاف المنتجات</h5>
                                    <p class="text-muted">إنشاء أوصاف تسويقية جذابة للمنتجات تلقائياً</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="feature-card">
                                    <div class="feature-icon">🎯</div>
                                    <h5>عناوين صفحات الهبوط</h5>
                                    <p class="text-muted">اقتراح عناوين مقنعة لصفحات الهبوط</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="feature-card">
                                    <div class="feature-icon">📄</div>
                                    <h5>محتوى تسويقي</h5>
                                    <p class="text-muted">كتابة محتوى تسويقي كامل لصفحات الهبوط</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="feature-card">
                                    <div class="feature-icon">🔍</div>
                                    <h5>أوصاف SEO</h5>
                                    <p class="text-muted">إنشاء أوصاف ميتا محسنة لمحركات البحث</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load AI Settings JavaScript -->
    <script src="js/ai-settings.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AI settings when loaded in admin panel
            if (typeof window.loadAISettings === 'function') {
                window.loadAISettings();
            }
            if (typeof window.setupEventListeners === 'function') {
                window.setupEventListeners();
            }
        });
    </script>
</body>
</html>
