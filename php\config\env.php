<?php
/**
 * Environment Configuration Loader for Mossaab Landing Page
 * Securely loads environment variables from .env file
 */

// Security check
if (!defined('SECURITY_CHECK')) {
    die('Direct access not allowed');
}

class EnvLoader
{
    private static $loaded = false;
    private static $env = [];

    /**
     * Load environment variables from .env file
     */
    public static function load($envFile = null)
    {
        if (self::$loaded) {
            return;
        }

        if ($envFile === null) {
            $envFile = dirname(dirname(__DIR__)) . '/.env';
        }

        if (!file_exists($envFile)) {
            error_log("Environment file not found: $envFile");
            return;
        }

        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // Skip comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }

            // Parse key=value pairs
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);

                // Remove quotes if present
                if (preg_match('/^(["\'])(.*)\\1$/', $value, $matches)) {
                    $value = $matches[2];
                }

                // Store in both $_ENV and our internal array
                $_ENV[$key] = $value;
                self::$env[$key] = $value;
                
                // Also set as environment variable if not already set
                if (!getenv($key)) {
                    putenv("$key=$value");
                }
            }
        }

        self::$loaded = true;
    }

    /**
     * Get environment variable value
     */
    public static function get($key, $default = null)
    {
        self::load();
        
        // Check in order: $_ENV, getenv(), internal array, default
        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }
        
        $value = getenv($key);
        if ($value !== false) {
            return $value;
        }
        
        if (isset(self::$env[$key])) {
            return self::$env[$key];
        }
        
        return $default;
    }

    /**
     * Get boolean environment variable
     */
    public static function getBool($key, $default = false)
    {
        $value = self::get($key, $default);
        
        if (is_bool($value)) {
            return $value;
        }
        
        return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
    }

    /**
     * Get integer environment variable
     */
    public static function getInt($key, $default = 0)
    {
        $value = self::get($key, $default);
        return (int) $value;
    }

    /**
     * Check if environment variable exists
     */
    public static function has($key)
    {
        self::load();
        return isset($_ENV[$key]) || getenv($key) !== false || isset(self::$env[$key]);
    }

    /**
     * Get all environment variables
     */
    public static function all()
    {
        self::load();
        return array_merge(self::$env, $_ENV);
    }
}

// Auto-load environment variables
EnvLoader::load();
