<?php
/**
 * Test Config Path Resolution
 */

echo "Testing config path resolution...\n\n";

// Test from root directory
echo "From root directory:\n";
echo "php/config.php exists: " . (file_exists('php/config.php') ? 'YES' : 'NO') . "\n";
echo "config/config.php exists: " . (file_exists('config/config.php') ? 'YES' : 'NO') . "\n\n";

// Test from php/api directory perspective
echo "From php/api/ directory perspective:\n";
echo "../config.php exists: " . (file_exists('php/config.php') ? 'YES' : 'NO') . "\n";
echo "../../config/config.php exists: " . (file_exists('config/config.php') ? 'YES' : 'NO') . "\n\n";

// Test actual include
echo "Testing actual include of php/config.php:\n";
try {
    require_once 'php/config.php';
    echo "✅ Successfully included php/config.php\n";
    
    // Test database connection
    if (function_exists('getPDOConnection')) {
        $pdo = getPDOConnection();
        echo "✅ Database connection successful\n";
    } else {
        echo "⚠️ getPDOConnection function not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error including php/config.php: " . $e->getMessage() . "\n";
}

echo "\nDone.\n";
?>
