-- Create products table
CREATE TABLE IF NOT EXISTS `produits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('book','backpack','laptop') NOT NULL,
  `titre` varchar(255) NOT NULL,
  `description` text,
  `prix` decimal(10,2) NOT NULL,
  `stock` int(11) NOT NULL DEFAULT '0',
  `image_url` varchar(255),
  -- Book specific fields
  `auteur` varchar(255),
  -- Backpack specific fields
  `materiel` varchar(255),
  `capacite` varchar(50),
  -- Laptop specific fields
  `processeur` varchar(255),
  `ram` varchar(50),
  `stockage` varchar(50),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create landing pages table
CREATE TABLE IF NOT EXISTS `landing_pages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `produit_id` int(11) NOT NULL,
  `titre` varchar(255) NOT NULL,
  `contenu_gauche` text,
  `contenu_droit` text,
  `lien_url` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create landing page images table
CREATE TABLE IF NOT EXISTS `landing_page_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `landing_page_id` int(11) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `ordre` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  FOREIGN KEY (`landing_page_id`) REFERENCES `landing_pages` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;