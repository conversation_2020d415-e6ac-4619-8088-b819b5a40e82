<?php
echo "=== Testing API Endpoints ===\n";

// Test products API
echo "\n1. Testing products API...\n";
$url = 'http://localhost/php/api/products.php';
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Content-Type: application/json'
    ]
]);

try {
    $response = file_get_contents($url, false, $context);
    if ($response !== false) {
        $data = json_decode($response, true);
        if (isset($data['products'])) {
            echo "✓ Products API works: " . count($data['products']) . " products found\n";
        } else {
            echo "✓ Products API response: " . substr($response, 0, 100) . "...\n";
        }
    } else {
        echo "✗ Could not connect to products API\n";
    }
} catch (Exception $e) {
    echo "✗ Error testing products API: " . $e->getMessage() . "\n";
}

// Test dashboard stats API
echo "\n2. Testing dashboard stats API...\n";
$url = 'http://localhost/php/api/dashboard-stats.php';

try {
    $response = file_get_contents($url, false, $context);
    if ($response !== false) {
        $data = json_decode($response, true);
        echo "✓ Dashboard stats API response: " . substr($response, 0, 100) . "...\n";
    } else {
        echo "✗ Could not connect to dashboard stats API\n";
    }
} catch (Exception $e) {
    echo "✗ Error testing dashboard stats API: " . $e->getMessage() . "\n";
}

echo "\n=== API Tests Completed ===\n";
?>
