<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المنتجات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔍 اختبار API المنتجات</h1>
    
    <button onclick="testProductsAPI()">اختبار API المنتجات</button>
    <button onclick="testFromAdmin()">اختبار من مجلد الإدارة</button>
    <button onclick="testDatabaseDirect()">اختبار قاعدة البيانات مباشرة</button>
    
    <div id="results"></div>

    <script>
        function addResult(title, content, type = 'result') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            results.appendChild(div);
        }

        async function testProductsAPI() {
            addResult('🔄 Testing Products API...', 'Starting test...');
            
            try {
                console.log('Testing products API...');
                const response = await fetch('/php/api/products.php');
                
                addResult('📡 Response Status', `Status: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                const text = await response.text();
                addResult('📄 Raw Response', text);
                
                try {
                    const json = JSON.parse(text);
                    addResult('✅ JSON Parse Success', JSON.stringify(json, null, 2), 'success');
                    
                    if (json.success && json.products) {
                        addResult('📦 Products Found', `Found ${json.products.length} products`, 'success');
                        json.products.forEach((product, index) => {
                            addResult(`Product ${index + 1}`, 
                                `ID: ${product.id}\nTitle: ${product.titre}\nType: ${product.type}\nActive: ${product.actif}`);
                        });
                    } else {
                        addResult('⚠️ No Products', 'API response does not contain products array', 'warning');
                    }
                } catch (parseError) {
                    addResult('❌ JSON Parse Error', parseError.message, 'error');
                }
                
            } catch (error) {
                addResult('❌ API Test Failed', error.message, 'error');
            }
        }

        async function testFromAdmin() {
            addResult('🔄 Testing from Admin Path...', 'Testing relative path from admin...');
            
            try {
                // Simulate the path used in admin panel
                const response = await fetch('../php/api/products.php');
                
                addResult('📡 Admin Path Response', `Status: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                const text = await response.text();
                addResult('📄 Admin Path Raw Response', text);
                
            } catch (error) {
                addResult('❌ Admin Path Test Failed', error.message, 'error');
            }
        }

        async function testDatabaseDirect() {
            addResult('🔄 Testing Database Direct...', 'Testing database connection...');
            
            try {
                const response = await fetch('/debug_products.php');
                const text = await response.text();
                
                // Extract just the text content, not the full HTML
                const parser = new DOMParser();
                const doc = parser.parseFromString(text, 'text/html');
                const content = doc.body.textContent || doc.body.innerText || '';
                
                addResult('🗄️ Database Debug', content.substring(0, 1000) + '...', 'success');
                
            } catch (error) {
                addResult('❌ Database Test Failed', error.message, 'error');
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testProductsAPI();
            }, 1000);
        });
    </script>
</body>
</html>
